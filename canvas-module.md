# Canvas Module Documentation

## Overview

The Canvas module provides a feature-rich drawing and image editing environment based on Konva.js. It supports various operations like drawing, image transformation, generation of images using AI, and magic fill functionality. The module follows a component-based architecture with state management using Zustand.

## Architecture

```
src/modules/canvas/
├── actions/           # Server actions and API calls
├── components/        # UI components  
│   ├── canvas-layers/ # Layer-specific components
│   ├── controls/      # UI controls (zoom, etc.)
│   ├── overlays/      # Loading and other overlays
│   └── toolbar/       # Toolbars for different operations
├── hooks/             # Custom React hooks for canvas operations
├── store/             # Zustand store for state management
├── types/             # TypeScript type definitions
└── utils/             # Utility functions
```

## Main Components

### `ImageCanvas`

The main component that orchestrates all canvas functionality. It renders the Konva Stage and manages:
- Drawing and masking operations
- Image transformations
- Selection areas
- Toolbar interactions
- Image generation and magic fill operations

### Canvas Layers Components

1. **`CanvasLayers`**: Renders input images, generated images, and brush composites
2. **`PlaceholderLayer`**: Renders placeholder elements for ongoing generation tasks
3. **`ActiveStrokePreview`**: Renders a real-time preview of the current brush stroke
4. **`LoadingOverlay`**: Shows loading overlay during image generation or upload

### Toolbar Components

1. **`MainToolbar`**: Primary toolbar with mode switching, undo/redo, and other actions
2. **`PromptInput`**: Input for AI image generation prompts
3. **`MagicFillToolbar`**: Specialized toolbar for magic fill operations

## Custom Hooks

### `useCanvasZoom`
Manages zoom functionality including wheel events, zoom in/out, and zoom reset.

```typescript
const { 
  handleWheel, 
  zoomIn, 
  zoomOut, 
  resetZoom, 
  initializeZoom
} = useCanvasZoom(options);
```

### `useCanvasOperations`
Handles mouse events for drawing, selection, and other canvas operations.

```typescript
const { 
  isDrawing,
  handleMouseDown,
  handleMouseMove,
  handleMouseUp,
  handleStageClick,
  renderActiveStrokePreview
} = useCanvasOperations();
```

### `useCanvasExport`
Provides functionality for exporting selections, capturing areas, and uploading images.

```typescript
const { 
  captureSelectionAreaImage, 
  captureAndUploadSelectionArea, 
  exportSelectionAsImage 
} = useCanvasExport();
```

### `useMagicFill`
Implements the magic fill functionality using drawn masks to generate images.

```typescript
const { handleMagicFill } = useMagicFill(userId);
```

### `useCanvasItems`
Manages canvas items like images and placeholders, handling transformations and selections.

```typescript
const {
  handleImageClick,
  handleImageDragEnd,
  handleTransformEnd,
  handleImageUpdate
} = useCanvasItems();
```

### `useFileUpload`
Manages file uploads to the canvas.

```typescript
const { handleFileInput } = useFileUpload();
```

### `useGenerationTasks`
Manages image generation tasks and their lifecycle.

```typescript
const {
  handleGenerate,
  addGenerationTask,
  updateGenerationTask,
  removeGenerationTask
} = useGenerationTasks();
```

## State Management

The module uses Zustand for state management via `useCanvasStore`. Key aspects of the store:

1. **Basic Canvas State**:
   - Mode (draw, move, generate, etc.)
   - Brush properties
   - Selection areas
   - Zoom level

2. **Images and Drawing**:
   - Input images
   - Generated images
   - Brush lines
   - Transformations

3. **Generation Tasks**:
   - Tracking progress of image generation
   - Managing placeholders

4. **History**:
   - Undo/redo functionality
   - Line history

## Current Implementation Analysis

### Areas for Improvement

1. **Inconsistent Custom Hook Usage**: The `ImageCanvas` component directly implements functionality that should be delegated to custom hooks, leading to code duplication and maintainability issues.

2. **Inline Event Handlers**: Many event handlers are defined inline instead of utilizing the handlers from custom hooks.

3. **Complex Component Logic**: The `ImageCanvas` component is over 1000 lines, making it difficult to maintain. Much of this logic could be extracted to custom hooks.

4. **Direct DOM Manipulation**: Some areas interact directly with the DOM instead of using React's declarative approach.

5. **Duplicate State Management**: Some state is managed both in the component and in the store, leading to potential synchronization issues.

6. **TypeScript Integration Issues**: The implementation has several TypeScript issues, particularly with hook parameter typing and usage patterns.

## Refactoring Completed

We've successfully refactored the `ImageCanvas` component to better utilize custom hooks. Key changes made:

1. **Hook Integration**: Properly imported and utilized custom hooks for various canvas operations:
   - `useCanvasZoom` for zoom operations
   - `useCanvasOperations` for mouse event handling
   - `useCanvasExport` for exporting canvas areas
   - `useMagicFill` for magic fill operations
   - `useCanvasItems` for managing image transformations
   - `useGenerationTasks` for AI image generation

2. **Custom Placeholder Handler Hook**: Created a `usePlaceholderHandlers` custom hook to manage placeholder interactions.

3. **Improved Event Handling**: Replaced inline event handlers with organized hook-based handlers.

4. **Callback Optimizations**: Added `useCallback` to event handlers to prevent unnecessary rerenders.

## Remaining Issues and Future Improvements

1. **TypeScript Type Definitions**: There are still some typing issues, particularly with the Zustand store integration. A more comprehensive type system would improve development experience.

2. **Component Size**: The `ImageCanvas` component, while improved, is still very large. Further decomposition into smaller, focused components would improve maintainability.

3. **Custom Hook Design**: Some hooks could be redesigned to better handle dependencies and follow React patterns. For example, the `useCanvasZoom` hook could be improved to handle stageRef internally.

4. **Store Integration**: The interaction between component state and store state could be improved to reduce duplication and ensure consistency.

5. **Error Handling**: More robust error handling should be implemented, particularly for network operations and image manipulation.

## Recommended Next Steps

1. **Complete Type System**: Resolve remaining TypeScript issues with proper type definitions.

2. **Component Extraction**: Further break down the `ImageCanvas` component into smaller, more focused components:
   ```typescript
   // Example decomposition
   <ImageCanvas>
     <CanvasBackground />
     <CanvasWorkspace>
       <CanvasLayers />
       <PlaceholderLayer />
       <SelectionLayer />
     </CanvasWorkspace>
     <CanvasControls>
       <MainToolbar />
       <ZoomControls />
       <PromptInput />
     </CanvasControls>
   </ImageCanvas>
   ```

3. **Hook Refinement**: Refine existing hooks to better follow React patterns and reduce dependencies:
   ```typescript
   // Current pattern
   const { handleWheel } = useCanvasZoom();
   const handleWheelEvent = (e) => handleWheel(e, stageRef);

   // Better pattern
   const { handleWheel } = useCanvasZoom(stageRef);
   ```

4. **Progressive Enhancement**: Consider adding features like:
   - Collaborative editing
   - History navigation UI
   - More export options
   - Custom brush tools

5. **Performance Optimizations**: Add:
   - Debounced handlers for high-frequency events
   - Virtualization for large canvases
   - Layer caching
   - Memoized rendering of static elements

## Conclusion

The Canvas module has been significantly improved by better utilizing custom hooks, but still has room for further refinement. The core functionality is solid, with a good separation of concerns between components, hooks, and the store. The next phase of refactoring should focus on TypeScript integration, component decomposition, and performance optimizations.

By following these recommendations, the Canvas module will become more maintainable, performant, and extensible, providing a robust foundation for future feature development. 