import React from "react";
import { unstable_noStore as noStore } from "next/cache";
import CanvasWrapper from "@/modules/canvas/components/canvas-wrapper";
import { PaywallDialog } from "@/modules/payments/components/paywall-dialog";
import { getUserImageCredits } from "@/modules/dashboard/main-features/actions/image-credits";
import { currentUser } from "@/modules/auth/actions/user-actions";

export default async function CanvasPage() {
  noStore();

  const user = await currentUser();
  const credits = user ? await getUserImageCredits() : 0;
  const userCredits = Number(credits) || 0;
  const showPaywall = userCredits < 1;

  return (
    <div className="relative flex flex-col h-full bg-gray-50 dark:bg-gray-900 canvas-container">
      <div className="flex-1" style={{ height: "100vh" }}>
        <CanvasWrapper />
      </div>
      {showPaywall && (
        <PaywallDialog
          open={true}
          feature="edit-image"
          credits={userCredits}
          requiredCredits={1}
          title="Get More Credits"
          description="You need 1 credit to edit images"
          persistDismissal={false}
        />
      )}
    </div>
  );
} 