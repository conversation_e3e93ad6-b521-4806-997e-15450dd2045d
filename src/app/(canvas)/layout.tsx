import React from "react";
import { Metada<PERSON> } from "next";
import { TooltipProvider } from "@/modules/ui/tooltip";

export const metadata: Metadata = {
  title: "Image Canvas | Renovaitor",
  description: "Edit and transform your images with AI-powered tools.",
};

interface CanvasLayoutProps {
  children: React.ReactNode;
}

export default function CanvasLayout({ children }: CanvasLayoutProps) {
  return (
    <div className="flex w-screen h-screen bg-background">
      <div className="flex-1 w-full h-full flex flex-col overflow-hidden">
        <main className="flex-1 relative">{children}</main>
      </div>
    </div>
  );
} 