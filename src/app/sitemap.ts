import { MetadataRoute } from "next";
import { getDesignSuggestions } from "@/modules/dashboard/design-suggestion/actions/get-design-suggestions";

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = "https://renovaitor.com";

  // Get all design suggestions
  const designSuggestions = await getDesignSuggestions();

  // Create sitemap entries for design suggestion pages
  const designSuggestionUrls = designSuggestions.map((suggestion) => ({
    url: `${baseUrl}/dashboard/design-suggestions/${suggestion.slug}`,
    lastModified: suggestion.createdAt,
  }));

  // Add other important pages
  const routes = ["", "/dashboard"].map((route) => ({
    url: `${baseUrl}${route}`,
    lastModified: new Date(),
  }));

  return [...routes, ...designSuggestionUrls];
}
