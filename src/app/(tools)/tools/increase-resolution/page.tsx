import React from "react";
import { unstable_noStore as noStore } from "next/cache";
import { UpscaleImageDisplay } from "@/modules/dashboard/main-features/components/image-display";
import UpscaleSettingsForm from "@/modules/dashboard/main-features/components/settings-form/upscale/upscale-image-form";
import { ScrollArea } from "@/modules/ui/scroll-area";
import { PaywallDialog } from "@/modules/payments/components/paywall-dialog";
import { getUserImageCredits } from "@/modules/dashboard/main-features/actions/image-credits";
import { currentUser } from "@/modules/auth/actions/user-actions";

export default async function UpscalePage() {
  noStore();

  const user = await currentUser();
  const credits = user ? await getUserImageCredits() : 0;
  const userCredits = Number(credits) || 0;
  const showPaywall = userCredits < 1;

  return (
    <>
      <div className="flex flex-col md:flex-row h-full">
        <div className="w-full md:w-96 flex-shrink-0 border-b md:border-r border-gray-200 dark:border-gray-700 order-1 p-4">
          <UpscaleSettingsForm />
        </div>
        <div className="flex-grow order-2">
          <ScrollArea className="h-full">
            <div className="min-h-full p-6">
              <UpscaleImageDisplay />
            </div>
          </ScrollArea>
        </div>
      </div>

      {showPaywall && (
        <PaywallDialog
          open={true}
          feature="increase-resolution"
          credits={userCredits}
          requiredCredits={1}
          title="Get More Credits"
          description="You need 1 credit to increase image resolution"
          persistDismissal={false}
        />
      )}
    </>
  );
}
