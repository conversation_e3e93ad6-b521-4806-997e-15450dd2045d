import React from "react";
import { unstable_noStore as noStore } from "next/cache";
import {
  StyleTransferImageDisplay,
  UpscaleImageDisplay,
} from "@/modules/dashboard/main-features/components/image-display";
import StyleTransferSettingsForm from "@/modules/dashboard/main-features/components/settings-form/style-transfer/style-transfer-settings-form";
import { ScrollArea } from "@/modules/ui/scroll-area";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/modules/ui/tabs";
import { PaywallDialog } from "@/modules/payments/components/paywall-dialog";
import { getUserImageCredits } from "@/modules/dashboard/main-features/actions/image-credits";
import { currentUser } from "@/modules/auth/actions/user-actions";

export default async function StyleTransferPage() {
  noStore();

  const user = await currentUser();
  const credits = user ? await getUserImageCredits() : 0;
  const userCredits = Number(credits) || 0;
  const showPaywall = userCredits < 1;

  return (
    <>
      <div className="flex flex-col md:flex-row h-[calc(100dvh-4rem)]">
        <div className="w-full md:w-96 flex-shrink-0 border-b md:border-r border-gray-200 dark:border-gray-700 order-1 p-4">
          <StyleTransferSettingsForm />
        </div>
        <div className="flex-grow order-2">
          <Tabs defaultValue="style-transfer" className="h-full flex flex-col">
            <div className="px-4 py-2 flex justify-center md:justify-start">
              <TabsList className="w-full md:w-auto">
                <TabsTrigger value="style-transfer">Style Transfer</TabsTrigger>
                <TabsTrigger value="upscale">Upscale</TabsTrigger>
              </TabsList>
            </div>
            <div className="flex-1">
              <TabsContent
                value="style-transfer"
                className="h-full pb-20 md:pb-0"
              >
                <ScrollArea className="h-full">
                  <div className="min-h-full p-6">
                    <StyleTransferImageDisplay />
                  </div>
                </ScrollArea>
              </TabsContent>
              <TabsContent value="upscale" className="h-full pb-20 md:pb-0">
                <ScrollArea className="h-full">
                  <div className="min-h-full p-6">
                    <UpscaleImageDisplay />
                  </div>
                </ScrollArea>
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </div>

      {showPaywall && (
        <PaywallDialog
          open={true}
          feature="style-transfer"
          credits={userCredits}
          requiredCredits={1}
          title="Get More Credits"
          description="You need 1 credit to generate style transfers"
          persistDismissal={false}
        />
      )}
    </>
  );
}
