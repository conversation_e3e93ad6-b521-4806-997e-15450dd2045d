import React from "react";
import { unstable_noStore as noStore } from "next/cache";
import { BackgroundRemovalImageDisplay } from "@/modules/dashboard/main-features/components/image-display";
import BackgroundRemovalSettingsForm from "@/modules/dashboard/main-features/components/settings-form/background-removal/background-removal-settings-form";
import { ScrollArea } from "@/modules/ui/scroll-area";
import { PaywallDialog } from "@/modules/payments/components/paywall-dialog";
import { getUserImageCredits } from "@/modules/dashboard/main-features/actions/image-credits";
import { currentUser } from "@/modules/auth/actions/user-actions";

export default async function RemoveBackgroundPage() {
  noStore();

  const user = await currentUser();
  const credits = user ? await getUserImageCredits() : 0;
  const userCredits = Number(credits) || 0;
  const showPaywall = userCredits < 1;

  return (
    <>
      <div className="flex flex-col md:flex-row h-full">
        <div className="w-full md:w-96 flex-shrink-0 border-b md:border-r border-gray-200 dark:border-gray-700 order-1 p-4">
          <BackgroundRemovalSettingsForm />
        </div>
        <div className="flex-grow order-2">
          <ScrollArea className="h-full">
            <div className="min-h-full p-6">
              <BackgroundRemovalImageDisplay />
            </div>
          </ScrollArea>
        </div>
      </div>

      {showPaywall && (
        <PaywallDialog
          open={true}
          feature="remove-background"
          credits={userCredits}
          requiredCredits={1}
          title="Get More Credits"
          description="You need 1 credit to remove background from images"
          persistDismissal={false}
        />
      )}
    </>
  );
}
