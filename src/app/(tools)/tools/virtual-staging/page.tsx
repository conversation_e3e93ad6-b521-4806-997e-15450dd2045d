import React from "react";
import { unstable_noStore as noStore } from "next/cache";
import {
  VirtualStagingImageDisplay,   
  
} from "@/modules/dashboard/main-features/components/image-display";
import VirtualStagingSettingsForm from "@/modules/dashboard/main-features/components/settings-form/virtual-staging/virtual-staging-settings-form";
import { ScrollArea } from "@/modules/ui/scroll-area";
import { getUserImageCredits } from "@/modules/dashboard/main-features/actions/image-credits";
import { currentUser } from "@/modules/auth/actions/user-actions";
import { checkPrivacyAccess } from "@/modules/dashboard/main-features/actions/check-privacy-access";
import { VirtualStagingStateProvider } from "@/modules/dashboard/main-features/providers/virtual-staging-state-provider";

export default async function VirtualStagingPage() {
  noStore();

  // Get all server data in one place to pass to client components
  const user = await currentUser();
  const userCredits = user ? await getUserImageCredits() : 0;

  // Determine initial privacy access state (no need to show UI yet as client will handle it)
  // We're just preloading this data for hydration
  const privacyAccess = user?.id ? await checkPrivacyAccess(user.id) : null;

  return (
    <VirtualStagingStateProvider initialCredits={Number(userCredits) || 0}>
      <div className="flex flex-col md:flex-row h-full">
        <div className="w-full md:w-96 flex-shrink-0 bg-inherit border-b md:border-r border-gray-200 dark:border-gray-700 order-1 p-4">
          <VirtualStagingSettingsForm />
        </div>
        <div className="flex-grow order-2">
          <ScrollArea className="h-full">
            <div className="min-h-full p-6">
              <VirtualStagingImageDisplay />
            </div>
          </ScrollArea>
        </div>
      </div>
    </VirtualStagingStateProvider>
  );
}
