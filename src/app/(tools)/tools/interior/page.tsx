import React from "react";
import { unstable_noStore as noStore } from "next/cache";
import {
  InteriorImageDisplay,
  UpscaleImageDisplay,
} from "@/modules/dashboard/main-features/components/image-display";
import InteriorSettingsForm from "@/modules/dashboard/main-features/components/settings-form/interior/interior-settings-form";
import { ScrollArea } from "@/modules/ui/scroll-area";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/modules/ui/tabs";
import { PaywallDialog } from "@/modules/payments/components/paywall-dialog";
import { getUserImageCredits } from "@/modules/dashboard/main-features/actions/image-credits";
import { currentUser } from "@/modules/auth/actions/user-actions";

export default async function InteriorPage() {
  noStore();

  const user = await currentUser();
  const credits = user ? await getUserImageCredits() : 0;
  const userCredits = Number(credits) || 0;
  const showPaywall = userCredits < 1;

  return (
    <>
      <div className="flex flex-col md:flex-row h-full">
        <div className="w-full md:w-96 flex-shrink-0 overflow-y-auto border-b md:border-r border-gray-200 dark:border-gray-700 order-1 p-4">
          <InteriorSettingsForm />
        </div>
        <div className="flex-grow order-2">
            <div className="px-4 py-2 flex justify-center md:justify-start">
             
            <div className="flex-1 overflow-auto">
                <ScrollArea className="h-full">
                  <div className="min-h-full">
                    <InteriorImageDisplay />
                  </div>
                </ScrollArea>
            </div>
            </div>
        </div>
      </div>

      {showPaywall && (
        <PaywallDialog
          open={true}
          feature="interior"
          credits={userCredits}
          requiredCredits={1}
          title="Get More Credits"
          description="You need 1 credit to generate interior designs"
          persistDismissal={false}
        />
      )}
    </>
  );
}
