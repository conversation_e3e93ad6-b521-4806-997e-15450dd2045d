import { redirect } from "next/navigation";
import { headers } from "next/headers";
import { currentUser } from "@/modules/auth/actions/user-actions";
import { Logo } from "@/modules/marketing/components/layout/header/logo";
import UserNavigation from "@/modules/marketing/components/layout/header/user-navigation";
import Link from "next/link";

export default async function ToolsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const user = await currentUser();

  if (!user) {
    // Get the current path server-side using headers
    const headersList = headers();
    const fullPath = headersList.get("x-invoke-path") || "/dashboard";

    // Redirect to login with the full path as callback
    redirect(`/login?callbackUrl=${encodeURIComponent(fullPath)}`);
  }

  return (
    <div className="flex flex-col h-screen">
      <header className="w-full border-b border-gray-200 dark:border-gray-700 z-10 bg-background flex-shrink-0">
        <div className="px-4 flex h-14 items-center justify-between">
          <div className="h-10 flex items-center">
            <Logo href="/dashboard" width={40} height={40} />
          </div>
          <UserNavigation />
        </div>
      </header>
      <main className="h-[calc(100vh-3.5rem)] overflow-hidden">
        {children}
      </main>
    </div>
  );
}
