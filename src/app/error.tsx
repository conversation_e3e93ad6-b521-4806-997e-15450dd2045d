"use client";

import { useEffect } from "react";
import { <PERSON><PERSON> } from "@/modules/ui/button";
import { AlertTriangleIcon } from "lucide-react";

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Optionally log to error reporting service
    console.error(error);
  }, [error]);

  return (
    <html>
      <body>
        <div className="min-h-screen flex items-center justify-center px-4">
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <AlertTriangleIcon className="h-12 w-12 text-red-500" />
            </div>
            <h1 className="text-2xl font-bold mb-2">Something went wrong!</h1>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              {error.message || "An unexpected error occurred"}
            </p>
            <Button onClick={reset}>Try again</Button>
          </div>
        </div>
      </body>
    </html>
  );
}
