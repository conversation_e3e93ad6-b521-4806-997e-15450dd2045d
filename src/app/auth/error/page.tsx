"use client";

import Link from "next/link";
import { useSearchParams } from "next/navigation";

const AuthError = () => {
  const searchParams = useSearchParams();
  const error = searchParams.get("error");

  const getErrorMessage = (error: string | null) => {
    switch (error) {
      case "AccessDenied":
        return "Access denied. You don't have permission to sign in.";
      case "Verification":
        return "The verification link has expired or is invalid.";
      case "Configuration":
        return "There is a problem with the server configuration.";
      case "CredentialsSignin":
        return "Invalid login credentials. Please check your username and password.";
      default:
        return "An error occurred during authentication.";
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-950">
      <div className="max-w-md w-full px-6 py-8 text-center">
        <h1 className="text-2xl font-bold text-white mb-2">
          Authentication Error
        </h1>
        <p className="text-gray-400 mb-8">{getErrorMessage(error)}</p>
        <div className="space-y-4">
          <button
            onClick={() => (window.location.href = "/login")}
            className="w-full py-2 px-4 bg-purple-600 hover:bg-purple-700 rounded-md text-white font-medium transition-colors"
          >
            Try Again
          </button>
          <Link
            href="/support"
            className="block w-full py-2 px-4 bg-transparent border border-gray-700 hover:border-gray-600 rounded-md text-gray-300 font-medium transition-colors"
          >
            Contact Support
          </Link>
        </div>
      </div>
    </div>
  );
};

export default AuthError;
