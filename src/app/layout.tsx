import { cn } from "@/lib/utils";
import { <PERSON>, <PERSON>o } from "next/font/google";
import "./globals.css";
import React, { Suspense } from "react";
import { Toaster } from "@/modules/ui/toaster";
import { Providers } from "./providers";
import { Metadata, Viewport } from "next";
import { Loading } from "@/modules/ui/loading";
import { Toaster as SonnerToaster } from "sonner";
import { GoogleTagManager } from "@next/third-parties/google";
import { CookieConsentProvider } from "@/components/cookie-consent/cookie-consent-context";
import { CookieConsent } from "@/components/cookie-consent/cookie-consent";
import { ScrollArea } from "@/modules/ui/scroll-area";

const inter = Inter({ subsets: ["latin"] });
const roboto = Roboto({
  weight: ["400", "700"],
  style: ["normal", "italic"],
  subsets: ["latin"],
  variable: "--font-roboto",
});

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  minimumScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "black" },
  ],
};

export const metadata: Metadata = {
  title: {
    default: "Renovaitor | AI Interior Design & Virtual Staging Platform",
    template: "%s | Renovaitor - AI Design Tools",
  },
  description:
    "Transform spaces instantly with AI-powered interior design and virtual staging. Create stunning room designs, renovations, and home makeovers in seconds.",
  metadataBase: new URL("https://renovaitor.com"),
  alternates: {
    languages: {
      "en-US": "/en-US",
    },
  },
  openGraph: {
    title: "Renovaitor | Transform Spaces with AI Interior Design",
    description:
      "Create stunning interior designs instantly with AI. Virtual staging, room makeovers, and renovation previews in seconds. Try free today.",
    url: "https://renovaitor.com",
    siteName: "Renovaitor",
    images: [
      {
        url: "https://renovaitor.com/og-image.png",
        width: 1200,
        height: 630,
        alt: "AI-Powered Interior Design Platform - Before and After Transformations",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  twitter: {
    card: "summary_large_image",
    title: "Transform Your Space with AI Interior Design | Renovaitor",
    description:
      "Design dream spaces instantly with AI. Virtual staging, room makeovers, and renovation previews at your fingertips. Start free today.",
    creator: "@renovaitor",
    images: ["https://renovaitor.com/og-image.png"],
  },
  icons: {
    icon: [
      {
        rel: "icon",
        url: "/favicon-light.ico",
        media: "(prefers-color-scheme: light)",
        type: "image/x-icon",
        sizes: "any",
      },
      {
        rel: "icon",
        url: "/favicon-dark.ico",
        media: "(prefers-color-scheme: dark)",
        type: "image/x-icon",
        sizes: "any",
      },
    ],
    shortcut: "/favicon-light.ico", // Default favicon
    apple: "/apple-touch-icon.png", // If you have one
  },
  manifest: "/site.webmanifest",
  keywords: [
    "AI Interior Design",
    "Virtual Staging",
    "Room Makeover",
    "Home Renovation Preview",
    "AI Design Tools",
    "Interior Design AI",
    "Virtual Home Design",
    "Room Transformation",
    "AI Renovation",
    "Design Visualization",
  ],
};

const jsonLd = {
  "@context": "https://schema.org",
  "@type": "WebApplication",
  name: "Renovaitor",
  description: "AI-powered interior design and virtual staging platform",
  url: "https://renovaitor.com",
  applicationCategory: "DesignApplication",
  offers: {
    "@type": "Offer",
    price: "0",
    priceCurrency: "USD",
    description: "Start designing with free credits",
  },
  featureList: [
    "AI Interior Design",
    "Virtual Staging",
    "Room Makeovers",
    "Renovation Previews",
  ],
  operatingSystem: "Web-based",
  browserRequirements: "Requires modern web browser",
};

interface RootLayoutProps {
  children: React.ReactNode;
}

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
        />
      </head>
      <body className={`font-sans antialiased ${roboto.className}`}>
        <GoogleTagManager gtmId="GTM-MKKWF9NB" />
        <Suspense fallback={<Loading />}>
          <CookieConsentProvider>
            <Providers>
              <ScrollArea
                className="h-screen w-full"
                role="region"
                aria-label="Main content area"
              >
                {children}
                <Toaster aria-live="polite" />
                <SonnerToaster />
              </ScrollArea>
            </Providers>
            <CookieConsent />
          </CookieConsentProvider>
        </Suspense>
      </body>
    </html>
  );
}
