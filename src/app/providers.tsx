"use client";

import { SessionProvider } from "next-auth/react";
import { ReactNode, useEffect, useState } from "react";
import { ThemeProvider } from "@/modules/marketing/components/theme-provider";
import * as Ably from "ably";
import { AblyProvider } from "ably/react";
import { Toaster } from "@/modules/ui/toaster";
import { TooltipProvider } from "@/modules/ui/tooltip";
import { CreditsProvider } from "@/modules/dashboard/main-features/providers/credits-provider";

export function Providers({ children }: { children: ReactNode }) {
  const [ablyClient, setAblyClient] = useState<Ably.Realtime | null>(null);

  useEffect(() => {
    const client = new Ably.Realtime({
      key: process.env.NEXT_PUBLIC_ABLY_API_KEY,
    });
    setAblyClient(client);

    return () => {
      client.close();
    };
  }, []);

  if (!ablyClient) return null;

  return (
    <ThemeProvider>
      <TooltipProvider>
        <SessionProvider>
          <AblyProvider client={ablyClient}>
            <CreditsProvider>
              {children}
              <Toaster />
            </CreditsProvider>
          </AblyProvider>
        </SessionProvider>
      </TooltipProvider>
    </ThemeProvider>
  );
}
