import { currentUser } from "@/modules/auth/actions/user-actions";
import prisma from "@/lib/prisma/prisma";
import { NextResponse } from "next/server";

export async function GET(
  request: Request,
  { params }: { params: { userId: string } }
) {
  const user = await currentUser();

  if (!user || user.role !== "ADMIN") {
    return new NextResponse("Unauthorized", { status: 401 });
  }

  const userId = params.userId;

  try {
    const [interiorDesigns, exteriorDesigns, virtualStagings, editImages] =
      await Promise.all([
        prisma.interiorDesign.findMany({
          where: { userId },
          orderBy: { createdAt: "desc" },
          take: 50,
        }),
        prisma.exteriorDesign.findMany({
          where: { userId },
          orderBy: { createdAt: "desc" },
          take: 50,
        }),
        prisma.virtualStaging.findMany({
          where: { userId },
          orderBy: { createdAt: "desc" },
          take: 50,
        }),
        prisma.editImage.findMany({
          where: { userId },
          orderBy: { createdAt: "desc" },
          take: 50,
        }),
      ]);

    return NextResponse.json({
      interiorDesigns,
      exteriorDesigns,
      virtualStagings,
      editImages,
    });
  } catch (error) {
    console.error("Failed to fetch user generations:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
