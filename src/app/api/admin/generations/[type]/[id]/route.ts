import { NextResponse } from "next/server";
import prisma from "@/lib/prisma/prisma";
import { currentUser } from "@/modules/auth/actions/user-actions";

export async function DELETE(
  req: Request,
  { params }: { params: { type: string; id: string } }
) {
  try {
    const user = await currentUser();
    if (!user || user.role !== "ADMIN") {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { type, id } = params;

    // Use switch statement for type-safe model selection
    switch (type) {
      case "interior":
        await prisma.interiorDesign.delete({
          where: { id },
        });
        break;
      case "exterior":
        await prisma.exteriorDesign.delete({
          where: { id },
        });
        break;
      case "virtualStaging":
        await prisma.virtualStaging.delete({
          where: { id },
        });
        break;
      case "editImage":
        await prisma.editImage.delete({
          where: { id },
        });
        break;
      default:
        return new NextResponse("Invalid generation type", { status: 400 });
    }

    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error("Error deleting generation:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
