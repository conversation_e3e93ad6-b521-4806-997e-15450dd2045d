import { openai } from "@ai-sdk/openai";
import { streamText, convertToCoreMessages } from "ai";
import { NextRequest } from "next/server";

export const maxDuration = 30;

export async function POST(req: NextRequest) {
  const { messages } = await req.json();

  const result = await streamText({
    model: openai("gpt-4"),
    messages: convertToCoreMessages(messages),
    prompt: `You are an expert interior designer. Generate a detailed interior design suggestion based on the user's input. 
    Provide suggestions for:
    1. Color scheme
    2. Furniture layout and key pieces
    3. Lighting ideas
    4. Decorative elements and accessories
    5. Flooring recommendations
    6. Wall treatments (paint, wallpaper, etc.)
    7. Window treatments
    8. Any unique features that complement the style and room type

    Ensure that the suggestions are appropriate for the specified budget level and incorporate any special requirements mentioned.
    Format your response using Markdown for better readability.`,
  });

  return result.toDataStreamResponse();
}
