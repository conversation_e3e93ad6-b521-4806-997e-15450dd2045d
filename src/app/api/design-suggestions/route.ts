import { openai } from "@ai-sdk/openai";
import { convertToCoreMessages, streamText } from "ai";

export const maxDuration = 30;

export async function POST(req: Request) {
  const body = await req.json();
  console.log("Received request body:", body);

  const systemPrompt = `You are an expert interior designer. Generate a detailed interior design suggestion based on the user's input. 
  Provide suggestions for:
  1. Color scheme
  2. Furniture layout and key pieces
  3. Lighting ideas
  4. Decorative elements and accessories
  5. Flooring recommendations
  6. Wall treatments (paint, wallpaper, etc.)
  7. Window treatments
  8. Any unique features that complement the style and room type

  Ensure that the suggestions are appropriate for the specified budget level and incorporate any special requirements mentioned.
  Format your response using Markdown for better readability.`;

  const messages = body.messages || [];
  if (body.message) {
    messages.push({ role: "user", content: body.message });
  }

  console.log("Processing messages:", messages);

  const result = await streamText({
    model: openai("gpt-4o"),
    messages: [
      { role: "system", content: systemPrompt },
      ...convertToCoreMessages(messages),
    ],
    experimental_providerMetadata: {
      openai: {
        maxCompletionTokens: 1000,
      },
    },
  });

  return result.toDataStreamResponse();
}
