// app/api/webhook/utils.ts
import { NextRequest } from "next/server";
import crypto from "crypto";
import * as Ably from "ably";
import prisma from "@/lib/prisma/prisma";

const REPLICATE_API_TOKEN = process.env.REPLICATE_API_TOKEN;
const ABLY_API_KEY = process.env.ABLY_API_KEY;
let WEBHOOK_SECRET: string;

const ably = new Ably.Rest(ABLY_API_KEY!);

export async function getWebhookSecret(): Promise<string> {
  if (WEBHOOK_SECRET) return WEBHOOK_SECRET;

  const response = await fetch(
    "https://api.replicate.com/v1/webhooks/default/secret",
    {
      headers: {
        Authorization: `Token ${REPLICATE_API_TOKEN}`,
      },
    }
  );

  if (!response.ok) {
    throw new Error(`Failed to fetch webhook secret: ${response.statusText}`);
  }

  const data = await response.json();
  WEBHOOK_SECRET = data.key;
  return WEBHOOK_SECRET;
}

export async function verifySignature(
  request: NextRequest,
  body: string
): Promise<boolean> {
  const signature = request.headers.get("webhook-signature");
  const timestamp = request.headers.get("webhook-timestamp");
  const webhookId = request.headers.get("webhook-id");

  if (!signature || !timestamp || !webhookId) {
    return false;
  }

  try {
    const secret = await getWebhookSecret();
    const signedContent = `${webhookId}.${timestamp}.${body}`;
    const secretBytes = Buffer.from(secret.split("_")[1], "base64");
    const expectedSignature = crypto
      .createHmac("sha256", secretBytes)
      .update(signedContent)
      .digest("base64");

    const providedSignatures = signature
      .split(" ")
      .map((sig) => sig.split(",")[1]);
    return providedSignatures.some((sig) => sig === expectedSignature);
  } catch {
    return false;
  }
}

export function sendAblyUpdate(
  userId: string,
  type: "interior" | "exterior" | "background-removal",
  update: any
) {
  console.log(`Sending Ably update for user ${userId} (${type}):`, update);
  const channel = ably.channels.get(`image-generation-${type}-${userId}`);
  channel.publish("prediction_update", update);
}

export async function refundImageCredits(userId: string, amount: number) {
  await prisma.user.update({
    where: { id: userId },
    data: { imageCredits: { increment: amount } },
  });
}
