import { NextRequest, NextResponse } from "next/server";
import { revalidatePath } from "next/cache";
import prisma from "@/lib/prisma/prisma";
import crypto from "crypto";
import * as Ably from "ably";
import {
  PredictionStatus,
  WebhookPayload,
} from "@/modules/dashboard/main-features/types";
import { z } from "zod";
import { refundImageCredits } from "@/modules/dashboard/main-features/actions/refund-image-credits";

const REPLICATE_API_TOKEN = process.env.REPLICATE_API_TOKEN;
const ABLY_API_KEY = process.env.ABLY_API_KEY;
let WEBHOOK_SECRET: string;

// Define the WebhookPayloadSchema using Zod
const WebhookPayloadSchema = z.object({
  id: z.string(),
  status: z.nativeEnum(PredictionStatus),
  output: z.union([z.string(), z.array(z.string())]).optional(),
  error: z.string().optional(),
});

const ably = new Ably.Rest(ABLY_API_KEY!);

async function getWebhookSecret(): Promise<string> {
  if (WEBHOOK_SECRET) return WEBHOOK_SECRET;

  const response = await fetch(
    "https://api.replicate.com/v1/webhooks/default/secret",
    {
      headers: {
        Authorization: `Token ${REPLICATE_API_TOKEN}`,
      },
    }
  );

  if (!response.ok) {
    throw new Error(`Failed to fetch webhook secret: ${response.statusText}`);
  }

  const data = await response.json();
  WEBHOOK_SECRET = data.key;
  return WEBHOOK_SECRET;
}

async function verifySignature(
  request: NextRequest,
  body: string
): Promise<boolean> {
  const signature = request.headers.get("webhook-signature");
  const timestamp = request.headers.get("webhook-timestamp");
  const webhookId = request.headers.get("webhook-id");

  if (!signature || !timestamp || !webhookId) {
    return false;
  }

  try {
    const secret = await getWebhookSecret();
    const signedContent = `${webhookId}.${timestamp}.${body}`;
    const secretBytes = Buffer.from(secret.split("_")[1], "base64");
    const expectedSignature = crypto
      .createHmac("sha256", secretBytes)
      .update(signedContent)
      .digest("base64");

    const providedSignatures = signature
      .split(" ")
      .map((sig) => sig.split(",")[1]);
    return providedSignatures.some((sig) => sig === expectedSignature);
  } catch {
    return false;
  }
}

function sendAblyUpdate(userId: string, update: any) {
  console.log(`Sending Ably update for user ${userId} (exterior):`, update);
  const channelName = `image-generation-exterior_design-${userId}`;
  const channel = ably.channels.get(channelName);

  channel
    .publish("prediction_update", update)
    .then(() => {
      // Publication successful
    })
    .catch((err) => {
      console.error(`Error publishing to Ably channel ${channelName}:`, err);
    });
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const isValid = await verifySignature(request, body);

    if (!isValid) {
      return NextResponse.json({ error: "Invalid signature" }, { status: 401 });
    }

    const payload: WebhookPayload = WebhookPayloadSchema.parse(
      JSON.parse(body)
    );

    if (!payload.id || !payload.status) {
      return NextResponse.json({ error: "Invalid payload" }, { status: 400 });
    }

    const exteriorDesign = await prisma.exteriorDesign.findUnique({
      where: { replicate_id: payload.id },
      include: { user: true },
    });

    if (!exteriorDesign) {
      return NextResponse.json(
        { message: "Exterior design prediction not found" },
        { status: 404 }
      );
    }

    switch (payload.status) {
      case PredictionStatus.PROCESSING:
        await handleStatusUpdate(exteriorDesign, PredictionStatus.PROCESSING);
        break;
      case PredictionStatus.SUCCEEDED:
        if (payload.output) {
          // Convert payload.output to array if it's a string
          const outputArray = Array.isArray(payload.output)
            ? payload.output
            : [payload.output];
          await handleSucceeded(exteriorDesign, outputArray);
        }
        break;
      case PredictionStatus.FAILED:
        await handleStatusUpdate(
          exteriorDesign,
          PredictionStatus.FAILED,
          payload.error || null
        );
        break;
      case PredictionStatus.CANCELED:
        await handleStatusUpdate(exteriorDesign, PredictionStatus.CANCELED);
        break;
      default:
        console.warn(`Unhandled prediction status: ${payload.status}`);
    }

    return NextResponse.json({ message: "Webhook processed successfully" });
  } catch (error) {
    console.error("Error processing webhook:", error);
    return NextResponse.json(
      { error: "Error processing webhook" },
      { status: 500 }
    );
  }
}

async function handleStatusUpdate(
  exteriorDesign: any,
  status: PredictionStatus,
  error: string | null = null
) {
  await prisma.exteriorDesign.update({
    where: { id: exteriorDesign.id },
    data: {
      status,
      completedAt: [
        PredictionStatus.FAILED,
        PredictionStatus.CANCELED,
      ].includes(status)
        ? new Date()
        : undefined,
    },
  });

  if (
    error &&
    (status === PredictionStatus.FAILED || status === PredictionStatus.CANCELED)
  ) {
    // Refund the image credit
    await refundImageCredits(exteriorDesign.userId, 1);
  }

  revalidatePath("/dashboard");

  sendAblyUpdate(exteriorDesign.userId, {
    id: exteriorDesign.replicate_id, // Use replicate_id instead of id
    status,
    error,
  });
}

async function handleSucceeded(exteriorDesign: any, output: string[]) {
  await prisma.exteriorDesign.update({
    where: { id: exteriorDesign.id },
    data: {
      status: PredictionStatus.SUCCEEDED,
      outputImages: output,
      completedAt: new Date(),
    },
  });

  revalidatePath("/dashboard");

  output.forEach((url) => {
    const updateData = {
      id: exteriorDesign.replicate_id, // Use replicate_id instead of id
      status: PredictionStatus.SUCCEEDED,
      url,
      inputImage: exteriorDesign.inputImage,
      prompt: exteriorDesign.prompt,
      style: exteriorDesign.style,
      building: exteriorDesign.building,
      excludedElements: exteriorDesign.excludedElements,
      createdAt: exteriorDesign.createdAt,
    };
    sendAblyUpdate(exteriorDesign.userId, updateData);
  });
}

export const dynamic = "force-dynamic";
