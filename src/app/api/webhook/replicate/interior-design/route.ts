import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma/prisma";
import crypto from "crypto";
import * as Ably from "ably";
import {
  PredictionStatus,
  WebhookPayload,
} from "@/modules/dashboard/main-features/types";
import { z } from "zod";
import { refundImageCredits } from "../utils";
import { revalidatePath } from "next/cache";

const REPLICATE_API_TOKEN = process.env.REPLICATE_API_TOKEN;
const ABLY_API_KEY = process.env.ABLY_API_KEY;
let WEBHOOK_SECRET: string;

// Define the WebhookPayloadSchema using Zod
const WebhookPayloadSchema = z.object({
  id: z.string(),
  status: z.nativeEnum(PredictionStatus),
  output: z.union([z.string(), z.array(z.string())]).optional(),
  error: z.string().optional(),
});

const ably = new Ably.Rest(ABLY_API_KEY!);

async function getWebhookSecret(): Promise<string> {
  if (WEBHOOK_SECRET) return WEBHOOK_SECRET;

  const response = await fetch(
    "https://api.replicate.com/v1/webhooks/default/secret",
    {
      headers: {
        Authorization: `Token ${REPLICATE_API_TOKEN}`,
      },
    }
  );

  if (!response.ok) {
    throw new Error(`Failed to fetch webhook secret: ${response.statusText}`);
  }

  const data = await response.json();
  WEBHOOK_SECRET = data.key;
  return WEBHOOK_SECRET;
}

async function verifySignature(
  request: NextRequest,
  body: string
): Promise<boolean> {
  const signature = request.headers.get("webhook-signature");
  const timestamp = request.headers.get("webhook-timestamp");
  const webhookId = request.headers.get("webhook-id");

  if (!signature || !timestamp || !webhookId) {
    return false;
  }

  try {
    const secret = await getWebhookSecret();
    const signedContent = `${webhookId}.${timestamp}.${body}`;
    const secretBytes = Buffer.from(secret.split("_")[1], "base64");
    const expectedSignature = crypto
      .createHmac("sha256", secretBytes)
      .update(signedContent)
      .digest("base64");

    const providedSignatures = signature
      .split(" ")
      .map((sig) => sig.split(",")[1]);
    return providedSignatures.some((sig) => sig === expectedSignature);
  } catch {
    return false;
  }
}

function sendAblyUpdate(userId: string, update: any) {
  console.log(`Sending Ably update for user ${userId} (interior):`, update);
  const channelName = `image-generation-interior_design-${userId}`;
  const channel = ably.channels.get(channelName);

  channel.publish("prediction_update", update);
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const isValid = await verifySignature(request, body);

    if (!isValid) {
      return NextResponse.json({ error: "Invalid signature" }, { status: 401 });
    }

    const jsonBody = JSON.parse(body);
    const payload: WebhookPayload = WebhookPayloadSchema.parse(jsonBody);

    if (!payload.id || !payload.status) {
      return NextResponse.json({ error: "Invalid payload" }, { status: 400 });
    }

    const interiorDesign = await prisma.interiorDesign.findUnique({
      where: { replicate_id: payload.id },
      include: { user: true },
    });

    if (!interiorDesign) {
      return NextResponse.json(
        { message: "Interior design prediction not found" },
        { status: 404 }
      );
    }

    switch (payload.status) {
      case PredictionStatus.PROCESSING:
        await handleStatusUpdate(interiorDesign, PredictionStatus.PROCESSING);
        break;
      case PredictionStatus.SUCCEEDED:
        if (payload.output) {
          // Convert payload.output to array if it's a string
          const outputArray = Array.isArray(payload.output)
            ? payload.output
            : [payload.output];
          await handleSucceeded(interiorDesign, outputArray);
        } else {
          console.warn("Received SUCCEEDED status without output");
          await handleStatusUpdate(interiorDesign, PredictionStatus.SUCCEEDED);
        }
        break;
      case PredictionStatus.FAILED:
        await handleStatusUpdate(
          interiorDesign,
          PredictionStatus.FAILED,
          payload.error || null
        );
        break;
      case PredictionStatus.CANCELED:
        await handleStatusUpdate(interiorDesign, PredictionStatus.CANCELED);
        break;
      default:
        console.warn(`Unhandled prediction status: ${payload.status}`);
    }

    return NextResponse.json({ message: "Webhook processed successfully" });
  } catch (error) {
    console.error("Error processing webhook:", error);
    return NextResponse.json(
      { error: "Error processing webhook" },
      { status: 500 }
    );
  }
}

async function handleStatusUpdate(
  interiorDesign: any,
  status: PredictionStatus,
  error: string | null = null
) {
  await prisma.interiorDesign.update({
    where: { id: interiorDesign.id },
    data: {
      status,
      completedAt: [
        PredictionStatus.FAILED,
        PredictionStatus.CANCELED,
      ].includes(status)
        ? new Date()
        : undefined,
    },
  });

  if (
    error &&
    (status === PredictionStatus.FAILED || status === PredictionStatus.CANCELED)
  ) {
    // Refund the image credit
    await refundImageCredits(interiorDesign.userId, 1);
  }

  revalidatePath("/dashboard");

  sendAblyUpdate(interiorDesign.userId, {
    id: interiorDesign.replicate_id, // Use replicate_id instead of id
    status,
    error,
  });
}

async function handleSucceeded(interiorDesign: any, output: string[]) {
  await prisma.interiorDesign.update({
    where: { id: interiorDesign.id },
    data: {
      status: PredictionStatus.SUCCEEDED,
      outputImages: output,
      completedAt: new Date(),
    },
  });

  revalidatePath("/dashboard");

  output.forEach((url) => {
    const updateData = {
      id: interiorDesign.replicate_id, // Use replicate_id instead of id
      status: PredictionStatus.SUCCEEDED,
      url,
      inputImage: interiorDesign.inputImage,
      prompt: interiorDesign.prompt,
      style: interiorDesign.style,
      room: interiorDesign.room,
      excludedElements: interiorDesign.excludedElements,
      createdAt: interiorDesign.createdAt,
    };
    sendAblyUpdate(interiorDesign.userId, updateData);
  });
}

export const dynamic = "force-dynamic";
