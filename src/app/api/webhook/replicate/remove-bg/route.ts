import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma/prisma";
import { verifySignature } from "../utils";
import { PredictionStatus } from "@/modules/dashboard/main-features/types";
import * as Ably from "ably";

const ABLY_API_KEY = process.env.ABLY_API_KEY;
let WEBHOOK_SECRET: string;

const ably = new Ably.Rest(ABLY_API_KEY!);

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const isValid = await verifySignature(request, body);

    if (!isValid) {
      return NextResponse.json({ error: "Invalid signature" }, { status: 401 });
    }

    const payload = JSON.parse(body);

    if (!payload.id || !payload.status) {
      return NextResponse.json({ error: "Invalid payload" }, { status: 400 });
    }

    const backgroundRemoval = await prisma.backgroundRemoval.findUnique({
      where: { replicate_id: payload.id },
      include: { user: true },
    });

    if (!backgroundRemoval) {
      return NextResponse.json(
        { message: "Background removal not found" },
        { status: 404 }
      );
    }

    switch (payload.status) {
      case "processing":
        await handleStatusUpdate(
          backgroundRemoval,
          PredictionStatus.PROCESSING
        );
        break;
      case "succeeded":
        if (payload.output) {
          await handleSucceeded(backgroundRemoval, payload.output);
        }
        break;
      case "failed":
        await handleStatusUpdate(
          backgroundRemoval,
          PredictionStatus.FAILED,
          payload.error
        );
        break;
      case "canceled":
        await handleStatusUpdate(backgroundRemoval, PredictionStatus.CANCELED);
        break;
      default:
        console.warn(`Unhandled background removal status: ${payload.status}`);
    }

    return NextResponse.json({ message: "Webhook processed successfully" });
  } catch (error) {
    console.error("Error processing background removal webhook:", error);
    return NextResponse.json(
      { error: "Error processing webhook" },
      { status: 500 }
    );
  }
}

function sendAblyUpdate(userId: string, eventName: string, update: any) {
  console.log(`Sending Ably update for user ${userId} (${eventName}):`, update);
  const channelName = `image-generation-background_removal-${userId}`;
  const channel = ably.channels.get(channelName);

  channel
    .publish("prediction_update", update)
    .then(() => {
      // Publication successful
    })
    .catch((err) => {
      console.error(`Error publishing to Ably channel ${channelName}:`, err);
    });
}

async function handleStatusUpdate(
  backgroundRemoval: any,
  status: PredictionStatus,
  error: string | null = null
) {
  await prisma.backgroundRemoval.update({
    where: { id: backgroundRemoval.id },
    data: {
      status,
      completedAt:
        status === PredictionStatus.FAILED ||
        status === PredictionStatus.CANCELED
          ? new Date()
          : undefined,
    },
  });

  sendAblyUpdate(backgroundRemoval.userId, "background-removal", {
    id: backgroundRemoval.id,
    status,
    inputImage: backgroundRemoval.inputImage,
    outputImage: null,
    createdAt: backgroundRemoval.createdAt,
    error,
  });
}

async function handleSucceeded(
  backgroundRemoval: any,
  output: string | string[]
) {
  const outputImage = Array.isArray(output) ? output[0] : output;
  await prisma.backgroundRemoval.update({
    where: { id: backgroundRemoval.id },
    data: {
      status: PredictionStatus.SUCCEEDED,
      outputImage,
      completedAt: new Date(),
    },
  });

  sendAblyUpdate(backgroundRemoval.userId, "background-removal", {
    id: backgroundRemoval.id,
    status: PredictionStatus.SUCCEEDED,
    inputImage: backgroundRemoval.inputImage,
    outputImage,
    createdAt: backgroundRemoval.createdAt,
  });
}

export const dynamic = "force-dynamic";
