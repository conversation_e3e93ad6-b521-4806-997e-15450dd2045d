import { NextRequest, NextResponse } from "next/server";

import prisma from "@/lib/prisma/prisma";
import * as Ably from "ably";
import {
  PredictionStatus,
} from "@/modules/dashboard/main-features/types";
import { WebhookPayload, WebhookPayloadSchema } from "@/modules/dashboard/main-features/types/webhook";

const ABLY_API_KEY = process.env.ABLY_API_KEY;

const ably = new Ably.Rest(ABLY_API_KEY!);

function sendAblyUpdate(userId: string, update: any) {
  console.log(`Sending Ably update for user ${userId} (upscale):`, update);
  const channelName = `image-generation-virtual_staging-${userId}`;
  const channel = ably.channels.get(channelName);

  // Add messageType to clearly identify this as an upscale message
  const updateWithType = {
    ...update,
    messageType: "upscale"
  };

  console.log(`Publishing to channel ${channelName}:`, updateWithType);

  channel
    .publish("prediction_update", updateWithType)
    .then(() => {
      console.log("Ably update published successfully to channel:", channelName);
    })
    .catch((err) => {
      console.error(`Error publishing to Ably channel ${channelName}:`, err);
    });
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    console.log("Received webhook payload:", body);
    
    let jsonData;
    try {
      jsonData = JSON.parse(body);
    } catch (error) {
      console.error("Failed to parse webhook JSON:", error);
      return NextResponse.json({ error: "Invalid JSON" }, { status: 400 });
    }
    
    const result = WebhookPayloadSchema.safeParse(jsonData);
    
    if (!result.success) {
      console.error("Validation errors:", result.error);
      // Continue anyway with the raw data
      jsonData.status = jsonData.status === "succeeded" ? PredictionStatus.SUCCEEDED : 
                        jsonData.status === "failed" ? PredictionStatus.FAILED :
                        jsonData.status === "canceled" ? PredictionStatus.CANCELED :
                        jsonData.status === "processing" ? PredictionStatus.PROCESSING :
                        jsonData.status;
      
      await processWebhookPayload(jsonData);
      return NextResponse.json({ message: "Processed with validation warnings" });
    }
    
    const payload = result.data;

    await processWebhookPayload(payload);

    return NextResponse.json({ message: "Webhook processed successfully" });
  } catch (error) {
    console.error("Error processing webhook:", error);
    return NextResponse.json(
      { error: "Error processing webhook" },
      { status: 500 }
    );
  }
}

async function processWebhookPayload(payload: any) {
  if (!payload.id) {
    console.error("No prediction ID in payload:", payload);
    return;
  }

  const upscaleImage = await prisma.upscaleImage.findFirst({
    where: {
      replicate_id: payload.id,
    },
  });

  if (!upscaleImage) {
    console.error(`No upscale image found for prediction ID ${payload.id}`);
    return;
  }

  console.log(`Handling ${payload.status} for upscale image ${upscaleImage.id}`);

  const status = typeof payload.status === 'string' ? 
    payload.status.toUpperCase() as PredictionStatus : 
    payload.status;

  switch (status) {
    case PredictionStatus.SUCCEEDED:
      if (payload.output) {
        const outputUrl = Array.isArray(payload.output)
          ? payload.output[0]
          : payload.output;
        await handleSucceeded(upscaleImage, outputUrl, payload);
      }
      break;
    case PredictionStatus.FAILED:
      await handleStatusUpdate(
        upscaleImage,
        PredictionStatus.FAILED,
        payload.error || "Unknown error"
      );
      break;
    case PredictionStatus.CANCELED:
      await handleStatusUpdate(upscaleImage, PredictionStatus.CANCELED);
      break;
    default:
      console.log(
        `Ignoring unhandled status ${status} for upscale image ${upscaleImage.id}`
      );
  }
}

async function handleStatusUpdate(
  upscaleImage: any,
  status: PredictionStatus,
  error: string | null = null
) {
  await prisma.upscaleImage.update({
    where: { id: upscaleImage.id },
    data: {
      status,
      completedAt: [
        PredictionStatus.FAILED,
        PredictionStatus.CANCELED,
      ].includes(status)
        ? new Date()
        : undefined,
    },
  });

  sendAblyUpdate(upscaleImage.userId, {
    id: upscaleImage.id,
    status,
    error,
  });
}

async function handleSucceeded(upscaleImage: any, outputUrl: string, predictionData: any) {
  await prisma.upscaleImage.update({
    where: { id: upscaleImage.id },
    data: {
      status: PredictionStatus.SUCCEEDED,
      outputImage: outputUrl,
      completedAt: new Date(),
    },
  });

  // Extract metadata about the source image
  const input = predictionData?.input || {};
  
  // Try to get source info from various possible locations in the payload
  const sourceType = input.source_type || 
                    predictionData.source_type || 
                    input.sourceType || 
                    "direct_upscale";
  
  const originalImageId = input.original_image_id || 
                         predictionData.original_image_id || 
                         input.originalImageId ||
                         "";

  console.log("Extracted source metadata:", { sourceType, originalImageId });

  sendAblyUpdate(upscaleImage.userId, {
    id: upscaleImage.id,
    status: PredictionStatus.SUCCEEDED,
    url: outputUrl,
    createdAt: upscaleImage.createdAt,
    inputImage: upscaleImage.inputImage,
    sourceType,
    originalImageId,
  });
}

export const dynamic = "force-dynamic";
