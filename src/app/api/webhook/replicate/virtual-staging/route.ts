import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma/prisma";
import { verifySignature } from "../utils";
import {
  PredictionStatus,
  
} from "@/modules/dashboard/main-features/types";
import * as Ably from "ably";
import { handleUrlUpload } from "@/modules/dashboard/main-features/actions/upload-image";
import Replicate from "replicate";
import { PrismaClient, Prisma } from "@prisma/client";

const ABLY_API_KEY = process.env.ABLY_API_KEY;
const ably = new Ably.Rest(ABLY_API_KEY!);

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    console.log("Received webhook payload:", body);

    const isValid = await verifySignature(request, body);
    console.log("Signature validation:", isValid);

    if (!isValid) {
      console.error("Invalid webhook signature");
      return NextResponse.json({ error: "Invalid signature" }, { status: 401 });
    }

    const payload = JSON.parse(body);
    console.log("Parsed webhook payload:", payload);

    if (!payload.id || !payload.status) {
      console.error("Invalid webhook payload structure:", payload);
      return NextResponse.json({ error: "Invalid payload" }, { status: 400 });
    }

    const virtualStaging = await prisma.virtualStaging.findUnique({
      where: { replicate_id: payload.id },
      include: { user: true },
    });
    console.log("Found virtual staging record:", virtualStaging);

    if (!virtualStaging) {
      console.error("Virtual staging not found for ID:", payload.id);
      return NextResponse.json(
        { message: "Virtual staging not found" },
        { status: 404 }
      );
    }

    console.log("Processing webhook status:", payload.status);
    switch (payload.status) {
      case "processing":
        console.log("Handling processing status");
        await handleStatusUpdate(virtualStaging, PredictionStatus.PROCESSING);
        break;
      case "succeeded":
        console.log("Handling succeeded status, output:", payload.output);
        if (payload.output) {
          await handleSucceeded(virtualStaging, payload.output);
        } else {
          console.error("No output in succeeded payload");
        }
        break;
      case "failed":
        console.log("Handling failed status, error:", payload.error);
        await handleStatusUpdate(
          virtualStaging,
          PredictionStatus.FAILED,
          payload.error
        );
        break;
      case "canceled":
        console.log("Handling canceled status");
        await handleStatusUpdate(virtualStaging, PredictionStatus.CANCELED);
        break;
      default:
        console.warn(`Unhandled virtual staging status: ${payload.status}`);
    }

    console.log("Webhook processing completed successfully");
    return NextResponse.json({ message: "Webhook processed successfully" });
  } catch (error) {
    console.error("Error processing virtual staging webhook:", error);
    return NextResponse.json(
      { error: "Error processing webhook" },
      { status: 500 }
    );
  }
}

function sendAblyUpdate(userId: string, update: any) {
  console.log(
    `Sending Ably update for user ${userId} (virtual staging):`,
    JSON.stringify(update, null, 2)
  );
  const channelName = `image-generation-virtual_staging-${userId}`;
  const channel = ably.channels.get(channelName);

  channel
    .publish("prediction_update", update)
    .then(() => {
      console.log("Ably update published successfully");
    })
    .catch((err) => {
      console.error(`Error publishing to Ably channel ${channelName}:`, err);
    });
}

async function handleStatusUpdate(
  virtualStaging: any,
  status: PredictionStatus,
  error: string | null = null
) {
  const updateData: any = {
    status: status,
    completedAt: [
      PredictionStatus.FAILED,
      PredictionStatus.CANCELED,
    ].includes(status)
      ? new Date()
      : undefined,
    style: virtualStaging.style || null,
    room: virtualStaging.room || null,
  };
  
  if (error) {
    updateData.error = error;
  }

  await prisma.virtualStaging.update({
    where: { id: virtualStaging.id },
    data: updateData,
  });

  sendAblyUpdate(virtualStaging.userId, {
    id: virtualStaging.replicate_id,
    status,
    inputImage: virtualStaging.inputImage,
    outputImage: null,
    prompt: virtualStaging.prompt,
    style: virtualStaging.style || null,
    room: virtualStaging.room || null,
    createdAt: virtualStaging.createdAt,
    error,
  });
}

async function upscaleGeneratedImage(imageUrl: string, userId: string) {
  const replicate = new Replicate({
    auth: process.env.REPLICATE_API_TOKEN!,
  });

  const input = {
    image: imageUrl,
    scale_factor: 2,
    creativity: 0.3,
    resemblance: 1.65,
    seed: Math.floor(Math.random() * 1000000),
    prompt: "masterpiece, best quality, highres",
    dynamic: 6,
    handfix: "disabled",
    pattern: false,
    sharpen: 0,
    sd_model: "juggernaut_reborn.safetensors [338b85bc4f]",
    scheduler: "DPM++ 3M SDE Karras",
    tiling_width: 112,
    tiling_height: 144,
    negative_prompt:
      "(worst quality, low quality, normal quality:2) JuggernautNegative-neg",
    num_inference_steps: 18,
  };

  console.log("Upscale input:", input);

  const prediction = await replicate.predictions.create({
    version: "dfad41707589d68ecdccd1dfa600d55a208f9310748e44bfe35b4a6291453d5e",
    input,
  });

  let upscaledImage = prediction.output;
  let attempts = 0;
  const maxAttempts = 30;

  while (!upscaledImage && attempts < maxAttempts) {
    await new Promise((resolve) => setTimeout(resolve, 2000));
    const status = await replicate.predictions.get(prediction.id);
    upscaledImage = status.output;
    attempts++;
  }

  if (!upscaledImage) {
    throw new Error("Upscaling timed out");
  }

  return Array.isArray(upscaledImage) ? upscaledImage[0] : upscaledImage;
}

async function handleSucceeded(virtualStaging: any, output: string | string[]) {
  const outputImage = Array.isArray(output) ? output[0] : output;

  try {
    console.log("Processing output image:", outputImage);
    
    // Send immediate Ably update with the raw output URL for faster feedback
    const initialUpdateData = {
      id: virtualStaging.replicate_id,
      status: PredictionStatus.PROCESSING_OUTPUT, // You'll need to add this status to your enum
      inputImage: virtualStaging.inputImage,
      rawOutputUrl: outputImage, // Send the raw, unprocessed image URL
      prompt: virtualStaging.prompt,
      style: virtualStaging.style || null,
      room: virtualStaging.room || null,
      excludedElements: virtualStaging.excludedElements || null,
      createdAt: virtualStaging.createdAt.toISOString(),
      message: "Processing output image...",
    };
    
    console.log("Sending initial update with raw output:", initialUpdateData);
    sendAblyUpdate(virtualStaging.userId, initialUpdateData);

    // Continue with normal processing - upscaling, etc.
    console.log("Mask for upscaling:", virtualStaging.mask);

    console.log("Starting image upscaling...");
    const upscaledImage = await upscaleGeneratedImage(
      outputImage,
      virtualStaging.userId
    );
    console.log("Image upscaled successfully:", upscaledImage);

    const folderPath = `virtual-staging/${virtualStaging.userId}/${virtualStaging.id}`;
    let cloudflareUpload;

    try {
      cloudflareUpload = await handleUrlUpload(upscaledImage, folderPath);

      if ("error" in cloudflareUpload && cloudflareUpload.error?.message) {
        throw new Error(cloudflareUpload.error.message);
      }

      if (!("displayUrl" in cloudflareUpload) || !("downloadUrl" in cloudflareUpload) || !("id" in cloudflareUpload)) {
        throw new Error("Invalid upload response structure");
      }

      await prisma.virtualStaging.update({
        where: { id: virtualStaging.id },
        data: {
          status: PredictionStatus.SUCCEEDED,
          displayUrl: cloudflareUpload.displayUrl,
          downloadUrl: cloudflareUpload.downloadUrl,
          cloudflareImageId: cloudflareUpload.id,
          completedAt: new Date(),
          style: virtualStaging.style || null,
          room: virtualStaging.room || null,
        } as Prisma.VirtualStagingUpdateInput,
      });

      // Final update with optimized URLs
      const finalUpdateData = {
        id: virtualStaging.replicate_id,
        status: PredictionStatus.SUCCEEDED,
        inputImage: virtualStaging.inputImage,
        displayUrl: cloudflareUpload.displayUrl,
        downloadUrl: cloudflareUpload.downloadUrl,
        prompt: virtualStaging.prompt,
        style: virtualStaging.style || null,
        room: virtualStaging.room || null,
        excludedElements: virtualStaging.excludedElements || null,
        createdAt: virtualStaging.createdAt.toISOString(),
      };

      console.log(
        "Final update data being sent:",
        JSON.stringify(finalUpdateData, null, 2)
      );
      sendAblyUpdate(virtualStaging.userId, finalUpdateData);
    } catch (uploadError) {
      console.error("Cloudflare upload failed:", uploadError);
      await prisma.virtualStaging.update({
        where: { id: virtualStaging.id },
        data: {
          status: PredictionStatus.FAILED,
          completedAt: new Date(),
          error: uploadError instanceof Error 
            ? uploadError.message 
            : typeof uploadError === 'string' 
              ? uploadError 
              : "Unknown upload error"
        } as Prisma.VirtualStagingUpdateInput,
      });
      sendAblyUpdate(virtualStaging.userId, {
        id: virtualStaging.replicate_id,
        status: PredictionStatus.FAILED,
        inputImage: virtualStaging.inputImage,
        error: `Cloudflare upload failed: ${uploadError instanceof Error ? uploadError.message : String(uploadError)}`,
        createdAt: virtualStaging.createdAt.toISOString(),
      });
    }
  } catch (error) {
    console.error("Error handling succeeded state:", error);
    await handleStatusUpdate(
      virtualStaging,
      PredictionStatus.FAILED,
      error instanceof Error ? error.message : "Failed to process output image"
    );
  }
}

export const dynamic = "force-dynamic";
