// src/app/api/webhook/replicate/image-generator/route.ts
import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma/prisma";
import { verifySignature } from "../utils";
import { PredictionStatus } from "@prisma/client";
import * as Ably from "ably";

const ABLY_API_KEY = process.env.ABLY_API_KEY;
const ably = new Ably.Rest(ABLY_API_KEY!);

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    console.log("Received image-generator webhook payload:", body);
    
    const isValid = await verifySignature(request, body);
    
    if (!isValid) {
      console.error("Invalid webhook signature");
      return NextResponse.json({ error: "Invalid signature" }, { status: 401 });
    }
    
    const payload = JSON.parse(body);
    console.log("Parsed webhook payload:", payload);
    
    if (!payload.id || !payload.status) {
      console.error("Invalid webhook payload structure:", payload);
      return NextResponse.json({ error: "Invalid payload" }, { status: 400 });
    }
    
    // Use any type to avoid clientTaskId type issues
    const editImage = await prisma.editImage.findUnique({
      where: { replicate_id: payload.id },
      select: {
        id: true,
        userId: true,
        replicate_id: true,
      }
    }) as any; // Cast to any to allow clientTaskId access
    
    if (!editImage) {
      console.error("Image generation record not found for ID:", payload.id);
      return NextResponse.json({ message: "Image generation not found" }, { status: 404 });
    }
    
    // Get the clientTaskId using a raw query
    const clientTaskIdResult = await prisma.$queryRaw<{ clientTaskId: string | null }[]>`
      SELECT "clientTaskId" FROM "edit_images" WHERE "replicate_id" = ${payload.id}
    `;
    
    // Extract clientTaskId if available (will be array with one object)
    const clientTaskId = clientTaskIdResult[0]?.clientTaskId;
    
    console.log("Processing webhook status:", payload.status, "for clientTaskId:", clientTaskId);
    
    // Update status based on payload
    switch (payload.status) {
      case "succeeded":
        if (payload.output) {
          const outputUrl = Array.isArray(payload.output) ? payload.output[0] : payload.output;
          
          await prisma.editImage.update({
            where: { id: editImage.id },
            data: {
              status: PredictionStatus.SUCCEEDED,
              outputImage: outputUrl,
              completedAt: new Date(),
            },
          });
          
          // Send update to client
          sendAblyUpdate(editImage.userId, {
            id: editImage.replicate_id,
            clientTaskId: clientTaskId,
            status: PredictionStatus.SUCCEEDED,
            outputUrl: outputUrl,
          });
        }
        break;
        
      case "failed":
        await prisma.editImage.update({
          where: { id: editImage.id },
          data: {
            status: PredictionStatus.FAILED,
            completedAt: new Date(),
          },
        });
        
        sendAblyUpdate(editImage.userId, {
          id: editImage.replicate_id,
          clientTaskId: clientTaskId,
          status: PredictionStatus.FAILED,
          error: payload.error || "Generation failed",
        });
        break;
        
      default:
        console.log(`Received webhook for status: ${payload.status} (no action needed)`);
    }
    
    return NextResponse.json({ message: "Webhook processed successfully" });
  } catch (error) {
    console.error("Error processing webhook:", error);
    return NextResponse.json({ error: "Error processing webhook" }, { status: 500 });
  }
}

function sendAblyUpdate(userId: string, update: any) {
  const channelName = `predictions-${userId}`; // Updated to match the frontend subscription channel name
  console.log(`Sending update to Ably channel ${channelName}:`, update);
  const channel = ably.channels.get(channelName);
  channel.publish("prediction-update", update) // Updated to match the frontend event name
    .then(() => console.log("Ably message published successfully"))
    .catch(err => console.error("Error publishing to Ably:", err));
}

export const dynamic = "force-dynamic";