import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma/prisma";
import crypto from "crypto";
import * as Ably from "ably";
import {
  PredictionStatus,
} from "@/modules/dashboard/main-features/types";
import { z } from "zod";
import { refundImageCredits } from "../utils";
import { revalidatePath } from "next/cache";

const REPLICATE_API_TOKEN = process.env.REPLICATE_API_TOKEN;
const ABLY_API_KEY = process.env.ABLY_API_KEY;
let WEBHOOK_SECRET: string;

// Define the WebhookPayloadSchema using Zod
const WebhookPayloadSchema = z.object({
  id: z.string(),
  status: z.nativeEnum(PredictionStatus),
  output: z.union([z.string(), z.array(z.string())]).optional(),
  error: z.string().optional(),
});

const ably = new Ably.Rest(ABLY_API_KEY!);

async function getWebhookSecret(): Promise<string> {
  if (WEBHOOK_SECRET) return WEBHOOK_SECRET;

  const response = await fetch(
    "https://api.replicate.com/v1/webhooks/default/secret",
    {
      headers: {
        Authorization: `Token ${REPLICATE_API_TOKEN}`,
      },
    }
  );

  if (!response.ok) {
    throw new Error(`Failed to fetch webhook secret: ${response.statusText}`);
  }

  const data = await response.json();
  WEBHOOK_SECRET = data.key;
  return WEBHOOK_SECRET;
}

async function verifySignature(
  request: NextRequest,
  body: string
): Promise<boolean> {
  const signature = request.headers.get("webhook-signature");
  const timestamp = request.headers.get("webhook-timestamp");
  const webhookId = request.headers.get("webhook-id");

  if (!signature || !timestamp || !webhookId) {
    return false;
  }

  try {
    const secret = await getWebhookSecret();
    const signedContent = `${webhookId}.${timestamp}.${body}`;
    const secretBytes = Buffer.from(secret.split("_")[1], "base64");
    const expectedSignature = crypto
      .createHmac("sha256", secretBytes)
      .update(signedContent)
      .digest("base64");

    const providedSignatures = signature
      .split(" ")
      .map((sig) => sig.split(",")[1]);
    return providedSignatures.some((sig) => sig === expectedSignature);
  } catch {
    return false;
  }
}

function sendAblyUpdate(userId: string, update: any) {
  console.log(
    `Sending Ably update for user ${userId} (style-transfer):`,
    update
  );
  const channelName = `image-generation-style_transfer-${userId}`;
  const channel = ably.channels.get(channelName);

  channel.publish("style-transfer", update);
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const payload = WebhookPayloadSchema.parse(body);

    const styleTransfer = await prisma.styleTransfer.findUnique({
      where: { replicate_id: payload.id },
      include: { user: true },
    });

    if (!styleTransfer) {
      return NextResponse.json(
        { error: "Style transfer not found" },
        { status: 404 }
      );
    }

    if (payload.status === PredictionStatus.SUCCEEDED && payload.output) {
      const outputUrl = Array.isArray(payload.output)
        ? payload.output[0]
        : payload.output;

      await prisma.styleTransfer.update({
        where: { id: styleTransfer.id },
        data: {
          status: PredictionStatus.SUCCEEDED,
          outputImage: outputUrl,
          completedAt: new Date(),
        },
      });

      // Send update via Ably
      sendAblyUpdate(styleTransfer.userId, {
        id: styleTransfer.id,
        status: PredictionStatus.SUCCEEDED,
        url: outputUrl,
        inputImage: styleTransfer.inputImage,
        styleImage: styleTransfer.styleImage,
        model: styleTransfer.model,
        structureImage: styleTransfer.structureImage,
        prompt: styleTransfer.prompt,
        createdAt: styleTransfer.createdAt,
      });
    } else if (payload.status === PredictionStatus.FAILED) {
      await prisma.styleTransfer.update({
        where: { id: styleTransfer.id },
        data: {
          status: PredictionStatus.FAILED,
          completedAt: new Date(),
        },
      });

      // Refund credits on failure
      await refundImageCredits(styleTransfer.userId, 1);

      sendAblyUpdate(styleTransfer.userId, {
        id: styleTransfer.id,
        status: PredictionStatus.FAILED,
        error: payload.error,
      });
    } else {
      // Handle other statuses (PROCESSING, CANCELED)
      await prisma.styleTransfer.update({
        where: { id: styleTransfer.id },
        data: {
          status: payload.status,
        },
      });

      sendAblyUpdate(styleTransfer.userId, {
        id: styleTransfer.id,
        status: payload.status,
      });
    }

    // Always revalidate the dashboard path
    revalidatePath("/dashboard");

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error processing style transfer webhook:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export const dynamic = "force-dynamic";
