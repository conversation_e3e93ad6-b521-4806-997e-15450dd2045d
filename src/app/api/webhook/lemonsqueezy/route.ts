import { NextResponse } from "next/server";
import crypto from "crypto";
import prisma from "@/lib/prisma/prisma";
import { revalidatePath } from "next/cache";
import { SubscriptionStatus } from "@prisma/client";
import { get } from "lodash";

// Type Definitions
interface WebhookPayload {
  meta: {
    event_name: string;
    custom_data?: {
      user_id?: string;
    };
    test_mode?: boolean;
    webhook_id: string;
  };
  data: {
    id: string;
    type: string;
    attributes: {
      status?: string;
      urls?: {
        update_payment_method?: string;
        customer_portal?: string;
        customer_portal_update_subscription?: string;
        invoice_url?: string;
        receipt?: string;
      };
      user_name: string;
      user_email: string;
      product_id?: string;
      variant_id?: string;
      subscription_id?: string;
      total?: string;
      first_order_item?: {
        variant_id: number;
        product_name: string;
        [key: string]: any;
      };
      subscription?: {
        id: string;
        status: string;
        ends_at: string | null;
        renews_at: string | null;
        interval?: string;
        interval_count?: number;
      };
      [key: string]: any;
    };
    relationships?: {
      subscription?: {
        data: {
          id: string;
          type: string;
        };
      };
      [key: string]: any;
    };
  };
}

type WebhookEvent =
  | "subscription_created"
  | "subscription_updated"
  | "subscription_cancelled"
  | "subscription_payment_success"
  | "subscription_payment_failed"
  | "subscription_expired"
  | "order_created";

// Utility Functions
function validateWebhookSignature(signature: string | null, body: string) {
  if (!signature) {
    throw new Error("No signature provided");
  }

  const hmac = crypto.createHmac(
    "sha256",
    process.env.LEMON_SQUEEZY_WEBHOOK_SECRET || ""
  );
  const digest = hmac.update(body).digest("hex");

  if (signature !== digest) {
    throw new Error("Invalid signature");
  }
}

function isCriticalError(error: unknown): boolean {
  if (error instanceof Error) {
    // Define what constitutes a critical error
    const criticalErrors = [
      "Database connection error",
      "Invalid signature",
      "Payment processing failed",
    ];
    return criticalErrors.some((msg) => error.message.includes(msg));
  }
  return false;
}

// Subscription Plan Interface and Mappings
interface SubscriptionPlan {
  credits: number;
  packageName: string;
  isSubscription: boolean;
  monthlyCredits: number;
  totalCredits: number;
}

const VARIANT_MAPPINGS = {
  test: {
    "585262": {
      credits: 0,
      packageName: "Renovaitor Premium Annual",
      isSubscription: true,
      monthlyCredits: 5000,
      totalCredits: 60000,
    },
    "585256": {
      credits: 0,
      packageName: "Renovaitor Premium Monthly",
      isSubscription: true,
      monthlyCredits: 5000,
      totalCredits: 5000,
    },
    "585255": {
      credits: 200,
      packageName: "200 Design Credits",
      isSubscription: false,
      monthlyCredits: 0,
      totalCredits: 200,
    },
    "134914": {
      credits: 0,
      packageName: "Renovaitor Pro Monthly",
      isSubscription: true,
      monthlyCredits: 1000,
      totalCredits: 1000,
    },
    "134915": {
      credits: 0,
      packageName: "Renovaitor Pro Annual",
      isSubscription: true,
      monthlyCredits: 1000,
      totalCredits: 12000,
    },
    "619715": {
      credits: 50,
      packageName: "50 Design Credits",
      isSubscription: false,
      monthlyCredits: 0,
      totalCredits: 50,
    },
    "619716": {
      credits: 500,
      packageName: "500 Design Credits",
      isSubscription: false,
      monthlyCredits: 0,
      totalCredits: 500,
    },
    "619721": {
      credits: 1000,
      packageName: "1000 Design Credits",
      isSubscription: false,
      monthlyCredits: 0,
      totalCredits: 1000,
    },
    "619722": {
      credits: 1500,
      packageName: "1500 Design Credits",
      isSubscription: false,
      monthlyCredits: 0,
      totalCredits: 1500,
    },
  },
  production: {
    "135060": {
      credits: 200,
      packageName: "200 Design Credits",
      isSubscription: false,
      monthlyCredits: 0,
      totalCredits: 200,
    },
    "526746": {
      credits: 0,
      packageName: "Renovaitor Premium Annual",
      isSubscription: true,
      monthlyCredits: 5000,
      totalCredits: 60000,
    },
    "526745": {
      credits: 0,
      packageName: "Renovaitor Premium Monthly",
      isSubscription: true,
      monthlyCredits: 5000,
      totalCredits: 5000,
    },
    "526739": {
      credits: 0,
      packageName: "Renovaitor Pro Monthly",
      isSubscription: true,
      monthlyCredits: 1000,
      totalCredits: 1000,
    },
    "526719": {
      credits: 0,
      packageName: "Renovaitor Pro Annual",
      isSubscription: true,
      monthlyCredits: 1000,
      totalCredits: 12000,
    },
    "619715": {
      credits: 50,
      packageName: "50 Design Credits",
      isSubscription: false,
      monthlyCredits: 0,
      totalCredits: 50,
    },
    "619716": {
      credits: 500,
      packageName: "500 Design Credits",
      isSubscription: false,
      monthlyCredits: 0,
      totalCredits: 500,
    },
    "619721": {
      credits: 1000,
      packageName: "1000 Design Credits",
      isSubscription: false,
      monthlyCredits: 0,
      totalCredits: 1000,
    },
    "619722": {
      credits: 1500,
      packageName: "1500 Design Credits",
      isSubscription: false,
      monthlyCredits: 0,
      totalCredits: 1500,
    },
  },
} as const;

// Status Transition Validation
const VALID_STATUS_TRANSITIONS: Record<
  SubscriptionStatus,
  SubscriptionStatus[]
> = {
  ACTIVE: ["CANCELED", "FAILED", "PAUSED", "PAST_DUE"],
  CANCELED: ["ACTIVE"],
  FAILED: ["ACTIVE", "CANCELED"],
  PAUSED: ["ACTIVE", "CANCELED"],
  EXPIRED: ["ACTIVE"],
  PAST_DUE: ["ACTIVE", "CANCELED", "EXPIRED"],
};

function isValidStatusTransition(
  from: SubscriptionStatus,
  to: SubscriptionStatus
): boolean {
  return VALID_STATUS_TRANSITIONS[from]?.includes(to) ?? false;
}

// Utility Functions
function getVariantDetails(
  variantId: string,
  isTestMode: boolean
): SubscriptionPlan | undefined {
  const mode = isTestMode ? "test" : "production";
  const mappings = VARIANT_MAPPINGS[mode];
  return mappings[variantId as keyof typeof mappings];
}

async function getUserId(payload: WebhookPayload): Promise<string> {
  const userId = payload.meta.custom_data?.user_id;
  if (userId) return userId;

  const userEmail = payload.data.attributes.user_email;
  const user = await prisma.user.findUnique({
    where: { email: userEmail },
  });

  if (!user) {
    throw new Error(`No user found for email: ${userEmail}`);
  }

  return user.id;
}

// Webhook Handlers
async function handleSubscriptionCreated(
  payload: WebhookPayload,
  userId: string
) {
  const isTestMode = payload.meta.test_mode === true;
  const variantId = payload.data.attributes.variant_id?.toString();

  console.log("Subscription Created Payload:", {
    attributes: payload.data.attributes,
    variantId,
    isTestMode,
  });

  if (!variantId) {
    throw new Error("No variant_id found in payload");
  }

  const plan = getVariantDetails(variantId, isTestMode);

  if (!plan) {
    throw new Error(`Invalid variant ID: ${variantId}`);
  }

  const subscriptionData = payload.data.attributes;
  const startDate = new Date();
  const endDate = subscriptionData.subscription?.ends_at
    ? new Date(subscriptionData.subscription.ends_at)
    : null;
  const renewsAt = subscriptionData.subscription?.renews_at
    ? new Date(subscriptionData.subscription.renews_at)
    : null;

  console.log("Creating subscription with ID:", {
    subscriptionId: payload.data.id,
    userId,
    variantId,
  });

  // Use upsert instead of update with create option
  await prisma.subscription.upsert({
    where: { userId },
    update: {
      packageName: plan.packageName,
      startDate,
      endDate:
        endDate || new Date(startDate.getTime() + 30 * 24 * 60 * 60 * 1000),
      isActive: true,
      allowPrivateImages: plan.packageName !== "Free",
      paymentStatus: "ACTIVE" as SubscriptionStatus,
      lastBillingDate: startDate,
      nextBillingDate: renewsAt || null,
      subscriptionId: payload.data.id,
      variantId: variantId,
      customerPortalUrl: payload.data.attributes.urls?.customer_portal || null,
      updatePaymentMethodUrl:
        payload.data.attributes.urls?.update_payment_method || null,
      cancelUrl:
        payload.data.attributes.urls?.customer_portal_update_subscription ||
        null,
      monthlyCredits: plan.monthlyCredits,
    },
    create: {
      userId,
      packageName: plan.packageName,
      startDate,
      endDate:
        endDate || new Date(startDate.getTime() + 30 * 24 * 60 * 60 * 1000),
      isActive: true,
      allowPrivateImages: plan.packageName !== "Free",
      paymentStatus: "ACTIVE" as SubscriptionStatus,
      lastBillingDate: startDate,
      nextBillingDate: renewsAt || null,
      subscriptionId: payload.data.id,
      variantId: variantId,
      customerPortalUrl: payload.data.attributes.urls?.customer_portal || null,
      updatePaymentMethodUrl:
        payload.data.attributes.urls?.update_payment_method || null,
      cancelUrl:
        payload.data.attributes.urls?.customer_portal_update_subscription ||
        null,
      monthlyCredits: plan.monthlyCredits,
    },
  });

  // Add initial credits if it's a monthly plan
  if (!plan.packageName.includes("Annual")) {
    await prisma.user.update({
      where: { id: userId },
      data: {
        imageCredits: {
          increment: plan.credits,
        },
      },
    });
  }

  // Create transaction record with correct amount and package name
  await prisma.transaction.create({
    data: {
      userId,
      amount: parseFloat(payload.data.attributes.total || "0"),
      credits: plan.credits || plan.monthlyCredits, // Use either one-time or monthly credits
      orderId: payload.data.id,
      packageName: plan.packageName,
      description: `Subscription created: ${plan.packageName}`,
    },
  });

  revalidatePath("/dashboard");
}

async function handleSubscriptionPaymentSuccess(
  payload: WebhookPayload,
  userId: string
) {
  const isTestMode = payload.meta.test_mode === true;
  const subscriptionId = payload.data.attributes.subscription_id?.toString();

  if (!subscriptionId) {
    console.error("No subscription ID found in payload");
    return;
  }

  try {
    // First get the subscription details from our database
    const subscription = await prisma.subscription.findFirst({
      where: {
        userId,
        subscriptionId: subscriptionId,
      },
    });

    if (!subscription || !subscription.variantId) {
      console.error("No subscription found in database or missing variant ID");
      return;
    }

    const plan = getVariantDetails(subscription.variantId, isTestMode);
    if (!plan) {
      console.error(`No plan found for variant ID: ${subscription.variantId}`);
      return;
    }

    // Only update subscription status if it's not already ACTIVE
    const updates: any = {
      lastBillingDate: new Date(),
    };

    if (subscription.paymentStatus !== "ACTIVE") {
      updates.paymentStatus = "ACTIVE" as SubscriptionStatus;
    }

    // Update subscription status
    await prisma.subscription.update({
      where: { userId },
      data: updates,
    });

    // Add monthly credits
    await prisma.user.update({
      where: { id: userId },
      data: {
        imageCredits: {
          increment: plan.monthlyCredits,
        },
      },
    });

    // Create transaction record
    await prisma.transaction.create({
      data: {
        userId,
        amount: parseFloat(payload.data.attributes.total || "0"),
        credits: plan.monthlyCredits,
        orderId: payload.data.id,
        packageName: plan.packageName,
        description: `Subscription renewal: ${plan.packageName}`,
      },
    });

    revalidatePath("/dashboard");
  } catch (error) {
    console.error("Error processing subscription payment:", {
      error,
      userId,
      subscriptionId,
      payload: JSON.stringify(payload),
    });
    await handleWebhookError(error, "subscription_payment_success", userId);
    throw error;
  }
}

async function handleSubscriptionCancelled(
  payload: WebhookPayload,
  userId: string
) {
  const subscription = payload.data.attributes.subscription;
  if (!subscription) return;

  const effectiveEndDate = new Date(subscription.ends_at || Date.now());

  await updateSubscriptionWithTransaction(userId, {
    paymentStatus: "CANCELED" as SubscriptionStatus,
    canceledAt: new Date(),
    effectiveCancelDate: effectiveEndDate,
    // Keep isActive true until the effective end date
    isActive: true,
  });

  // Schedule a job or create a check to handle subscription expiration
  // when effectiveCancelDate is reached

  revalidatePath("/dashboard");
}

async function handleSubscriptionPaymentFailed(
  payload: WebhookPayload,
  userId: string
) {
  await updateSubscriptionWithTransaction(userId, {
    paymentStatus: "FAILED" as SubscriptionStatus,
    // Don't deactivate immediately to allow for retry
    isActive: true,
  });

  // You might want to send a notification to the user here
  // about their failed payment
  revalidatePath("/dashboard");
}

async function handleSubscriptionExpired(
  payload: WebhookPayload,
  userId: string
) {
  await prisma.$transaction(async (tx) => {
    // Get current subscription
    const subscription = await tx.subscription.findUnique({
      where: { userId },
    });

    if (!subscription) return;

    // Update subscription status
    await tx.subscription.update({
      where: { userId },
      data: {
        isActive: false,
        paymentStatus: "EXPIRED" as SubscriptionStatus,
        endDate: new Date(),
        monthlyCredits: 0, // Reset monthly credits allowance
      },
    });

    // Reset user credits to 0 or a free tier amount
    await tx.user.update({
      where: { id: userId },
      data: {
        imageCredits: 0, // Or set to free tier amount
        lastCreditReset: new Date(),
      },
    });

    // Create a new free tier subscription or handle accordingly
    await tx.subscription.create({
      data: {
        userId,
        packageName: "Free",
        startDate: new Date(),
        endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
        isActive: true,
        paymentStatus: "ACTIVE",
        monthlyCredits: 0, // Free tier credits
        allowPrivateImages: false,
      },
    });
  });

  revalidatePath("/dashboard");
}

async function handleOrderCreated(payload: WebhookPayload, userId: string) {
  const isTestMode = payload.meta.test_mode === true;
  const variantId =
    payload.data.attributes.first_order_item?.variant_id.toString();

  if (!variantId) {
    throw new Error("No variant ID found in order payload");
  }

  const plan = getVariantDetails(variantId, isTestMode);

  if (!plan) {
    throw new Error(`Invalid variant ID: ${variantId}`);
  }

  // Check if the order is a subscription
  if (plan.isSubscription) {
    console.log("Subscription order received:", {
      orderId: payload.data.id,
      userId,
      variantId,
    });
    return; // Let subscription_created webhook handle it
  }

  // Process credit top-up
  try {
    // Update user's credits
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        imageCredits: {
          increment: plan.credits,
        },
      },
      select: {
        imageCredits: true,
      },
    });

    // Create transaction record
    await prisma.transaction.create({
      data: {
        userId,
        amount: parseFloat(payload.data.attributes.total || "0"),
        credits: plan.credits,
        orderId: payload.data.id,
        packageName: plan.packageName,
        description: `Credit purchase: ${plan.credits} credits`,
      },
    });

    revalidatePath("/dashboard");

    console.log("Credit purchase processed:", {
      userId,
      credits: plan.credits,
      orderId: payload.data.id,
      variantId,
      newTotalCredits: updatedUser.imageCredits,
    });
  } catch (error) {
    console.error("Error processing credit purchase:", {
      error,
      userId,
      orderId: payload.data.id,
      variantId,
    });
    throw error;
  }
}

async function handleSubscriptionUpdated(
  payload: WebhookPayload,
  userId: string
) {
  const subscriptionData = payload.data.attributes;
  const subscriptionId = payload.data.id;
  const variantId = subscriptionData.variant_id?.toString();

  if (!subscriptionId || !variantId) {
    console.error("Missing subscription ID or variant ID in update payload");
    return;
  }

  // Map Lemon Squeezy status to our SubscriptionStatus enum
  const statusMapping: Record<string, SubscriptionStatus> = {
    active: "ACTIVE",
    cancelled: "CANCELED", // Note: Lemon Squeezy uses 'cancelled' with two 'l's
    canceled: "CANCELED", // Handle both spellings
    failed: "FAILED",
    expired: "EXPIRED",
    paused: "PAUSED",
    past_due: "PAST_DUE",
  };

  try {
    const status = subscriptionData.status?.toLowerCase();
    const mappedStatus = status ? statusMapping[status] : undefined;

    console.log("Updating subscription with status:", {
      originalStatus: subscriptionData.status,
      mappedStatus,
      userId,
      subscriptionId,
    });

    await prisma.subscription.update({
      where: { userId },
      data: {
        packageName: subscriptionData.product_name || undefined,
        endDate: subscriptionData.ends_at
          ? new Date(subscriptionData.ends_at)
          : undefined,
        isActive: subscriptionData.status === "active",
        paymentStatus: mappedStatus, // Use the mapped status
        nextBillingDate: subscriptionData.renews_at
          ? new Date(subscriptionData.renews_at)
          : undefined,
        subscriptionId: subscriptionId,
        variantId: variantId,
        customerPortalUrl: subscriptionData.urls?.customer_portal || undefined,
        updatePaymentMethodUrl:
          subscriptionData.urls?.update_payment_method || undefined,
        cancelUrl:
          subscriptionData.urls?.customer_portal_update_subscription ||
          undefined,
      },
    });

    revalidatePath("/dashboard");
  } catch (error) {
    console.error("Error updating subscription:", {
      error,
      userId,
      subscriptionId,
      status: subscriptionData.status,
      payload: JSON.stringify(payload),
    });
    await handleWebhookError(error, "subscription_updated", userId);
    throw error;
  }
}

// Add a new function to handle credit resets
async function resetMonthlyCredits(userId: string) {
  const subscription = await prisma.subscription.findUnique({
    where: { userId },
    include: { user: true },
  });

  if (!subscription || !subscription.isActive) return;

  await prisma.user.update({
    where: { id: userId },
    data: {
      imageCredits: subscription.monthlyCredits,
      lastCreditReset: new Date(),
    },
  });
}

// API Route Handler
export async function POST(request: Request) {
  try {
    const body = await request.text();
    const signature = request.headers.get("x-signature");
    const event = request.headers.get("x-event-name") as WebhookEvent;

    // Validate webhook
    validateWebhookSignature(signature, body);
    const payload = JSON.parse(body) as WebhookPayload;
    validateWebhookPayload(payload);

    const userId = await getUserId(payload);

    console.log(`Processing webhook event: ${event}`, {
      userId,
      payloadId: payload.data.id,
      timestamp: new Date().toISOString(),
    });

    // Handle different webhook events
    try {
      switch (event) {
        case "order_created":
          await handleOrderCreated(payload, userId);
          break;
        case "subscription_created":
          await handleSubscriptionCreated(payload, userId);
          break;
        case "subscription_updated":
          await handleSubscriptionUpdated(payload, userId);
          break;
        case "subscription_payment_success":
          await handleSubscriptionPaymentSuccess(payload, userId);
          break;
        case "subscription_cancelled":
          await handleSubscriptionCancelled(payload, userId);
          break;
        case "subscription_payment_failed":
          await handleSubscriptionPaymentFailed(payload, userId);
          break;
        case "subscription_expired":
          await handleSubscriptionExpired(payload, userId);
          break;
        default:
          console.log(`Unhandled webhook event: ${event}`);
      }

      console.log(`Successfully processed webhook event: ${event}`, {
        userId,
        payloadId: payload.data.id,
        timestamp: new Date().toISOString(),
      });

      return NextResponse.json({
        received: true,
        event,
        userId,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      await handleWebhookError(error, event, userId);
      throw error;
    }
  } catch (error) {
    console.error("Webhook processing failed:", {
      error,
      timestamp: new Date().toISOString(),
      headers: Object.fromEntries(request.headers.entries()),
    });

    return NextResponse.json(
      {
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Helper Functions
async function fetchWithRetry(
  url: string,
  options: RequestInit,
  retries = 3
): Promise<any> {
  for (let i = 0; i < retries; i++) {
    try {
      const response = await fetch(url, options);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      if (i === retries - 1) throw error;
      await new Promise((resolve) =>
        setTimeout(resolve, 1000 * Math.pow(2, i))
      );
    }
  }
}

function validateWebhookPayload(payload: WebhookPayload) {
  const requiredFields = ["meta.event_name", "data.id", "data.attributes"];
  for (const field of requiredFields) {
    if (!get(payload, field)) {
      throw new Error(`Missing required field: ${field}`);
    }
  }
}

async function handleWebhookError(
  error: unknown,
  event: string,
  userId: string
) {
  console.error(`Error processing ${event} for user ${userId}:`, error);

  if (isCriticalError(error)) {
    // Implement your critical error handling here
    // For example: notify admin, log to error tracking service, etc.
  }
}

async function updateSubscriptionWithTransaction(userId: string, updates: any) {
  return await prisma.$transaction(async (tx) => {
    const subscription = await tx.subscription.findUnique({
      where: { userId },
    });

    if (!subscription) {
      throw new Error(`No subscription found for user ${userId}`);
    }

    // Only validate status transition if we're updating the status
    if (
      updates.paymentStatus &&
      subscription.paymentStatus !== updates.paymentStatus &&
      !isValidStatusTransition(
        subscription.paymentStatus,
        updates.paymentStatus
      )
    ) {
      throw new Error(
        `Invalid status transition from ${subscription.paymentStatus} to ${updates.paymentStatus}`
      );
    }

    return await tx.subscription.update({
      where: { userId },
      data: updates,
    });
  });
}

export const dynamic = "force-dynamic";
