@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import canvas container styles */
@import "../modules/canvas/styles/canvas-container.css";

@layer base {
  :root {
    /* Base shadcn-ui colors */
    --background: 220 14% 96%;
    --foreground: 224 71.4% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;
    --primary: 262.1 83.3% 57.8%;
    --primary-foreground: 210 20% 98%;
    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220.9 39.3% 11%;
    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;
    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 20% 98%;
    --border: 220 13% 88%;
    --input: 220 13% 88%;
    --ring: 262.1 83.3% 57.8%;
    --radius: 1rem;

    /* Chart colors */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    /* Gradient Colors - Light Mode */
    --gradient-start: 265 89% 62%; /* Rich Purple */
    --gradient-middle: 262 83% 58%; /* Vibrant Purple */
    --gradient-end: 244 76% 54%; /* Deep Blue */

    /* Secondary Gradient - Light Mode */
    --gradient-stop-1: 240 5% 96%; /* Cool slate/gray */
    --gradient-stop-2: 240 6% 90%; /* Mid slate/gray */
    --gradient-stop-3: 240 5% 84%; /* Deeper slate/gray */

    /* Gradient Definitions - Light Mode */
    --gradient-primary: linear-gradient(
      135deg,
      hsl(var(--gradient-start)) 0%,
      hsl(var(--gradient-middle)) 50%,
      hsl(var(--gradient-end)) 100%
    );

    --gradient-secondary: linear-gradient(
      to right,
      hsl(var(--gradient-stop-1)) 0%,
      hsl(var(--gradient-stop-2)) 50%,
      hsl(var(--gradient-stop-3)) 100%
    );

    --gradient-subtle: radial-gradient(
      circle at top right,
      hsl(var(--primary) / 0.15),
      transparent 70%
    );

    --gradient-card: linear-gradient(
      180deg,
      hsl(var(--background)) 0%,
      hsl(var(--background) / 0.95) 100%
    );

    --gradient-spotlight: radial-gradient(
      circle at center,
      hsl(var(--primary) / 0.15),
      transparent 70%
    );

    --gradient-glow: linear-gradient(
      to right,
      transparent,
      hsl(var(--primary) / 0.1),
      transparent
    );
  }

  .dark {
    --background: 224 40% 6%;
    --foreground: 210 20% 98%;
    --card: 224 40% 12%;
    --card-foreground: 210 20% 98%;
    --popover: 224 40% 6%;
    --popover-foreground: 210 20% 98%;
    --primary: 263.4 70% 60%;
    --primary-foreground: 210 20% 98%;
    --secondary: 215 30% 18%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 30% 18%;
    --muted-foreground: 217.9 20% 80%;
    --accent: 215 30% 22%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 20% 98%;
    --border: 215 28% 20%;
    --input: 215 28% 16%;
    --ring: 263.4 70% 60%;

    /* Dark mode gradient adjustments */
    --gradient-start: 265 89% 65%; /* Brighter Purple */
    --gradient-middle: 262 83% 60%; /* Vibrant Purple */
    --gradient-end: 244 76% 58%; /* Brighter Blue */

    --gradient-primary: linear-gradient(
      135deg,
      hsl(var(--gradient-start)) 0%,
      hsl(var(--gradient-middle)) 50%,
      hsl(var(--gradient-end)) 100%
    );

    /* Secondary Gradient - Dark Mode */
    --gradient-stop-1: 240 10% 30%; /* Lighter Dark slate */
    --gradient-stop-2: 240 8% 20%; /* Dark slate */
    --gradient-stop-3: 240 6% 15%; /* Deeper slate */

    --gradient-secondary: linear-gradient(
      to right,
      hsl(var(--gradient-stop-1)) 0%,
      hsl(var(--gradient-stop-2)) 50%,
      hsl(var(--gradient-stop-3)) 100%
    );

    --gradient-subtle: radial-gradient(
      circle at top right,
      hsl(var(--primary) / 0.2),
      transparent 70%
    );

    --gradient-card: linear-gradient(
      180deg,
      hsl(var(--background) / 0.9) 0%,
      hsl(var(--background) / 0.8) 100%
    );

    --gradient-spotlight: radial-gradient(
      circle at center,
      hsl(var(--primary) / 0.2),
      transparent 70%
    );
  }
}

@layer base {
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Utility classes for gradients */
@layer utilities {
  .gradient-primary {
    background-image: var(--gradient-primary);
  }

  .gradient-secondary {
    background-image: var(--gradient-secondary);
  }

  .gradient-subtle {
    background-image: var(--gradient-subtle);
  }

  .gradient-card {
    background-image: var(--gradient-card);
  }

  .gradient-spotlight {
    background-image: var(--gradient-spotlight);
  }

  .gradient-glow {
    background-image: var(--gradient-glow);
  }

  .gradient-fade {
    @apply bg-gradient-to-r from-primary/10 via-primary/5 to-transparent;
  }

  .gradient-mask-t {
    mask-image: linear-gradient(to bottom, black 0%, transparent 100%);
  }

  .gradient-mask-b {
    mask-image: linear-gradient(to top, black 0%, transparent 100%);
  }
}

/* Animation keyframes */
@keyframes gradient-shift {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes glow {
  0%,
  100% {
    text-shadow: 0 0 20px rgba(var(--primary-rgb), 0.5);
  }
  50% {
    text-shadow: 0 0 30px rgba(var(--primary-rgb), 0.8);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes video-fade-in {
  0% {
    opacity: 0;
    transform: scale(0.98);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes accordion-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}

.animate-gradient {
  animation: gradient-shift 15s ease infinite;
  background-size: 200% 200%;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out forwards;
}

.animate-accordion-down {
  animation: accordion-down 0.2s ease-out;
}

.animate-accordion-up {
  animation: accordion-up 0.2s ease-out;
}

/* Glass effect utilities */
.glass-effect {
  @apply backdrop-blur-md bg-background/80 border border-primary/10;
}

.glass-card {
  @apply backdrop-blur-lg bg-background/90 border border-primary/20 shadow-xl;
}

.glass-button {
  @apply backdrop-blur-sm bg-primary/80 hover:bg-primary/90 transition-colors;
}

/* Performance optimizations for iOS */
.transform-gpu {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000;
  perspective: 1000;
}

.fixed {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

@supports (-webkit-overflow-scrolling: touch) {
  .motion-safe\:animate-none {
    animation: none !important;
  }
}
