"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { DataTable } from "@/modules/ui/data-table";
import { columns } from "./columns";
import { Generation } from "./columns";
import { SortingState } from "@tanstack/react-table";

interface GenerationsTableProps {
  data: Generation[];
  pageCount: number;
  pageIndex: number;
}

export function GenerationsTable({
  data,
  pageCount,
  pageIndex,
}: GenerationsTableProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const createQueryString = (params: Record<string, string | null>) => {
    const newSearchParams = new URLSearchParams(searchParams.toString());

    for (const [key, value] of Object.entries(params)) {
      if (value === null) {
        newSearchParams.delete(key);
      } else {
        newSearchParams.set(key, value);
      }
    }

    return newSearchParams.toString();
  };

  return (
    <DataTable
      columns={columns}
      data={data}
      pageCount={pageCount}
      pagination={{
        pageIndex,
        pageSize: 10,
      }}
      onPaginationChange={(newPage) => {
        const queryString = createQueryString({
          page: String(newPage + 1),
        });
        router.push(`?${queryString}`);
      }}
      onSortingChange={(newSorting: SortingState) => {
        const queryString = createQueryString({
          sort:
            newSorting.length > 0
              ? `${newSorting[0].id}.${newSorting[0].desc ? "desc" : "asc"}`
              : null,
          page: "1", // Reset to first page when sorting changes
        });
        router.push(`?${queryString}`);
      }}
    />
  );
}
