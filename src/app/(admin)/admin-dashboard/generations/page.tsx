import { redirect } from "next/navigation";
import { currentUser } from "@/modules/auth/actions/user-actions";
import { columns } from "./columns";
import { getGenerations } from "@/modules/admin-dashboard/actions/get-generations";
import { GenerationsTable } from "./generations-table";
import { GenerationsFilter } from "./generations-filter";
import { GenerationType } from "./columns";

interface PageProps {
  searchParams: {
    page?: string;
    type?: string;
    status?: string;
    search?: string;
    dateFrom?: string;
    dateTo?: string;
    sort?: string;
  };
}

export default async function GenerationsPage({ searchParams }: PageProps) {
  const user = await currentUser();

  if (!user || user.role !== "ADMIN") {
    redirect("/");
  }

  const page = Number(searchParams.page) || 1;
  const sort = searchParams.sort?.split(".") || [];

  const filters = {
    type: searchParams.type as GenerationType | undefined,
    status: searchParams.status as
      | "COMPLETED"
      | "PROCESSING"
      | "FAILED"
      | undefined,
    search: searchParams.search,
    dateFrom: searchParams.dateFrom
      ? new Date(searchParams.dateFrom)
      : undefined,
    dateTo: searchParams.dateTo ? new Date(searchParams.dateTo) : undefined,
    sort:
      sort.length === 2
        ? {
            field: sort[0],
            direction: sort[1] as "asc" | "desc",
          }
        : undefined,
  };

  const {
    data: generations,
    pageCount,
    totalCount,
    typeStats,
  } = await getGenerations(filters, {
    pageSize: 10,
    pageIndex: page - 1,
  });

  return (
    <div className="min-h-screen bg-background">
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-foreground">
            Image Generations
          </h1>
          <div className="flex items-center space-x-4">
            <div className="text-sm text-muted-foreground">
              Total: {totalCount} generations
            </div>
            <div className="text-sm">
              {Object.entries(typeStats).map(([type, count]) => (
                <div key={type} className="flex items-center space-x-1">
                  <span className="capitalize text-muted-foreground">
                    {(type as string).replace(/([A-Z])/g, " $1").trim()}:
                  </span>
                  <span className="font-medium text-foreground">
                    {count as number}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <GenerationsFilter />

          <div className="bg-card shadow-sm rounded-lg border border-border">
            <GenerationsTable
              data={generations}
              pageCount={pageCount}
              pageIndex={page - 1}
            />
          </div>
        </div>
      </main>
    </div>
  );
}
