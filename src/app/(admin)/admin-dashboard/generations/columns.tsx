"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "@/modules/ui/badge";
import { formatDate } from "@/lib/utils";
import Image from "next/image";

export type GenerationType =
  | "interior"
  | "exterior"
  | "virtualStaging"
  | "backgroundRemoval"
  | "upscaleImage";

export interface Generation {
  id: string;
  type: GenerationType;
  prompt?: string;
  outputUrl: string;
  status: "COMPLETED" | "PROCESSING" | "FAILED";
  user: {
    name: string;
    email: string;
  };
  createdAt: Date;
  metadata?: {
    style?: string;
    room?: string;
    building?: string;
    upscaleAmount?: number;
    creativity?: number;
  };
}

export const columns: ColumnDef<Generation>[] = [
  {
    accessorKey: "user",
    header: "User",
    cell: ({ row }) => (
      <div>
        <p className="font-medium text-foreground">{row.original.user.name}</p>
        <p className="text-sm text-muted-foreground">
          {row.original.user.email}
        </p>
      </div>
    ),
  },
  {
    accessorKey: "type",
    header: "Type",
    cell: ({ row }) => (
      <Badge variant="secondary" className="capitalize">
        {row.original.type.replace(/([A-Z])/g, " $1").trim()}
      </Badge>
    ),
  },
  {
    accessorKey: "metadata",
    header: "Details",
    cell: ({ row }) => {
      const metadata = row.original.metadata;
      if (!metadata) return null;

      return (
        <div className="text-sm text-muted-foreground">
          {metadata.style && <p>Style: {metadata.style}</p>}
          {metadata.room && <p>Room: {metadata.room}</p>}
          {metadata.building && <p>Building: {metadata.building}</p>}
          {metadata.upscaleAmount && <p>Upscale: {metadata.upscaleAmount}x</p>}
          {metadata.creativity && <p>Creativity: {metadata.creativity}</p>}
        </div>
      );
    },
  },
  {
    accessorKey: "prompt",
    header: "Prompt",
    cell: ({ row }) =>
      row.original.prompt ? (
        <p
          className="max-w-[300px] truncate text-foreground"
          title={row.original.prompt}
        >
          {row.original.prompt}
        </p>
      ) : null,
  },
  {
    accessorKey: "outputUrl",
    header: "Output",
    cell: ({ row }) => (
      <div className="relative h-16 w-16">
        <Image
          src={row.original.outputUrl}
          alt="Generated image"
          fill
          className="object-cover rounded-md ring-1 ring-border"
        />
      </div>
    ),
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const status = row.original.status;
      const variant =
        status === "COMPLETED"
          ? "default"
          : status === "PROCESSING"
          ? "secondary"
          : "destructive";

      return <Badge variant={variant}>{status}</Badge>;
    },
  },
  {
    accessorKey: "createdAt",
    header: "Date",
    cell: ({ row }) => (
      <span className="text-muted-foreground">
        {formatDate(row.original.createdAt)}
      </span>
    ),
  },
];
