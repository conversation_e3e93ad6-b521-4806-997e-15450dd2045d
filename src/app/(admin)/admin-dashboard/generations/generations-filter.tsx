"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { useCallback } from "react";
import { Input } from "@/modules/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/modules/ui/select";
import { Button } from "@/modules/ui/button";
import { Calendar } from "@/modules/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/modules/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";

export function GenerationsFilter() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const createQueryString = useCallback(
    (params: Record<string, string | null>) => {
      const newSearchParams = new URLSearchParams(searchParams.toString());

      for (const [key, value] of Object.entries(params)) {
        if (value === null) {
          newSearchParams.delete(key);
        } else {
          newSearchParams.set(key, value);
        }
      }

      return newSearchParams.toString();
    },
    [searchParams]
  );

  const updateFilter = (key: string, value: string | null) => {
    const queryString = createQueryString({
      [key]: value === "all" ? null : value,
      page: "1", // Reset to first page when filter changes
    });
    router.push(`?${queryString}`);
  };

  return (
    <div className="bg-card p-4 rounded-lg border border-border space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">Type</label>
          <Select
            value={searchParams.get("type") || "all"}
            onValueChange={(value) => updateFilter("type", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="All types" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All types</SelectItem>
              <SelectItem value="interior">Interior</SelectItem>
              <SelectItem value="exterior">Exterior</SelectItem>
              <SelectItem value="virtualStaging">Virtual Staging</SelectItem>
              <SelectItem value="backgroundRemoval">
                Background Removal
              </SelectItem>
              <SelectItem value="upscaleImage">Upscale Image</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">Status</label>
          <Select
            value={searchParams.get("status") || "all"}
            onValueChange={(value) => updateFilter("status", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="All statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All statuses</SelectItem>
              <SelectItem value="COMPLETED">Completed</SelectItem>
              <SelectItem value="PROCESSING">Processing</SelectItem>
              <SelectItem value="FAILED">Failed</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Date From
          </label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-start text-left font-normal"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {searchParams.get("dateFrom") ? (
                  format(new Date(searchParams.get("dateFrom")!), "PPP")
                ) : (
                  <span>Pick a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={
                  searchParams.get("dateFrom")
                    ? new Date(searchParams.get("dateFrom")!)
                    : undefined
                }
                onSelect={(date) =>
                  updateFilter("dateFrom", date ? date.toISOString() : null)
                }
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">Date To</label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-start text-left font-normal"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {searchParams.get("dateTo") ? (
                  format(new Date(searchParams.get("dateTo")!), "PPP")
                ) : (
                  <span>Pick a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={
                  searchParams.get("dateTo")
                    ? new Date(searchParams.get("dateTo")!)
                    : undefined
                }
                onSelect={(date) =>
                  updateFilter("dateTo", date ? date.toISOString() : null)
                }
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        <Input
          placeholder="Search by user or prompt..."
          value={searchParams.get("search") || ""}
          onChange={(e) => updateFilter("search", e.target.value || null)}
          className="max-w-sm"
        />
        <Button
          variant="secondary"
          onClick={() => {
            router.push("?");
          }}
        >
          Reset Filters
        </Button>
      </div>
    </div>
  );
}
