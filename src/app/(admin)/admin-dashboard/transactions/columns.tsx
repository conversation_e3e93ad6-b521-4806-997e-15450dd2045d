"use client";

import { ColumnDef, type Column } from "@tanstack/react-table";
import { Badge } from "@/modules/ui/badge";
import { formatDate, formatCurrency } from "@/lib/utils";
import { Transaction } from "@/modules/admin-dashboard/types";
import { ArrowUpDown } from "lucide-react";
import { Button } from "@/modules/ui/button";

function SortableHeader({
  column,
  title,
}: {
  column: Column<Transaction>;
  title: string;
}) {
  return (
    <Button
      variant="ghost"
      className="p-0 hover:bg-transparent"
      onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    >
      {title}
      <ArrowUpDown className="ml-2 h-4 w-4" />
      {column.getIsSorted() && (
        <span className="ml-2 text-xs text-muted-foreground">
          ({column.getIsSorted() === "asc" ? "asc" : "desc"})
        </span>
      )}
    </Button>
  );
}

export const columns: ColumnDef<Transaction>[] = [
  {
    id: "user",
    accessorKey: "user.name",
    header: ({ column }) => <SortableHeader column={column} title="User" />,
    cell: ({ row }) => (
      <div>
        <p className="font-medium text-foreground">{row.original.user.name}</p>
        <p className="text-sm text-muted-foreground">
          {row.original.user.email}
        </p>
      </div>
    ),
  },
  {
    id: "package",
    accessorKey: "packageName",
    header: ({ column }) => <SortableHeader column={column} title="Package" />,
    cell: ({ row }) => (
      <Badge variant="outline">{row.original.packageName}</Badge>
    ),
  },
  {
    id: "amount",
    accessorKey: "amount",
    header: ({ column }) => <SortableHeader column={column} title="Amount" />,
    cell: ({ row }) => formatCurrency(row.original.amount / 100),
  },
  {
    id: "credits",
    accessorKey: "credits",
    header: ({ column }) => <SortableHeader column={column} title="Credits" />,
    cell: ({ row }) => row.original.credits.toLocaleString(),
  },
  {
    id: "date",
    accessorKey: "createdAt",
    header: ({ column }) => <SortableHeader column={column} title="Date" />,
    cell: ({ row }) => formatDate(row.original.createdAt),
  },
];
