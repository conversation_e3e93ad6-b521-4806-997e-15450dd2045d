import { columns } from "./columns";
import {
  getTransactions,
  TransactionSortField,
  SortOrder,
} from "@/modules/admin-dashboard/actions";
import { TransactionsTableFilters } from "./transactions-table-filters";
import { TablePagination } from "./table-pagination";

interface PageProps {
  searchParams: {
    page?: string;
    limit?: string;
    sort?: string;
    order?: string;
    search?: string;
    packageName?: string;
    minAmount?: string;
    maxAmount?: string;
  };
}

export default async function TransactionsPage({ searchParams }: PageProps) {
  const page = Number(searchParams.page) || 1;
  const limit = Number(searchParams.limit) || 10;
  const sortParams = searchParams.sort?.split(".") || ["createdAt", "desc"];
  const sortBy = sortParams[0] as TransactionSortField;
  const sortOrder = sortParams[1] as SortOrder;

  const result = await getTransactions({
    page,
    limit,
    sortBy,
    sortOrder,
    search: searchParams.search,
    packageName: searchParams.packageName,
    minAmount: searchParams.minAmount
      ? Number(searchParams.minAmount) * 100
      : undefined,
    maxAmount: searchParams.maxAmount
      ? Number(searchParams.maxAmount) * 100
      : undefined,
  });

  return (
    <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold text-foreground">Transactions</h1>
        <div className="text-sm text-muted-foreground">
          Total transactions: {result.pagination.total}
        </div>
      </div>

      <div className="bg-card rounded-lg border border-border shadow-sm">
        <TransactionsTableFilters />
        <TablePagination
          columns={columns}
          data={result.transactions}
          pageCount={result.pagination.pageCount}
          pageIndex={page - 1}
          pageSize={limit}
        />
      </div>
    </main>
  );
}
