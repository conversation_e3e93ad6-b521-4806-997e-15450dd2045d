"use client";

import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { Input } from "@/modules/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/modules/ui/select";
import { useDebounce } from "@/modules/admin-dashboard/hooks/use-debounce";
import { useEffect, useState } from "react";

const PACKAGES = ["Basic", "Pro", "Enterprise"] as const;

export function TransactionsTableFilters() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [search, setSearch] = useState(searchParams.get("search") || "");
  const debouncedSearch = useDebounce(search, 500);

  const createQueryString = (params: Record<string, string | null>) => {
    const newSearchParams = new URLSearchParams(searchParams.toString());

    Object.entries(params).forEach(([key, value]) => {
      if (value === null) {
        newSearchParams.delete(key);
      } else {
        newSearchParams.set(key, value);
      }
    });

    return newSearchParams.toString();
  };

  useEffect(() => {
    const queryString = createQueryString({
      search: debouncedSearch || null,
      page: "1",
    });
    router.push(`${pathname}?${queryString}`);
  }, [debouncedSearch]);

  return (
    <div className="p-4 flex gap-4 items-center border-b border-border bg-card">
      <Input
        placeholder="Search transactions..."
        value={search}
        onChange={(e) => setSearch(e.target.value)}
        className="max-w-xs"
      />
      <Select
        defaultValue={searchParams.get("packageName") || "ALL"}
        onValueChange={(value) => {
          const queryString = createQueryString({
            packageName: value === "ALL" ? null : value,
            page: "1",
          });
          router.push(`${pathname}?${queryString}`);
        }}
      >
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Filter by package" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="ALL">All Packages</SelectItem>
          {PACKAGES.map((pkg) => (
            <SelectItem key={pkg} value={pkg}>
              {pkg}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      <div className="flex gap-2 items-center">
        <Input
          type="number"
          placeholder="Min amount"
          className="w-24"
          value={searchParams.get("minAmount") || ""}
          onChange={(e) => {
            const queryString = createQueryString({
              minAmount: e.target.value || null,
              page: "1",
            });
            router.push(`${pathname}?${queryString}`);
          }}
        />
        <span className="text-muted-foreground">-</span>
        <Input
          type="number"
          placeholder="Max amount"
          className="w-24"
          value={searchParams.get("maxAmount") || ""}
          onChange={(e) => {
            const queryString = createQueryString({
              maxAmount: e.target.value || null,
              page: "1",
            });
            router.push(`${pathname}?${queryString}`);
          }}
        />
      </div>
    </div>
  );
}
