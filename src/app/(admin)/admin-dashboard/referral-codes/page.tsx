import { redirect } from "next/navigation";
import { currentUser } from "@/modules/auth/actions/user-actions";
import { columns } from "@/app/(admin)/admin-dashboard/referral-codes/columns";
import { ReferralCodesTableFilters } from "@/app/(admin)/admin-dashboard/referral-codes/referral-codes-table-filters";
import { TablePagination } from "@/app/(admin)/admin-dashboard/referral-codes/table-pagination";
import { getReferralCodes } from "@/modules/admin-dashboard/actions/get-referral-codes";

interface PageProps {
  searchParams: {
    page?: string;
    limit?: string;
    sort?: string;
    order?: string;
    search?: string;
  };
}

export default async function ReferralCodesPage({ searchParams }: PageProps) {
  const user = await currentUser();

  if (!user || user.role !== "ADMIN") {
    redirect("/");
  }

  const page = Number(searchParams.page) || 1;
  const limit = Number(searchParams.limit) || 10;

  const result = await getReferralCodes({
    page,
    limit,
    search: searchParams.search,
  });

  return (
    <div className="h-full bg-background">
      <main className="container mx-auto py-6 space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold tracking-tight">Referral Codes</h1>
          <div className="text-sm text-muted-foreground">
            Total referral codes: {result.pagination.total}
          </div>
        </div>

        <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
          <ReferralCodesTableFilters />
          <TablePagination
            columns={columns}
            data={result.referralCodes}
            pageCount={result.pagination.pageCount}
            pageIndex={page - 1}
            pageSize={limit}
          />
        </div>
      </main>
    </div>
  );
}
