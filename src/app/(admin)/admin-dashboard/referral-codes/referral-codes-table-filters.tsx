"use client";

import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { Input } from "@/modules/ui/input";
import { Button } from "@/modules/ui/button";
import { useCallback, useTransition } from "react";
import { useDebounce } from "@/modules/admin-dashboard/hooks/use-debounce";

export function ReferralCodesTableFilters() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();

  const createQueryString = useCallback(
    (params: Record<string, string | null>) => {
      const newSearchParams = new URLSearchParams(searchParams?.toString());

      for (const [key, value] of Object.entries(params)) {
        if (value === null) {
          newSearchParams.delete(key);
        } else {
          newSearchParams.set(key, value);
        }
      }

      return newSearchParams.toString();
    },
    [searchParams]
  );

  const debouncedCallback = useDebounce((value: string) => {
    startTransition(() => {
      const queryString = createQueryString({
        search: value || null,
        page: "1",
      });
      router.push(`${pathname}?${queryString}`);
    });
  }, 500);

  return (
    <div className="p-4 flex items-center gap-4 border-b">
      <div className="max-w-sm flex-1">
        <Input
          placeholder="Search by code or user..."
          defaultValue={searchParams?.get("search") ?? ""}
          onChange={(e) => debouncedCallback(e.target.value)}
        />
      </div>
      <Button
        variant="outline"
        onClick={() => {
          startTransition(() => {
            router.push(pathname);
          });
        }}
        disabled={isPending}
      >
        Reset filters
      </Button>
    </div>
  );
}
