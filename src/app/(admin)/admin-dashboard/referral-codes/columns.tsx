"use client";

import { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { Badge } from "@/modules/ui/badge";
import { ReferralStatus } from "@prisma/client";

export type ReferralCodeRow = {
  id: string;
  code: string;
  user: {
    id: string;
    name: string | null;
    email: string | null;
  };
  referrals: {
    id: string;
    status: ReferralStatus;
    creditedAt: Date | null;
    referred: {
      name: string | null;
      email: string | null;
    };
  }[];
  createdAt: Date;
  updatedAt: Date;
};

export const columns: ColumnDef<ReferralCodeRow>[] = [
  {
    accessorKey: "code",
    header: "Code",
    cell: ({ row }) => <div className="font-medium">{row.original.code}</div>,
  },
  {
    accessorKey: "user",
    header: "Owner",
    cell: ({ row }) => (
      <div>
        <div className="font-medium">{row.original.user.name || "N/A"}</div>
        <div className="text-sm text-muted-foreground">
          {row.original.user.email}
        </div>
      </div>
    ),
  },
  {
    accessorKey: "referrals",
    header: "Referrals",
    cell: ({ row }) => {
      const totalReferrals = row.original.referrals.length;
      const creditedReferrals = row.original.referrals.filter(
        (ref) => ref.status === "CREDITED"
      ).length;

      return (
        <div>
          <div className="font-medium">{totalReferrals} total</div>
          <div className="text-sm text-muted-foreground">
            {creditedReferrals} credited
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: "createdAt",
    header: "Created",
    cell: ({ row }) => (
      <div className="text-sm">
        {format(new Date(row.original.createdAt), "MMM d, yyyy")}
      </div>
    ),
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const hasActiveReferrals = row.original.referrals.some(
        (ref) => ref.status === "PENDING"
      );

      return (
        <Badge variant={hasActiveReferrals ? "default" : "secondary"}>
          {hasActiveReferrals ? "Active" : "Inactive"}
        </Badge>
      );
    },
  },
];
