import { redirect } from "next/navigation";
import { currentUser } from "@/modules/auth/actions/user-actions";
import { columns } from "./columns";
import {
  getSubscriptions,
  AdminSubscriptionSortField,
  SortOrder,
} from "@/modules/admin-dashboard/actions";
import { SubscriptionsTableFilters } from "./subscriptions-table-filters";
import { TablePagination } from "./table-pagination";
import { SubscriptionStatus } from "@prisma/client";

interface PageProps {
  searchParams: {
    page?: string;
    limit?: string;
    sort?: string;
    order?: string;
    search?: string;
    status?: SubscriptionStatus;
    packageName?: string;
  };
}

export default async function SubscriptionsPage({ searchParams }: PageProps) {
  const user = await currentUser();

  if (!user || user.role !== "ADMIN") {
    redirect("/");
  }

  const page = Number(searchParams.page) || 1;
  const limit = Number(searchParams.limit) || 10;

  let sortBy: AdminSubscriptionSortField = "endDate";
  let sortOrder: SortOrder = "desc";

  if (searchParams.sort) {
    const [field, order] = searchParams.sort.split(".");
    if (field && order) {
      const validFields: AdminSubscriptionSortField[] = [
        "startDate",
        "endDate",
        "packageName",
        "paymentStatus",
        "user.name",
        "user.email",
        "user.imageCredits",
      ];

      if (validFields.includes(field as AdminSubscriptionSortField)) {
        sortBy = field as AdminSubscriptionSortField;
        if (order === "asc" || order === "desc") {
          sortOrder = order;
        }
      }
    }
  }

  const result = await getSubscriptions({
    page,
    limit,
    sortBy,
    sortOrder,
    search: searchParams.search,
    status: searchParams.status as SubscriptionStatus | undefined,
    packageName: searchParams.packageName,
  });

  return (
    <div className="min-h-screen bg-background">
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-foreground">Subscriptions</h1>
          <div className="text-sm text-muted-foreground">
            Total subscriptions: {result.pagination.total}
          </div>
        </div>

        <div className="bg-card rounded-lg border border-border shadow-sm">
          <SubscriptionsTableFilters />
          <TablePagination
            columns={columns}
            data={result.subscriptions}
            pageCount={result.pagination.pageCount}
            pageIndex={page - 1}
            pageSize={limit}
          />
        </div>
      </main>
    </div>
  );
}
