"use client";

import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { Input } from "@/modules/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/modules/ui/select";
import { useDebounce } from "@/modules/admin-dashboard/hooks/use-debounce";
import { useEffect, useState } from "react";
import { SubscriptionStatus } from "@prisma/client";

const PACKAGES = ["Basic", "Pro", "Enterprise"] as const;

export function SubscriptionsTableFilters() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [search, setSearch] = useState(searchParams.get("search") || "");
  const debouncedSearch = useDebounce(search, 500);

  const createQueryString = (params: Record<string, string | null>) => {
    const newSearchParams = new URLSearchParams(searchParams.toString());

    Object.entries(params).forEach(([key, value]) => {
      if (value === null) {
        newSearchParams.delete(key);
      } else {
        newSearchParams.set(key, value);
      }
    });

    return newSearchParams.toString();
  };

  useEffect(() => {
    const queryString = createQueryString({
      search: debouncedSearch || null,
      page: "1",
    });
    router.push(`${pathname}?${queryString}`);
  }, [debouncedSearch]);

  return (
    <div className="p-4 flex gap-4 items-center border-b border-border bg-card">
      <Input
        placeholder="Search subscriptions..."
        value={search}
        onChange={(e) => setSearch(e.target.value)}
        className="max-w-xs"
      />
      <Select
        defaultValue={searchParams.get("status") || "ALL"}
        onValueChange={(value) => {
          const queryString = createQueryString({
            status: value === "ALL" ? null : value,
            page: "1",
          });
          router.push(`${pathname}?${queryString}`);
        }}
      >
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Filter by status" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="ALL">All Statuses</SelectItem>
          <SelectItem value={SubscriptionStatus.ACTIVE}>Active</SelectItem>
          <SelectItem value={SubscriptionStatus.PAST_DUE}>Past Due</SelectItem>
          <SelectItem value={SubscriptionStatus.CANCELED}>Canceled</SelectItem>
          <SelectItem value={SubscriptionStatus.EXPIRED}>Expired</SelectItem>
          <SelectItem value={SubscriptionStatus.PAUSED}>Paused</SelectItem>
        </SelectContent>
      </Select>
      <Select
        defaultValue={searchParams.get("packageName") || "ALL"}
        onValueChange={(value) => {
          const queryString = createQueryString({
            packageName: value === "ALL" ? null : value,
            page: "1",
          });
          router.push(`${pathname}?${queryString}`);
        }}
      >
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Filter by package" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="ALL">All Packages</SelectItem>
          {PACKAGES.map((pkg) => (
            <SelectItem key={pkg} value={pkg}>
              {pkg}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
