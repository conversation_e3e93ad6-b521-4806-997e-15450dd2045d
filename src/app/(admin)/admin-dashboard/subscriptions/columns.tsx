"use client";

import { ColumnDef, type Column } from "@tanstack/react-table";
import { Badge } from "@/modules/ui/badge";
import { formatDate } from "@/lib/utils";
import { SubscriptionWithUser } from "@/modules/admin-dashboard/types";
import { ArrowUpDown } from "lucide-react";
import { Button } from "@/modules/ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/modules/ui/tooltip";

function SortableHeader({
  column,
  title,
  field,
}: {
  column: Column<SubscriptionWithUser>;
  title: string;
  field: string;
}) {
  return (
    <Button
      variant="ghost"
      className="p-0 hover:bg-transparent"
      onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    >
      {title}
      <ArrowUpDown className="ml-2 h-4 w-4" />
      {column.getIsSorted() && (
        <span className="ml-2 text-xs text-muted-foreground">
          ({column.getIsSorted() === "asc" ? "asc" : "desc"})
        </span>
      )}
    </Button>
  );
}

export const columns: ColumnDef<SubscriptionWithUser>[] = [
  {
    id: "user.name",
    accessorKey: "user.name",
    header: ({ column }) => (
      <SortableHeader column={column} title="User" field="user.name" />
    ),
    cell: ({ row }) => (
      <div>
        <p className="font-medium">{row.original.user.name}</p>
        <p className="text-sm text-gray-500">{row.original.user.email}</p>
      </div>
    ),
  },
  {
    id: "packageName",
    accessorKey: "packageName",
    header: ({ column }) => (
      <SortableHeader column={column} title="Package" field="packageName" />
    ),
    cell: ({ row }) => (
      <Badge variant="outline">{row.original.packageName}</Badge>
    ),
  },
  {
    id: "paymentStatus",
    accessorKey: "paymentStatus",
    header: ({ column }) => (
      <SortableHeader column={column} title="Status" field="paymentStatus" />
    ),
    cell: ({ row }) => (
      <Badge
        variant={
          row.original.paymentStatus === "ACTIVE" ? "default" : "secondary"
        }
      >
        {row.original.paymentStatus}
      </Badge>
    ),
  },
  {
    id: "startDate",
    accessorKey: "startDate",
    header: ({ column }) => (
      <SortableHeader column={column} title="Start Date" field="startDate" />
    ),
    cell: ({ row }) => formatDate(row.original.startDate),
  },
  {
    id: "endDate",
    accessorKey: "endDate",
    header: ({ column }) => (
      <SortableHeader column={column} title="End Date" field="endDate" />
    ),
    cell: ({ row }) => formatDate(row.original.endDate),
  },
  {
    id: "user.imageCredits",
    accessorKey: "user.imageCredits",
    header: ({ column }) => (
      <SortableHeader
        column={column}
        title="Credits"
        field="user.imageCredits"
      />
    ),
    cell: ({ row }) => row.original.user.imageCredits.toLocaleString(),
    sortingFn: "basic",
  },
];
