"use client";

import { useRouter, useSearchParams, usePathname } from "next/navigation";
import { DataTable } from "@/modules/ui/data-table";
import { type SortingState } from "@tanstack/react-table";
import { type ColumnDef } from "@tanstack/react-table";
import { SubscriptionWithUser } from "@/modules/admin-dashboard/types";

interface TablePaginationProps {
  data: SubscriptionWithUser[];
  columns: ColumnDef<SubscriptionWithUser, any>[];
  pageCount: number;
  pageIndex: number;
  pageSize: number;
}

export function TablePagination({
  data,
  columns,
  pageCount,
  pageIndex,
  pageSize,
}: TablePaginationProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();

  const createQueryString = (params: Record<string, string | null>) => {
    const newSearchParams = new URLSearchParams(searchParams.toString());

    Object.entries(params).forEach(([key, value]) => {
      if (value === null) {
        newSearchParams.delete(key);
      } else {
        newSearchParams.set(key, value);
      }
    });

    return newSearchParams.toString();
  };

  return (
    <div className="bg-card rounded-lg border-none">
      <DataTable
        columns={columns}
        data={data}
        pageCount={pageCount}
        pagination={{
          pageIndex,
          pageSize,
        }}
        onPaginationChange={(newPage) => {
          const queryString = createQueryString({
            page: String(newPage + 1),
          });
          router.push(`${pathname}?${queryString}`);
        }}
        onSortingChange={(sorting: SortingState) => {
          const queryString = createQueryString({
            sort:
              sorting.length > 0
                ? `${sorting[0].id}.${sorting[0].desc ? "desc" : "asc"}`
                : null,
            page: "1",
          });
          router.push(`${pathname}?${queryString}`);
        }}
      />
    </div>
  );
}
