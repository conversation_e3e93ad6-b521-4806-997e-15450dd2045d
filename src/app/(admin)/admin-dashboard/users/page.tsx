import { redirect } from "next/navigation";
import { currentUser } from "@/modules/auth/actions/user-actions";
import { columns } from "./columns";
import {
  getUsers,
  SortField,
  SortOrder,
} from "@/modules/admin-dashboard/actions";
import { UsersTableFilters } from "./users-table-filters";
import { TablePagination } from "./table-pagination";
import { SubscriptionStatus } from "@prisma/client";
import { EmailActions } from "./email-actions";
import { Card, CardContent, CardHeader, CardTitle } from "@/modules/ui/card";

interface PageProps {
  searchParams: {
    page?: string;
    limit?: string;
    sort?: string;
    order?: string;
    search?: string;
    role?: string;
    subscriptionStatus?: SubscriptionStatus;
  };
}

export default async function UsersPage({ searchParams }: PageProps) {
  const user = await currentUser();

  if (!user || user.role !== "ADMIN") {
    redirect("/");
  }

  const page = Number(searchParams.page) || 1;
  const limit = Number(searchParams.limit) || 10;

  let sortBy: SortField = "lastCreditReset";
  let sortOrder: SortOrder = "desc";

  if (searchParams.sort) {
    const [field, order] = searchParams.sort.split(".");
    if (field && order) {
      const validFields: SortField[] = [
        "name",
        "email",
        "role",
        "imageCredits",
        "lastCreditReset",
        "industry",
        "source",
        "subscription.packageName",
        "subscription.paymentStatus",
        "subscription.endDate",
        "generationCount",
      ];

      if (
        validFields.includes(field as SortField) &&
        (order === "asc" || order === "desc")
      ) {
        sortBy = field as SortField;
        sortOrder = order as SortOrder;
      }
    }
  }

  const result = await getUsers({
    page,
    limit,
    sortBy,
    sortOrder,
    search: searchParams.search,
    role: searchParams.role,
    subscriptionStatus: searchParams.subscriptionStatus as
      | SubscriptionStatus
      | undefined,
  });

  return (
    <main className="mx-auto py-6 mb-20 px-4 space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h1 className="text-3xl font-bold tracking-tight">Users</h1>
        <EmailActions />
      </div>

      <Card>
        <CardHeader className="p-0">
          <UsersTableFilters />
        </CardHeader>
        <CardContent className="p-0">
          <TablePagination
            columns={columns}
            data={result.users}
            pageCount={result.pagination.pageCount}
            pageIndex={page - 1}
            pageSize={limit}
          />
        </CardContent>
      </Card>
    </main>
  );
}
