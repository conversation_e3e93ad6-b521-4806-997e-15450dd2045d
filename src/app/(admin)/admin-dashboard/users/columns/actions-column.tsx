import { ColumnDef } from "@tanstack/react-table";
import { AdminUser } from "@/modules/admin-dashboard/types";
import { Button } from "@/modules/ui/button";
import { Dialog } from "@/modules/ui/dialog";
import { MoreHorizontal, Mail, Copy, Coins } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/modules/ui/dropdown-menu";
import { useState } from "react";
import { toast } from "sonner";
import { SingleUserEmailDialog } from "../dialogs/single-user-email-dialog";
import { UpdateCreditsDialog } from "../dialogs/update-credits-dialog";

function UserActions({ user }: { user: AdminUser }) {
  const [isEmailDialogOpen, setIsEmailDialogOpen] = useState(false);
  const [isCreditsDialogOpen, setIsCreditsDialogOpen] = useState(false);

  const copyEmail = (email: string | null) => {
    if (email) {
      navigator.clipboard.writeText(email);
      toast.success("Email copied to clipboard");
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuItem
            onClick={() => copyEmail(user.email)}
            className="flex items-center"
          >
            <Copy className="mr-2 h-4 w-4" />
            Copy email
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={() => setIsEmailDialogOpen(true)}
            className="flex items-center"
          >
            <Mail className="mr-2 h-4 w-4" />
            Send Email
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => setIsCreditsDialogOpen(true)}
            className="flex items-center"
          >
            <Coins className="mr-2 h-4 w-4" />
            Update Credits
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <Dialog open={isEmailDialogOpen} onOpenChange={setIsEmailDialogOpen}>
        <SingleUserEmailDialog
          user={user}
          onOpenChange={setIsEmailDialogOpen}
        />
      </Dialog>

      <Dialog open={isCreditsDialogOpen} onOpenChange={setIsCreditsDialogOpen}>
        <UpdateCreditsDialog
          user={user}
          onOpenChange={setIsCreditsDialogOpen}
        />
      </Dialog>
    </>
  );
}

export const actionsColumn: ColumnDef<AdminUser> = {
  id: "actions",
  cell: ({ row }) => <UserActions user={row.original} />,
};
