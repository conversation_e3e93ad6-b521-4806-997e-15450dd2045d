import { ColumnDef } from "@tanstack/react-table";
import { AdminUser } from "@/modules/admin-dashboard/types";
import { Avatar, AvatarFallback, AvatarImage } from "@/modules/ui/avatar";
import { SortableHeader } from "./sortable-header";
import { Badge } from "@/modules/ui/badge";

export const userColumn: ColumnDef<AdminUser> = {
  accessorKey: "name",
  header: ({ column }) => <SortableHeader column={column} title="User" />,
  cell: ({ row }) => {
    const user = row.original;
    return (
      <div className="flex items-center gap-3">
        <Avatar>
          <AvatarImage src={user.image || undefined} />
          <AvatarFallback>{user.name?.charAt(0) || "U"}</AvatarFallback>
        </Avatar>
        <div className="flex flex-col gap-1">
          <div>
            <p className="font-medium">{user.name}</p>
            <p className="text-sm text-gray-500">{user.email}</p>
          </div>
          {(user.totalReferrals > 0 || user.referralCredits > 0) && (
            <div className="flex gap-2 flex-wrap">
              {user.totalReferrals > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {user.totalReferrals} referrals
                </Badge>
              )}
              {user.referralCredits > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {user.referralCredits} referral credits
                </Badge>
              )}
            </div>
          )}
        </div>
      </div>
    );
  },
};
