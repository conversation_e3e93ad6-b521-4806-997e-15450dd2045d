import { ColumnDef } from "@tanstack/react-table";
import { AdminUser } from "@/modules/admin-dashboard/types";
import { formatDate } from "@/lib/utils";
import { SortableHeader } from "./sortable-header";

export const lastActiveColumn: ColumnDef<AdminUser> = {
  accessorKey: "lastCreditReset",
  header: ({ column }) => (
    <SortableHeader column={column} title="Last Active" />
  ),
  cell: ({ row }) => formatDate(row.original.lastCreditReset || new Date()),
};
