import { ColumnDef } from "@tanstack/react-table";
import { AdminUser } from "@/modules/admin-dashboard/types";
import { Badge } from "@/modules/ui/badge";
import { SortableHeader } from "./sortable-header";

export const sourceColumn: ColumnDef<AdminUser> = {
  accessorKey: "source",
  header: ({ column }) => <SortableHeader column={column} title="Source" />,
  cell: ({ row }) => {
    const source = row.original.source;
    return source ? (
      <Badge variant="outline" className="capitalize">
        {source}
      </Badge>
    ) : (
      <span className="text-muted-foreground text-sm">-</span>
    );
  },
  enableSorting: true,
};
