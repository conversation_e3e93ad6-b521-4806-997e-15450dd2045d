import { ColumnDef } from "@tanstack/react-table";
import { AdminUser } from "@/modules/admin-dashboard/types";
import { userColumn } from "./user-column";
import { subscriptionColumn } from "./subscription-column";
import { statusColumn } from "./status-column";
import { roleColumn } from "./role-column";
import { creditsColumn } from "./credits-column";
import { generationsColumn } from "./generations-column";
import { lastActiveColumn } from "./last-active-column";
import { actionsColumn } from "./actions-column";

export const columns: ColumnDef<AdminUser>[] = [
  userColumn,
  subscriptionColumn,
  statusColumn,
  roleColumn,
  creditsColumn,
  generationsColumn,
  lastActiveColumn,
  actionsColumn,
];
