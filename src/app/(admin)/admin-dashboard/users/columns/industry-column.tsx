import { ColumnDef } from "@tanstack/react-table";
import { AdminUser } from "@/modules/admin-dashboard/types";
import { Badge } from "@/modules/ui/badge";
import { SortableHeader } from "./sortable-header";

export const industryColumn: ColumnDef<AdminUser> = {
  accessorKey: "industry",
  header: ({ column }) => <SortableHeader column={column} title="Industry" />,
  cell: ({ row }) => {
    const industry = row.original.industry;
    return industry ? (
      <Badge variant="outline" className="capitalize">
        {industry}
      </Badge>
    ) : (
      <span className="text-muted-foreground text-sm">-</span>
    );
  },
  enableSorting: true,
};
