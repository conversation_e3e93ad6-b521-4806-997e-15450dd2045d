import { ColumnDef } from "@tanstack/react-table";
import { AdminUser } from "@/modules/admin-dashboard/types";
import { Badge } from "@/modules/ui/badge";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/modules/ui/tooltip";
import { formatDate } from "@/lib/utils";
import { SortableHeader } from "./sortable-header";

export const statusColumn: ColumnDef<AdminUser> = {
  accessorKey: "subscription.paymentStatus",
  header: ({ column }) => <SortableHeader column={column} title="Status" />,
  cell: ({ row }) => {
    const subscription = row.original.subscription;
    if (!subscription) return <Badge variant="outline">No Subscription</Badge>;
    return (
      <Tooltip>
        <TooltipTrigger>
          <Badge
            variant={
              subscription.paymentStatus === "ACTIVE" ? "default" : "secondary"
            }
          >
            {subscription.paymentStatus}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p>Status: {subscription.paymentStatus}</p>
          <p>Ends: {formatDate(subscription.endDate)}</p>
        </TooltipContent>
      </Tooltip>
    );
  },
};
