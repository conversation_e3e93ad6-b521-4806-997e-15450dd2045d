import { ColumnDef } from "@tanstack/react-table";
import { AdminUser } from "@/modules/admin-dashboard/types";
import { Badge } from "@/modules/ui/badge";
import { SortableHeader } from "./sortable-header";

export const roleColumn: ColumnDef<AdminUser> = {
  accessorKey: "role",
  header: ({ column }) => <SortableHeader column={column} title="Role" />,
  cell: ({ row }) => (
    <Badge variant={row.original.role === "ADMIN" ? "default" : "secondary"}>
      {row.original.role}
    </Badge>
  ),
};
