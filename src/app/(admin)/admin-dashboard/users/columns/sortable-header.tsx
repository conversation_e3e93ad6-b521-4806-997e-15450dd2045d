import { Column } from "@tanstack/react-table";
import { But<PERSON> } from "@/modules/ui/button";
import { ArrowUpDown } from "lucide-react";
import { AdminUser } from "@/modules/admin-dashboard/types";

interface SortableHeaderProps {
  column: Column<AdminUser>;
  title: string;
}

export function SortableHeader({ column, title }: SortableHeaderProps) {
  return (
    <Button
      variant="ghost"
      className="p-0 hover:bg-transparent"
      onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
    >
      {title}
      <ArrowUpDown className="ml-2 h-4 w-4" />
      {column.getIsSorted() && (
        <span className="ml-2 text-xs text-muted-foreground">
          ({column.getIsSorted() === "asc" ? "asc" : "desc"})
        </span>
      )}
    </Button>
  );
}
