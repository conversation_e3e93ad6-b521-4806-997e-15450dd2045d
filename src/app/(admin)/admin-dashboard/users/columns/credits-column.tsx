import { ColumnDef } from "@tanstack/react-table";
import { AdminUser } from "@/modules/admin-dashboard/types";
import { SortableHeader } from "./sortable-header";

export const creditsColumn: ColumnDef<AdminUser> = {
  accessorKey: "imageCredits",
  header: ({ column }) => <SortableHeader column={column} title="Credits" />,
  cell: ({ row }) => {
    const credits = row.original.imageCredits;
    return <div className="font-medium">{credits.toLocaleString()}</div>;
  },
};
