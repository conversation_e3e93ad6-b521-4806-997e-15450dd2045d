import { ColumnDef } from "@tanstack/react-table";
import { AdminUser } from "@/modules/admin-dashboard/types";
import { Button } from "@/modules/ui/button";
import { SortableHeader } from "./sortable-header";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/modules/ui/dialog";
import { UserDetails } from "../user-details";

export const generationsColumn: ColumnDef<AdminUser> = {
  accessorKey: "_count",
  id: "generationCount",
  header: ({ column }) => (
    <SortableHeader column={column} title="Generations" />
  ),
  cell: ({ row }) => {
    const user = row.original;
    const counts = {
      interior: user._count.interiorDesigns,
      exterior: user._count.exteriorDesigns,
      virtualStaging: user._count.virtualStagings,
      editImage: user._count.editImages,
      total:
        user._count.interiorDesigns +
        user._count.exteriorDesigns +
        user._count.virtualStagings +
        user._count.editImages,
    };

    return (
      <Dialog>
        <DialogTrigger asChild>
          <Button variant="ghost" className="hover:bg-transparent">
            <div className="text-left">
              <div className="font-medium">{counts.total} total</div>
              <div className="text-xs text-muted-foreground">
                Click for details
              </div>
            </div>
          </Button>
        </DialogTrigger>
        <DialogContent size="full" className="max-h-[95vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>User Details - {user.name}</DialogTitle>
          </DialogHeader>
          <UserDetails userId={user.id} />
        </DialogContent>
      </Dialog>
    );
  },
};
