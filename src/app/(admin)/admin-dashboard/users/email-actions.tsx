"use client";

import { <PERSON><PERSON> } from "@/modules/ui/button";
import { Mail, Coins } from "lucide-react";
import { sendBulkEmailWithFilters } from "@/modules/admin-dashboard/actions";
import { useState } from "react";
import { Dialog } from "@/modules/ui/dialog";
import { EmailDialog } from "./email-dialog";
import { BulkCreditUpdateDialog } from "./dialogs/bulk-credit-update-dialog";
import { usePreviewCount } from "@/hooks/use-preview-count";
import type { EmailFiltersState, EmailTemplate } from "@/lib/email/types";

interface EmailData {
  template: EmailTemplate;
  subject: string;
  message?: string;
  discountCode?: string;
  expiryDate?: Date;
  fromEmail?: string;
  fromName?: string;
  creditAmount?: number;
  incidentTitle?: string;
  incidentMessage?: string;
  discountPercentage?: number;
  customFooterMessage?: string;
}

export function EmailActions() {
  const [isEmailOpen, setIsEmailOpen] = useState(false);
  const [isCreditsOpen, setIsCreditsOpen] = useState(false);
  const [filters, setFilters] = useState<EmailFiltersState>({
    roles: ["USER"],
    subscriptionStatus: [],
    lastActiveWithin: undefined,
    minCredits: undefined,
    maxCredits: undefined,
    minGenerations: undefined,
    maxGenerations: undefined,
    hasSubscription: undefined,
  });

  const { count: previewCount, isLoading: isLoadingPreview } =
    usePreviewCount(filters);

  // Initialize state with default values
  const [creditAmount, setCreditAmount] = useState<number>(10);
  const [incidentTitle, setIncidentTitle] = useState<string>("");
  const [incidentMessage, setIncidentMessage] = useState<string>("");
  const [discountPercentage, setDiscountPercentage] = useState<number>(20);
  const [customFooterMessage, setCustomFooterMessage] = useState<string>("");

  const handleSendEmail = async (data: EmailData) => {
    try {
      const result = await sendBulkEmailWithFilters(filters, {
        ...data,
        creditAmount: data.creditAmount || creditAmount,
        incidentTitle: data.incidentTitle || incidentTitle,
        incidentMessage: data.incidentMessage || incidentMessage,
        discountPercentage: data.discountPercentage || discountPercentage,
        customFooterMessage: data.customFooterMessage || customFooterMessage,
      });

      if (!result.success) {
        throw new Error("Failed to send emails");
      }
    } catch (error) {
      throw error instanceof Error ? error : new Error("Failed to send emails");
    }
  };

  return (
    <div className="flex items-center gap-2">
      <Dialog open={isEmailOpen} onOpenChange={setIsEmailOpen}>
        <Button
          className="flex items-center gap-2"
          onClick={() => setIsEmailOpen(true)}
        >
          <Mail className="h-4 w-4" />
          Send Bulk Email
        </Button>

        {isEmailOpen && (
          <EmailDialog
            onSend={handleSendEmail}
            onClose={() => setIsEmailOpen(false)}
            title="Send Bulk Email"
            description="Configure filters and email template to send bulk emails."
            recipientCount={previewCount}
            creditAmount={creditAmount}
            incidentTitle={incidentTitle}
            incidentMessage={incidentMessage}
            discountPercentage={discountPercentage}
            customFooterMessage={customFooterMessage}
            onCreditAmountChange={setCreditAmount}
            onIncidentTitleChange={setIncidentTitle}
            onIncidentMessageChange={setIncidentMessage}
            onDiscountPercentageChange={setDiscountPercentage}
            onCustomFooterMessageChange={setCustomFooterMessage}
          />
        )}
      </Dialog>

      <Dialog open={isCreditsOpen} onOpenChange={setIsCreditsOpen}>
        <Button
          variant="outline"
          className="flex items-center gap-2"
          onClick={() => setIsCreditsOpen(true)}
        >
          <Coins className="h-4 w-4" />
          Bulk Update Credits
        </Button>

        {isCreditsOpen && (
          <BulkCreditUpdateDialog onOpenChange={setIsCreditsOpen} />
        )}
      </Dialog>
    </div>
  );
}
