"use client";

import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/modules/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/modules/ui/card";
import { ScrollArea } from "@/modules/ui/scroll-area";
import Image from "next/image";
import { formatDate } from "@/lib/utils";
import { Badge } from "@/modules/ui/badge";
import { Trash2 } from "lucide-react";
import { Button } from "@/modules/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/modules/ui/alert-dialog";
import { toast } from "sonner";

interface UserDetailsProps {
  userId: string;
}

interface GenerationDetails {
  id: string;
  createdAt: Date;
  inputImage: string;
  outputImages?: string[];
  outputImage?: string;
  prompt?: string;
  style?: string;
  room?: string;
  status: string;
}

interface UserGenerations {
  interiorDesigns: GenerationDetails[];
  exteriorDesigns: GenerationDetails[];
  virtualStagings: GenerationDetails[];
  editImages: GenerationDetails[];
}

function GenerationList({
  generations,
  type,
  onDelete,
}: {
  generations: GenerationDetails[];
  type: string;
  onDelete: (id: string, type: string) => Promise<void>;
}) {
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedGeneration, setSelectedGeneration] = useState<{
    id: string;
    type: string;
  } | null>(null);

  const handleDelete = async (id: string) => {
    setIsDeleting(id);
    try {
      await onDelete(id, type);
      toast.success("Generation deleted successfully");
    } catch (error) {
      toast.error("Failed to delete generation");
      console.error("Error deleting generation:", error);
    } finally {
      setIsDeleting(null);
      setShowDeleteDialog(false);
    }
  };

  return (
    <>
      <ScrollArea className="h-[90vh]">
        <div className="grid grid-cols-4 gap-4 p-4">
          {generations.map((gen) => (
            <Card key={gen.id}>
              <CardHeader className="p-4">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium">
                    {formatDate(gen.createdAt)}
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="ml-2">
                      {gen.status}
                    </Badge>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 text-muted-foreground hover:text-destructive"
                      onClick={() => {
                        setSelectedGeneration({ id: gen.id, type });
                        setShowDeleteDialog(true);
                      }}
                      disabled={isDeleting === gen.id}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-4 pt-0 space-y-4">
                <div className="relative aspect-square w-full overflow-hidden rounded-md">
                  <Image
                    src={
                      Array.isArray(gen.outputImages)
                        ? gen.outputImages[0]
                        : gen.outputImage || ""
                    }
                    alt="Generated image"
                    fill
                    className="object-cover transition-all hover:scale-105"
                  />
                </div>
                {gen.prompt && (
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {gen.prompt}
                  </p>
                )}
                <div className="flex flex-wrap gap-2">
                  {gen.style && <Badge variant="secondary">{gen.style}</Badge>}
                  {gen.room && <Badge variant="secondary">{gen.room}</Badge>}
                </div>
              </CardContent>
            </Card>
          ))}
          {generations.length === 0 && (
            <div className="col-span-4 text-center py-8 text-muted-foreground">
              No generations found
            </div>
          )}
        </div>
      </ScrollArea>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              generated image.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              onClick={() => {
                if (selectedGeneration) {
                  handleDelete(selectedGeneration.id);
                }
              }}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}

export function UserDetails({ userId }: UserDetailsProps) {
  const [generations, setGenerations] = useState<UserGenerations | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const fetchGenerations = async () => {
    try {
      const response = await fetch(`/api/admin/users/${userId}/generations`);
      const data = await response.json();
      setGenerations(data);
    } catch (error) {
      console.error("Failed to fetch generations:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchGenerations();
  }, [userId]);

  const handleDelete = async (id: string, type: string) => {
    try {
      const response = await fetch(`/api/admin/generations/${type}/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete generation");
      }

      await fetchGenerations();
    } catch (error) {
      console.error("Error deleting generation:", error);
      throw error;
    }
  };

  if (isLoading) return <div>Loading...</div>;
  if (!generations) return <div>No data available</div>;

  return (
    <Tabs defaultValue="interior" className="w-full">
      <TabsList className="grid w-full grid-cols-4">
        <TabsTrigger value="interior">
          Interior ({generations?.interiorDesigns.length || 0})
        </TabsTrigger>
        <TabsTrigger value="exterior">
          Exterior ({generations?.exteriorDesigns.length || 0})
        </TabsTrigger>
        <TabsTrigger value="virtualStaging">
          Virtual Staging ({generations?.virtualStagings.length || 0})
        </TabsTrigger>
        <TabsTrigger value="editImage">
          Image Edits ({generations?.editImages.length || 0})
        </TabsTrigger>
      </TabsList>

      <TabsContent value="interior">
        <GenerationList
          generations={generations?.interiorDesigns || []}
          type="interior"
          onDelete={handleDelete}
        />
      </TabsContent>
      <TabsContent value="exterior">
        <GenerationList
          generations={generations?.exteriorDesigns || []}
          type="exterior"
          onDelete={handleDelete}
        />
      </TabsContent>
      <TabsContent value="virtualStaging">
        <GenerationList
          generations={generations?.virtualStagings || []}
          type="virtualStaging"
          onDelete={handleDelete}
        />
      </TabsContent>
      <TabsContent value="editImage">
        <GenerationList
          generations={generations?.editImages || []}
          type="editImage"
          onDelete={handleDelete}
        />
      </TabsContent>
    </Tabs>
  );
}
