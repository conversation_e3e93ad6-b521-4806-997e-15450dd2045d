"use client";

import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { Input } from "@/modules/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/modules/ui/select";
import { useDebounce } from "@/modules/admin-dashboard/hooks/use-debounce";
import { useEffect, useState } from "react";
import { SubscriptionStatus } from "@prisma/client";

export function UsersTableFilters() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [search, setSearch] = useState(searchParams.get("search") || "");
  const debouncedSearch = useDebounce(search, 500);

  const createQueryString = (params: Record<string, string | null>) => {
    const newSearchParams = new URLSearchParams(searchParams.toString());

    Object.entries(params).forEach(([key, value]) => {
      if (value === null) {
        newSearchParams.delete(key);
      } else {
        newSearchParams.set(key, value);
      }
    });

    return newSearchParams.toString();
  };

  useEffect(() => {
    const queryString = createQueryString({
      search: debouncedSearch || null,
      page: "1", // Reset to first page on search
    });
    router.push(`${pathname}?${queryString}`);
  }, [debouncedSearch]);

  return (
    <div className="border-b">
      <div className="flex flex-col sm:flex-row gap-4 p-4">
        <Input
          placeholder="Search users..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="w-full sm:max-w-xs"
        />
        <div className="flex flex-1 gap-4 flex-col sm:flex-row">
          <Select
            defaultValue={searchParams.get("role") || "ALL"}
            onValueChange={(value) => {
              const queryString = createQueryString({
                role: value === "ALL" ? null : value,
                page: "1",
              });
              router.push(`${pathname}?${queryString}`);
            }}
          >
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Filter by role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">All Roles</SelectItem>
              <SelectItem value="USER">User</SelectItem>
              <SelectItem value="ADMIN">Admin</SelectItem>
            </SelectContent>
          </Select>
          <Select
            defaultValue={searchParams.get("subscriptionStatus") || "ALL"}
            onValueChange={(value) => {
              const queryString = createQueryString({
                subscriptionStatus: value === "ALL" ? null : value,
                page: "1",
              });
              router.push(`${pathname}?${queryString}`);
            }}
          >
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Subscription status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">All Statuses</SelectItem>
              <SelectItem value={SubscriptionStatus.ACTIVE}>Active</SelectItem>
              <SelectItem value={SubscriptionStatus.PAST_DUE}>
                Past Due
              </SelectItem>
              <SelectItem value={SubscriptionStatus.CANCELED}>
                Canceled
              </SelectItem>
              <SelectItem value={SubscriptionStatus.EXPIRED}>
                Expired
              </SelectItem>
              <SelectItem value={SubscriptionStatus.PAUSED}>Paused</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
}
