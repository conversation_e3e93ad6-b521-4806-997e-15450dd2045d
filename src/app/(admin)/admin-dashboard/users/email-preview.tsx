"use client";

import { render } from "@react-email/render";
import { useMemo, useState, useEffect } from "react";
import {
  EmailTemplate,
  CommonEmailProps,
  DEFAULT_EMAIL_CONFIG,
} from "@/lib/email/types";
import { getEmailComponent } from "@/lib/email/get-email-component";

type EmailPreviewProps = {
  template: EmailTemplate;
  subject: string;
  name?: string;
  message?: string;
  actionUrl?: string;
  actionText?: string;
  discountCode?: string;
  expiryDate?: Date;
  fromEmail?: string;
  fromName?: string;
  // Add new props for incident email
  creditAmount?: number;
  incidentTitle?: string;
  incidentMessage?: string;
  discountPercentage?: number;
  customFooterMessage?: string;
};

export function EmailPreview({
  template,
  subject,
  message,
  name = "Designer",
  actionUrl = DEFAULT_EMAIL_CONFIG.defaultActionUrl,
  actionText = DEFAULT_EMAIL_CONFIG.defaultActionText,
  discountCode = DEFAULT_EMAIL_CONFIG.defaultDiscountCode,
  expiryDate = new Date(
    Date.now() +
      (DEFAULT_EMAIL_CONFIG.defaultExpiryDays || 7) * 24 * 60 * 60 * 1000
  ),
  fromEmail = DEFAULT_EMAIL_CONFIG.fromEmail,
  fromName = DEFAULT_EMAIL_CONFIG.fromName,
  // Add new props with defaults
  creditAmount = 10,
  incidentTitle = "",
  incidentMessage = "",
  discountPercentage = 20,
  customFooterMessage = "",
}: EmailPreviewProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [renderedHtml, setRenderedHtml] = useState<string | null>(null);

  const emailPromise = useMemo(() => {
    try {
      const EmailComponent = getEmailComponent(template, {
        template,
        name,
        subject,
        message,
        actionUrl,
        actionText,
        discountCode,
        expiryDate,
        // Add new props to the email component
        creditAmount,
        incidentTitle,
        incidentMessage,
        discountPercentage,
        customFooterMessage,
      } as CommonEmailProps);

      return render(EmailComponent);
    } catch (err) {
      setError(
        err instanceof Error
          ? err
          : new Error("Failed to generate email preview")
      );
      return null;
    }
  }, [
    template,
    subject,
    message,
    name,
    actionUrl,
    actionText,
    discountCode,
    expiryDate,
    // Add new dependencies
    creditAmount,
    incidentTitle,
    incidentMessage,
    discountPercentage,
    customFooterMessage,
  ]);

  useEffect(() => {
    if (emailPromise) {
      emailPromise
        .then((html) => {
          setRenderedHtml(html);
        })
        .catch((err) => {
          setError(
            err instanceof Error
              ? err
              : new Error("Failed to render email preview")
          );
        });
    }
  }, [emailPromise]);

  if (error) {
    return (
      <div className="text-red-500 p-4 border border-red-200 rounded bg-red-50">
        <h4 className="font-medium">Error Previewing Email</h4>
        <p className="text-sm">{error.message}</p>
      </div>
    );
  }

  return (
    <div className="prose prose-sm max-w-none">
      <h3>Email Preview</h3>
      <div className="text-sm text-muted-foreground mb-2">
        This is how your email will appear to recipients.
      </div>
      <div className="bg-white p-4 rounded border">
        <div className="font-medium mb-2">
          From: {fromName}{" "}
          <span className="text-muted-foreground">
            &lt;{fromEmail}@renovaitor.com&gt;
          </span>
        </div>
        <div className="font-medium mb-2">Subject: {subject}</div>
        {template === "incident" && (
          <div className="text-sm text-muted-foreground mb-4 p-2 bg-gray-50 rounded">
            <div className="grid grid-cols-2 gap-2">
              <div><span className="font-medium">Credit Amount:</span> {creditAmount}</div>
              <div><span className="font-medium">Discount Code:</span> {discountCode}</div>
              <div><span className="font-medium">Discount:</span> {discountPercentage}%</div>
              <div><span className="font-medium">Expires:</span> {expiryDate.toLocaleDateString()}</div>
            </div>
          </div>
        )}
        {isLoading && (
          <div className="w-full h-[400px] flex items-center justify-center bg-gray-50">
            Loading preview...
          </div>
        )}
        {renderedHtml && (
          <iframe
            srcDoc={renderedHtml}
            className={`w-full min-h-[450px] border-0 bg-white transition-opacity duration-200 ${
              isLoading ? "opacity-0" : "opacity-100"
            }`}
            title="Email Preview"
            onLoad={() => setIsLoading(false)}
          />
        )}
      </div>
    </div>
  );
}
