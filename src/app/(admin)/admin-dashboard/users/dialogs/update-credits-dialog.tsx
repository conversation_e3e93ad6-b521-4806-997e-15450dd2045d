import { useState, useEffect } from "react";
import { AdminUser } from "@/modules/admin-dashboard/types";
import { Button } from "@/modules/ui/button";
import { Input } from "@/modules/ui/input";
import { Label } from "@/modules/ui/label";
import { Coins } from "lucide-react";
import { toast } from "sonner";
import { updateUserCredits } from "@/modules/admin-dashboard/actions";
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/modules/ui/dialog";

interface UpdateCreditsDialogProps {
  user: AdminUser;
  onOpenChange: (open: boolean) => void;
}

export function UpdateCreditsDialog({
  user,
  onOpenChange,
}: UpdateCreditsDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [credits, setCredits] = useState<number>(user.imageCredits);

  useEffect(() => {
    setCredits(user.imageCredits);
  }, [user.imageCredits]);

  const handleUpdateCredits = async () => {
    try {
      setIsLoading(true);
      const updatedUser = await updateUserCredits(user.id, credits);
      toast.success(
        `Credits updated successfully to ${updatedUser.imageCredits}`
      );
      window.location.reload();
      onOpenChange(false);
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "Failed to update credits"
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <DialogContent>
      <DialogHeader>
        <DialogTitle>Update User Credits</DialogTitle>
        <DialogDescription>
          Update credits for {user.name || user.email}
        </DialogDescription>
      </DialogHeader>

      <div className="grid gap-4 py-4">
        <div className="grid gap-2">
          <Label htmlFor="credits">Credits</Label>
          <Input
            id="credits"
            type="number"
            value={credits}
            onChange={(e) => {
              const value = parseInt(e.target.value);
              setCredits(isNaN(value) ? 0 : value);
            }}
            min={0}
          />
          <p className="text-sm text-muted-foreground">
            Current credits: {user.imageCredits}
          </p>
        </div>
      </div>

      <DialogFooter>
        <Button
          variant="outline"
          onClick={() => onOpenChange(false)}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button
          onClick={handleUpdateCredits}
          disabled={isLoading || credits < 0 || credits === user.imageCredits}
        >
          {isLoading ? (
            <span className="flex items-center gap-2">
              <Coins className="h-4 w-4 animate-spin" />
              Updating...
            </span>
          ) : (
            <span className="flex items-center gap-2">
              <Coins className="h-4 w-4" />
              Update Credits
            </span>
          )}
        </Button>
      </DialogFooter>
    </DialogContent>
  );
}
