import { useState } from "react";
import { AdminUser } from "@/modules/admin-dashboard/types";
import { EmailDialog } from "../email-dialog";
import { sendEmailToUser } from "../../actions/email-actions";
import { toast } from "sonner";
import { EmailTemplate } from "@/lib/email/types";

interface SingleUserEmailDialogProps {
  user: AdminUser;
  onOpenChange: (open: boolean) => void;
}

export function SingleUserEmailDialog({
  user,
  onOpenChange,
}: SingleUserEmailDialogProps) {
  // Initialize state with default values
  const [creditAmount, setCreditAmount] = useState<number>(10);
  const [incidentTitle, setIncidentTitle] = useState<string>("");
  const [incidentMessage, setIncidentMessage] = useState<string>("");
  const [discountPercentage, setDiscountPercentage] = useState<number>(20);
  const [customFooterMessage, setCustomFooterMessage] = useState<string>("");

  const handleSendEmail = async (data: {
    template: EmailTemplate;
    subject: string;
    message?: string;
    discountCode?: string;
    expiryDate?: Date;
    fromEmail?: string;
    fromName?: string;
    creditAmount?: number;
    incidentTitle?: string;
    incidentMessage?: string;
    discountPercentage?: number;
    customFooterMessage?: string;
  }) => {
    try {
      await sendEmailToUser(
        user.id,
        data.subject,
        data.message,
        data.discountCode,
        data.expiryDate,
        data.template,
        {
          fromEmail: data.fromEmail,
          fromName: data.fromName,
          creditAmount: data.creditAmount || creditAmount,
          incidentTitle: data.incidentTitle || incidentTitle,
          incidentMessage: data.incidentMessage || incidentMessage,
          discountPercentage: data.discountPercentage || discountPercentage,
          customFooterMessage: data.customFooterMessage || customFooterMessage,
        }
      );
      toast.success("Email sent successfully");
      onOpenChange(false);
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "Failed to send email"
      );
    }
  };

  return (
    <EmailDialog
      onSend={handleSendEmail}
      onClose={() => onOpenChange(false)}
      title="Send Email to User"
      description={`Send a personalized email to ${user.name || user.email}`}
      creditAmount={creditAmount}
      incidentTitle={incidentTitle}
      incidentMessage={incidentMessage}
      discountPercentage={discountPercentage}
      customFooterMessage={customFooterMessage}
      onCreditAmountChange={setCreditAmount}
      onIncidentTitleChange={setIncidentTitle}
      onIncidentMessageChange={setIncidentMessage}
      onDiscountPercentageChange={setDiscountPercentage}
      onCustomFooterMessageChange={setCustomFooterMessage}
    />
  );
}
