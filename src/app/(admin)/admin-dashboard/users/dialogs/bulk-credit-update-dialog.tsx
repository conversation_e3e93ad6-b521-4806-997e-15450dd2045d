import { useState } from "react";
import { But<PERSON> } from "@/modules/ui/button";
import { Input } from "@/modules/ui/input";
import { Label } from "@/modules/ui/label";
import { Coins } from "lucide-react";
import { toast } from "sonner";
import { updateBulkUserCredits } from "@/modules/admin-dashboard/actions";
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/modules/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/modules/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/modules/ui/tabs";
import type { EmailFiltersState } from "@/lib/email/types";

interface BulkCreditUpdateDialogProps {
  onOpenChange: (open: boolean) => void;
}

export function BulkCreditUpdateDialog({
  onOpenChange,
}: BulkCreditUpdateDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [operation, setOperation] = useState<"set" | "add" | "subtract">("add");
  const [amount, setAmount] = useState(10);
  const [filters, setFilters] = useState<EmailFiltersState>({
    roles: ["USER"],
    subscriptionStatus: [],
    lastActiveWithin: undefined,
    minCredits: undefined,
    maxCredits: undefined,
    minGenerations: undefined,
    maxGenerations: undefined,
    hasSubscription: undefined,
  });

  const handleUpdateCredits = async () => {
    try {
      setIsLoading(true);
      const result = await updateBulkUserCredits(filters, operation, amount);
      toast.success(
        `Successfully updated credits for ${result.updatedUsers} out of ${result.matchedUsers} users`
      );
      onOpenChange(false);
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "Failed to update credits"
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <DialogContent className="sm:max-w-[700px]">
      <DialogHeader>
        <DialogTitle>Bulk Update Credits</DialogTitle>
        <DialogDescription>
          Update credits for multiple users based on filters
        </DialogDescription>
      </DialogHeader>

      <Tabs defaultValue="update" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="filters">Filters</TabsTrigger>
          <TabsTrigger value="update">Update</TabsTrigger>
        </TabsList>

        <TabsContent value="update">
          <div className="grid gap-4">
            <div className="grid gap-2">
              <Label>Operation</Label>
              <Select
                value={operation}
                onValueChange={(v: "set" | "add" | "subtract") =>
                  setOperation(v)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="add">Add Credits</SelectItem>
                  <SelectItem value="subtract">Subtract Credits</SelectItem>
                  <SelectItem value="set">Set Credits</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label>Amount</Label>
              <Input
                type="number"
                value={amount}
                onChange={(e) => setAmount(Number(e.target.value))}
                min={0}
              />
            </div>
          </div>
        </TabsContent>
      </Tabs>

      <DialogFooter>
        <Button
          variant="outline"
          onClick={() => onOpenChange(false)}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button
          onClick={handleUpdateCredits}
          disabled={isLoading || amount < 0}
        >
          {isLoading ? (
            <span className="flex items-center gap-2">
              <Coins className="h-4 w-4 animate-spin" />
              Updating...
            </span>
          ) : (
            <span className="flex items-center gap-2">
              <Coins className="h-4 w-4" />
              Update Credits
            </span>
          )}
        </Button>
      </DialogFooter>
    </DialogContent>
  );
}
