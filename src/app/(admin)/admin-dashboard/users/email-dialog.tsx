import { useState } from "react";
import { toast } from "sonner";
import { But<PERSON> } from "@/modules/ui/button";
import { Label } from "@/modules/ui/label";
import { Input } from "@/modules/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/modules/ui/select";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/modules/ui/tabs";
import { EmailPreview } from "./email-preview";
import { EmailTemplateInputs } from "./email-template-inputs";
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/modules/ui/dialog";
import { ScrollArea } from "@/modules/ui/scroll-area";
import { Send } from "lucide-react";
import { EMAIL_TEMPLATES } from "@/modules/admin-dashboard/components/email-templates";
import type { EmailTemplate } from "@/lib/email/types";

interface EmailDialogProps {
  onSend: (data: {
    template: EmailTemplate;
    subject: string;
    message?: string;
    discountCode?: string;
    expiryDate?: Date;
    fromEmail?: string;
    fromName?: string;
    // Add new props
    creditAmount?: number;
    incidentTitle?: string;
    incidentMessage?: string;
    discountPercentage?: number;
    customFooterMessage?: string;
  }) => Promise<void>;
  onClose: () => void;
  title: string;
  description: string;
  recipientCount?: number;
  // Add new props
  creditAmount?: number;
  incidentTitle?: string;
  incidentMessage?: string;
  discountPercentage?: number;
  customFooterMessage?: string;
  onCreditAmountChange: (value: number) => void;
  onIncidentTitleChange: (value: string) => void;
  onIncidentMessageChange: (value: string) => void;
  onDiscountPercentageChange: (value: number) => void;
  onCustomFooterMessageChange: (value: string) => void;
}

export function EmailDialog({
  onSend,
  onClose,
  title,
  description,
  recipientCount,
  creditAmount = 10,
  incidentTitle = "",
  incidentMessage = "",
  discountPercentage = 20,
  customFooterMessage = "",
  onCreditAmountChange,
  onIncidentTitleChange,
  onIncidentMessageChange,
  onDiscountPercentageChange,
  onCustomFooterMessageChange,
}: EmailDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [template, setTemplate] = useState<EmailTemplate>("announcement");
  const [subject, setSubject] = useState(EMAIL_TEMPLATES[0].subject);
  const [message, setMessage] = useState("");
  const [discountCode, setDiscountCode] = useState("SORRY50");
  const [expiryDate, setExpiryDate] = useState(
    new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
  );
  const [fromEmail, setFromEmail] = useState("ugurcan");
  const [fromName, setFromName] = useState("Ugurcan | Renovaitor");

  const handleSend = async () => {
    if (!subject.trim()) {
      toast.error("Email subject cannot be empty");
      return;
    }

    if (template === "custom" && !message.trim()) {
      toast.error("Custom message cannot be empty");
      return;
    }

    try {
      setIsLoading(true);
      await onSend({
        template,
        subject,
        message,
        discountCode,
        expiryDate,
        fromEmail,
        fromName,
        // Add new fields
        creditAmount,
        incidentTitle,
        incidentMessage,
        discountPercentage,
        customFooterMessage,
      });
      onClose();
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "Failed to send email"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleTemplateChange = (newTemplate: EmailTemplate) => {
    setTemplate(newTemplate);
    const templateInfo = EMAIL_TEMPLATES.find((t) => t.id === newTemplate);
    if (templateInfo) {
      setSubject(templateInfo.subject);
    }
  };

  const selectedTemplateInfo = EMAIL_TEMPLATES.find((t) => t.id === template);

  return (
    <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-hidden">
      <DialogHeader>
        <DialogTitle>{title}</DialogTitle>
        <DialogDescription>{description}</DialogDescription>
      </DialogHeader>

      <Tabs defaultValue="template" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="template">Template</TabsTrigger>
          <TabsTrigger value="preview">Preview</TabsTrigger>
        </TabsList>

        <TabsContent value="template">
          <ScrollArea className="h-[60vh] pr-4">
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label>Email Template</Label>
                <Select value={template} onValueChange={handleTemplateChange}>
                  <SelectTrigger>
                    <SelectValue>{selectedTemplateInfo?.name}</SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {EMAIL_TEMPLATES.map((t) => (
                      <SelectItem key={t.id} value={t.id}>
                        <div className="flex flex-col">
                          <span className="font-medium">{t.name}</span>
                          <span className="text-xs text-muted-foreground">
                            {t.description}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label>Subject</Label>
                <Input
                  value={subject}
                  onChange={(e) => setSubject(e.target.value)}
                  placeholder="Enter email subject"
                />
              </div>

              <EmailTemplateInputs
                template={template}
                discountCode={discountCode}
                expiryDate={expiryDate}
                message={message}
                onDiscountCodeChange={setDiscountCode}
                onExpiryDateChange={setExpiryDate}
                onMessageChange={setMessage}
                fromEmail={fromEmail}
                fromName={fromName}
                onFromEmailChange={setFromEmail}
                onFromNameChange={setFromName}
                // Add new props
                creditAmount={creditAmount}
                incidentTitle={incidentTitle}
                incidentMessage={incidentMessage}
                discountPercentage={discountPercentage}
                customFooterMessage={customFooterMessage}
                onCreditAmountChange={onCreditAmountChange}
                onIncidentTitleChange={onIncidentTitleChange}
                onIncidentMessageChange={onIncidentMessageChange}
                onDiscountPercentageChange={onDiscountPercentageChange}
                onCustomFooterMessageChange={onCustomFooterMessageChange}
              />

              {recipientCount && (
                <div className="bg-muted p-3 rounded-md">
                  <p className="text-sm">
                    This email will be sent to {recipientCount} recipient
                    {recipientCount !== 1 ? "s" : ""}
                  </p>
                </div>
              )}
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="preview">
          <ScrollArea className="h-[60vh] pr-4">
            <EmailPreview
              template={template}
              subject={subject}
              message={message}
              discountCode={discountCode}
              expiryDate={expiryDate}
              // Add new props for preview
              creditAmount={creditAmount}
              incidentTitle={incidentTitle}
              incidentMessage={incidentMessage}
              discountPercentage={discountPercentage}
              customFooterMessage={customFooterMessage}
            />
          </ScrollArea>
        </TabsContent>
      </Tabs>

      <DialogFooter>
        <Button variant="outline" onClick={onClose} disabled={isLoading}>
          Cancel
        </Button>
        <Button
          onClick={handleSend}
          disabled={
            isLoading ||
            !subject.trim() ||
            (template === "custom" && !message.trim())
          }
        >
          {isLoading ? (
            <span className="flex items-center gap-2">
              <Send className="h-4 w-4 animate-spin" />
              Sending...
            </span>
          ) : (
            <span className="flex items-center gap-2">
              <Send className="h-4 w-4" />
              Send Email
            </span>
          )}
        </Button>
      </DialogFooter>
    </DialogContent>
  );
}
