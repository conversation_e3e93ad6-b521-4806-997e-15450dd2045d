"use client";
import { ColumnDef } from "@tanstack/react-table";
import { AdminUser } from "@/modules/admin-dashboard/types";
import { userColumn } from "./columns/user-column";
import { subscriptionColumn } from "./columns/subscription-column";
import { statusColumn } from "./columns/status-column";
import { roleColumn } from "./columns/role-column";
import { sourceColumn } from "./columns/source-column";
import { industryColumn } from "./columns/industry-column";
import { creditsColumn } from "./columns/credits-column";
import { generationsColumn } from "./columns/generations-column";
import { lastActiveColumn } from "./columns/last-active-column";
import { actionsColumn } from "./columns/actions-column";

export const columns: ColumnDef<AdminUser>[] = [
  userColumn,
  roleColumn,
  sourceColumn,
  industryColumn,
  subscriptionColumn,
  statusColumn,
  creditsColumn,
  generationsColumn,
  lastActiveColumn,
  actionsColumn,
];
