// src/app/(admin)/admin-dashboard/users/email-template-inputs.tsx
import { Input } from "@/modules/ui/input";
import { Label } from "@/modules/ui/label";
import { EmailTemplate } from "@/lib/email/types";
interface TemplateInputsProps {
  template: EmailTemplate;
  discountCode: string;
  expiryDate: Date;
  message?: string;
  fromEmail: string;
  fromName: string;
  // Add new props
  creditAmount?: number;
  incidentTitle?: string;
  incidentMessage?: string;
  discountPercentage?: number;
  customFooterMessage?: string;
  // Add new handlers
  onCreditAmountChange: (value: number) => void;
  onIncidentTitleChange: (value: string) => void;
  onIncidentMessageChange: (value: string) => void;
  onDiscountPercentageChange: (value: number) => void;
  onCustomFooterMessageChange: (value: string) => void;
  onDiscountCodeChange: (value: string) => void;
  onExpiryDateChange: (value: Date) => void;
  onMessageChange: (value: string) => void;
  onFromEmailChange: (value: string) => void;
  onFromNameChange: (value: string) => void;
}

export function EmailTemplateInputs({
  template,
  discountCode,
  expiryDate,
  message,
  fromEmail,
  fromName,
  onDiscountCodeChange,
  onExpiryDateChange,
  onMessageChange,
  onFromEmailChange,
  onFromNameChange,
  creditAmount = 10,
  incidentTitle,
  incidentMessage,
  discountPercentage = 20,
  customFooterMessage,
  onCreditAmountChange,
  onIncidentTitleChange,
  onIncidentMessageChange,
  onDiscountPercentageChange,
  onCustomFooterMessageChange,
}: TemplateInputsProps) {
  switch (template) {
    case "incident":
      return (
        <div className="grid gap-4">
          <div className="grid gap-4">
            <div>
              <Label>From Name</Label>
              <Input
                value={fromName}
                onChange={(e) => onFromNameChange(e.target.value)}
                placeholder="e.g., Renovaitor Support"
              />
            </div>
            <div>
              <Label>From Email</Label>
              <div className="flex items-center gap-2">
                <Input
                  value={fromEmail}
                  onChange={(e) => onFromEmailChange(e.target.value)}
                  placeholder="e.g., support"
                />
                <span className="text-muted-foreground">@renovaitor.com</span>
              </div>
            </div>
            <div>
              <Label>Incident Title</Label>
              <Input
                value={incidentTitle}
                onChange={(e) => onIncidentTitleChange(e.target.value)}
                placeholder="e.g., We Apologize for the Inconvenience"
              />
            </div>
            <div>
              <Label>Incident Message</Label>
              <textarea
                value={incidentMessage}
                onChange={(e) => onIncidentMessageChange(e.target.value)}
                placeholder="Describe the incident and your response"
                className="min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
              />
            </div>
            <div>
              <Label>Credit Amount</Label>
              <Input
                type="number"
                value={creditAmount}
                onChange={(e) => onCreditAmountChange(Number(e.target.value))}
                min={1}
              />
            </div>
            <div>
              <Label>Discount Percentage</Label>
              <Input
                type="number"
                value={discountPercentage}
                onChange={(e) => onDiscountPercentageChange(Number(e.target.value))}
                min={1}
                max={100}
              />
            </div>
            <div>
              <Label>Discount Code</Label>
              <Input
                value={discountCode}
                onChange={(e) => onDiscountCodeChange(e.target.value)}
                placeholder="e.g., SORRY50"
              />
            </div>
            <div>
              <Label>Expiry Date</Label>
              <Input
                type="date"
                value={expiryDate.toISOString().split('T')[0]}
                onChange={(e) => onExpiryDateChange(new Date(e.target.value))}
              />
            </div>
            <div>
              <Label>Custom Footer Message</Label>
              <Input
                value={customFooterMessage}
                onChange={(e) => onCustomFooterMessageChange(e.target.value)}
                placeholder="Add a custom message to the footer"
              />
            </div>
          </div>
        </div>
      );
    case "feedback":
      return (
        <div className="grid gap-4">
          <div className="grid gap-4">
            <div>
              <Label>From Name</Label>
              <Input
                value={fromName}
                onChange={(e) => onFromNameChange(e.target.value)}
                placeholder="e.g., Renovaitor Support"
              />
            </div>
            <div>
              <Label>From Email</Label>
              <div className="flex items-center gap-2">
                <Input
                  value={fromEmail}
                  onChange={(e) => onFromEmailChange(e.target.value)}
                  placeholder="e.g., support"
                />
                <span className="text-muted-foreground">@renovaitor.com</span>
              </div>
            </div>
          </div>

          <div>
            <Label>Survey URL</Label>
            <Input
              value={message}
              onChange={(e) => onMessageChange(e.target.value)}
              placeholder="Enter survey URL"
            />
          </div>
        </div>
      );
    case "inactivity-feedback":
      return (
        <div className="grid gap-4">
          <div className="grid gap-4">
            <div>
              <Label>From Name</Label>
              <Input
                value={fromName}
                onChange={(e) => onFromNameChange(e.target.value)}
                placeholder="e.g., Renovaitor Support"
              />
            </div>
            <div>
              <Label>From Email</Label>
              <div className="flex items-center gap-2">
                <Input
                  value={fromEmail}
                  onChange={(e) => onFromEmailChange(e.target.value)}
                  placeholder="e.g., support"
                />
                <span className="text-muted-foreground">@renovaitor.com</span>
              </div>
            </div>
          </div>
          <div>
            <Label>Survey URL</Label>
            <Input
              value={message}
              onChange={(e) => onMessageChange(e.target.value)}
              placeholder="Enter survey URL"
            />
          </div>
        </div>
      );
    case "custom":
    case "announcement":
    case "notification":
    case "marketing":
      return (
        <div>
          <Label>Message</Label>
          <textarea
            value={message}
            onChange={(e) => onMessageChange(e.target.value)}
            placeholder="Enter your message"
            className="min-h-[100px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          />
        </div>
      );
    default:
      return null;
  }
}
