import { currentUser } from "@/modules/auth/actions/user-actions";
import { AdminHeader } from "@/modules/admin-dashboard/components/admin-header";
import { redirect } from "next/navigation";
import { ScrollArea } from "@/modules/ui/scroll-area";

export default async function AdminDashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const user = await currentUser();

  if (!user || user.role !== "ADMIN") {
    redirect("/");
  }

  return (
    <div className="min-h-screen bg-background">
      <AdminHeader
        user={{
          name: user.name ?? null,
          email: user.email ?? null,
          image: user.image ?? null,
        }}
      />
      <ScrollArea className="h-screen py-4 sm:py-20">{children}</ScrollArea>
    </div>
  );
}
