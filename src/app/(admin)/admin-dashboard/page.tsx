import { redirect } from "next/navigation";
import { currentUser } from "@/modules/auth/actions/user-actions";
import { getDashboardStats } from "@/modules/admin-dashboard/actions";
import { DashboardStats } from "@/modules/admin-dashboard/components/dashboard-stats";
import { SubscriptionsTable } from "@/modules/admin-dashboard/components/subscription-table";
import { RecentActivities } from "@/modules/admin-dashboard/components/recent-activities";

export default async function AdminDashboard() {
  const user = await currentUser();

  if (!user || user.role !== "ADMIN") {
    redirect("/");
  }

  const stats = await getDashboardStats();

  return (
    <div className="h-full bg-background">
      <main className="container mx-auto py-6 space-y-8">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-foreground tracking-tight">
            Admin Dashboard
          </h1>
          <div className="flex space-x-4">
            {/* Add quick action buttons here */}
          </div>
        </div>

        <DashboardStats stats={stats} />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <div className="rounded-lg border border-border bg-card text-card-foreground shadow-sm">
              <div className="p-6">
                <h2 className="text-2xl font-semibold text-foreground tracking-tight">
                  Active Subscriptions
                </h2>
              </div>
              <SubscriptionsTable />
            </div>
          </div>
          <div className="rounded-lg border border-border bg-card text-card-foreground shadow-sm p-6">
            <RecentActivities />
          </div>
        </div>
      </main>
    </div>
  );
}
