"use server";

import { sendEmail } from "@/lib/email/send-email";
import prisma from "@/lib/prisma/prisma";
import { revalidatePath } from "next/cache";
import CustomEmail from "@/lib/auth/email-templates/custom-email";
import NotificationEmail from "@/lib/auth/email-templates/notification-email";
import AnnouncementEmail from "@/lib/auth/email-templates/announcement-email";
import MarketingEmail from "@/lib/auth/email-templates/marketing-email";
import WelcomeEmail from "@/lib/auth/email-templates/welcome-email";
import IncidentEmail from "@/lib/auth/email-templates/incident-email";
import { currentUser } from "@/modules/auth/actions/user-actions";
import { Role, SubscriptionStatus } from "@prisma/client";
import FeedbackSurveyEmail from "@/lib/auth/email-templates/feedback-survey-email";
import type { EmailTemplate } from "@/modules/admin-dashboard/types";

const getEmailComponent = (template: EmailTemplate) => {
  switch (template) {
    case "announcement":
      return AnnouncementEmail;
    case "notification":
      return NotificationEmail;
    case "marketing":
      return MarketingEmail;
    case "welcome":
      return WelcomeEmail;
    case "incident":
      return IncidentEmail;
    case "feedback":
      return FeedbackSurveyEmail;
    case "custom":
    default:
      return CustomEmail;
  }
};

interface BulkEmailParams {
  userIds: string[];
  subject: string;
  template: EmailTemplate;
  message?: string;
  discountCode?: string;
  expiryDate?: Date;
}

interface BulkEmailFilters {
  roles?: Role[];
  subscriptionStatus?: SubscriptionStatus[];
  lastActiveWithin?: number;
  minCredits?: number;
  maxCredits?: number;
  minGenerations?: number;
  maxGenerations?: number;
  hasSubscription?: boolean;
}

const validateEmailParams = (params: BulkEmailParams) => {
  if (!params.userIds.length) {
    throw new Error("No users selected");
  }
  if (!params.subject.trim()) {
    throw new Error("Email subject is required");
  }
  if (
    ![
      "announcement",
      "notification",
      "marketing",
      "welcome",
      "incident",
      "custom",
      "feedback",
    ].includes(params.template)
  ) {
    throw new Error("Invalid template selected");
  }
  if (params.template === "custom" && !params.message?.trim()) {
    throw new Error("Custom message is required for custom template");
  }
};

export async function sendEmailToUser(
  userId: string,
  subject: string,
  message?: string,
  discountCode?: string,
  expiryDate?: Date,
  template: EmailTemplate = "custom",
  {
    fromEmail,
    fromName,
    creditAmount,
    incidentTitle,
    incidentMessage,
    discountPercentage,
    customFooterMessage,
    actionUrl,
    actionText,
  }: {
    fromEmail?: string;
    fromName?: string;
    creditAmount?: number;
    incidentTitle?: string;
    incidentMessage?: string;
    discountPercentage?: number;
    customFooterMessage?: string;
    actionUrl?: string;
    actionText?: string;
  } = {}
) {
  try {
    const admin = await currentUser();
    if (!admin || admin.role !== "ADMIN") {
      throw new Error("Unauthorized access");
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { email: true, name: true },
    });

    if (!user || !user.email) {
      throw new Error("User not found or has no email");
    }

    if (template === "custom" && !message?.trim()) {
      throw new Error("Custom message is required for custom template");
    }

    const baseProps = {
      name: user.name || "Designer",
      customFooterMessage
    };
    const templateProps = (() => {
      switch (template) {
        case "custom":
          return { ...baseProps, message };
        case "marketing":
          return {
            ...baseProps,
            offerTitle: subject,
            offerDescription: message,
            discountPercentage
          };
        case "announcement":
          return {
            ...baseProps,
            title: subject,
            message,
            actionUrl,
            actionText
          };
        case "incident":
          return {
            ...baseProps,
            discountCode,
            expiryDate: expiryDate?.toLocaleDateString("en-US", {
              year: "numeric",
              month: "long",
              day: "numeric",
            }),
            creditAmount: creditAmount || 10,
            incidentTitle: incidentTitle || subject,
            incidentMessage: incidentMessage || message,
            discountPercentage: discountPercentage || 20,
          };
        case "feedback":
          return {
            ...baseProps,
            message,
            discountCode,
            title: subject
          };
        case "inactivity-feedback":
          return {
            ...baseProps,
            title: subject,
            message,
            discountCode,
            discountPercentage,
            expiryDate: expiryDate?.toLocaleDateString("en-US", {
              year: "numeric",
              month: "long",
              day: "numeric",
            }),
            tryNowUrl: actionUrl || "https://renovaitor.com/dashboard",
          };
        default:
          return baseProps;
      }
    })();

    await sendEmail({
      to: user.email,
      subject: subject || "Message from Renovaitor",
      template,
      props: templateProps,
      config: fromEmail ? { fromEmail, fromName } : undefined,
    });

    revalidatePath("/admin-dashboard/users");
    return { success: true };
  } catch (error) {
    console.error("Error sending email to user:", error);
    throw error;
  }
}

export async function sendBulkEmail(params: BulkEmailParams) {
  try {
    const admin = await currentUser();
    if (!admin || admin.role !== "ADMIN") {
      throw new Error("Unauthorized access");
    }

    validateEmailParams(params);

    const users = await prisma.user.findMany({
      where: { id: { in: params.userIds } },
      select: { id: true, email: true, name: true },
      take: 100,
    });

    const validUsers = users.filter((user) => user.email);
    if (!validUsers.length) {
      throw new Error("No valid recipients found");
    }

    const batchSize = 20;
    const delay = 2000;
    let successCount = 0;

    for (let i = 0; i < validUsers.length; i += batchSize) {
      const batch = validUsers.slice(i, i + batchSize);

      for (const user of batch) {
        try {
          const baseProps = {
            name: user.name || "Designer",
          };

          const templateProps = (() => {
            switch (params.template) {
              case "custom":
                return { ...baseProps, message: params.message };
              case "marketing":
                return {
                  ...baseProps,
                  offerTitle: params.subject,
                  offerDescription: params.message,
                };
              case "announcement":
                return { ...baseProps, title: params.subject };
              case "incident":
                return {
                  ...baseProps,
                  discountCode: params.discountCode,
                  expiryDate: params.expiryDate?.toLocaleDateString("en-US", {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                  }),
                  creditAmount: 10, // Default value
                  incidentTitle: params.subject,
                  incidentMessage: params.message,
                  discountPercentage: 20, // Default value
                };
              case "feedback":
                return {
                  ...baseProps,
                  message: params.message,
                  discountCode: params.discountCode,
                };
              default:
                return baseProps;
            }
          })();

          await sendEmail({
            to: user.email!,
            subject: params.subject,
            template: params.template,
            props: templateProps,
          });
          successCount++;
        } catch (error) {
          console.error(`Failed to send email to ${user.email}:`, error);
        }
      }

      if (i + batchSize < validUsers.length) {
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }

    revalidatePath("/admin-dashboard/users");
    return {
      success: true,
      emailsSent: successCount,
      totalAttempted: validUsers.length,
    };
  } catch (error) {
    console.error("Error sending bulk emails:", error);
    throw error;
  }
}
