"use server";

import prisma from "@/lib/prisma/prisma";
import { currentUser } from "@/modules/auth/actions/user-actions";
import { revalidatePath } from "next/cache";

export interface UpdateUserCreditsResponse {
  id: string;
  name: string | null;
  email: string | null;
  imageCredits: number;
}

export async function updateUserCredits(
  userId: string,
  credits: number
): Promise<UpdateUserCreditsResponse> {
  try {
    const admin = await currentUser();
    if (!admin || admin.role !== "ADMIN") {
      throw new Error("Unauthorized access");
    }

    const user = await prisma.user.update({
      where: { id: userId },
      data: { imageCredits: credits },
      select: {
        id: true,
        name: true,
        email: true,
        imageCredits: true,
      },
    });

    revalidatePath("/admin-dashboard/users");
    return user;
  } catch (error) {
    console.error("Error updating user credits:", error);
    throw error;
  }
}
