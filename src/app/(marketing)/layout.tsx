import Header from "@/modules/marketing/components/layout/header";
import Footer from "@/modules/marketing/components/layout/footer";
import { ChristmasBanner } from "@/modules/marketing/components/christmas-banner";
import { Metadata } from "next";

export const metadata: Metadata = {
  description:
    "Experience the future of interior design with AI-powered tools. Create photorealistic room designs, virtual staging, and renovation previews in minutes, not days.",
  openGraph: {
    type: "website",
    title: "Design Your Dream Space with AI | Renovaitor",
    description:
      "Transform any room with AI interior design. Virtual staging, instant makeovers, and renovation previews. Start your design journey today.",
    images: [
      {
        url: "https://www.renovaitor.com/marketing-og.png",
        width: 1200,
        height: 630,
        alt: "AI Interior Design Transformations - Before and After",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Revolutionary AI Interior Design Tools | Renovaitor",
    description:
      "Create stunning spaces with AI. Instant room transformations, virtual staging, and renovation previews at your fingertips.",
    images: ["https://www.renovaitor.com/marketing-og.png"],
  },
  keywords: [
    "AI Room Design",
    "Virtual Home Staging",
    "Interior Design AI",
    "Room Makeover App",
    "AI Renovation Preview",
  ],
};

export default function MarketingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="relative flex flex-col min-h-screen">
      <header>
        <Header />
      </header>
      <main className="flex-1 min-h-[calc(100vh-4rem)]">{children}</main>
      <footer>
        <Footer />
      </footer>
    </div>
  );
}
