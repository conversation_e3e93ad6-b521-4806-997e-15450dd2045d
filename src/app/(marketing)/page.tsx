import { Suspense } from "react";
import dynamic from "next/dynamic";
import HeroSkeleton from "@/modules/marketing/components/hero-skeleton";
import FeaturesGrid from "@/modules/marketing/components/features-grid";
import TestimonialsCarousel from "@/modules/marketing/components/testimonials-carousel";
import CTASection from "@/modules/marketing/components/cta-section";
import Faq from "@/modules/marketing/components/faq";
import PricingPlans from "@/modules/payments/components/subscription-pricing-plan";
import { Metadata, Viewport } from "next";

export const metadata: Metadata = {
  title: "Home",
  description:
    "Transform your living spaces with Renovaitor's AI-powered interior design tools.",
  alternates: {
    canonical: "https://renovaitor.com",
  },
  openGraph: {
    title: "Renovaitor - AI-Powered Interior Design",
    description:
      "Transform your living spaces with Renovaitor's AI-powered interior design tools.",
    images: [
      {
        url: "https://renovaitor.com/og-image.png",
        width: 1200,
        height: 630,
        alt: "Renovaitor Home Page",
      },
    ],
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  minimumScale: 1,
};

// Dynamic imports for better code splitting
const Hero = dynamic(() => import("@/modules/marketing/components/hero"), {
  loading: () => <HeroSkeleton />,
  ssr: true,
});

const FeatureShowcase = dynamic(
  () => import("@/modules/marketing/components/feature-showcase"),
  {
    loading: () => (
      <div
        className="h-96 bg-foreground/5 animate-pulse"
        role="progressbar"
        aria-label="Loading feature showcase"
      />
    ),
    ssr: true,
  }
);

export default function MarketingPage() {
  return (
    <main className="flex flex-col min-h-screen" role="main">
      <Suspense fallback={<HeroSkeleton />}>
        <Hero />
      </Suspense>

      <div className="w-full px-4 md:px-6 lg:px-8 max-w-[100vw] overflow-x-hidden">
        <div className="max-w-7xl mx-auto">
          <div className="space-y-20 py-12">
            <section
              aria-labelledby="feature-showcase-heading"
            >
              <h2 id="feature-showcase-heading" className="sr-only">
                Feature Showcase
              </h2>
              <Suspense
                fallback={
                  <div
                    className="h-96 bg-card/60 dark:bg-card/70 animate-pulse rounded-xl shadow-sm border border-primary/10 dark:border-primary/20 max-w-5xl mx-auto"
                    role="progressbar"
                    aria-label="Loading feature showcase"
                  />
                }
              >
                <FeatureShowcase />
              </Suspense>
            </section>

            <section
              aria-labelledby="features-grid-heading"
            >
              <h2 id="features-grid-heading" className="sr-only">
                Features Grid
              </h2>
              <Suspense
                fallback={
                  <div
                    className="h-96 bg-card/60 dark:bg-card/70 animate-pulse rounded-xl shadow-sm border border-primary/10 dark:border-primary/20 max-w-5xl mx-auto"
                    role="progressbar"
                    aria-label="Loading features grid"
                  />
                }
              >
                <FeaturesGrid />
              </Suspense>
            </section>

            <section
              aria-labelledby="pricing-heading"
            >
              <h2 id="pricing-heading" className="sr-only">
                Pricing Plans
              </h2>
              <Suspense
                fallback={
                  <div
                    className="h-96 bg-card/60 dark:bg-card/70 animate-pulse rounded-xl shadow-sm border border-primary/10 dark:border-primary/20 max-w-5xl mx-auto"
                    role="progressbar"
                    aria-label="Loading pricing plans"
                  />
                }
              >
                <PricingPlans />
              </Suspense>
            </section>

            <section
              aria-labelledby="testimonials-heading"
            >
              <h2 id="testimonials-heading" className="sr-only">
                Customer Testimonials
              </h2>
              <Suspense
                fallback={
                  <div
                    className="h-96 bg-card/60 dark:bg-card/70 animate-pulse rounded-xl shadow-sm border border-primary/10 dark:border-primary/20 max-w-5xl mx-auto"
                    role="progressbar"
                    aria-label="Loading testimonials"
                  />
                }
              >
                <TestimonialsCarousel />
              </Suspense>
            </section>

            <section
              aria-labelledby="cta-heading"
            >
              <h2 id="cta-heading" className="sr-only">
                Call to Action
              </h2>
              <Suspense
                fallback={
                  <div
                    className="h-96 bg-card/60 dark:bg-card/70 animate-pulse rounded-xl shadow-sm border border-primary/10 dark:border-primary/20 max-w-5xl mx-auto"
                    role="progressbar"
                    aria-label="Loading call to action"
                  />
                }
              >
                <CTASection />
              </Suspense>
            </section>

            <section
              aria-labelledby="faq-heading"
            >
              <h2 id="faq-heading" className="sr-only">
                Frequently Asked Questions
              </h2>
              <Suspense
                fallback={
                  <div
                    className="h-96 bg-card/60 dark:bg-card/70 animate-pulse rounded-xl shadow-sm border border-primary/10 dark:border-primary/20 max-w-5xl mx-auto"
                    role="progressbar"
                    aria-label="Loading FAQ section"
                  />
                }
              >
                <Faq />
              </Suspense>
            </section>
          </div>
        </div>
      </div>
    </main>
  );
}
