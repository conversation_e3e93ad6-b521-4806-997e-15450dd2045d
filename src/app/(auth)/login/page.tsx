import React from "react";
import Link from "next/link";
import { redirect } from "next/navigation";
import LoginForm from "@/modules/auth/components/login/login-form";
import { cn } from "@/modules/ui/utils/cn";
import { buttonVariants } from "@/modules/ui/button";
import * as Icons from "@/modules/ui/icons";
import { auth } from "@/lib/auth/auth";
import { Logo } from "@/modules/marketing/components/layout/header/logo";

async function Login() {
  const session = await auth();

  if (session) {
    redirect("/");
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-grid-black/[0.02]" />
      <div className="absolute inset-0 bg-gradient-to-br from-background/95 to-background" />

      {/* Animated gradient orbs - adjusted for mobile */}
      <div className="absolute -top-20 -left-20 w-72 sm:w-96 h-72 sm:h-96 bg-primary/10 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob" />
      <div className="absolute -top-20 -right-20 w-72 sm:w-96 h-72 sm:h-96 bg-accent/20 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-2000" />
      <div className="absolute -bottom-20 left-20 w-72 sm:w-96 h-72 sm:h-96 bg-secondary/20 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-4000" />

      <div className="relative flex min-h-screen items-center justify-center p-4">
        <Link
          href={`/`}
          className={cn(
            buttonVariants({
              variant: "ghost",
              size: "default",
              className:
                "absolute left-2 top-2 md:left-8 md:top-8 text-muted-foreground hover:text-foreground",
            })
          )}
        >
          <span className="flex items-center gap-2">
            <Icons.ChevronLeft className="h-4 w-4" />
            <span className="hidden sm:inline">Return to Home</span>
          </span>
        </Link>

        <div className="w-full max-w-[380px] sm:max-w-md relative">
          <div className="relative rounded-2xl overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-primary/10 via-secondary/10 to-primary/10 rounded-2xl blur opacity-50" />

            <div
              className="relative bg-background/80 backdrop-blur-xl p-4 sm:p-8 
                          border border-primary/10 rounded-xl
                          shadow-2xl shadow-primary/5"
            >
              <div className="flex flex-col space-y-4 sm:space-y-6 text-center items-center">
                <div className="transform hover:scale-105 transition-transform duration-300">
                  <Logo />
                </div>
                <div className="space-y-2">
                  <h1
                    className="text-2xl sm:text-3xl font-bold tracking-tight bg-clip-text text-transparent 
                               bg-gradient-to-br from-foreground to-foreground/70"
                  >
                    Welcome to Renovaitor
                  </h1>
                  <p className="text-sm sm:text-base text-muted-foreground max-w-sm mx-auto">
                    Transform your spaces with AI-powered design. Sign in to
                    access your personalized dashboard.
                  </p>
                </div>
              </div>

              <LoginForm />

              <p className="mt-4 sm:mt-6 text-xs sm:text-sm text-center text-muted-foreground">
                By signing in, you agree to our{" "}
                <Link
                  href="/terms"
                  className="text-primary hover:text-primary/80 transition-colors"
                >
                  Terms of Service
                </Link>{" "}
                and{" "}
                <Link
                  href="/privacy"
                  className="text-primary hover:text-primary/80 transition-colors"
                >
                  Privacy Policy
                </Link>
              </p>
            </div>
          </div>

          {/* Spot gradients */}
          <div
            className="absolute -z-10 blur-[100px] opacity-20 bg-[radial-gradient(circle_at_center,_var(--tw-gradient-stops))] 
                        from-primary/40 via-primary/10 to-transparent w-full h-24 sm:h-32 rounded-full -top-10"
          />
          <div
            className="absolute -z-10 blur-[100px] opacity-20 bg-[radial-gradient(circle_at_center,_var(--tw-gradient-stops))] 
                        from-secondary/40 via-secondary/10 to-transparent w-full h-24 sm:h-32 rounded-full -bottom-10"
          />
        </div>
      </div>
    </div>
  );
}

export default Login;
