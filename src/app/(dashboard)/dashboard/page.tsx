// DashboardPage.tsx
import React from "react";
import DesignDashboard from "@/modules/dashboard/main-features/components/features/design-dashboard";
import { DashboardPageLayout } from "@/modules/dashboard/shared/components/dashboard-page-layout";
import { Metadata } from "next";
import { currentUser } from "@/modules/auth/actions/user-actions";
import prisma from "@/lib/prisma/prisma";
import OnboardingDialog from "@/modules/onboarding/components/onboarding-dialog";
import { HeroSection } from "@/modules/dashboard/main-features/components/landing/hero-section";
import { MainToolsGrid } from "@/modules/dashboard/main-features/components/landing/main-tools-grid";
import { CommunitySlider } from "@/modules/dashboard/main-features/components/landing/community-slider";

export default async function DashboardPage() {
  const user = await currentUser();

  // Only check for onboarding if we have a user
  const needsOnboarding = user
    ? !(
        await prisma.user.findUnique({
          where: { id: user.id },
          include: { referralCode: true },
        })
      )?.referralCode
    : false;

  return (
    <DashboardPageLayout
      heading="Home"
      description="Discover the newest features and updates recently launched"
    >
      {user && needsOnboarding && (
        <OnboardingDialog
          userId={user.id}
          email={user.email!}
          name={user.name}
        />
      )}
      <div className="space-y-12">
        <HeroSection />
        <MainToolsGrid />
        <CommunitySlider />
        <DesignDashboard />
      </div>
    </DashboardPageLayout>
  );
}
