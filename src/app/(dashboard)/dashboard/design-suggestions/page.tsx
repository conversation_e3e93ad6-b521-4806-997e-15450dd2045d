import React from "react";
import Link from "next/link";
import { DashboardPageLayout } from "@/modules/dashboard/shared/components/dashboard-page-layout";
import { getAllDesignSuggestions } from "@/lib/design-suggestions";
import DesignSuggestionList from "@/modules/dashboard/design-suggestion/components/design-suggestion-list";
import DesignSuggestionFilters from "@/modules/dashboard/design-suggestion/components/design-suggestion-filters";
import { Button } from "@/modules/ui/button";
import { PlusCircle } from "lucide-react";

interface PageProps {
  searchParams: {
    page?: string;
    room?: string;
    style?: string;
    [key: string]: string | string[] | undefined;
  };
}

const ITEMS_PER_PAGE = 15;

async function DesignSuggestionsPage({ searchParams }: PageProps) {
  const page = Number(searchParams.page) || 1;
  const { room, style } = searchParams;

  const { suggestions, totalCount } = await getAllDesignSuggestions({
    limit: ITEMS_PER_PAGE,
    offset: (page - 1) * ITEMS_PER_PAGE,
    room,
    style,
  });

  const totalPages = Math.max(1, Math.ceil(totalCount / ITEMS_PER_PAGE));

  return (
    <DashboardPageLayout
      heading="Design Suggestions"
      description="Browse and explore design inspiration for your projects"
      className="px-4 container sm:px-6 lg:px-8 pb-2 "
    >
      <DesignSuggestionList
        suggestions={suggestions}
        currentPage={page}
        totalPages={totalPages}
        totalCount={totalCount}
        filters={<DesignSuggestionFilters />}
      />
    </DashboardPageLayout>
  );
}

export default DesignSuggestionsPage;
