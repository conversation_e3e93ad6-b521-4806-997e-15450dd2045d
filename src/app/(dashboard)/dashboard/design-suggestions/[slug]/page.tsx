import { getDesignSuggestionBySlug } from "@/lib/design-suggestions";
import Image from "next/image";
import { Metadata } from "next";
import RelatedDesigns from "@/modules/dashboard/design-suggestion/components/related-designs";
import { ScrollArea } from "@/modules/ui/scroll-area";
import { Dialog, DialogContent, DialogTrigger } from "@/modules/ui/dialog";
import InteractiveButtons from "./InteractiveButtons";
import { ZoomInIcon } from "lucide-react";
import { DesignVisualization } from "@/modules/dashboard/design-suggestion/components/design-visualization";
import { parseDesignResponse } from "@/modules/dashboard/design-suggestion/utils/parse-design-response";
import { StructuredDesignContent } from "@/modules/dashboard/design-suggestion/components/structured-design-content";

export async function generateMetadata({
  params,
}: {
  params: { slug: string };
}): Promise<Metadata> {
  const suggestion = await getDesignSuggestionBySlug(params.slug);
  return {
    title: `${suggestion.room} Design in ${suggestion.style} Style | Interior AI`,
    description: `Explore our AI-generated design suggestion for a ${suggestion.room} in ${suggestion.style} style. ${suggestion.dimensions} square meters of inspiration!`,
    openGraph: {
      title: `${suggestion.room} Design in ${suggestion.style} Style`,
      description: `AI-generated interior design for a ${suggestion.dimensions}sqm ${suggestion.room} in ${suggestion.style} style.`,
      images: [suggestion.outputImage || "/default-og-image.jpg"],
    },
    twitter: {
      card: "summary_large_image",
      title: `${suggestion.room} Design in ${suggestion.style} Style`,
      description: `AI-generated interior design for a ${suggestion.dimensions}sqm ${suggestion.room} in ${suggestion.style} style.`,
      images: [suggestion.outputImage || "/default-og-image.jpg"],
    },
  };
}

export default async function DesignSuggestionPage({
  params,
}: {
  params: { slug: string };
}) {
  const suggestion = await getDesignSuggestionBySlug(params.slug);
  const parsedContent = suggestion.suggestion
    ? parseDesignResponse(suggestion.suggestion)
    : null;

  // Transform the parsed content into the structure expected by StructuredDesignContent
  const structuredContent = parsedContent
    ? {
        colorPalettes: parsedContent.colorPalettes || [],
        designElements: parsedContent.designElements || [],
        designDescription: {
          overallConcept: parsedContent.sections[0]?.content[0] || "",
          sections: parsedContent.sections.slice(1).map((section) => ({
            title: section.title,
            content: section.content,
          })),
        },
      }
    : null;

  return (
    <ScrollArea className="h-[calc(100vh-64px)] w-full">
      <div className="container mx-auto p-4 max-w-7xl">
        <div className="flex flex-col lg:flex-row gap-8">
          <div className="lg:w-2/3">
            <h1 className="text-3xl font-bold mb-4 dark:text-white">
              {suggestion.room} Design in {suggestion.style} Style
            </h1>
            <p className="text-lg mb-4 dark:text-gray-300">
              Dimensions: {suggestion.dimensions} square meters
            </p>

            <InteractiveButtons />

            {suggestion.outputImage && (
              <Dialog>
                <DialogTrigger asChild>
                  <div className="relative w-full aspect-video mb-6 cursor-zoom-in">
                    <Image
                      src={suggestion.outputImage}
                      alt={`Generated design for ${suggestion.room} in ${suggestion.style} style`}
                      layout="fill"
                      objectFit="cover"
                      className="rounded-lg"
                    />
                    <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white p-2 rounded-full">
                      <ZoomInIcon className="h-6 w-6" />
                    </div>
                  </div>
                </DialogTrigger>
                <DialogContent className="max-w-4xl">
                  <Image
                    src={suggestion.outputImage}
                    alt={`Generated design for ${suggestion.room} in ${suggestion.style} style`}
                    width={1200}
                    height={800}
                    layout="responsive"
                    objectFit="contain"
                  />
                </DialogContent>
              </Dialog>
            )}

            <div className="mb-6">
              <h2 className="text-2xl font-semibold mb-4 dark:text-white">
                Design Details
              </h2>
              {structuredContent && (
                <StructuredDesignContent {...structuredContent} />
              )}
            </div>

            {suggestion.inputImage && (
              <div className="mb-6">
                <h3 className="text-xl font-semibold mb-2 dark:text-white">
                  Inspiration Image
                </h3>
                <Dialog>
                  <DialogTrigger asChild>
                    <div className="relative w-full aspect-video cursor-zoom-in">
                      <Image
                        src={suggestion.inputImage}
                        alt="Inspiration image"
                        layout="fill"
                        objectFit="cover"
                        className="rounded-lg"
                      />
                      <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white p-2 rounded-full">
                        <ZoomInIcon className="h-6 w-6" />
                      </div>
                    </div>
                  </DialogTrigger>
                  <DialogContent className="max-w-4xl">
                    <Image
                      src={suggestion.inputImage}
                      alt="Inspiration image"
                      width={1200}
                      height={800}
                      layout="responsive"
                      objectFit="contain"
                    />
                  </DialogContent>
                </Dialog>
              </div>
            )}
          </div>

          <div className="lg:w-1/3">
            <div className="sticky top-4 space-y-6">
              <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                <h2 className="text-2xl font-semibold mb-4 dark:text-white">
                  Design Summary
                </h2>
                <ul className="space-y-2 dark:text-gray-300">
                  <li>
                    <span className="font-semibold">Room:</span>{" "}
                    {suggestion.room}
                  </li>
                  <li>
                    <span className="font-semibold">Style:</span>{" "}
                    {suggestion.style}
                  </li>
                  <li>
                    <span className="font-semibold">Dimensions:</span>{" "}
                    {suggestion.dimensions} sq m
                  </li>
                </ul>
              </div>

              <RelatedDesigns
                room={suggestion.room}
                style={suggestion.style}
                dimensions={suggestion.dimensions}
                currentSlug={params.slug}
              />
            </div>
          </div>
        </div>
      </div>
    </ScrollArea>
  );
}
