import { LoadingSpinner } from "@/modules/ui/loading-states";
import { DashboardPageLayout } from "@/modules/dashboard/shared/components/dashboard-page-layout";

export default function DesignSuggestionLoading() {
  return (
    <DashboardPageLayout
      heading="Loading Design"
      description="Retrieving design details..."
    >
      <div className="min-h-[60vh] flex flex-col items-center justify-center gap-6">
        <LoadingSpinner />
        <div className="text-center">
          <p className="text-lg font-medium mb-2">Loading Design Details</p>
          <p className="text-sm text-muted-foreground">
            Please wait while we fetch your design...
          </p>
        </div>
      </div>
    </DashboardPageLayout>
  );
}
