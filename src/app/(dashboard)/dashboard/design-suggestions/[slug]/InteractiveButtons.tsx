"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/modules/ui/button";
import { Share2, Heart, Download } from "lucide-react";

export default function InteractiveButtons() {
  const [isLiked, setIsLiked] = useState(false);

  return (
    <div className="flex flex-wrap gap-4 mb-6">
      <Button variant="outline" size="sm">
        <Share2 className="mr-2 h-4 w-4" />
        Share
      </Button>
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsLiked(!isLiked)}
        aria-pressed={isLiked}
      >
        <Heart
          className={`mr-2 h-4 w-4 ${
            isLiked ? "fill-current text-red-500" : ""
          }`}
        />
        {isLiked ? "Saved" : "Save"}
      </Button>
      <Button variant="outline" size="sm">
        <Download className="mr-2 h-4 w-4" />
        Download PDF
      </Button>
    </div>
  );
}
