import { LoadingCircles } from "@/modules/ui/loading-states";
import { DashboardPageLayout } from "@/modules/dashboard/shared/components/dashboard-page-layout";

export default function NewDesignSuggestionLoading() {
  return (
    <DashboardPageLayout
      heading="New Design Suggestion"
      description="Loading design suggestion form..."
    >
      <div className="min-h-[60vh] flex flex-col items-center justify-center gap-6">
        <LoadingCircles />
        <div className="text-center">
          <p className="text-lg font-medium mb-2">Preparing Form</p>
          <p className="text-sm text-muted-foreground">
            Setting up your design workspace...
          </p>
        </div>
      </div>
    </DashboardPageLayout>
  );
}
