"use client";

import { useEffect } from "react";
import { <PERSON><PERSON> } from "@/modules/ui/button";
import { AlertCircle } from "lucide-react";
import { DashboardPageLayout } from "@/modules/dashboard/shared/components/dashboard-page-layout";

export default function DesignSuggestionsError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error(error);
  }, [error]);

  return (
    <DashboardPageLayout
      heading="Error"
      description="Failed to load design suggestions"
    >
      <div className="flex flex-col items-center justify-center min-h-[40vh]">
        <AlertCircle className="h-8 w-8 text-red-500 mb-4" />
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          {error.message || "Failed to load design suggestions"}
        </p>
        <Button onClick={reset}>Try again</Button>
      </div>
    </DashboardPageLayout>
  );
}
