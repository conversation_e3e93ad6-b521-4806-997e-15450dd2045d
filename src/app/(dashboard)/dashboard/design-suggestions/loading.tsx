import { LoadingCircles } from "@/modules/ui/loading-states";
import { DashboardPageLayout } from "@/modules/dashboard/shared/components/dashboard-page-layout";

export default function DesignSuggestionsLoading() {
  return (
    <DashboardPageLayout
      heading="Design Suggestions"
      description="Loading your design suggestions..."
    >
      <div className="min-h-[60vh] flex flex-col items-center justify-center gap-6">
        <LoadingCircles />
        <div className="text-center">
          <p className="text-lg font-medium mb-2">Preparing Your Designs</p>
          <p className="text-sm text-muted-foreground">
            This may take a few moments...
          </p>
        </div>
      </div>
    </DashboardPageLayout>
  );
}
