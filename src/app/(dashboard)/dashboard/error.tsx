"use client";

import { useEffect } from "react";
import { But<PERSON> } from "@/modules/ui/button";
import { AlertTriangleIcon } from "lucide-react";

export default function DashboardError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log to error reporting service
    console.error(error);
  }, [error]);

  return (
    <div className="flex items-center justify-center min-h-[60vh] p-4">
      <div className="text-center">
        <AlertTriangleIcon className="h-8 w-8 text-red-500 mx-auto mb-4" />
        <h2 className="text-xl font-semibold mb-2">Something went wrong!</h2>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          {error.message || "An error occurred while loading the dashboard"}
        </p>
        <Button onClick={reset}>Try again</Button>
      </div>
    </div>
  );
}
