import React from "react";
import Header from "@/modules/dashboard/main-features/components/layout/header";
import Sidebar from "@/modules/dashboard/main-features/components/layout/sidebar";
import { Metadata } from "next";
import { TooltipProvider } from "@/modules/ui/tooltip";

export const metadata: Metadata = {
  title: "AI Design Studio | Renovaitor Dashboard",
  description:
    "Access powerful AI interior design tools. Transform spaces with virtual staging, room redesigns, and renovation previews. Start creating now.",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
    nocache: true,
  },
  openGraph: {
    type: "website",
    title: "AI Design Studio Dashboard | Renovaitor",
    description:
      "Create stunning interior designs with AI tools. Virtual staging, room makeovers, and renovation previews in one powerful platform.",
    images: [
      {
        url: "https://www.renovaitor.com/dashboard-og.png",
        width: 1200,
        height: 630,
        alt: "Renovaitor AI Design Studio - Transform Spaces Instantly",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "AI Design Studio | Create Beautiful Spaces",
    description:
      "Access professional AI design tools. Transform any space with virtual staging and room redesigns.",
    images: ["https://www.renovaitor.com/dashboard-og.png"],
  },
  keywords: [
    "AI Design Tools",
    "Virtual Staging Software",
    "Room Design Platform",
    "AI Renovation Tool",
    "Interior Design Dashboard",
  ],
};

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <div className="flex w-screen h-screen bg-background">
      <aside className="z-20">
        <Sidebar />
      </aside>
      <div className="flex-1 w-full h-full flex flex-col overflow-hidden">
        <main className="flex-1 relative md:pt-0 pt-14">{children}</main>
      </div>
    </div>
  );
}
