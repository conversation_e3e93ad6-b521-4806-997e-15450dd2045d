import { redirect } from "next/navigation";
import { currentUser } from "@/modules/auth/actions/user-actions";
import { DashboardPageLayout } from "@/modules/dashboard/shared/components/dashboard-page-layout";
import ReferralInfo from "@/modules/profile/components/referral-info";
import prisma from "@/lib/prisma/prisma";
import { Card } from "@/modules/ui/card";
import { BodyText, H3 } from "@/modules/ui/typography";
import { Gift, Users, Coins, TrendingUp } from "lucide-react";
import Image from "next/image";

export default async function ReferralPage() {
  const user = await currentUser();

  if (!user) {
    redirect("/login");
  }

  const userWithReferral = await prisma.user.findUnique({
    where: { id: user.id },
    include: {
      referralCode: true,
    },
  });

  const benefits = [
    {
      icon: <Gift className="w-6 h-6 text-primary" />,
      title: "5 Free Credits",
      description: "Both you and your friend get 5 free credits on signup",
    },
    {
      icon: <Users className="w-6 h-6 text-primary" />,
      title: "Unlimited Referrals",
      description: "No limit on how many friends you can refer",
    },
    {
      icon: <Coins className="w-6 h-6 text-primary" />,
      title: "Instant Rewards",
      description: "Credits are added instantly upon friend's signup",
    },
    {
      icon: <TrendingUp className="w-6 h-6 text-primary" />,
      title: "Track Progress",
      description: "Monitor your referrals and earned credits",
    },
  ];

  return (
    <DashboardPageLayout
      heading="Referral Program"
      description="Share Renovaitor with friends and earn credits"
    >
      <div className="grid gap-8 max-w-5xl mx-auto pb-10">
        {/* Hero Section */}
        <div className="relative overflow-hidden rounded-xl bg-gradient-to-r from-primary/10 via-primary/5 to-transparent">
          <div className="grid md:grid-cols-2 gap-6 p-6 md:p-8">
            <div className="space-y-4">
              <H3>Earn While You Share</H3>
              <BodyText>
                Share Renovaitor with your friends and colleagues in the real
                estate industry. For every friend who signs up using your
                referral code, you&apos;ll both receive 5 credits to transform
                your property photos with AI.
              </BodyText>
            </div>
            <div className="relative h-48 md:h-full min-h-[200px]">
              <Image
                src="/images/dashboard/refer-earn.png"
                alt="Referral Program"
                fill
                className="object-contain"
              />
            </div>
          </div>
        </div>

        {/* Benefits Grid */}
        <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {benefits.map((benefit, index) => (
            <Card
              key={index}
              className="p-6 hover:bg-muted/5 transition-colors"
            >
              <div className="space-y-4">
                <div className="bg-primary/10 w-12 h-12 rounded-lg flex items-center justify-center">
                  {benefit.icon}
                </div>
                <div>
                  <h4 className="font-medium mb-1">{benefit.title}</h4>
                  <p className="text-sm text-muted-foreground">
                    {benefit.description}
                  </p>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Referral Info Component */}
        <ReferralInfo user={userWithReferral!} />

        {/* How It Works */}
        <Card className="p-6">
          <div className="space-y-4">
            <h3 className="text-lg font-medium">How It Works</h3>
            <div className="grid gap-4">
              <div className="flex gap-4 items-start">
                <div className="bg-primary/10 w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="font-medium">1</span>
                </div>
                <div>
                  <h4 className="font-medium mb-1">Share Your Code</h4>
                  <p className="text-sm text-muted-foreground">
                    Copy your unique referral code or share directly via social
                    media
                  </p>
                </div>
              </div>
              <div className="flex gap-4 items-start">
                <div className="bg-primary/10 w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="font-medium">2</span>
                </div>
                <div>
                  <h4 className="font-medium mb-1">Friend Signs Up</h4>
                  <p className="text-sm text-muted-foreground">
                    Your friend creates an account using your referral code
                  </p>
                </div>
              </div>
              <div className="flex gap-4 items-start">
                <div className="bg-primary/10 w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="font-medium">3</span>
                </div>
                <div>
                  <h4 className="font-medium mb-1">Both Get Rewarded</h4>
                  <p className="text-sm text-muted-foreground">
                    You and your friend each receive 5 credits automatically
                  </p>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </DashboardPageLayout>
  );
}
