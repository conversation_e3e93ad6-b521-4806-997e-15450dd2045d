import { LoadingCircles } from "@/modules/ui/loading-states";
import { DashboardPageLayout } from "@/modules/dashboard/shared/components/dashboard-page-layout";

export default function ProfileLoading() {
  return (
    <DashboardPageLayout
      heading="Profile"
      description="Loading your profile information..."
    >
      <div className="min-h-[60vh] flex items-center justify-center">
        <div className="text-center space-y-4">
          <LoadingCircles />
          <p className="text-lg font-medium">Loading Profile</p>
          <p className="text-sm text-muted-foreground">
            Retrieving your information...
          </p>
        </div>
      </div>
    </DashboardPageLayout>
  );
}
