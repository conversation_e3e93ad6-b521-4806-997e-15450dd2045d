import { redirect } from "next/navigation";
import { getUserSubscription } from "@/lib/subscription";
import ProfileInfo from "@/modules/profile/components/profile-info";
import SubscriptionInfo from "@/modules/profile/components/subscription-info";
import { currentUser } from "@/modules/auth/actions/user-actions";
import { DashboardPageLayout } from "@/modules/dashboard/shared/components/dashboard-page-layout";
import { Card } from "@/modules/ui/card";
import { Button } from "@/modules/ui/button";
import { Share2, Gift } from "lucide-react";
import Link from "next/link";
import Image from "next/image";

export default async function ProfilePage() {
  const user = await currentUser();

  if (!user) {
    redirect("/login");
  }

  const subscription = await getUserSubscription(user.id);

  return (
    <DashboardPageLayout
      heading="Profile Settings"
      description="Manage your account settings and subscription"
    >
      <div className="container grid gap-8 md:grid-cols-2 pb-10 sm:pb-0">
        <ProfileInfo user={user} />
        <SubscriptionInfo subscription={subscription} />
        <Link href="/dashboard/referral" className="md:col-span-2">
          <Card className="p-6 hover:bg-muted/5 transition-colors group relative overflow-hidden">
            <div className="flex items-center justify-between relative z-10">
              <div className="flex items-center gap-4">
                <div className="bg-primary/10 p-3 rounded-xl">
                  <Gift className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h3 className="font-medium text-lg">
                    Refer Friends & Earn Credits
                  </h3>
                  <p className="text-sm text-foreground/70">
                    Get 5 free credits for every friend who joins
                  </p>
                </div>
              </div>
              <Button
                variant="default"
                className="group-hover:translate-x-1 transition-transform"
              >
                View Program <Share2 className="w-4 h-4 ml-2" />
              </Button>
            </div>
            <div className="absolute right-0 top-0 h-full w-1/3 opacity-10">
              <Image
                src="/images/dashboard/refer-earn.png"
                alt="Referral"
                fill
                className="object-cover object-left"
              />
            </div>
          </Card>
        </Link>
      </div>
    </DashboardPageLayout>
  );
}
