import { LoadingSpinner } from "@/modules/ui/loading-states";
import { DashboardPageLayout } from "@/modules/dashboard/shared/components/dashboard-page-layout";

export default function DesignsLoading() {
  return (
    <DashboardPageLayout
      heading="My Designs"
      description="Loading your design projects..."
    >
     <div className="flex h-[calc(100vh-4rem)] flex-col items-center justify-center">
      <LoadingSpinner />
      <div className="text-center mt-4">
        <p className="text-lg font-medium">Loading Designs</p>
        <p className="text-sm text-muted-foreground">
          Please wait while we load your designs...
        </p>
      </div>
    </div>
    </DashboardPageLayout>
  );
}
