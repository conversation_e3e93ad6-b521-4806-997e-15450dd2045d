import {
  InteriorDesign as PrismaInteriorDesign,
  ExteriorDesign as PrismaExteriorDesign,
  BackgroundRemoval as PrismaBackgroundRemoval,
  UpscaleImage as PrismaUpscaleImage,
  VirtualStaging as PrismaVirtualStaging,
  StyleTransfer as PrismaStyleTransfer,
  User,
  ImageRating,
  PredictionStatus,
} from "@prisma/client";

type BaseDesign = {
  id: string;
  createdAt: Date;
  completedAt?: Date | null;
  user: User;
  ratings: ImageRating[];
  favoritedBy: User[];
  status: PredictionStatus;
  isPublic: boolean;
  userId: string;
  replicate_id: string;
  totalRatings?: number;
  averageRating?: number;
  inputImage: string;
};

export type Design = (
  | (Omit<PrismaInteriorDesign, keyof BaseDesign> & {
      type: "interior";
      outputImages: string[];
    })
  | (Omit<PrismaExteriorDesign, keyof BaseDesign> & {
      type: "exterior";
      outputImages: string[];
    })
  | (Omit<PrismaBackgroundRemoval, keyof BaseDesign> & {
      type: "remove-background";
      outputImage: string | null;
      outputImages: string[];
    })
  | (Omit<PrismaUpscaleImage, keyof BaseDesign> & {
      type: "upscale";
      outputImage: string | null;
      outputImages: string[];
    })
  | (Omit<PrismaVirtualStaging, keyof BaseDesign> & {
      type: "virtual-staging";
      outputImage: string | null;
      outputImages: string[];
    })
  | (Omit<PrismaStyleTransfer, keyof BaseDesign> & {
      type: "style-transfer";
      outputImage: string | null;
      styleImage: string;
      structureImage?: string;
    })
) &
  BaseDesign;
