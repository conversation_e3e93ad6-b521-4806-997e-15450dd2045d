import Image from "next/image";
import { Card, CardContent } from "@/modules/ui/card";
import { Design } from "../types";

interface DesignImageProps {
  design: Design;
}

function assertNever(x: never): never {
  throw new Error("Unexpected object: " + x);
}

export function DesignImage({ design }: DesignImageProps) {
  const imageSrc = getImageSrc(design);

  return (
    <Card>
      <CardContent className="p-0">
        <div className="relative w-full h-0 pb-[56.25%] overflow-hidden rounded-lg">
          <Image
            src={imageSrc}
            alt={`${design.type} design`}
            fill
            className="object-contain"
            sizes="(max-width: 1280px) 100vw, 1280px"
            priority
          />
        </div>
      </CardContent>
    </Card>
  );
}

function getImageSrc(design: Design): string {
  switch (design.type) {
    case "interior":
    case "exterior":
      return design.outputImages[0] || design.inputImage;
    case "style-transfer":
      return design.outputImage || design.styleImage;
    case "remove-background":
    case "upscale":
    case "virtual-staging":
      return design.outputImage || design.outputImages[0] || design.inputImage;
    default:
      return assertNever(design);
  }
}
