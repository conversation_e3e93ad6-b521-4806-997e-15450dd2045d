"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/modules/ui/card";
import { Star } from "lucide-react";
import { Design } from "../types";
import { ImageRating } from "@prisma/client";
import { formatDistanceToNow } from "date-fns";

interface RatingsTabProps {
  design: Design;
}

function RatingStars({ rating }: { rating: number }) {
  return (
    <div className="flex items-center">
      {Array.from({ length: 5 }).map((_, i) => (
        <Star
          key={i}
          className={`w-4 h-4 ${
            i < (rating || 0)
              ? "text-yellow-400 fill-yellow-400"
              : "text-gray-300"
          }`}
        />
      ))}
    </div>
  );
}

export function RatingsTab({ design }: RatingsTabProps) {
  const ratings = (design?.ratings || []) as ImageRating[];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Ratings & Reviews</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {ratings.length > 0 ? (
            ratings.map((rating) => (
              <div key={rating.id} className="border-b pb-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <RatingStars rating={rating.rating} />
                    <span className="text-sm text-muted-foreground">
                      {formatDistanceToNow(new Date(rating.createdAt), {
                        addSuffix: true,
                      })}
                    </span>
                  </div>
                  {"model" in design && design.model && (
                    <span className="text-sm text-muted-foreground">
                      Model: {design.model}
                    </span>
                  )}
                </div>
              </div>
            ))
          ) : (
            <p className="text-muted-foreground">No ratings yet</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
