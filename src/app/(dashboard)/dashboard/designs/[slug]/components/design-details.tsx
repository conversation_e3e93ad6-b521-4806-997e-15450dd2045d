import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/modules/ui/card";
import { User as UserIcon } from "lucide-react";
import { Design } from "../types";

interface DesignDetailsProps {
  design: Design;
}

export function DesignDetails({ design }: DesignDetailsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Design Details</CardTitle>
      </CardHeader>
      <CardContent>
        <dl className="space-y-4">
          {design.completedAt && (
            <div>
              <dt className="font-medium mb-2">Completed:</dt>
              <dd>
                {new Date(design.completedAt).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "long",
                  day: "numeric",
                  hour: "2-digit",
                  minute: "2-digit",
                })}
              </dd>
            </div>
          )}
          {renderSpecificDetails(design)}
        </dl>
      </CardContent>
    </Card>
  );
}

function renderSpecificDetails(design: Design) {
  switch (design.type) {
    case "interior":
      return (
        <>
          <div>
            <dt className="font-medium mb-2">Room Type:</dt>
            <dd>{design.room || "N/A"}</dd>
          </div>
          <div>
            <dt className="font-medium mb-2">Style:</dt>
            <dd>{design.style || "N/A"}</dd>
          </div>
          {design.prompt && (
            <div>
              <dt className="font-medium mb-2">Prompt:</dt>
              <dd className="text-sm">{design.prompt}</dd>
            </div>
          )}
        </>
      );
    case "exterior":
      return (
        <>
          <div>
            <dt className="font-medium mb-2">Building Type:</dt>
            <dd>{design.building || "N/A"}</dd>
          </div>
          <div>
            <dt className="font-medium mb-2">Style:</dt>
            <dd>{design.style || "N/A"}</dd>
          </div>
          {design.prompt && (
            <div>
              <dt className="font-medium mb-2">Prompt:</dt>
              <dd className="text-sm">{design.prompt}</dd>
            </div>
          )}
        </>
      );
    case "virtual-staging":
      return (
        <>
          <div>
            <dt className="font-medium mb-2">Room Type:</dt>
            <dd>{design.room || "N/A"}</dd>
          </div>
          <div>
            <dt className="font-medium mb-2">Style:</dt>
            <dd>{design.style || "N/A"}</dd>
          </div>
          {design.prompt && (
            <div>
              <dt className="font-medium mb-2">Prompt:</dt>
              <dd className="text-sm">{design.prompt}</dd>
            </div>
          )}
        </>
      );
    case "remove-background":
      return (
        <div>
          <dt className="font-medium mb-2">Original Image:</dt>
          <dd>{design.inputImage || "N/A"}</dd>
        </div>
      );
    case "upscale":
      return (
        <div>
          <dt className="font-medium mb-2">Scale Factor:</dt>
          <dd>{design.upscaleAmount || "N/A"}</dd>
        </div>
      );
    case "style-transfer":
      return (
        <>
          <div>
            <dt className="font-medium mb-2">Model:</dt>
            <dd>{design.model || "Default"}</dd>
          </div>
          {design.prompt && (
            <div>
              <dt className="font-medium mb-2">Prompt:</dt>
              <dd className="text-sm">{design.prompt}</dd>
            </div>
          )}
        </>
      );
    default:
      return null;
  }
}
