"use client";

import { <PERSON><PERSON> } from "@/modules/ui/button";
import { Badge } from "@/modules/ui/badge";
import { Clock, Download, Share2 } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { PredictionStatus } from "@prisma/client";
import { Design } from "../types";
import { useToast } from "@/modules/ui/use-toast";

interface DesignHeaderProps {
  design: Design;
}

export function DesignHeader({ design }: DesignHeaderProps) {
  const { toast } = useToast();

  const handleDownload = async () => {
    try {
      const imageUrl = getMainImageUrl(design);
      if (!imageUrl) {
        toast({
          title: "Download Failed",
          description: "No image available for download",
          variant: "destructive",
        });
        return;
      }

      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `${design.type}-${design.id}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast({
        title: "Success",
        description: "Image downloaded successfully",
        variant: "default",
      });
    } catch (error) {
      console.error("Download error:", error);
      toast({
        title: "Download Failed",
        description: "Failed to download image",
        variant: "destructive",
      });
    }
  };

  const handleShare = async () => {
    try {
      const imageUrl = getMainImageUrl(design);
      if (!imageUrl) {
        toast({
          title: "Share Failed",
          description: "No image available to share",
          variant: "destructive",
        });
        return;
      }

      const shareData = {
        title: `${getDesignTitle(design.type)} - Renovaitor`,
        text: `Check out this ${design.type} design on Renovaitor!`,
        url: window.location.href,
      };

      if (navigator.share) {
        // Use Web Share API if available (mobile devices)
        await navigator.share(shareData);
        toast({
          title: "Success",
          description: "Shared successfully",
          variant: "default",
        });
      } else {
        // Fallback for desktop: Copy link to clipboard
        await navigator.clipboard.writeText(window.location.href);
        toast({
          title: "Success",
          description: "Link copied to clipboard",
          variant: "default",
        });
      }
    } catch (error) {
      if ((error as Error).name === "AbortError") {
        // User cancelled sharing
        return;
      }
      console.error("Share error:", error);
      toast({
        title: "Share Failed",
        description: "Failed to share",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
      <div>
        <div className="flex items-center gap-2 mt-2 text-muted-foreground">
          <Clock className="w-4 h-4" />
          <span>
            Created {formatDistanceToNow(new Date(design.createdAt))} ago
          </span>
        </div>
      </div>
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm" onClick={handleDownload}>
          <Download className="w-4 h-4 mr-2" />
          Download
        </Button>
        <Button variant="outline" size="sm" onClick={handleShare}>
          <Share2 className="w-4 h-4 mr-2" />
          Share
        </Button>
      </div>
    </div>
  );
}

function getDesignTitle(type: Design["type"]) {
  const titles = {
    interior: "Interior Design",
    exterior: "Exterior Design",
    "virtual-staging": "Virtual Staging",
    "remove-background": "Background Removal",
    upscale: "Upscaled Image",
    "style-transfer": "Style Transfer",
  } as const;
  return titles[type];
}

function getMainImageUrl(design: Design): string | null {
  if ("outputImage" in design && design.outputImage) {
    return design.outputImage;
  }
  if ("outputImages" in design && design.outputImages?.[0]) {
    return design.outputImages[0];
  }
  if ("inputImage" in design && design.inputImage) {
    return design.inputImage;
  }
  return null;
}
