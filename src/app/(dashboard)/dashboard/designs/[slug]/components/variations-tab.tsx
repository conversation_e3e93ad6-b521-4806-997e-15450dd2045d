import Image from "next/image";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/modules/ui/card";
import { Design } from "../types";

interface VariationsTabProps {
  design: Design;
}

export function VariationsTab({ design }: VariationsTabProps) {
  if ("styleImage" in design) {
    // Style Transfer specific layout
    return (
      <Card>
        <CardHeader>
          <CardTitle>Style Transfer Images</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Style Image</h3>
              <div className="relative aspect-square rounded-lg overflow-hidden">
                <Image
                  src={design.styleImage}
                  alt="Style Image"
                  fill
                  className="object-cover"
                />
              </div>
            </div>
            {design.structureImage && (
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Structure Image</h3>
                <div className="relative aspect-square rounded-lg overflow-hidden">
                  <Image
                    src={design.structureImage}
                    alt="Structure Image"
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            )}
            {design.outputImage && (
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Output Image</h3>
                <div className="relative aspect-square rounded-lg overflow-hidden">
                  <Image
                    src={design.outputImage}
                    alt="Output Image"
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Default behavior for other design types
  const images = "outputImages" in design ? design.outputImages : [];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Variations</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {images.map((image, index) => (
            <div
              key={index}
              className="relative aspect-square rounded-lg overflow-hidden"
            >
              <Image
                src={image}
                alt={`Variation ${index + 1}`}
                fill
                className="object-cover"
              />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
