import { Card, CardContent } from "@/modules/ui/card";
import { Design } from "../types";
import { formatDistanceToNow } from "date-fns";

interface DesignStatsProps {
  design: Design;
}

export function DesignStats({ design }: DesignStatsProps) {
  const getModelInfo = () => {
    if ("model" in design && design.model) {
      return (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="text-lg font-medium">{design.model}</div>
              <p className="text-sm text-muted-foreground">Model</p>
            </div>
          </CardContent>
        </Card>
      );
    }
    return null;
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8">
      <Card>
        <CardContent className="pt-6">
          <div className="text-center">
            <div className="text-2xl font-bold">{design.totalRatings || 0}</div>
            <p className="text-sm text-muted-foreground">Total Ratings</p>
          </div>
        </CardContent>
      </Card>
      <Card>
        <CardContent className="pt-6">
          <div className="text-center">
            <div className="text-2xl font-bold">
              {design.averageRating?.toFixed(1) || "N/A"}
            </div>
            <p className="text-sm text-muted-foreground">Average Rating</p>
          </div>
        </CardContent>
      </Card>
      {getModelInfo() || (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="text-2xl font-bold">
                {design.favoritedBy?.length || 0}
              </div>
              <p className="text-sm text-muted-foreground">Favorites</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
