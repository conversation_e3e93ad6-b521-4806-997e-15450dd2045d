import { notFound } from "next/navigation";
import { auth } from "@/lib/auth/auth";
import { DashboardPageLayout } from "@/modules/dashboard/shared/components/dashboard-page-layout";
import { getDesignBySlug } from "@/modules/dashboard/main-features/actions/get-design";
import { DesignType } from "@/types/designs";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/modules/ui/tabs";
import { Design } from "./types";
import { DesignHeader } from "./components/design-header";
import { DesignImage } from "./components/design-image";
import { DesignStats } from "./components/design-stats";
import { DesignDetails } from "./components/design-details";
import { RatingsTab } from "./components/ratings-tab";
import { VariationsTab } from "./components/variations-tab";

interface PageProps {
  params: {
    slug: string;
  };
}

export default async function DesignDetailsPage({ params }: PageProps) {
  const session = await auth();
  if (!session?.user?.id) {
    return (
      <div className="flex items-center justify-center min-h-[50vh] px-4">
        Please log in to view design details.
      </div>
    );
  }

  const slugParts = params.slug.split("-");
  let designType: DesignType;
  let id: string;

  if (slugParts[0] === "virtual" && slugParts[1] === "staging") {
    designType = "virtual-staging";
    id = slugParts.slice(2).join("-");
  } else if (slugParts[0] === "style" && slugParts[1] === "transfer") {
    designType = "style-transfer";
    id = slugParts.slice(2).join("-");
  } else {
    designType = slugParts[0] as DesignType;
    id = slugParts.slice(1).join("-");
  }

  if (!isValidDesignType(designType)) {
    notFound();
  }

  const design = (await getDesignBySlug(
    session.user.id,
    designType,
    id
  )) as Design;

  if (!design) {
    notFound();
  }

  const hasVariations =
    ("outputImages" in design && design.outputImages.length > 1) ||
    (design.type === "style-transfer" && design.styleImage);

  return (
    <DashboardPageLayout
      heading="Design Details"
      className="px-4 sm:px-6 lg:px-8"
    >
      <div className="max-w-7xl mx-auto space-y-8">
        <DesignHeader design={design} />
        <DesignImage design={design} />
        <DesignStats design={design} />

        <Tabs defaultValue="details" className="w-full">
          <TabsList>
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="ratings">Ratings & Reviews</TabsTrigger>
            {hasVariations && (
              <TabsTrigger value="variations">
                {design.type === "style-transfer" ? "Images" : "Variations"}
              </TabsTrigger>
            )}
          </TabsList>
          <TabsContent value="details">
            <DesignDetails design={design} />
          </TabsContent>
          <TabsContent value="ratings">
            <RatingsTab design={design} />
          </TabsContent>
          {hasVariations && (
            <TabsContent value="variations">
              <VariationsTab design={design} />
            </TabsContent>
          )}
        </Tabs>
      </div>
    </DashboardPageLayout>
  );
}

// Helper function to validate design type
function isValidDesignType(type: string): type is DesignType {
  const validTypes: DesignType[] = [
    "interior",
    "exterior",
    "remove-background",
    "upscale",
    "virtual-staging",
    "style-transfer",
  ];
  return validTypes.includes(type as DesignType);
}
