import { LoadingCircles } from "@/modules/ui/loading-states";
import { DashboardPageLayout } from "@/modules/dashboard/shared/components/dashboard-page-layout";

export default function DesignDetailLoading() {
  return (
    <DashboardPageLayout
      heading="Design Details"
      description="Loading design details..."
    >
      <div className="min-h-[60vh] flex flex-col items-center justify-center gap-6">
        <LoadingCircles />
        <div className="text-center">
          <p className="text-lg font-medium mb-2">Loading Design</p>
          <p className="text-sm text-muted-foreground">
            Retrieving design details...
          </p>
        </div>
      </div>
    </DashboardPageLayout>
  );
}
