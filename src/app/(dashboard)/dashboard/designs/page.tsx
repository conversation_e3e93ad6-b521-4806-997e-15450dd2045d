import React from "react";
import { auth } from "@/lib/auth/auth";
import { DashboardPageLayout } from "@/modules/dashboard/shared/components/dashboard-page-layout";
import DesignsPageWrapper from "@/modules/dashboard/main-features/components/designs/designs-page-wrapper";
import { getCompletedDesigns } from "@/modules/dashboard/main-features/actions/get-completed-designs";
import { DesignType } from "@/types/designs"; // Import DesignType

interface PageProps {
  searchParams: {
    page?: string;
    type?: DesignType;
    favorites?: string;
    [key: string]: string | string[] | undefined;
  };
}

export default async function DesignsPage({ searchParams }: PageProps) {
  const session = await auth();
  if (!session?.user?.id) {
    return (
      <div className="flex items-center justify-center min-h-[50vh] px-4">
        Please log in to view your designs.
      </div>
    );
  }

  const page = Number(searchParams.page) || 1;
  const type = (searchParams.type as DesignType) || "style-transfer";
  const showFavorites = searchParams.favorites === "true";

  // Validate the design type
  if (!isValidDesignType(type)) {
    return (
      <div className="flex items-center justify-center min-h-[50vh] px-4">
        Invalid design type.
      </div>
    );
  }

  const result = await getCompletedDesigns(
    session.user.id,
    type,
    page,
    showFavorites
  );

  if (!result) {
    return (
      <div className="flex items-center justify-center min-h-[50vh] px-4">
        No designs found.
      </div>
    );
  }

  return (
    <DashboardPageLayout
      heading="My Designs"
      description="View and manage all your design projects"
      className="px-4 container sm:px-6 lg:px-8 pb-2 "
    >
      <div className="w-full ">
        <DesignsPageWrapper
          {...result}
          type={type}
          searchParams={searchParams}
        />
      </div>
    </DashboardPageLayout>
  );
}

// Helper function to validate design type
function isValidDesignType(type: string): type is DesignType {
  const validTypes: DesignType[] = [
    "interior",
    "exterior",
    "remove-background",
    "upscale",
    "virtual-staging",
    "style-transfer",
  ];
  return validTypes.includes(type as DesignType);
}
