import React from "react";
import <PERSON>A<PERSON> from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import ResendProvider from "next-auth/providers/resend";
import { PrismaAdapter } from "@auth/prisma-adapter";
import prisma from "@/lib/prisma/prisma";
import EmailTemplate from "./email-templates/sign-in-email";
import { render } from "@react-email/render";
import Credentials from "next-auth/providers/credentials";
import bcrypt from "bcryptjs";
import { User } from "@prisma/client";
import { handleNewUserSignup } from "@/lib/services/user-service";
import { processReferral } from "@/lib/services/referral-service";
import { headers } from "next/headers";

export const { auth, handlers, signIn, signOut } = NextAuth({
  adapter: PrismaAdapter(prisma),
  pages: {
    signIn: "/login",
    verifyRequest: "/login",
    error: "/auth/error",
  },
  session: {
    strategy: "database",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  providers: [
    GoogleProvider({
      clientId: process.env.AUTH_GOOGLE_ID,
      clientSecret: process.env.AUTH_GOOGLE_SECRET,
      authorization: {
        params: {
          prompt: "select_account",
          access_type: "offline",
          response_type: "code",
          scope: "openid email profile",
        },
      },
      allowDangerousEmailAccountLinking: true,
    }),
    ResendProvider({
      from: process.env.RESEND_FROM_EMAIL,
      sendVerificationRequest: async (params) => {
        const { identifier: to, url, provider } = params;
        const { host } = new URL(url);

        const emailHtml = render(
          React.createElement(EmailTemplate, { url, host })
        );
        const text = `Sign in to ${host}\n${url}\n\n`;

        try {
          const res = await fetch("https://api.resend.com/emails", {
            method: "POST",
            headers: {
              Authorization: `Bearer ${provider.apiKey}`,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              from: `Renovaitor Login <${process.env.RESEND_FROM_EMAIL}>`,
              to,
              subject: `Sign in to ${host}`,
              html: emailHtml,
              text,
            }),
          });

          if (!res.ok) {
            const error = await res.json();
            console.error("Resend API error:", error);
            throw new Error(error.message || "Failed to send email");
          }

          return;
        } catch (error) {
          console.error("Verification email error:", error);
          throw error;
        }
      },
    }),
    Credentials({
      credentials: {
        username: { label: "Username", type: "text" },
        password: { label: "Password", type: "password" },
      },
      async authorize(
        credentials:
          | Partial<Record<"username" | "password", unknown>>
          | undefined,
        request: Request
      ) {
        try {
          const username = credentials?.username as string | undefined;
          const password = credentials?.password as string | undefined;
          if (!username || !password) {
            throw new Error("Missing username or password");
          }

          // Find user with the given username (or email)
          const user = await prisma.user.findUnique({
            where: { email: username },
          });

          // If user not found or password is incorrect, return null
          if (
            !user ||
            !user.password ||
            !bcrypt.compareSync(password, user.password)
          ) {
            throw new Error("Invalid credentials");
          }

          // If user is found and password is correct, return the user object
          return user;
        } catch (error) {
          console.error("Authorization error:", error);
          throw error;
        }
      },
    }),
  ],
  cookies: {
    pkceCodeVerifier: {
      name: "next-auth.pkce.code_verifier",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
    sessionToken: {
      name: "next-auth.session-token",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
    callbackUrl: {
      name: "next-auth.callback-url",
      options: {
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
    csrfToken: {
      name: "next-auth.csrf-token",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
  },
  callbacks: {
    async session({ session, user }) {
      if (session.user) {
        session.user.id = user.id;

        // Fetch subscription information
        const subscription = await prisma.subscription.findUnique({
          where: { userId: user.id },
        });

        // Add subscription status to session with correct operator precedence
        session.user.isSubscribed = subscription?.isActive ?? false;
        session.user.subscription = subscription;
        session.user.monthlyCredits = subscription?.monthlyCredits ?? 0;
        session.user.nextBillingDate =
          subscription?.nextBillingDate?.toISOString() ?? null;
        session.user.lastBillingDate =
          subscription?.lastBillingDate?.toISOString() ?? null;
        session.user.trialEndsAt =
          subscription?.trialEndsAt?.toISOString() ?? null;
      }
      return session;
    },
    async signIn({ user, account, profile, email, credentials }) {
      try {
        const headersList = headers();
        const referer = headersList.get("referer");

        // Only try to parse URL if referer exists
        let referralCode = null;
        if (referer) {
          try {
            const url = new URL(referer);
            referralCode = url.searchParams.get("ref");
          } catch (error) {
            console.error("Error parsing referer URL:", error);
          }
        }

        if (referralCode) {
          (user as any).referralCode = referralCode;
        }

        if (account?.provider === "credentials") {
          if (!user) {
            return false;
          }
          if ((user as User).role === "ADMIN") {
            // Handle admin checks
          }
        }

        if (account?.provider === "google" && !user.email) {
          return false;
        }

        if (account?.provider === "resend" && !user.email) {
          return false;
        }

        return true;
      } catch (error) {
        console.error("Sign in callback error:", error);
        return false;
      }
    },
    async redirect({ url, baseUrl }) {
      try {
        // Handle empty or invalid URLs
        if (!url) {
          return `${baseUrl}/dashboard`;
        }

        // Handle callback URL
        try {
          const urlObject = new URL(
            url.startsWith("http") ? url : `${baseUrl}${url}`
          );
          const callbackUrl = urlObject.searchParams.get("callbackUrl");

          if (callbackUrl) {
            if (callbackUrl.startsWith("/")) {
              return `${baseUrl}${callbackUrl}`;
            }
            try {
              const callbackUrlObject = new URL(callbackUrl);
              if (callbackUrlObject.origin === baseUrl) {
                return callbackUrl;
              }
            } catch {
              // Invalid callback URL, fall through to default
            }
          }
        } catch {
          // Invalid URL, fall through to default
        }

        // Default redirect logic
        if (url.startsWith("/")) {
          return `${baseUrl}${url}`;
        }
        if (url.startsWith(baseUrl)) {
          return url;
        }

        // Default fallback
        return `${baseUrl}/dashboard`;
      } catch (error) {
        console.error("Redirect error:", error);
        return `${baseUrl}/dashboard`;
      }
    },
  },
  events: {
    async signIn({ user, isNewUser }) {
      try {
        if (isNewUser && user.id) {
          const fromEmail = "welcome";
          await handleNewUserSignup(user.id, fromEmail);

          const newUserId = user.id;
          const maybeReferralCode = (user as any).referralCode;
          if (
            typeof maybeReferralCode === "string" &&
            maybeReferralCode.length > 0
          ) {
            await prisma.$transaction(async (tx) => {
              await processReferral(tx, newUserId, maybeReferralCode);
            });
          }
        }
      } catch (error) {
        console.error("Sign in event error:", error);
      }
    },
  },
});
