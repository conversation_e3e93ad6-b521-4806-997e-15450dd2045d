import * as React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Text,
  Link,
} from "@react-email/components";

interface WelcomeEmailProps {
  name: string;
  credits: number;
}

const LogoComponent: React.FC = () => (
  <div style={{ marginBottom: "24px", textAlign: "center" }}>
    <div
      style={{
        width: "180px",
        height: "40px",
        position: "relative",
        margin: "0 auto 12px",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <img
        src={`${process.env.NEXT_PUBLIC_APP_URL}/images/renovaitor-logo-gradient-light.png`}
        alt="Renovaitor Logo"
        style={{
          width: "100%",
          height: "100%",
          objectFit: "contain",
        }}
      />
    </div>

    <div
      style={{
        backgroundColor: "rgba(107, 70, 193, 0.1)",
        padding: "6px 16px",
        display: "inline-block",
        borderRadius: "9999px",
        border: "1px solid rgba(107, 70, 193, 0.2)",
        marginTop: "8px",
      }}
    >
      <span
        style={{
          fontSize: "12px",
          fontWeight: "500",
          color: "rgb(107, 70, 193)",
          letterSpacing: "0.5px",
          lineHeight: "1",
        }}
      >
        AI INTERIOR DESIGNER
      </span>
    </div>
  </div>
);

export const WelcomeEmail: React.FC<WelcomeEmailProps> = ({
  name,
  credits,
}) => {
  const isDefaultName = name === "Designer";
  const welcomeHeading = isDefaultName
    ? "Welcome to Your Design Journey! ✨"
    : `Welcome to Your Design Journey, ${name}! ✨`;

  const welcomeText = isDefaultName
    ? "We're thrilled to have you join our community of creative designers. Get ready to transform your living spaces into stunning environments using the power of AI technology."
    : `We're excited to help you transform your living spaces into stunning environments using the power of AI technology.`;

  return (
    <Html>
      <Head />
      <Preview>
        Welcome to Renovaitor - Transform Your Space with AI-Powered Design
        Magic!
      </Preview>
      <Body
        style={{
          backgroundColor: "#f4f4f4",
          margin: "0",
          padding: "0",
          fontFamily: "system-ui, -apple-system, 'Segoe UI', sans-serif",
        }}
      >
        <Container
          style={{
            margin: "0 auto",
            padding: "20px",
            width: "465px",
          }}
        >
          <Section
            style={{
              backgroundColor: "#ffffff",
              padding: "40px",
              borderRadius: "8px",
              border: "1px solid #e2e8f0",
            }}
          >
            <LogoComponent />

            <Heading
              style={{
                color: "#1a1a1a",
                fontSize: "24px",
                fontWeight: "bold",
                textAlign: "center",
                margin: "30px 0 20px",
              }}
            >
              {welcomeHeading}
            </Heading>

            <Text
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "24px",
                textAlign: "center",
                margin: "0 0 20px",
              }}
            >
              {welcomeText}
            </Text>

            <Section
              style={{
                backgroundColor: "#f8f4ff",
                border: "1px solid #e9d8fd",
                borderRadius: "8px",
                padding: "20px",
                margin: "0 0 30px",
              }}
            >
              <Text
                style={{
                  color: "#553C9A",
                  fontSize: "18px",
                  fontWeight: "bold",
                  textAlign: "center",
                  margin: "0 0 10px",
                }}
              >
                🎁 Your Creative Starter Pack
              </Text>
              <Text
                style={{
                  color: "#553C9A",
                  fontSize: "16px",
                  textAlign: "center",
                  margin: "0",
                }}
              >
                To kickstart your design adventure, we&apos;ve added{" "}
                <strong>{credits} design credits</strong> to your account. Each
                credit brings you closer to your dream space!
              </Text>
            </Section>

            <Text
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "24px",
                margin: "0 0 20px",
              }}
            >
              Ready to revolutionize your space? Here&apos;s what you can create
              with Renovaitor:
            </Text>

            <ul
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "24px",
                margin: "0 0 30px",
                paddingLeft: "20px",
              }}
            >
              <li style={{ margin: "10px 0" }}>
                Generate stunning room designs in seconds
              </li>
              <li style={{ margin: "10px 0" }}>
                Explore countless style combinations and color palettes
              </li>
              <li style={{ margin: "10px 0" }}>
                Receive personalized design recommendations
              </li>
              <li style={{ margin: "10px 0" }}>
                Transform photos of your space into designer masterpieces
              </li>
            </ul>

            <Button
              href={`${process.env.NEXT_PUBLIC_APP_URL}/dashboard`}
              style={{
                backgroundColor: "#6b46c1",
                borderRadius: "6px",
                color: "#ffffff",
                fontSize: "16px",
                fontWeight: "bold",
                textDecoration: "none",
                textAlign: "center",
                display: "block",
                width: "100%",
                padding: "12px 0",
                margin: "0 0 30px",
              }}
            >
              Start Your Design Adventure
            </Button>

            <Text
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "24px",
                margin: "0 0 30px",
              }}
            >
              Want to make the most of your experience? Get free suggestions
              from expert AI Interior designers and{" "}
              <Link
                href={`${process.env.NEXT_PUBLIC_APP_URL}/dashboard/design-suggestions`}
                style={{ color: "#6b46c1", textDecoration: "underline" }}
              >
                design suggestion gallery
              </Link>{" "}
              or connect with our friendly support team for guidance.
            </Text>

            <div
              style={{
                borderTop: "1px solid #e2e8f0",
                margin: "30px 0 0",
                padding: "20px 0 0",
                textAlign: "center",
              }}
            >
              <Text
                style={{
                  color: "#718096",
                  fontSize: "12px",
                  margin: "0 0 5px",
                }}
              >
                Get inspired by following our design community:
              </Text>
              <div style={{ margin: "10px 0" }}>
                {/* Add your social media links here */}
                <Link
                  href="https://twitter.com/renovaitor"
                  style={{ color: "#6b46c1", margin: "0 10px" }}
                >
                  Twitter
                </Link>
                <Link
                  href="https://instagram.com/renovaitor"
                  style={{ color: "#6b46c1", margin: "0 10px" }}
                >
                  Instagram
                </Link>
              </div>
              <Text
                style={{
                  color: "#718096",
                  fontSize: "12px",
                  margin: "10px 0 0",
                }}
              >
                © {new Date().getFullYear()} Renovaitor. All rights reserved.
              </Text>
            </div>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

export default WelcomeEmail;
