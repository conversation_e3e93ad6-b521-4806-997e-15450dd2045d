import * as React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Text,
  Link,
} from "@react-email/components";

interface EmailTemplateProps {
  url: string;
  host: string;
}

const LogoComponent: React.FC = () => (
  <div style={{ marginBottom: "24px", textAlign: "center" }}>
    <div
      style={{
        width: "180px",
        height: "40px",
        position: "relative",
        margin: "0 auto 12px",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <img
        src={`${process.env.NEXT_PUBLIC_APP_URL}/images/renovaitor-logo-gradient-light.png`}
        alt="Renovaitor Logo"
        style={{
          width: "100%",
          height: "100%",
          objectFit: "contain",
        }}
      />
    </div>

    <div
      style={{
        backgroundColor: "rgba(107, 70, 193, 0.1)",
        padding: "6px 16px",
        display: "inline-block",
        borderRadius: "9999px",
        border: "1px solid rgba(107, 70, 193, 0.2)",
        marginTop: "8px",
      }}
    >
      <span
        style={{
          fontSize: "12px",
          fontWeight: "500",
          color: "rgb(107, 70, 193)",
          letterSpacing: "0.5px",
          lineHeight: "1",
        }}
      >
        AI INTERIOR DESIGNER
      </span>
    </div>
  </div>
);

export const EmailTemplate: React.FC<EmailTemplateProps> = ({ url, host }) => {
  return (
    <Html>
      <Head />
      <Preview>Sign in to continue your journey</Preview>
      <Body
        style={{
          backgroundColor: "#f4f4f4",
          margin: "0",
          padding: "0",
          fontFamily: "system-ui, -apple-system, 'Segoe UI', sans-serif",
        }}
      >
        <Container
          style={{
            margin: "0 auto",
            padding: "20px",
            width: "465px",
          }}
        >
          <Section
            style={{
              backgroundColor: "#ffffff",
              padding: "40px",
              borderRadius: "8px",
              border: "1px solid #e2e8f0",
            }}
          >
            <LogoComponent />

            <Heading
              style={{
                color: "#1a1a1a",
                fontSize: "24px",
                fontWeight: "bold",
                textAlign: "center",
                margin: "30px 0 20px",
              }}
            >
              Sign in to continue your journey
            </Heading>

            <Text
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "24px",
                textAlign: "center",
                margin: "0 0 30px",
              }}
            >
              Click the button below to securely sign in to your account and
              continue creating beautiful spaces with AI.
            </Text>

            <Button
              href={url}
              style={{
                backgroundColor: "#6b46c1",
                borderRadius: "6px",
                color: "#ffffff",
                fontSize: "16px",
                fontWeight: "bold",
                textDecoration: "none",
                textAlign: "center",
                display: "block",
                width: "100%",
                padding: "12px 0",
                margin: "0 0 30px",
              }}
            >
              Sign in to Renovaitor
            </Button>

            <Text
              style={{
                color: "#4a5568",
                fontSize: "14px",
                textAlign: "center",
                margin: "0 0 8px",
              }}
            >
              Button not working? Use this link:
            </Text>

            <Link
              href={url}
              style={{
                color: "#6b46c1",
                display: "block",
                fontSize: "14px",
                textAlign: "center",
                textDecoration: "underline",
                margin: "0 0 30px",
                wordBreak: "break-all",
              }}
            >
              {url}
            </Link>

            <div
              style={{
                borderTop: "1px solid #e2e8f0",
                margin: "30px 0 0",
                padding: "20px 0 0",
                textAlign: "center",
              }}
            >
              <Text
                style={{
                  color: "#718096",
                  fontSize: "12px",
                  margin: "0 0 5px",
                }}
              >
                If you did not request this email, you can safely ignore it.
              </Text>

              <Text
                style={{
                  color: "#718096",
                  fontSize: "12px",
                  margin: "0",
                }}
              >
                © {new Date().getFullYear()} Renovaitor. All rights reserved.
              </Text>
            </div>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

export default EmailTemplate;
