import * as React from "react";
import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Text,
  Row,
  Column,
} from "@react-email/components";
import { LogoComponent } from "./components/logo";

interface FeedbackSurveyEmailProps {
  name: string;
  message?: string;
  title?: string;
}

export const FeedbackSurveyEmail: React.FC<FeedbackSurveyEmailProps> = ({
  name = "Designer",
  message,
  title = "Your Opinion Matters! 🌟",
}) => {
  const textStyles = {
    color: "#4a5568",
    fontSize: "14px",
    lineHeight: "1.4",
    margin: "0 0 10px",
  };

  const sectionStyles = {
    borderRadius: "6px",
    padding: "10px",
    margin: "0 0 10px",
  };

  return (
    <Html>
      <Head />
      <Preview>
        Share your thoughts on our latest AI improvements and get a special
        discount! 🎨
      </Preview>
      <Body style={{ margin: 0, padding: 0, backgroundColor: "#f4f4f4" }}>
        <Container
          style={{ padding: "12px 0", width: "100%", maxWidth: "460px" }}
        >
          <Section
            style={{
              backgroundColor: "#ffffff",
              padding: "16px 10px",
              borderRadius: "6px",
              border: "1px solid #e2e8f0",
            }}
          >
            <Row>
              <Column>
                <LogoComponent />
              </Column>
            </Row>

            <Row style={{ marginTop: "16px" }}>
              <Column>
                <Heading
                  style={{
                    color: "#1a1a1a",
                    fontSize: "18px",
                    fontWeight: "bold",
                    textAlign: "center",
                    margin: "0 0 10px",
                    lineHeight: "1.3",
                  }}
                >
                  {title}
                </Heading>

                <Text style={{ ...textStyles, marginBottom: "12px" }}>
                  Hi {name},
                </Text>

                {message ? (
                  <Text style={textStyles}>{message}</Text>
                ) : (
                  <>
                    <Text style={textStyles}>
                      I&apos;m excited to share that we&apos;ve made significant
                      improvements to Renovaitor&apos;s virtual staging tool!
                      Our AI engine has been upgraded to generate even more
                      realistic and inspiring rooms.
                    </Text>
                    <Text style={textStyles}>
                      As someone who&apos;s been using this feature, your
                      insights would be incredibly valuable.
                    </Text>
                  </>
                )}
              </Column>
            </Row>

            <Row>
              <Column>
                <Section
                  style={{
                    ...sectionStyles,
                    backgroundColor: "#fff1f2",
                    border: "1px solid #fecdd3",
                    textAlign: "center",
                  }}
                >
                  <Text
                    style={{
                      color: "#be123c",
                      fontSize: "15px",
                      fontWeight: "bold",
                      margin: "0 0 4px",
                      lineHeight: "1.3",
                    }}
                  >
                    🎄 Happy New Year! 🎅
                  </Text>
                  <Text
                    style={{
                      color: "#be123c",
                      fontSize: "14px",
                      lineHeight: "1.4",
                      margin: "0",
                    }}
                  >
                    As we celebrate this festive season, I want to thank you for
                    being part of our journey. May your holidays be filled with
                    joy, inspiration, and beautifully designed spaces! 🎁✨
                  </Text>
                </Section>
              </Column>
            </Row>

            <Row>
              <Column>
                <Section
                  style={{
                    ...sectionStyles,
                    backgroundColor: "#f8f4ff",
                    border: "1px solid #e9d8fd",
                  }}
                >
                  <Text
                    style={{
                      color: "#553C9A",
                      fontSize: "14px",
                      margin: "0 0 6px",
                      lineHeight: "1.4",
                    }}
                  >
                    I&apos;d love your thoughts on our virtual staging
                    improvements:
                  </Text>
                  <Text
                    style={{
                      color: "#553C9A",
                      fontSize: "14px",
                      lineHeight: "1.4",
                      margin: "0 0 4px",
                      paddingLeft: "8px",
                    }}
                  >
                    • How do you feel about the enhanced realism in our newly
                    staged rooms? What details stand out to you?
                  </Text>
                  <Text
                    style={{
                      color: "#553C9A",
                      fontSize: "14px",
                      lineHeight: "1.4",
                      margin: "0 0 4px",
                      paddingLeft: "8px",
                    }}
                  >
                    • Which new style or room options in the virtual staging
                    tool have you found most useful?
                  </Text>
                  <Text
                    style={{
                      color: "#553C9A",
                      fontSize: "14px",
                      lineHeight: "1.4",
                      margin: "0 0 4px",
                      paddingLeft: "8px",
                    }}
                  >
                    • Have you experienced any challenges with the virtual
                    staging process that we should address?
                  </Text>
                  <Text
                    style={{
                      color: "#553C9A",
                      fontSize: "14px",
                      lineHeight: "1.4",
                      margin: "0 0 4px",
                      paddingLeft: "8px",
                    }}
                  >
                    • How has our virtual staging tool helped you save time or
                    improve your design presentations?
                  </Text>
                  <Text
                    style={{
                      color: "#553C9A",
                      fontSize: "14px",
                      lineHeight: "1.4",
                      margin: "0",
                      paddingLeft: "8px",
                    }}
                  >
                    • What additional features would make our virtual staging
                    tool even more valuable for your work?
                  </Text>
                </Section>
              </Column>
            </Row>

            <Row>
              <Column>
                <Section
                  style={{
                    ...sectionStyles,
                    backgroundColor: "#ffffff",
                    border: "1px solid #e2e8f0",
                  }}
                >
                  <Text style={{ ...textStyles, margin: "0" }}>
                    Your feedback is crucial in shaping the future of our
                    virtual staging tool. Simply reply to this email with your
                    thoughts - every detail helps us create a better experience
                    for you!
                  </Text>
                </Section>
              </Column>
            </Row>

            <Row>
              <Column>
                <Text
                  style={{
                    color: "#718096",
                    fontSize: "13px",
                    textAlign: "center",
                    margin: "12px 0 0",
                    lineHeight: "1.4",
                  }}
                >
                  Best regards,
                  <br />
                  Ugurcan
                  <br />
                  Founder - Chief Renovaitor
                </Text>
              </Column>
            </Row>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

export default FeedbackSurveyEmail;
