import { PrismaClient, Prisma } from "@prisma/client";
import { REFERRAL_CREDITS } from "@/lib/constants/user";

type TransactionClient = Omit<
  PrismaClient,
  "$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends"
>;

export async function processReferral(
  tx: TransactionClient,
  newUserId: string,
  referralCode: string | undefined
) {
  const referralRecord = await tx.referralCode.findUnique({
    where: { code: referralCode },
    include: { user: true },
  });

  if (!referralRecord) {
    return;
  }

  // Create referral relationship
  await tx.referral.create({
    data: {
      referralCode: { connect: { id: referralRecord.id } },
      referrer: { connect: { id: referralRecord.userId } },
      referred: { connect: { id: newUserId } },
    },
  });

  // Update referrer's stats (User A)
  await tx.user.update({
    where: { id: referralRecord.userId },
    data: {
      totalReferrals: { increment: 1 },
      referralCredits: { increment: REFERRAL_CREDITS },
      imageCredits: { increment: REFERRAL_CREDITS },
    },
  });

  // Add credits to the referred user (User B)
  await tx.user.update({
    where: { id: newUserId },
    data: {
      referralCredits: REFERRAL_CREDITS,
      imageCredits: { increment: REFERRAL_CREDITS },
    },
  });
}
