import prisma from "@/lib/prisma/prisma";
import { sendWelcomeEmail } from "@/lib/email/send-email";
import { DEFAULT_USER_CREDITS } from "@/lib/constants/user";

export const handleNewUserSignup = async (
  userId: string,
  fromEmail?: string
) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
        imageCredits: true,
      },
    });

    if (!user) {
      throw new Error(`User not found with ID: ${userId}`);
    }

    if (!user.email) {
      throw new Error(`No email address found for user: ${userId}`);
    }

    // Update user with initial credits if they haven't been assigned yet
    if (user.imageCredits === 0) {
      await prisma.$transaction(async (tx) => {
        // Update user credits
        await tx.user.update({
          where: { id: userId },
          data: {
            imageCredits: DEFAULT_USER_CREDITS,
            lastCreditReset: new Date(),
          },
        });

        // Send welcome email with the specified fromEmail
        await sendWelcomeEmail(
          user.email!,
          user.name || undefined,
          fromEmail ? { fromEmail } : undefined
        );
      });
    }

    return user;
  } catch (error) {
    console.error("Error handling new user signup:", error);
    throw error;
  }
};
