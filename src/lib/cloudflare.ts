"use server";

import { writeFile, unlink } from "fs/promises";
import { join } from "path";
import os from "os";

export async function uploadImageToCloudflare(file: File): Promise<string> {
  const bytes = await file.arrayBuffer();
  const buffer = Buffer.from(bytes);

  // Use os.tmpdir() to get the appropriate temp directory for the OS
  const tempPath = join(os.tmpdir(), file.name);
  await writeFile(tempPath, buffer);

  try {
    const formData = new FormData();
    formData.append("file", new Blob([buffer]), file.name);

    const response = await fetch(
      `https://api.cloudflare.com/client/v4/accounts/${process.env.CLOUDFLARE_ACCOUNT_ID}/images/v1`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${process.env.CLOUDFLARE_API_TOKEN}`,
        },
        body: formData,
      }
    );

    const data = await response.json();

    if (!data.success) {
      throw new Error("Failed to upload image to Cloudflare");
    }

    return data.result.variants[0];
  } catch (error) {
    console.error("Error uploading to Cloudflare:", error);
    throw new Error("Failed to upload image to Cloudflare");
  } finally {
    // Clean up the temporary file
    await unlink(tempPath);
  }
}

export async function uploadUserImageToCloudflare(
  formData: FormData
): Promise<string> {
  console.log("Starting upload process...");

  const file = formData.get("file") as File;
  if (!file) {
    console.error("No file found in FormData");
    throw new Error("No file provided");
  }

  console.log("File details:", {
    name: file.name,
    type: file.type,
    size: file.size,
  });

  const bytes = await file.arrayBuffer();
  const buffer = Buffer.from(bytes);

  const tempPath = join(os.tmpdir(), file.name);
  await writeFile(tempPath, buffer);
  console.log("Temporary file written to:", tempPath);

  try {
    const cloudflareForm = new FormData();

    // Log the buffer details
    console.log("Buffer size:", buffer.length);

    const blob = new Blob([buffer], { type: file.type });
    console.log("Blob created:", {
      size: blob.size,
      type: blob.type,
    });

    cloudflareForm.append("file", blob, file.name);
    console.log("FormData created with file");

    const url = `https://api.cloudflare.com/client/v4/accounts/${process.env.CLOUDFLARE_ACCOUNT_ID}/images/v1`;
    console.log("Cloudflare API URL:", url);

    const response = await fetch(url, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${process.env.CLOUDFLARE_API_TOKEN}`,
      },
      body: cloudflareForm,
    });

    console.log("Cloudflare response status:", response.status);
    const data = await response.json();
    console.log("Cloudflare response data:", data);

    if (!data.success) {
      console.error("Cloudflare error details:", {
        errors: data.errors,
        messages: data.messages,
      });
      throw new Error(
        `Failed to upload image to Cloudflare: ${JSON.stringify(data.errors)}`
      );
    }

    console.log("Upload successful, returning URL");
    return data.result.variants[0];
  } catch (error) {
    console.error("Detailed error:", {
      error,
      message: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
    });
    throw new Error("Failed to upload profile image");
  } finally {
    console.log("Cleaning up temporary file");
    await unlink(tempPath);
  }
}
