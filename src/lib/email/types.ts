import { Role, SubscriptionStatus } from "@prisma/client";

export type EmailTemplate =
  | "announcement"
  | "notification"
  | "marketing"
  | "welcome"
  | "incident"
  | "custom"
  | "feedback"
  | "inactivity-feedback";

interface BaseEmailProps {
  name: string;
  subject: string;
}

interface AnnouncementEmailProps extends BaseEmailProps {
  template: "announcement";
  message?: string;
  actionUrl?: string;
  actionText?: string;
}

interface NotificationEmailProps extends BaseEmailProps {
  template: "notification";
  message?: string;
  actionUrl?: string;
  actionText?: string;
}

interface MarketingEmailProps extends BaseEmailProps {
  template: "marketing";
  message?: string;
  offerTitle?: string;
  offerDescription?: string;
  actionUrl?: string;
  actionText?: string;
}

interface WelcomeEmailProps extends BaseEmailProps {
  template: "welcome";
  credits: number;
}

interface IncidentEmailProps extends BaseEmailProps {
  template: "incident";
  discountCode?: string;
  expiryDate?: Date | string;
  creditAmount?: number;
  incidentTitle?: string;
  incidentMessage?: string;
  message?: string;
  discountPercentage?: number;
  customFooterMessage?: string;
}

interface FeedbackEmailProps extends BaseEmailProps {
  template: "feedback";
  message?: string;
  title?: string;
  discountCode?: string;
  expiryDate?: string;
}

interface InactivityFeedbackEmailProps extends BaseEmailProps {
  template: "inactivity-feedback";
  message?: string;
  tryNowUrl?: string;
  title?: string;
}

interface CustomEmailProps extends BaseEmailProps {
  template: "custom";
  message?: string;
}

export type CommonEmailProps =
  | AnnouncementEmailProps
  | NotificationEmailProps
  | MarketingEmailProps
  | WelcomeEmailProps
  | IncidentEmailProps
  | FeedbackEmailProps
  | InactivityFeedbackEmailProps
  | CustomEmailProps;

export interface EmailConfig {
  fromEmail?: string;
  fromName?: string;
  defaultActionUrl?: string;
  defaultActionText?: string;
  defaultDiscountCode?: string;
  defaultExpiryDays?: number;
}

export const DEFAULT_EMAIL_CONFIG: EmailConfig = {
  fromEmail: "ugurcan",
  fromName: "Ugurcan | Renovaitor",
  defaultActionUrl: "https://renovaitor.com",
  defaultActionText: "View Details",
  defaultDiscountCode: "SORRY50",
  defaultExpiryDays: 7,
};

export interface EmailTemplateConfig {
  id: EmailTemplate;
  name: string;
  subject: string;
  description: string;
}

export interface EmailFiltersState {
  roles: Role[];
  subscriptionStatus: SubscriptionStatus[];
  lastActiveWithin?: number;
  minCredits?: number;
  maxCredits?: number;
  minGenerations?: number;
  maxGenerations?: number;
  hasSubscription?: boolean;
}

export interface EmailTemplateData {
  template: EmailTemplate;
  subject: string;
  message?: string;
  discountCode?: string;
  expiryDate?: Date;
  actionUrl?: string;
  actionText?: string;
  fromEmail?: string;
  fromName?: string;
}
