import { render } from "@react-email/render";
import { Resend } from "resend";
import * as React from "react";
import WelcomeEmail from "../auth/email-templates/welcome-email";
import { DEFAULT_USER_CREDITS } from "../constants/user";
import {
  EmailTemplate,
  EmailConfig,
  DEFAULT_EMAIL_CONFIG,
  CommonEmailProps,
} from "./types";
import { getEmailComponent } from "./get-email-component";

const resend = new Resend(process.env.RESEND_API_KEY);

interface SendEmailProps<T extends EmailTemplate> {
  to: string;
  subject: string;
  template: T;
  props: Omit<
    Extract<CommonEmailProps, { template: T }>,
    "subject" | "template"
  >;
  config?: Partial<EmailConfig>;
  headers?: Record<string, string>;
  attachments?: {
    filename: string;
    content?: Buffer;
    path?: string;
    contentType?: string;
    cid?: string;
  }[];
}

export async function sendEmail<T extends EmailTemplate>({
  to,
  subject,
  template,
  props,
  config = {},
  headers = {},
  attachments = [],
}: SendEmailProps<T>) {
  try {
    const emailConfig = { ...DEFAULT_EMAIL_CONFIG, ...config };
    const { fromEmail, fromName } = emailConfig;

    const EmailComponent = getEmailComponent(template, {
      ...props,
      subject,
      template,
    } as Extract<CommonEmailProps, { template: T }>);
    
    // Await the render result
    const emailHtml = await render(EmailComponent);

    // Default headers for better deliverability
    const defaultHeaders = {
      "X-Entity-Ref-ID": `${Date.now()}`,
      "List-Unsubscribe": "<https://renovaitor.com/unsubscribe>",
    };

    const { data, error } = await resend.emails.send({
      from: `${fromName} <${fromEmail}@renovaitor.com>`,
      to,
      subject,
      html: emailHtml,
      headers: {
        ...defaultHeaders,
        ...headers,
      },
      attachments,
    });

    if (error) {
      console.error("Error sending email:", error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error("Error in sendEmail:", error);
    throw error;
  }
}

export const sendWelcomeEmail = async (
  email: string,
  name?: string,
  config?: Partial<EmailConfig>
) => {
  const formattedName = name?.trim() || "Designer";

  return sendEmail({
    to: email,
    subject: `Welcome to Renovaitor, ${formattedName}! Your AI Design Journey Begins 🎨`,
    template: "welcome" as const,
    props: {
      name: formattedName,
      credits: DEFAULT_USER_CREDITS,
    },
    config,
    headers: {
      "X-Message-Type": "welcome",
    },
  });
};

export const sendFeedbackSurveyEmail = async (
  email: string,
  name?: string,
  message?: string,
  discountCode?: string,
  expiryDate?: Date,
  config?: Partial<EmailConfig>
) => {
  const formattedName = name?.trim() || "Designer";
  const formattedExpiryDate = expiryDate?.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  return sendEmail({
    to: email,
    subject: `Your Opinion Matters! Help Us Improve Renovaitor 🎨`,
    template: "feedback" as const,
    props: {
      name: formattedName,
      message,
      discountCode,
      expiryDate: formattedExpiryDate,
    },
    config,
    headers: {
      "X-Message-Type": "feedback",
    },
  });
};
