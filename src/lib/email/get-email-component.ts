import { ReactElement } from "react";
import React from "react";
import CustomEmail from "@/lib/auth/email-templates/custom-email";
import NotificationEmail from "@/lib/auth/email-templates/notification-email";
import AnnouncementEmail from "@/lib/auth/email-templates/announcement-email";
import MarketingEmail from "@/lib/auth/email-templates/marketing-email";
import WelcomeEmail from "@/lib/auth/email-templates/welcome-email";
import IncidentEmail from "@/lib/auth/email-templates/incident-email";
import { FeedbackSurveyEmail } from "@/lib/auth/email-templates/feedback-survey-email";
import { FeedbackInactivityEmail } from "@/lib/auth/email-templates/feedback-inactivity-email";
import { EmailTemplate, CommonEmailProps } from "./types";

export function getEmailComponent(
  template: EmailTemplate,
  props: CommonEmailProps
): ReactElement {
  const { name, subject } = props;

  switch (template) {
    case "announcement": {
      const p = props as Extract<
        CommonEmailProps,
        { template: "announcement" }
      >;
      return React.createElement(AnnouncementEmail, {
        name,
        message: p.message,
        title: subject,
        actionUrl: p.actionUrl,
        actionText: p.actionText,
      });
    }
    case "notification": {
      const p = props as Extract<
        CommonEmailProps,
        { template: "notification" }
      >;
      return React.createElement(NotificationEmail, {
        name,
        message: p.message,
        actionUrl: p.actionUrl,
        actionText: p.actionText,
      });
    }
    case "marketing": {
      const p = props as Extract<CommonEmailProps, { template: "marketing" }>;
      return React.createElement(MarketingEmail, {
        name,
        message: p.message,
        offerTitle: subject,
        offerDescription: p.message,
        actionUrl: p.actionUrl,
        actionText: p.actionText,
      });
    }
    case "welcome": {
      const p = props as Extract<CommonEmailProps, { template: "welcome" }>;
      return React.createElement(WelcomeEmail, {
        name,
        credits: p.credits,
      });
    }
    case "incident": {
      const p = props as Extract<CommonEmailProps, { template: "incident" }>;
      return React.createElement(IncidentEmail, {
        name,
        discountCode: p.discountCode || "SORRY50",
        expiryDate: typeof p.expiryDate === 'string' ? p.expiryDate : (
          p.expiryDate || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
        ).toLocaleDateString("en-US", {
          year: "numeric",
          month: "long",
          day: "numeric",
        }),
        creditAmount: p.creditAmount || 10,
        incidentTitle: p.incidentTitle || subject || `We Apologize for the Inconvenience, ${name}`,
        incidentMessage: p.incidentMessage || p.message || "We noticed an issue with your account. We sincerely apologize for this and want to make it right.",
        discountPercentage: p.discountPercentage || 20,
        customFooterMessage: p.customFooterMessage,
      });
    }
    case "feedback": {
      const p = props as Extract<CommonEmailProps, { template: "feedback" }>;
      return React.createElement(FeedbackSurveyEmail, {
        name,
        message: p.message,
        title: subject,
      });
    }
    case "inactivity-feedback": {
      const p = props as Extract<
        CommonEmailProps,
        { template: "inactivity-feedback" }
      >;
      return React.createElement(FeedbackInactivityEmail, {
        name,
        tryNowUrl: p.tryNowUrl,
        message: p.message,
        title: subject,
      });
    }
    case "custom":
    default: {
      const p = props as Extract<CommonEmailProps, { template: "custom" }>;
      return React.createElement(CustomEmail, {
        name,
        message: p.message,
        template: "announcement",
      });
    }
  }
}
