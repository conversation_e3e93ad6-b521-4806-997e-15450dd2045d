import * as React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Text,
  Link,
} from "@react-email/components";

interface NotificationEmailProps {
  name: string;
  message?: string;
  actionUrl?: string;
  actionText?: string;
}

const LogoComponent: React.FC = () => (
  <div style={{ marginBottom: "24px", textAlign: "center" }}>
    <div
      style={{
        width: "180px",
        height: "40px",
        position: "relative",
        margin: "0 auto 12px",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <img
        src={`${process.env.NEXT_PUBLIC_APP_URL}/images/renovaitor-logo-gradient-light.png`}
        alt="Renovaitor Logo"
        style={{
          width: "100%",
          height: "100%",
          objectFit: "contain",
        }}
      />
    </div>

    <div
      style={{
        backgroundColor: "rgba(107, 70, 193, 0.1)",
        padding: "6px 16px",
        display: "inline-block",
        borderRadius: "9999px",
        border: "1px solid rgba(107, 70, 193, 0.2)",
        marginTop: "8px",
      }}
    >
      <span
        style={{
          fontSize: "12px",
          fontWeight: "500",
          color: "rgb(107, 70, 193)",
          letterSpacing: "0.5px",
          lineHeight: "1",
        }}
      >
        AI INTERIOR DESIGNER
      </span>
    </div>
  </div>
);

export const NotificationEmail: React.FC<NotificationEmailProps> = ({
  name = "Designer",
  message = "There's been some activity on your account that we'd like to inform you about.",
  actionUrl = "https://renovaitor.com/dashboard",
  actionText = "View Details",
}) => {
  return (
    <Html>
      <Head />
      <Preview>New Notification from Renovaitor</Preview>
      <Body
        style={{
          backgroundColor: "#f4f4f4",
          margin: "0",
          padding: "0",
          fontFamily: "system-ui, -apple-system, 'Segoe UI', sans-serif",
        }}
      >
        <Container
          style={{
            margin: "0 auto",
            padding: "20px",
            width: "465px",
          }}
        >
          <Section
            style={{
              backgroundColor: "#ffffff",
              padding: "40px",
              borderRadius: "8px",
              border: "1px solid #e2e8f0",
            }}
          >
            <LogoComponent />

            <Heading
              style={{
                color: "#1a1a1a",
                fontSize: "24px",
                fontWeight: "bold",
                textAlign: "center",
                margin: "30px 0 20px",
              }}
            >
              New Notification
            </Heading>

            <Text
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "24px",
                margin: "0 0 20px",
              }}
            >
              Hi {name},
            </Text>

            <Text
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "24px",
                margin: "0 0 30px",
              }}
            >
              {message}
            </Text>

            {actionUrl && actionText && (
              <Button
                href={actionUrl}
                style={{
                  backgroundColor: "#6b46c1",
                  borderRadius: "6px",
                  color: "#ffffff",
                  fontSize: "16px",
                  fontWeight: "bold",
                  textDecoration: "none",
                  textAlign: "center",
                  display: "block",
                  width: "100%",
                  padding: "12px 0",
                  margin: "0 0 30px",
                }}
              >
                {actionText}
              </Button>
            )}

            <Text
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "24px",
                margin: "0 0 20px",
              }}
            >
              If you didn&apos;t expect this notification, please contact our
              support team.
            </Text>

            <Text
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "24px",
                margin: "0 0 30px",
              }}
            >
              Best regards,
              <br />
              The Renovaitor Team
            </Text>

            <div
              style={{
                borderTop: "1px solid #e2e8f0",
                margin: "30px 0 0",
                padding: "20px 0 0",
                textAlign: "center",
              }}
            >
              <Text
                style={{
                  color: "#718096",
                  fontSize: "12px",
                  margin: "0 0 5px",
                }}
              >
                Stay connected with our design community:
              </Text>
              <div style={{ margin: "10px 0" }}>
                <Link
                  href="https://twitter.com/renovaitor"
                  style={{ color: "#6b46c1", margin: "0 10px" }}
                >
                  Twitter
                </Link>
                <Link
                  href="https://instagram.com/renovaitor"
                  style={{ color: "#6b46c1", margin: "0 10px" }}
                >
                  Instagram
                </Link>
              </div>
              <Text
                style={{
                  color: "#718096",
                  fontSize: "12px",
                  margin: "10px 0 0",
                }}
              >
                © {new Date().getFullYear()} Renovaitor. All rights reserved.
              </Text>
            </div>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

export default NotificationEmail;
