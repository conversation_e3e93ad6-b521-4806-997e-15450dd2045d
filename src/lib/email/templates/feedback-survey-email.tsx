import * as React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Text,
  Link,
} from "@react-email/components";
import { LogoComponent } from "./components/logo";

interface FeedbackSurveyEmailProps {
  name: string;
  discountCode?: string;
  expiryDate?: string;
  message?: string;
  title?: string;
}

export const FeedbackSurveyEmail: React.FC<FeedbackSurveyEmailProps> = ({
  name = "Designer",
  discountCode,
  expiryDate,
  message,
  title = "Your Opinion Matters! 🌟",
}) => {
  return (
    <Html>
      <Head />
      <Preview>{title}</Preview>
      <Body
        style={{
          backgroundColor: "#f4f4f4",
          margin: "0",
          padding: "0",
          fontFamily: "system-ui, -apple-system, 'Segoe UI', sans-serif",
        }}
      >
        <Container
          style={{
            margin: "0 auto",
            padding: "20px",
            width: "465px",
          }}
        >
          <Section
            style={{
              backgroundColor: "#ffffff",
              padding: "40px",
              borderRadius: "8px",
              border: "1px solid #e2e8f0",
            }}
          >
            <LogoComponent />

            <Heading
              style={{
                color: "#1a1a1a",
                fontSize: "24px",
                fontWeight: "bold",
                textAlign: "center",
                margin: "30px 0 20px",
              }}
            >
              {title}
            </Heading>

            <Text
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "24px",
                margin: "0 0 20px",
              }}
            >
              Hi {name},
            </Text>

            <Text
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "24px",
                margin: "0 0 20px",
              }}
            >
              {message ||
                "I'm excited to share that we've made significant improvements to Renovaitor's virtual staging tool! Our AI engine has been upgraded to generate even more realistic and inspiring rooms, making it easier than ever to visualize stunning designs. As someone who's been using this feature, your insights would be incredibly valuable."}
            </Text>

            <Section
              style={{
                backgroundColor: "#fff1f2",
                border: "1px solid #fecdd3",
                borderRadius: "8px",
                padding: "20px",
                margin: "0 0 30px",
                textAlign: "center",
              }}
            >
              <Text
                style={{
                  color: "#be123c",
                  fontSize: "18px",
                  fontWeight: "bold",
                  margin: "0 0 10px",
                }}
              >
                🎄 Merry Christmas & Happy New Year! 🎅
              </Text>
              <Text
                style={{
                  color: "#be123c",
                  fontSize: "16px",
                  lineHeight: "24px",
                  margin: "0",
                }}
              >
                As we celebrate this festive season, I want to thank you for
                being part of our journey. May your holidays be filled with joy,
                inspiration, and beautifully designed spaces! 🎁✨
              </Text>
            </Section>

            <Section
              style={{
                backgroundColor: "#f8f4ff",
                border: "1px solid #e9d8fd",
                borderRadius: "8px",
                padding: "20px",
                margin: "0 0 30px",
              }}
            >
              <Text
                style={{
                  color: "#553C9A",
                  fontSize: "16px",
                  margin: "0 0 15px",
                }}
              >
                I&apos;d love your thoughts on our virtual staging improvements:
              </Text>
              <ul
                style={{
                  color: "#553C9A",
                  fontSize: "16px",
                  margin: "0",
                  paddingLeft: "20px",
                }}
              >
                <li style={{ margin: "8px 0" }}>
                  How do you feel about the enhanced realism in our newly staged
                  rooms? What details stand out to you?
                </li>
                <li style={{ margin: "8px 0" }}>
                  Which new style or furniture options in the virtual staging
                  tool have you found most useful?
                </li>
                <li style={{ margin: "8px 0" }}>
                  Have you experienced any challenges with the virtual staging
                  process that we should address?
                </li>
                <li style={{ margin: "8px 0" }}>
                  How has our virtual staging tool helped you save time or
                  improve your design presentations?
                </li>
                <li style={{ margin: "8px 0" }}>
                  What additional features would make our virtual staging tool
                  even more valuable for your work?
                </li>
              </ul>
            </Section>

            <Section
              style={{
                backgroundColor: "#ffffff",
                border: "1px solid #e2e8f0",
                borderRadius: "8px",
                padding: "20px",
                margin: "0 0 30px",
              }}
            >
              <Text
                style={{
                  color: "#4a5568",
                  fontSize: "16px",
                  lineHeight: "24px",
                  margin: "0 0 15px",
                }}
              >
                Your feedback is crucial in shaping the future of our virtual
                staging tool. Simply reply to this email with your thoughts -
                every detail helps us create a better experience for you!
              </Text>
            </Section>

            {discountCode && (
              <Section
                style={{
                  backgroundColor: "#f0fff4",
                  border: "1px solid #c6f6d5",
                  borderRadius: "8px",
                  padding: "20px",
                  margin: "0 0 30px",
                }}
              >
                <Text
                  style={{
                    color: "#2F855A",
                    fontSize: "16px",
                    textAlign: "center",
                    margin: "0 0 10px",
                  }}
                >
                  As a thank you for your feedback, here&apos;s a special
                  discount code applicable to all plans:
                </Text>
                <Text
                  style={{
                    backgroundColor: "#ffffff",
                    padding: "12px",
                    borderRadius: "6px",
                    color: "#2F855A",
                    fontSize: "24px",
                    fontWeight: "bold",
                    textAlign: "center",
                    margin: "0",
                    letterSpacing: "2px",
                  }}
                >
                  {discountCode}
                </Text>
                {expiryDate && (
                  <Text
                    style={{
                      color: "#2F855A",
                      fontSize: "16px",
                      textAlign: "center",
                      margin: "0 0 10px",
                    }}
                  >
                    Valid until {expiryDate}
                  </Text>
                )}
              </Section>
            )}

            <Text
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "24px",
                margin: "0 0 30px",
              }}
            >
              Your feedback helps us create a better design experience for
              everyone. Thank you for being part of our journey!
            </Text>

            <Text
              style={{
                color: "#718096",
                fontSize: "14px",
                textAlign: "center",
                margin: "30px 0 0",
              }}
            >
              Best regards,
              <br />
              Ugurcan
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

export default FeedbackSurveyEmail;
