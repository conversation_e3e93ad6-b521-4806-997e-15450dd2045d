import * as React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Text,
} from "@react-email/components";
import { LogoComponent } from "./components/logo";

interface FeedbackInactivityEmailProps {
  name: string;
  tryNowUrl?: string;
  message?: string;
  title?: string;
}

export const FeedbackInactivityEmail: React.FC<
  FeedbackInactivityEmailProps
> = ({
  name = "Designer",
  tryNowUrl = "https://renovaitor.com/dashboard",
  message,
  title = "Let's Get You Started with Renovaitor! 🎨",
}) => {
  return (
    <Html>
      <Head />
      <Preview>{title}</Preview>
      <Body
        style={{
          backgroundColor: "#f4f4f4",
          margin: "0",
          padding: "0",
          fontFamily: "system-ui, -apple-system, 'Segoe UI', sans-serif",
        }}
      >
        <Container
          style={{
            margin: "0 auto",
            padding: "20px",
            width: "465px",
          }}
        >
          <Section
            style={{
              backgroundColor: "#ffffff",
              padding: "40px",
              borderRadius: "8px",
              border: "1px solid #e2e8f0",
            }}
          >
            <LogoComponent />

            <Heading
              style={{
                color: "#1a1a1a",
                fontSize: "24px",
                fontWeight: "bold",
                textAlign: "center",
                margin: "30px 0 20px",
              }}
            >
              {title}
            </Heading>

            <Text
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "24px",
                margin: "0 0 20px",
              }}
            >
              Hi {name},
            </Text>

            <Text
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "24px",
                margin: "0 0 20px",
              }}
            >
              {message ||
                "I noticed you haven't created any designs with Renovaitor yet, and I wanted to personally reach out. I'm genuinely interested in understanding if you encountered any difficulties or if there's anything specific I can help you with."}
            </Text>

            <Section
              style={{
                backgroundColor: "#f8f4ff",
                border: "1px solid #e9d8fd",
                borderRadius: "8px",
                padding: "20px",
                margin: "0 0 30px",
              }}
            >
              <Text
                style={{
                  color: "#553C9A",
                  fontSize: "16px",
                  margin: "0 0 15px",
                }}
              >
                I&apos;d love to hear about:
              </Text>
              <ul
                style={{
                  color: "#553C9A",
                  fontSize: "16px",
                  margin: "0",
                  paddingLeft: "20px",
                }}
              >
                <li style={{ margin: "8px 0" }}>
                  What made you interested in Renovaitor?
                </li>
                <li style={{ margin: "8px 0" }}>
                  Have you encountered any issues getting started?
                </li>
                <li style={{ margin: "8px 0" }}>
                  What kind of design project did you have in mind?
                </li>
              </ul>
            </Section>

            <Button
              href={tryNowUrl}
              style={{
                backgroundColor: "#6b46c1",
                borderRadius: "6px",
                color: "#ffffff",
                fontSize: "16px",
                fontWeight: "bold",
                textDecoration: "none",
                textAlign: "center",
                display: "block",
                width: "100%",
                padding: "12px 0",
                margin: "0 0 30px",
              }}
            >
              Try Creating a Design Now
            </Button>

            <Text
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "24px",
                margin: "0 0 30px",
              }}
            >
              Just reply directly to this email - I personally read and respond
              to every message. I&apos;m here to ensure you have the best
              possible experience with Renovaitor.
            </Text>

            <Text
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "24px",
                margin: "0 0 30px",
              }}
            >
              Looking forward to hearing from you!
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

export default FeedbackInactivityEmail;
