import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Link,
  Preview,
  Text,
  Section,
  Button,
} from "@react-email/components";
import * as React from "react";
import { LogoComponent } from "./components/logo";

interface CustomEmailProps {
  name: string;
  message?: string;
  title?: string;
  actionUrl?: string;
  actionText?: string;
  template?: "announcement" | "notification" | "marketing";
}

export default function CustomEmail({
  name = "Designer",
  message,
  title = "Message from Renovaitor",
  actionUrl,
  actionText,
}: CustomEmailProps) {
  return (
    <Html>
      <Head />
      <Preview>{title}</Preview>
      <Body
        style={{
          backgroundColor: "#f4f4f4",
          margin: "0",
          padding: "0",
          fontFamily: "system-ui, -apple-system, 'Segoe UI', sans-serif",
        }}
      >
        <Container
          style={{
            margin: "0 auto",
            padding: "20px",
            width: "465px",
          }}
        >
          <Section
            style={{
              backgroundColor: "#ffffff",
              padding: "40px",
              borderRadius: "8px",
              border: "1px solid #e2e8f0",
            }}
          >
            <LogoComponent />

            <Heading
              style={{
                color: "#1a1a1a",
                fontSize: "24px",
                fontWeight: "bold",
                textAlign: "center",
                margin: "30px 0 20px",
              }}
            >
              {title}
            </Heading>

            <Text
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "24px",
                margin: "0 0 20px",
              }}
            >
              Hi {name},
            </Text>

            <Text
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "24px",
                margin: "0 0 30px",
              }}
            >
              {message}
            </Text>

            {actionUrl && actionText && (
              <Button
                href={actionUrl}
                style={{
                  backgroundColor: "#6b46c1",
                  borderRadius: "6px",
                  color: "#ffffff",
                  fontSize: "16px",
                  fontWeight: "bold",
                  textDecoration: "none",
                  textAlign: "center",
                  display: "block",
                  width: "100%",
                  padding: "12px 0",
                  margin: "0 0 30px",
                }}
              >
                {actionText}
              </Button>
            )}

            <Text
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "24px",
                margin: "0 0 30px",
              }}
            >
              Best regards,
              <br />
              The Renovaitor Team
            </Text>

            <div
              style={{
                borderTop: "1px solid #e2e8f0",
                margin: "30px 0 0",
                padding: "20px 0 0",
                textAlign: "center",
              }}
            >
              <Text
                style={{
                  color: "#718096",
                  fontSize: "12px",
                  margin: "0 0 5px",
                }}
              >
                Stay connected with our design community:
              </Text>
              <div style={{ margin: "10px 0" }}>
                <Link
                  href="https://twitter.com/renovaitor"
                  style={{ color: "#6b46c1", margin: "0 10px" }}
                >
                  Twitter
                </Link>
                <Link
                  href="https://instagram.com/renovaitor"
                  style={{ color: "#6b46c1", margin: "0 10px" }}
                >
                  Instagram
                </Link>
              </div>
              <Text
                style={{
                  color: "#718096",
                  fontSize: "12px",
                  margin: "10px 0 0",
                }}
              >
                © {new Date().getFullYear()} Renovaitor. All rights reserved.
              </Text>
            </div>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}
