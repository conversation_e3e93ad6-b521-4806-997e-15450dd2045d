import * as React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Text,
  Link,
} from "@react-email/components";

interface IncidentEmailProps {
  name: string;
  discountCode: string;
  expiryDate: string;
  creditAmount?: number;
  incidentTitle?: string;
  incidentMessage?: string;
  discountPercentage?: number;
  customFooterMessage?: string;
}

const LogoComponent: React.FC = () => (
  <div style={{ marginBottom: "24px", textAlign: "center" }}>
    <div
      style={{
        width: "180px",
        height: "40px",
        position: "relative",
        margin: "0 auto 12px",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <img
        src={`${process.env.NEXT_PUBLIC_APP_URL}/images/renovaitor-logo-gradient-light.png`}
        alt="Renovaitor Logo"
        style={{
          width: "100%",
          height: "100%",
          objectFit: "contain",
        }}
      />
    </div>

    <div
      style={{
        backgroundColor: "rgba(107, 70, 193, 0.1)",
        padding: "6px 16px",
        display: "inline-block",
        borderRadius: "9999px",
        border: "1px solid rgba(107, 70, 193, 0.2)",
        marginTop: "8px",
      }}
    >
      <span
        style={{
          fontSize: "12px",
          fontWeight: "500",
          color: "rgb(107, 70, 193)",
          letterSpacing: "0.5px",
          lineHeight: "1",
        }}
      >
        AI INTERIOR DESIGNER
      </span>
    </div>
  </div>
);

export const IncidentEmail: React.FC<IncidentEmailProps> = ({
  name,
  discountCode,
  expiryDate,
  creditAmount = 10,
  incidentTitle,
  incidentMessage,
  discountPercentage = 20,
  customFooterMessage,
}) => {
  const isDefaultName = name === "Designer";
  const incidentHeading = incidentTitle || (isDefaultName
    ? "We Apologize for the Inconvenience"
    : `We Apologize for the Inconvenience, ${name}`);

  const incidentText = incidentMessage ||
    "We noticed that you weren't able to receive your welcome credits during sign-up. We sincerely apologize for this issue and want to make it right.";

  // Create React elements for formatted text instead of using HTML strings
  const creditsText = (
    <>
      We&apos;ve added <strong>{creditAmount} credits</strong> to your account as part of our commitment to making this right.
    </>
  );

  // Create React elements for formatted text instead of using HTML strings
  const discountText = (
    <>
      As a token of our apology, we&apos;re offering you a special <strong>{discountPercentage}% discount</strong> on any design package!
    </>
  );

  return (
    <Html>
      <Head />
      <Preview>
        Renovaitor - We apologize for the inconvenience. Here&apos;s a special
        offer for you!
      </Preview>
      <Body
        style={{
          backgroundColor: "#f4f4f4",
          margin: "0",
          padding: "0",
          fontFamily: "system-ui, -apple-system, 'Segoe UI', sans-serif",
        }}
      >
        <Container
          style={{
            margin: "0 auto",
            padding: "20px",
            width: "465px",
          }}
        >
          <Section
            style={{
              backgroundColor: "#ffffff",
              padding: "40px",
              borderRadius: "8px",
              border: "1px solid #e2e8f0",
            }}
          >
            <LogoComponent />

            <Heading
              style={{
                color: "#1a1a1a",
                fontSize: "24px",
                fontWeight: "bold",
                textAlign: "center",
                margin: "30px 0 20px",
              }}
            >
              {incidentHeading}
            </Heading>

            <Text
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "24px",
                textAlign: "center",
                margin: "0 0 20px",
              }}
            >
              {incidentText}
            </Text>

            <Section
              style={{
                backgroundColor: "#f0fff4",
                border: "1px solid #c6f6d5",
                borderRadius: "8px",
                padding: "20px",
                margin: "0 0 20px",
              }}
            >
              <Text
                style={{
                  color: "#2F855A",
                  fontSize: "18px",
                  fontWeight: "bold",
                  textAlign: "center",
                  margin: "0 0 10px",
                }}
              >
                ✨ Credits Added to Your Account
              </Text>
              <Text
                style={{
                  color: "#2F855A",
                  fontSize: "16px",
                  textAlign: "center",
                  margin: "0",
                }}
              >
                {creditsText}
              </Text>
            </Section>

            <Section
              style={{
                backgroundColor: "#f8f4ff",
                border: "1px solid #e9d8fd",
                borderRadius: "8px",
                padding: "20px",
                margin: "0 0 30px",
              }}
            >
              <Text
                style={{
                  color: "#553C9A",
                  fontSize: "18px",
                  fontWeight: "bold",
                  textAlign: "center",
                  margin: "0 0 10px",
                }}
              >
                🎁 Exclusive Offer Just for You
              </Text>
              <Text
                style={{
                  color: "#553C9A",
                  fontSize: "16px",
                  textAlign: "center",
                  margin: "0 0 15px",
                }}
              >
                {discountText}
              </Text>
              <Text
                style={{
                  backgroundColor: "#ffffff",
                  padding: "12px",
                  borderRadius: "6px",
                  color: "#553C9A",
                  fontSize: "24px",
                  fontWeight: "bold",
                  textAlign: "center",
                  margin: "0 0 15px",
                  letterSpacing: "2px",
                }}
              >
                {discountCode}
              </Text>
              <Text
                style={{
                  color: "#553C9A",
                  fontSize: "14px",
                  textAlign: "center",
                  margin: "0",
                }}
              >
                ⚡ Limited time offer - expires on {expiryDate}
              </Text>
            </Section>

            <Text
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "24px",
                margin: "0 0 20px",
              }}
            >
              With your new credits and discount, here&apos;s what you can
              create with Renovaitor:
            </Text>

            <ul
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "24px",
                margin: "0 0 30px",
                paddingLeft: "20px",
              }}
            >
              <li style={{ margin: "10px 0" }}>
                Use your {creditAmount} credits for instant design generations
              </li>
              <li style={{ margin: "10px 0" }}>
                Generate stunning room designs in seconds
              </li>
              <li style={{ margin: "10px 0" }}>
                Explore countless style combinations and color palettes
              </li>
              <li style={{ margin: "10px 0" }}>
                Receive personalized design recommendations
              </li>
              <li style={{ margin: "10px 0" }}>
                Transform photos of your space into designer masterpieces
              </li>
            </ul>

            <Button
              href={`${process.env.NEXT_PUBLIC_APP_URL}/pricing`}
              style={{
                backgroundColor: "#6b46c1",
                borderRadius: "6px",
                color: "#ffffff",
                fontSize: "16px",
                fontWeight: "bold",
                textDecoration: "none",
                textAlign: "center",
                display: "block",
                width: "100%",
                padding: "12px 0",
                margin: "0 0 30px",
              }}
            >
              Explore Our Pricing Plans
            </Button>

            <Text
              style={{
                color: "#718096",
                fontSize: "14px",
                fontStyle: "italic",
                lineHeight: "20px",
                margin: "0 0 30px",
                padding: "15px",
                backgroundColor: "#f7fafc",
                borderRadius: "6px",
              }}
            >
              P.S. Don&apos;t forget to use your discount code{" "}
              <strong>{discountCode}</strong> at the Lemon Squeezy checkout to
              get your {discountPercentage}% off!
            </Text>

            <Text
              style={{
                color: "#4a5568",
                fontSize: "16px",
                lineHeight: "24px",
                margin: "0 0 30px",
              }}
            >
              If you have any questions or need assistance, our support team is
              always here to help. Just reply to this email or visit our{" "}
              <Link
                href={`${process.env.NEXT_PUBLIC_APP_URL}/support`}
                style={{ color: "#6b46c1", textDecoration: "underline" }}
              >
                support center
              </Link>
              .
            </Text>

            <div
              style={{
                borderTop: "1px solid #e2e8f0",
                margin: "30px 0 0",
                padding: "20px 0 0",
                textAlign: "center",
              }}
            >
              <Text
                style={{
                  color: "#718096",
                  fontSize: "12px",
                  margin: "0 0 5px",
                }}
              >
                Get inspired by following our design community:
              </Text>
              <div style={{ margin: "10px 0" }}>
                <Link
                  href="https://twitter.com/renovaitor"
                  style={{ color: "#6b46c1", margin: "0 10px" }}
                >
                  Twitter
                </Link>
                <Link
                  href="https://instagram.com/renovaitor"
                  style={{ color: "#6b46c1", margin: "0 10px" }}
                >
                  Instagram
                </Link>
              </div>
              <Text
                style={{
                  color: "#718096",
                  fontSize: "12px",
                  margin: "10px 0 0",
                }}
              >
                © {new Date().getFullYear()} Renovaitor. All rights reserved.
              </Text>
            </div>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

export default IncidentEmail;
