"use server";

import prisma from "@/lib/prisma/prisma";

export async function updateUserCredits(userId: string, credits: number) {
  await prisma.user.update({
    where: { id: userId },
    data: { imageCredits: { increment: credits } },
  });
}

export async function updateUserProfile(
  userId: string,
  data: { name?: string; image?: string }
) {
  try {
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        ...(data.name && { name: data.name }),
        ...(data.image && { image: data.image }),
      },
    });
    return { success: true, user: updatedUser };
  } catch (error) {
    console.error("Error updating user profile:", error);
    throw new Error("Failed to update profile");
  }
}
