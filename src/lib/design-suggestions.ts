"use server";
import prisma from "@/lib/prisma/prisma";
import { Prisma } from "@prisma/client";

export async function getDesignSuggestionBySlug(slug: string) {
  const suggestion = await prisma.designSuggestion.findUnique({
    where: { slug },
  });

  if (!suggestion) {
    throw new Error("Design suggestion not found");
  }

  return suggestion;
}

export async function getRelatedDesigns(
  room: string,
  style: string,
  dimensions: string,
  currentSlug: string
) {
  const relatedDesigns = await prisma.designSuggestion.findMany({
    where: {
      OR: [
        { room: { equals: room, mode: "insensitive" } },
        { style: { equals: style, mode: "insensitive" } },
      ],
      NOT: {
        slug: currentSlug,
      },
    },
    orderBy: {
      createdAt: "desc",
    },
    take: 3,
    select: {
      id: true,
      slug: true,
      room: true,
      style: true,
      dimensions: true,
      outputImage: true,
    },
  });

  return relatedDesigns;
}

export async function getAllDesignSuggestions(params: {
  limit?: number;
  offset?: number;
  room?: string;
  style?: string;
}) {
  const { limit = 10, offset = 0, room, style } = params;

  const where: Prisma.DesignSuggestionWhereInput = {
    ...(room && { room: { equals: room, mode: "insensitive" } }),
    ...(style && { style: { equals: style, mode: "insensitive" } }),
  };

  const [suggestions, totalCount] = await Promise.all([
    prisma.designSuggestion.findMany({
      where,
      take: limit,
      skip: offset,
      orderBy: { createdAt: "desc" },
      select: {
        id: true,
        slug: true,
        room: true,
        style: true,
        dimensions: true,
        outputImage: true,
        inputImage: true,
        metaDescription: true,
        createdAt: true,
      },
    }),
    prisma.designSuggestion.count({ where }),
  ]);

  return { suggestions, totalCount };
}
