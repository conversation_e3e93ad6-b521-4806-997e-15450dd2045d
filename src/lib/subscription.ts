import prisma from "@/lib/prisma/prisma";
import { Subscription } from "@prisma/client";

export async function getUserSubscription(
  userId?: string
): Promise<Subscription | null> {
  if (!userId) return null;

  try {
    const subscription = await prisma.subscription.findUnique({
      where: {
        userId: userId,
      },
    });

    console.log("Debug - Fetched subscription:", subscription);
    return subscription;
  } catch (error) {
    console.error("Error fetching user subscription:", error);
    return null;
  }
}
