const CONSENT_COOKIE_NAME = "cookie-consent";

export interface CookieConsentOptions {
  hasConsented: boolean;
  necessary: boolean;
  analytics: boolean;
  marketing: boolean;
}

export const COOKIE_CATEGORIES = [
  {
    id: "necessary",
    title: "Strictly Necessary",
    description:
      "These cookies are essential for the website to function properly.",
    required: true,
  },
  {
    id: "analytics",
    title: "Analytics",
    description:
      "These cookies help us understand how visitors interact with our website.",
    required: false,
  },
  {
    id: "marketing",
    title: "Marketing",
    description:
      "These cookies are used to deliver advertisements more relevant to you and your interests.",
    required: false,
  },
] as const;

export function getConsentCookie(): CookieConsentOptions | null {
  try {
    const consent = document.cookie
      .split("; ")
      .find((row) => row.startsWith(`${CONSENT_COOKIE_NAME}=`))
      ?.split("=")[1];

    if (!consent) {
      return null;
    }

    return JSON.parse(decodeURIComponent(consent));
  } catch (error) {
    console.error("Error reading cookie consent:", error);
    return null;
  }
}

export function setConsentCookie(consent: CookieConsentOptions): void {
  try {
    const value = encodeURIComponent(JSON.stringify(consent));
    // Set cookie for 1 year
    const expires = new Date(
      Date.now() + 365 * 24 * 60 * 60 * 1000
    ).toUTCString();
    document.cookie = `${CONSENT_COOKIE_NAME}=${value}; expires=${expires}; path=/; SameSite=Lax`;
  } catch (error) {
    console.error("Error setting cookie consent:", error);
  }
}

export function deleteCookieConsent(): void {
  document.cookie = `${CONSENT_COOKIE_NAME}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
}
