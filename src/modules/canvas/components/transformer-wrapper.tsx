import React, { useEffect, useRef } from 'react';
import { Transformer } from 'react-konva';
import Konva from 'konva';
import { KonvaEventObject } from 'konva/lib/Node';
import useImage from 'use-image';
import { ROTATE_ICON } from './canvas-items/canvas-item-types';

// Define consistent styling values as constants
const ANCHOR_SIZE = 14;
const ANCHOR_CORNER_RADIUS = 7;
const PRIMARY_COLOR = '#8A2BE2'; // Purple
const SECONDARY_COLOR = '#FFFFFF'; // White
const BORDER_COLOR = '#9CA3AF'; // Soft gray for borders
const ROTATER_SIZE = 40;
const ROTATER_OFFSET = 20;
const SIDE_HANDLE_WIDTH = 12;
const SIDE_HANDLE_HEIGHT = 48;
const SIDE_HANDLE_RADIUS = 12;

interface TransformerWrapperProps {
  isSelected: boolean;
  nodeRef: React.RefObject<Konva.Group>;
  boundBoxFunc?: (oldBox: any, newBox: any) => any;
  onTransform?: (activeAnchor: string | null, newBox: any) => void;
}

// Use correct Konva event type
type KonvaEvent = KonvaEventObject<Event>;

export const TransformerWrapper: React.FC<TransformerWrapperProps> = ({
  isSelected,
  nodeRef,
  boundBoxFunc,
  onTransform,
}) => {
  const transformerRef = useRef<Konva.Transformer>(null);
  const [rotateIcon] = useImage(ROTATE_ICON);
  // Keep track of the active anchor name
  const activeAnchorRef = useRef<string | null>(null);
  
  // Custom bound box function that detects which anchor is being used
  const handleBoundBoxFunc = (oldBox: any, newBox: any) => {
    // If a custom boundBoxFunc is provided, use it
    if (boundBoxFunc) {
      return boundBoxFunc(oldBox, newBox);
    }
    
    // Get the transformer
    const tr = transformerRef.current;
    if (!tr) return newBox;
    
    // Get the current active anchor name
    const activeAnchor = tr.getActiveAnchor();
    
    // Store the active anchor for reference in events
    activeAnchorRef.current = activeAnchor;
    
    // Handle different anchors
    if (activeAnchor === 'middle-left' || activeAnchor === 'middle-right') {
      // Side handles - ONLY change width, strictly preserve height and y position
      return {
        ...newBox,
        y: oldBox.y,
        height: oldBox.height
      };
    } else if (activeAnchor === 'top-center' || activeAnchor === 'bottom-center') {
      // Top/bottom handles - ONLY change height, strictly preserve width and x position
      return {
        ...newBox,
        x: oldBox.x,
        width: oldBox.width
      };
    }
    
    // For corner handles, strictly maintain aspect ratio
    if (activeAnchor && activeAnchor.includes('top-') || activeAnchor && activeAnchor.includes('bottom-')) {
      const originalRatio = oldBox.width / oldBox.height;
      const newRatio = newBox.width / newBox.height;
      
      if (Math.abs(newRatio - originalRatio) > 0.01) {
        // Determine which dimension changed more
        const widthChange = Math.abs(newBox.width - oldBox.width);
        const heightChange = Math.abs(newBox.height - oldBox.height);
        
        if (widthChange >= heightChange) {
          // Width changed more, adjust height to match
          newBox.height = newBox.width / originalRatio;
        } else {
          // Height changed more, adjust width to match
          newBox.width = newBox.height * originalRatio;
        }
      }
    }
    
    return newBox;
  };
  
  // Setup transformer when selected and rotateIcon is loaded
  useEffect(() => {
    if (isSelected && transformerRef.current && nodeRef.current && rotateIcon) {
      const tr = transformerRef.current;
      
      // Attach transformer to the target node
      tr.nodes([nodeRef.current]);
      
      // Explicitly set the boundBoxFunc
      tr.boundBoxFunc(handleBoundBoxFunc);
      
      // Add data attributes to all anchors for identification
      const anchors = tr.find('.anchor');
      anchors.forEach((anchor) => {
        const name = anchor.name();
        
        // Set a data attribute that our image component can use to detect the anchor
        anchor.setAttr('data-anchor-name', name);
        
        // When an anchor is active, set an attribute to help detect it from DOM
        anchor.on('mousedown touchstart', () => {
          anchor.setAttr('data-anchor-active', 'true');
          activeAnchorRef.current = name;
        });
        
        // Clear the active state on release
        anchor.on('mouseup touchend', () => {
          anchor.setAttr('data-anchor-active', 'false');
        });
      });
      
      // Find the rotater element
      const rotater = tr.findOne('.rotater');
      
      if (rotater) {
        // Set data attributes for the rotater too
        rotater.setAttr('data-anchor-name', 'rotater');
        
        // Add active state handling
        rotater.on('mousedown touchstart', () => {
          rotater.setAttr('data-anchor-active', 'true');
          activeAnchorRef.current = 'rotater';
        });
        
        rotater.on('mouseup touchend', () => {
          rotater.setAttr('data-anchor-active', 'false');
        });
        
        // Generate rotater background
        const iconCanvas = document.createElement('canvas');
        iconCanvas.width = ROTATER_SIZE;
        iconCanvas.height = ROTATER_SIZE;
        
        const ctx = iconCanvas.getContext('2d');
        if (ctx) {
          // Create purple background circle
          ctx.beginPath();
          ctx.arc(ROTATER_SIZE/2, ROTATER_SIZE/2, ROTATER_SIZE/2 - 2, 0, Math.PI * 2);
          ctx.fillStyle = PRIMARY_COLOR;
          ctx.fill();
          ctx.strokeStyle = SECONDARY_COLOR;
          ctx.lineWidth = 2;
          ctx.stroke();
          
          // Create a temporary canvas for the white icon
          const tempCanvas = document.createElement('canvas');
          tempCanvas.width = ROTATER_SIZE;
          tempCanvas.height = ROTATER_SIZE;
          const tempCtx = tempCanvas.getContext('2d');
          
          if (tempCtx) {
            // Draw the original icon
            const padding = ROTATER_SIZE * 0.25;
            tempCtx.drawImage(
              rotateIcon,
              padding,
              padding,
              ROTATER_SIZE - padding * 2,
              ROTATER_SIZE - padding * 2
            );
            
            // Set composite operation to use the icon as a mask
            tempCtx.globalCompositeOperation = 'source-in';
            tempCtx.fillStyle = SECONDARY_COLOR;
            tempCtx.fillRect(0, 0, ROTATER_SIZE, ROTATER_SIZE);
            
            // Draw the white icon onto the main canvas
            ctx.drawImage(tempCanvas, 0, 0);
          }
        }
        
        // Override the transformer's update method
        tr.update = function() {
          // Call the original Konva transformer update
          Konva.Transformer.prototype.update.call(this);
          
          // Find the rotater and apply our custom icon
          const rotater = this.findOne('.rotater');
          if (rotater && rotater instanceof Konva.Shape) {
            // Disable fill by using empty string
            rotater.fill('');
            // Enable icon pattern
            rotater.fillPatternImage(iconCanvas);
          }
          
          // Ensure all anchors have data attributes
          const anchors = this.find('.anchor');
          anchors.forEach((anchor) => {
            const name = anchor.name();
            anchor.setAttr('data-anchor-name', name);
          });
        };
        
        // Add a hook to the transform event
        nodeRef.current.on('transform', (e) => {
          const activeAnchor = activeAnchorRef.current;
          console.log('Transform event with active anchor:', activeAnchor);
          
          // Always set the active anchor attribute on the node
          nodeRef.current?.setAttr('data-active-anchor', activeAnchor);
          
          // For side anchors, maintain specific constraints
          if (activeAnchor === 'middle-left' || activeAnchor === 'middle-right') {
            const node = e.target;
            const scaleX = node.scaleX();
            const scaleY = node.scaleY();
            
            // Force scaleY to remain unchanged during side handle transforms
            if (scaleY !== 1) {
              node.scaleY(1);
            }
            
            // Inform parent of the transform with active anchor info
            if (onTransform) {
              onTransform(activeAnchor, {
                x: node.x(),
                y: node.y(),
                width: node.width() * scaleX,
                height: node.height(),
                scaleX: scaleX,
                scaleY: 1
              });
            }
          } else if (activeAnchor === 'top-center' || activeAnchor === 'bottom-center') {
            const node = e.target;
            const scaleX = node.scaleX();
            const scaleY = node.scaleY();
            
            // Force scaleX to remain unchanged during top/bottom handle transforms
            if (scaleX !== 1) {
              node.scaleX(1);
            }
            
            // Inform parent of the transform with active anchor info
            if (onTransform) {
              onTransform(activeAnchor, {
                x: node.x(),
                y: node.y(),
                width: node.width(),
                height: node.height() * scaleY,
                scaleX: 1,
                scaleY: scaleY
              });
            }
          } else if (onTransform) {
            // Pass the active anchor to the event for other anchors
            onTransform(activeAnchor, {
              x: e.target.x(),
              y: e.target.y(),
              width: e.target.width() * e.target.scaleX(),
              height: e.target.height() * e.target.scaleY(),
              scaleX: e.target.scaleX(),
              scaleY: e.target.scaleY()
            });
          }
        });
        
        // Update and redraw
        tr.update();
        tr.getLayer()?.batchDraw();
      }
    }
    
    // Cleanup when component unmounts
    return () => {
      // Reset the active anchor reference
      activeAnchorRef.current = null;
      
      // Remove transform listener if nodeRef exists
      if (nodeRef.current) {
        nodeRef.current.off('transform');
      }
    };
  }, [isSelected, rotateIcon, boundBoxFunc, nodeRef, handleBoundBoxFunc, onTransform]);
  
  if (!isSelected) return null;
  
  return (
    <Transformer
      ref={transformerRef}
      boundBoxFunc={handleBoundBoxFunc}
      
      // Anchor configuration - include all resize anchors
      enabledAnchors={[
        'top-left', 'top-center', 'top-right',
        'middle-left', 'middle-right',
        'bottom-left', 'bottom-center', 'bottom-right'
      ]}
      anchorSize={ANCHOR_SIZE}
      anchorCornerRadius={ANCHOR_CORNER_RADIUS}
      anchorStroke={SECONDARY_COLOR}
      anchorStrokeWidth={2}
      anchorFill={SECONDARY_COLOR}
      
      // Border configuration
      borderStroke={PRIMARY_COLOR}
      borderStrokeWidth={2}
      borderDash={[]}
      
      // Rotation configuration
      rotateEnabled={true}
      rotationSnaps={[0, 90, 180, 270]}
      rotationSnapTolerance={5}
      rotateAnchorOffset={40}
      rotateLineVisible={false}
      rotateLineDash={[2, 2]}
      rotateLineStroke={SECONDARY_COLOR}
      rotateAnchorCursor="grab"
      
      // Resize configuration
      resizeEnabled={true}
      keepRatio={false}  // We'll handle aspect ratio in our boundBoxFunc instead
      centeredScaling={false}
      shouldOverdrawWholeArea={true}
      
      anchorStyleFunc={(anchor) => {
        const name = anchor.name();
        
        // Set data attributes for anchor identification
        anchor.setAttr('data-anchor-name', name);
        
        // Only style the resize anchors here, the rotater will be handled separately
        if (!name.includes('rotater')) {
          // Corner anchors - white circles with soft gray border
          if (name.includes('top-') || name.includes('bottom-')) {
            if (name.includes('center')) {
              // Top and bottom middle handles - horizontal rectangles
              anchor.fill(SECONDARY_COLOR);
              anchor.stroke(BORDER_COLOR);
              anchor.strokeWidth(1.5);
              anchor.shadowColor(BORDER_COLOR);
              anchor.shadowBlur(3);
              anchor.shadowOpacity(0.2);
              anchor.width(SIDE_HANDLE_HEIGHT); // Using height as width for horizontal handles
              anchor.height(SIDE_HANDLE_WIDTH); // Using width as height for horizontal handles
              anchor.offsetX(SIDE_HANDLE_HEIGHT / 2);
              anchor.offsetY(SIDE_HANDLE_WIDTH / 2);
              anchor.cornerRadius(SIDE_HANDLE_RADIUS);
              
              // Set cursor
              anchor.setAttr('cursor', 'ns-resize');
            } else {
              // Corner handles - circles
              anchor.fill(SECONDARY_COLOR);
              anchor.cornerRadius(9);
              anchor.stroke(BORDER_COLOR);
              anchor.strokeWidth(1.5);
              anchor.shadowColor(BORDER_COLOR);
              anchor.shadowBlur(3);
              anchor.shadowOpacity(0.2);
              
              // Set corner cursors
              if (name === 'top-left' || name === 'bottom-right') {
                anchor.setAttr('cursor', 'nwse-resize');
              } else {
                anchor.setAttr('cursor', 'nesw-resize');
              }
            }
          }
          // Middle anchors - vertical rectangles
          else if (name.includes('middle-')) {
            anchor.fill(SECONDARY_COLOR);
            anchor.stroke(BORDER_COLOR);
            anchor.strokeWidth(1.5);
            anchor.shadowColor(BORDER_COLOR);
            anchor.shadowBlur(3);
            anchor.shadowOpacity(0.2);
            anchor.width(SIDE_HANDLE_WIDTH);
            anchor.height(SIDE_HANDLE_HEIGHT);
            anchor.offsetX(SIDE_HANDLE_WIDTH / 2);
            anchor.offsetY(SIDE_HANDLE_HEIGHT / 2);
            anchor.cornerRadius(SIDE_HANDLE_RADIUS);
            
            // Set cursor
            anchor.setAttr('cursor', 'ew-resize');
          }
        } else {
          // For the rotater, set consistent size and appearance
          anchor.width(ROTATER_SIZE);
          anchor.height(ROTATER_SIZE);
          anchor.offsetX(ROTATER_OFFSET);
          anchor.offsetY(ROTATER_OFFSET);
          anchor.cornerRadius(ROTATER_SIZE / 2);
          anchor.fill(SECONDARY_COLOR);
          anchor.strokeWidth(0);
          anchor.setAttr('cursor', 'grab');
        }
      }}
    />
  );
}; 