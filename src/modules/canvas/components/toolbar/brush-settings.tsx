"use client";
import React from "react";
import { Slider } from "@/modules/ui/slider";
import useCanvasStore from "@/modules/dashboard/main-features/store/canvas-store";
import { Card, CardContent } from "@/modules/ui/card";

export interface BrushSettingsProps {
  inline?: boolean;
}

export const BrushSettings: React.FC<BrushSettingsProps> = ({ inline = false }) => {
  const { brushSize, setBrushSize } = useCanvasStore();

  // Simplified inline mode: a small card with only a brush size slider.
  if (inline) {
    return (
      <Card className="mx-auto w-40 bg-white dark:bg-gray-800 p-2">
        <CardContent className="py-2 justify-center items-center space-y-2">
          <Slider
            value={[brushSize]}
            onValueChange={(value) => setBrushSize(value[0])}
            min={1}
            max={200}
            step={1}
            className="w-full max-w-xs"
          />
        </CardContent>
      </Card>
    );
  }

  // Non-inline mode can be expanded if needed.
  return null;
};