"use client";

import React, { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/modules/ui/button";
import { cn } from "@/lib/utils";
import {
  computeAspectRatio,
  isValidResolution,
  formatResolution,
  getOrientation,
  findClosestValidResolution,
  getValidResolutions
} from "@/modules/canvas/utils/canvas-utils";


export interface ImageDimensionsCardProps {
  onClose: () => void;
  width: number;
  height: number;
  initialAspectRatio?: string;
  initialCustomMode?: boolean;
  onChange: (
    width: number,
    height: number,
    orientation: "landscape" | "portrait",
    customResolutionEnabled: boolean,
    aspectRatio: string
  ) => void;
}

// Get valid resolutions at component level by importing from canvas-utils
// This avoids duplicating the list in multiple places
const getValidResolutionsFromUtils = (): string[] => {
  return getValidResolutions();
};

interface AspectRatioOption {
  label: string;
  value: string;
  ratio: [number, number]; // e.g. [1, 3] for 1:3
}

// Pre-set aspect ratio options for portrait and landscape
const portraitAspectRatios: AspectRatioOption[] = [
  { label: "1:3", value: "1:3", ratio: [1, 3] },
  { label: "1:2", value: "1:2", ratio: [1, 2] },
  { label: "9:16", value: "9:16", ratio: [9, 16] },
  { label: "10:16", value: "10:16", ratio: [10, 16] },
  { label: "2:3", value: "2:3", ratio: [2, 3] },
  { label: "3:4", value: "3:4", ratio: [3, 4] },
  { label: "4:5", value: "4:5", ratio: [4, 5] },
];

const landscapeAspectRatios: AspectRatioOption[] = [
  { label: "3:1", value: "3:1", ratio: [3, 1] },
  { label: "2:1", value: "2:1", ratio: [2, 1] },
  { label: "16:9", value: "16:9", ratio: [16, 9] },
  { label: "16:10", value: "16:10", ratio: [16, 10] },
  { label: "3:2", value: "3:2", ratio: [3, 2] },
  { label: "4:3", value: "4:3", ratio: [4, 3] },
  { label: "5:4", value: "5:4", ratio: [5, 4] },
];

const additionalAspectRatios: AspectRatioOption[] = [
  { label: "1:1", value: "1:1", ratio: [1, 1] },
  { label: "21:11", value: "21:11", ratio: [21, 11] },
];

// Combine all aspect ratios for easier lookups
const allAspectRatios = [...portraitAspectRatios, ...landscapeAspectRatios, ...additionalAspectRatios];

// Update component to use shared utilities
export const ImageDimensionsCard: React.FC<ImageDimensionsCardProps> = ({
  onClose,
  width: initialWidth,
  height: initialHeight,
  initialAspectRatio = "16:10",
  initialCustomMode = false,
  onChange,
}) => {
  // Define validResolutions list using the utility function
  const validResolutions: string[] = getValidResolutionsFromUtils();

  // Consolidated common resolution groups
  const resolutionGroups = {
    square: ["1024x1024"],
    landscape: [
      "1408x704", "1312x736", "1280x800", "1248x832", "1152x864", 
      "1536x512", "1088x768", "1024x832", "1024x896", "1024x960"
    ],
    portrait: [
      "704x1408", "736x1312", "800x1280", "832x1248", "864x1152", 
      "512x1536", "768x1088", "832x1024", "896x1024", "960x1024"
    ]
  };

  // Retrieve stored aspect ratio from localStorage if available
  const storedAspectRatio = React.useMemo(() => {
    try {
      return localStorage.getItem('lastAspectRatio') || initialAspectRatio;
    } catch (e) {
      console.warn('Error accessing localStorage:', e);
      return initialAspectRatio;
    }
  }, [initialAspectRatio]);
  
  const [customMode, setCustomMode] = useState<boolean>(initialCustomMode);
  
  // Find the initial aspect ratio option from our presets
  const findAspectRatioOption = (ratio: string): AspectRatioOption | null => 
    allAspectRatios.find(option => option.value === ratio) || null;

  const [selectedAspect, setSelectedAspect] = useState<AspectRatioOption | null>(() => {
    // First try using stored aspect ratio
    const found = findAspectRatioOption(storedAspectRatio);
    if (found) return found;
    
    // Next, try using computed ratio from width/height
    if (initialWidth && initialHeight) {
      const computedRatio = computeAspectRatio(initialWidth, initialHeight);
      const computedAspect = findAspectRatioOption(computedRatio);
      if (computedAspect) return computedAspect;
    }
    
    // If we can't find the ratio in our presets, default to custom mode
    setCustomMode(true);
    return landscapeAspectRatios.find((r) => r.value === "16:10") || null;
  });

  // Initialize dimensions
  const [width, setWidth] = useState(initialWidth || 512);
  const [height, setHeight] = useState(initialHeight || 288);

  // Initialize orientation
  const [orientation, setOrientation] = useState<"landscape" | "portrait">(
    getOrientation(initialWidth || 512, initialHeight || 288)
  );

  // Get valid resolutions matching the selected aspect ratio
  const getValidResolutionsForAspectRatio = (aspectRatio: [number, number]): string[] => {
    const tolerance = 0.1; 
    const targetRatio = aspectRatio[0] / aspectRatio[1];
    
    return validResolutions.filter(res => {
      const [w, h] = res.split('x').map(Number);
      const ratio = w / h;
      return Math.abs(ratio - targetRatio) / targetRatio < tolerance;
    }).sort((a, b) => {
      const [w1, h1] = a.split('x').map(Number);
      const [w2, h2] = b.split('x').map(Number);
      return (w1 * h1) - (w2 * h2);
    });
  };

  const [validResolutionsForAspect, setValidResolutionsForAspect] = useState<string[]>([]);

  // Update valid resolutions when aspect ratio changes
  useEffect(() => {
    if (selectedAspect) {
      const resolutions = getValidResolutionsForAspectRatio(selectedAspect.ratio);
      setValidResolutionsForAspect(resolutions);
    }
  }, [selectedAspect]);

  // Simplified handler for selecting a valid resolution
  const handleSelectValidResolution = (resolution: string) => {
    const [w, h] = resolution.split('x').map(Number);
    setWidth(w);
    setHeight(h);
    setOrientation(getOrientation(w, h));
    setCustomMode(false);
  };

  // Consolidated handler for updating dimensions when a preset aspect is chosen
  const updateDimensionsForAspect = (aspect: AspectRatioOption) => {
    setSelectedAspect(aspect);
    
    const [w, h] = aspect.ratio;
    let newWidth, newHeight;
    
    // Determine new dimensions based on current size while maintaining aspect ratio
    if (width >= height) {
      newWidth = Math.min(Math.max(width, 512), 1536);
      newHeight = Math.round(newWidth * h / w);
      
      if (newHeight < 512 || newHeight > 1536) {
        newHeight = Math.min(Math.max(512, newHeight), 1536);
        newWidth = Math.round(newHeight * w / h);
      }
    } else {
      newHeight = Math.min(Math.max(height, 512), 1536);
      newWidth = Math.round(newHeight * w / h);
      
      if (newWidth < 512 || newWidth > 1536) {
        newWidth = Math.min(Math.max(512, newWidth), 1536);
        newHeight = Math.round(newWidth * h / w);
      }
    }
    
    // Find closest valid resolution
    const resolutionString = `${newWidth}x${newHeight}`;
    if (!isValidResolution(resolutionString)) {
      const closestValidRes = findClosestValidResolution(newWidth, newHeight);
      if (closestValidRes !== "None") {
        const [closestWidth, closestHeight] = closestValidRes.split('x').map(Number);
        newWidth = closestWidth;
        newHeight = closestHeight;
      }
    }
    
    // Update state
    setWidth(newWidth);
    setHeight(newHeight);
    setCustomMode(false);
    setOrientation(getOrientation(newWidth, newHeight));
  };

  // Simplified handlers for dimension changes
  const handleWidthInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newWidth = parseInt(e.target.value);
    if (isNaN(newWidth) || newWidth < 512 || newWidth > 1536) return;

    if (customMode) {
      setWidth(newWidth);
      const resolutionString = `${newWidth}x${height}`;
      if (!isValidResolution(resolutionString)) {
        const closestRes = findClosestValidResolution(newWidth, height);
        if (closestRes !== "None") {
          const [validWidth, validHeight] = closestRes.split('x').map(Number);
          setWidth(validWidth);
          setHeight(validHeight);
        }
      }
      return;
    }

    if (selectedAspect) {
      const [w, h] = selectedAspect.ratio;
      const newHeight = Math.round(newWidth * h / w);
      
      const resolutionString = `${newWidth}x${newHeight}`;
      if (isValidResolution(resolutionString)) {
        setWidth(newWidth);
        setHeight(newHeight);
      } else {
        const closestRes = findClosestValidResolution(newWidth, newHeight);
        if (closestRes !== "None") {
          const [validWidth, validHeight] = closestRes.split('x').map(Number);
          setWidth(validWidth);
          setHeight(validHeight);
        }
      }
      setOrientation(getOrientation(newWidth, newHeight));
    }
  };

  const handleHeightInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newHeight = parseInt(e.target.value);
    if (isNaN(newHeight) || newHeight < 512 || newHeight > 1536) return;

    if (customMode) {
      setHeight(newHeight);
      const resolutionString = `${width}x${newHeight}`;
      if (!isValidResolution(resolutionString)) {
        const closestRes = findClosestValidResolution(width, newHeight);
        if (closestRes !== "None") {
          const [validWidth, validHeight] = closestRes.split('x').map(Number);
          setWidth(validWidth);
          setHeight(validHeight);
        }
      }
      return;
    }

    if (selectedAspect) {
      const [w, h] = selectedAspect.ratio;
      const newWidth = Math.round(newHeight * w / h);
      
      const resolutionString = `${newWidth}x${newHeight}`;
      if (isValidResolution(resolutionString)) {
        setWidth(newWidth);
        setHeight(newHeight);
      } else {
        const closestRes = findClosestValidResolution(newWidth, newHeight);
        if (closestRes !== "None") {
          const [validWidth, validHeight] = closestRes.split('x').map(Number);
          setWidth(validWidth);
          setHeight(validHeight);
        }
      }
      setOrientation(getOrientation(newWidth, newHeight));
    }
  };

  // Compute preview aspect ratio for the left panel preview box
  const previewAspectRatio = 
    !customMode && selectedAspect
      ? selectedAspect.ratio[0] / selectedAspect.ratio[1]
      : width / height || 1.77;

  // Update orientation when dimensions change
  useEffect(() => {
    setOrientation(getOrientation(width, height));
  }, [width, height]);

  // Simplified handleSave function
  const handleSave = () => {
    const resolutionString = formatResolution(width, height);
    const isValidRes = isValidResolution(resolutionString);
    
    let finalAspectRatio: string;
    
    if (selectedAspect) {
      finalAspectRatio = selectedAspect.value;
    } else {
      finalAspectRatio = computeAspectRatio(width, height);
      
      const matchingAspect = findAspectRatioOption(finalAspectRatio);
      if (matchingAspect) {
        finalAspectRatio = matchingAspect.value;
      }
    }
    
    const actualCustomMode = !isValidRes || customMode;
    
    onChange(
      width, 
      height, 
      orientation,
      actualCustomMode, 
      finalAspectRatio
    );
    
    onClose();
  };

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 z-[100] h-[100dvh] flex items-start justify-center p-4 overflow-y-auto"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        style={{ maxHeight: "100vh", paddingTop: "5vh" }}
      >
        <motion.div
          className="bg-background rounded-lg shadow-md w-full max-w-4xl p-4 relative border border-border"
          role="dialog"
          aria-modal="true"
          aria-labelledby="image-dimensions-modal-title"
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -20, opacity: 0 }}
          transition={{ duration: 0.2 }}
          style={{ marginBottom: "5vh" }}
        >
          <h3
            id="image-dimensions-modal-title"
            className="text-xl font-medium text-foreground mb-4"
          >
            Image Dimensions
          </h3>
          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-3 right-3 text-muted-foreground hover:text-foreground focus:outline-none"
            aria-label="Close"
          >
            ✕
          </button>
          <div className="flex flex-col md:flex-row gap-4">
            {/* Left Panel: Preview, Slider, and Width/Height Fields */}
            <div className="flex-1 flex flex-col gap-3">
              <div className="flex items-center justify-center bg-background border rounded-md shadow-sm p-3">
                <div
                  className="w-full relative border border-border rounded-sm"
                  style={{
                    aspectRatio: previewAspectRatio,
                    backgroundColor: "var(--background)"
                  }}
                >
                  <div className="absolute inset-0 flex items-center justify-center text-sm font-medium text-muted-foreground">
                    {!customMode && selectedAspect
                      ? `${selectedAspect.label} (${width}x${height})`
                      : `Custom (${width}x${height})`}
                  </div>
                </div>
              </div>
              
              <div>
                <label className="block text-xs text-muted-foreground mb-1">
                  Current Resolution
                </label>
                <div className="flex items-center gap-2">
                  <div className="text-sm font-medium text-foreground">
                    {width}x{height}
                  </div>
                  {!isValidResolution(`${width}x${height}`) && (
                    <span className="bg-amber-100 text-amber-700 text-xs px-2 py-0.5 rounded-full">
                      Invalid resolution
                    </span>
                  )}
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs text-muted-foreground mb-1">
                    Width
                  </label>
                  <select
                    value={width}
                    onChange={(e) => {
                      const newWidth = parseInt(e.target.value);
                      if (customMode) {
                        setWidth(newWidth);
                        // Try to find a valid resolution with this width
                        const possibleResolutions = validResolutions.filter(r => r.startsWith(`${newWidth}x`));
                        if (possibleResolutions.length > 0) {
                          const [w, h] = possibleResolutions[0].split('x').map(Number);
                          setHeight(h);
                        }
                      } else if (selectedAspect) {
                        // In aspect ratio mode, adjust height according to ratio
                        const [w, h] = selectedAspect.ratio;
                        const newHeight = Math.round(newWidth * h / w);
                        const resString = `${newWidth}x${newHeight}`;
                        
                        if (isValidResolution(resString)) {
                          setWidth(newWidth);
                          setHeight(newHeight);
                        } else {
                          // Find closest valid resolution
                          const closestRes = findClosestValidResolution(newWidth, newHeight);
                          if (closestRes !== "None") {
                            const [validW, validH] = closestRes.split('x').map(Number);
                            setWidth(validW);
                            setHeight(validH);
                          }
                        }
                      }
                    }}
                    className="w-full h-9 rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm focus:outline-none focus:ring-1 focus:ring-primary"
                  >
                    {Array.from(new Set(validResolutions.map((r: string) => r.split('x')[0]))).sort((a, b) => parseInt(a) - parseInt(b)).map((w: string) => (
                      <option key={w} value={w}>{w}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-xs text-muted-foreground mb-1">
                    Height
                  </label>
                  <select
                    value={height}
                    onChange={(e) => {
                      const newHeight = parseInt(e.target.value);
                      if (customMode) {
                        setHeight(newHeight);
                        // Try to find a valid resolution with this height
                        const possibleResolutions = validResolutions.filter((r: string) => r.endsWith(`x${newHeight}`));
                        if (possibleResolutions.length > 0) {
                          const [w, h] = possibleResolutions[0].split('x').map(Number);
                          setWidth(w);
                        }
                      } else if (selectedAspect) {
                        // In aspect ratio mode, adjust width according to ratio
                        const [w, h] = selectedAspect.ratio;
                        const newWidth = Math.round(newHeight * w / h);
                        const resString = `${newWidth}x${newHeight}`;
                        
                        if (isValidResolution(resString)) {
                          setWidth(newWidth);
                          setHeight(newHeight);
                        } else {
                          // Find closest valid resolution
                          const closestRes = findClosestValidResolution(newWidth, newHeight);
                          if (closestRes !== "None") {
                            const [validW, validH] = closestRes.split('x').map(Number);
                            setWidth(validW);
                            setHeight(validH);
                          }
                        }
                      }
                    }}
                    className="w-full h-9 rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm focus:outline-none focus:ring-1 focus:ring-primary"
                  >
                    {Array.from(new Set(validResolutions.map((r: string) => r.split('x')[1]))).sort((a, b) => parseInt(a) - parseInt(b)).map((h: string) => (
                      <option key={h} value={h}>{h}</option>
                    ))}
                  </select>
                </div>
              </div>
              
              {(validResolutionsForAspect.length > 0 || customMode) && (
                <div className="mt-1">
                  <label className="block text-xs text-muted-foreground mb-1">
                    {customMode ? "Recommended resolutions:" : "Valid resolutions for this aspect ratio:"}
                  </label>
                  <div className="flex flex-wrap gap-1 max-h-36 overflow-y-auto p-2 bg-muted/30 rounded-md border border-border">
                    {customMode ? (
                      // In custom mode, show all resolutions grouped by category
                      <>
                        <div className="w-full text-xs font-medium text-muted-foreground mb-1 pb-1 border-b border-border">Square</div>
                        {resolutionGroups.square.map(resolution => (
                          <button
                            key={resolution}
                            className={cn(
                              "text-xs px-2 py-0.5 rounded-sm border",
                              `${width}x${height}` === resolution 
                                ? "bg-primary/20 border-primary/30 text-primary" 
                                : "border-border text-muted-foreground hover:bg-muted/50"
                            )}
                            onClick={() => handleSelectValidResolution(resolution)}
                          >
                            {resolution}
                          </button>
                        ))}

                        <div className="w-full text-xs font-medium text-muted-foreground my-1 py-1 border-b border-border">Landscape</div>
                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-1 w-full">
                          {resolutionGroups.landscape.map(resolution => (
                            <button
                              key={resolution}
                              className={cn(
                                "text-xs px-2 py-0.5 rounded-sm border",
                                `${width}x${height}` === resolution 
                                  ? "bg-primary/20 border-primary/30 text-primary" 
                                  : "border-border text-muted-foreground hover:bg-muted/50"
                              )}
                              onClick={() => handleSelectValidResolution(resolution)}
                            >
                              {resolution}
                            </button>
                          ))}
                        </div>

                        <div className="w-full text-xs font-medium text-muted-foreground my-1 py-1 border-b border-border">Portrait</div>
                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-1 w-full">
                          {resolutionGroups.portrait.map(resolution => (
                            <button
                              key={resolution}
                              className={cn(
                                "text-xs px-2 py-0.5 rounded-sm border",
                                `${width}x${height}` === resolution 
                                  ? "bg-primary/20 border-primary/30 text-primary" 
                                  : "border-border text-muted-foreground hover:bg-muted/50"
                              )}
                              onClick={() => handleSelectValidResolution(resolution)}
                            >
                              {resolution}
                            </button>
                          ))}
                        </div>
                      </>
                    ) : (
                      // In preset mode, show valid resolutions for the selected aspect ratio
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-1 w-full">
                        {validResolutionsForAspect.map(resolution => (
                          <button
                            key={resolution}
                            className={cn(
                              "text-xs px-2 py-0.5 rounded-sm border",
                              `${width}x${height}` === resolution 
                                ? "bg-primary/20 border-primary/30 text-primary" 
                                : "border-border text-muted-foreground hover:bg-muted/50"
                            )}
                            onClick={() => handleSelectValidResolution(resolution)}
                          >
                            {resolution}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
            {/* Right Panel: Aspect Ratio Selection */}
            <div className="w-full md:w-1/2 flex flex-col gap-3">
              <div>
                <h4 className="text-xs font-semibold text-muted-foreground mb-1">Portrait</h4>
                <div className="grid grid-cols-3 gap-1">
                  {portraitAspectRatios.map((option) => (
                    <Button
                      key={option.value}
                      variant={!customMode && selectedAspect && selectedAspect.value === option.value ? "secondary" : "outline"}
                      size="sm"
                      onClick={() => {
                        setCustomMode(false);
                        setSelectedAspect(option);
                        updateDimensionsForAspect(option);
                      }}
                      className="h-8 text-xs"
                    >
                      {option.label}
                    </Button>
                  ))}
                </div>
              </div>
              <div>
                <h4 className="text-xs font-semibold text-muted-foreground mb-1">Landscape</h4>
                <div className="grid grid-cols-3 gap-1">
                  {landscapeAspectRatios.map((option) => (
                    <Button
                      key={option.value}
                      variant={!customMode && selectedAspect && selectedAspect.value === option.value ? "secondary" : "outline"}
                      size="sm"
                      onClick={() => {
                        setCustomMode(false);
                        setSelectedAspect(option);
                        updateDimensionsForAspect(option);
                      }}
                      className="h-8 text-xs"
                    >
                      {option.label}
                    </Button>
                  ))}
                </div>
              </div>
              <div className="flex gap-1">
                <Button
                  variant={!customMode && selectedAspect && selectedAspect.value === "1:1" ? "secondary" : "outline"}
                  size="sm"
                  onClick={() => {
                    setCustomMode(false);
                    const squareOption: AspectRatioOption = {
                      label: "1:1",
                      value: "1:1",
                      ratio: [1, 1],
                    };
                    setSelectedAspect(squareOption);
                    updateDimensionsForAspect(squareOption);
                  }}
                  className="h-8 text-xs"
                >
                  1:1 (Square)
                </Button>
                <Button
                  variant={customMode ? "secondary" : "outline"}
                  size="sm"
                  onClick={() => {
                    setCustomMode(true);
                    setSelectedAspect(null);
                  }}
                  className="h-8 text-xs"
                >
                  Custom
                </Button>
              </div>
            </div>
          </div>
          <div className="mt-4 flex justify-end gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onClose}
              className="h-8"
            >
              Cancel
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={handleSave}
              className="h-8"
            >
              Save
            </Button>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};
