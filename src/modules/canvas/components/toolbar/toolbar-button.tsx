import React from "react";
import { <PERSON><PERSON> } from "@/modules/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/modules/ui/tooltip";
import { cn } from "@/lib/utils";
import { ToolbarButtonProps } from "@/modules/canvas/types/canvas";

export const ToolbarButton: React.FC<ToolbarButtonProps> = ({
  onClick,
  icon,
  label,
  active,
  disabled,
  variant = "ghost",
  className,
  hideLabel,
  size = "default",
  disableActiveBackground,
}) => {
  const buttonSizes = {
    sm: "w-8 h-8",
    default: "w-10 h-10",
    wide: "w-20 h-14",
  };

  const iconSizes = {
    sm: "h-4 w-4",
    default: "h-5 w-5",
    wide: "h-4 w-4",
  };

  const labelSizes = {
    sm: "text-[10px]",
    default: "text-xs",
    wide: "text-[10px]",
  };

  return (
    <TooltipProvider delayDuration={300}>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            aria-label={label}
            variant={active ? "secondary" : variant}
            size="sm"
            onClick={onClick}
            disabled={disabled}
            className={cn(
              buttonSizes[size],
              "rounded-sm shadow-sm transition-transform duration-150 hover:scale-105 focus:outline-none",
              className,
              active &&
                !disableActiveBackground &&
                "bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300",
              !disabled && !active && "hover:bg-gray-100 dark:hover:bg-gray-800"
            )}
          >
            <div className="flex flex-col items-center justify-center gap-1">
              {React.cloneElement(icon as React.ReactElement, {
                className: cn(
                  iconSizes[size],
                  "transition-colors duration-150",
                  active
                    ? "text-purple-700 dark:text-purple-300"
                    : "text-gray-700 dark:text-gray-300"
                ),
              })}
              {!hideLabel && (
                <span
                  className={cn(
                    labelSizes[size],
                    "text-gray-700 dark:text-gray-300"
                  )}
                >
                  {label}
                </span>
              )}
            </div>
          </Button>
        </TooltipTrigger>
        <TooltipContent side="bottom" className="text-xs">
          <p>{label}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
