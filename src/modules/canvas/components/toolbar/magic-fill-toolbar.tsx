import React, { useState } from "react";
import { Square, Lasso, Paintbrush, ChevronDown, Eraser, Download } from "lucide-react";
import { Button } from "@/modules/ui/button";
import { MaskToolMode } from "@/modules/canvas/types/canvas";
import { ToolbarButton } from "./toolbar-button";
import { BrushSettings } from "./brush-settings";
import useCanvasStore from "@/modules/canvas/store/canvas-store";

interface MagicFillToolbarProps {
  activeMaskTool: MaskToolMode;
  onMaskToolChange: (tool: MaskToolMode) => void;
  onInvert: () => void;
  onCancelMagicFill: () => void;
  onProceedMagicFill: () => void;
  onExportSelection?: () => void;
}

export const MagicFillToolbar: React.FC<MagicFillToolbarProps> = ({
  activeMaskTool,
  onMaskToolChange,
  onInvert,
  onCancelMagicFill,
  onProceedMagicFill,
  onExportSelection,
}) => {
  const [showBrushSettings, setShowBrushSettings] = useState(false);
  const store = useCanvasStore();

  return (
    <div className="fixed top-4 left-0 right-0 mx-auto z-50 w-full max-w-4xl min-w-3xl flex justify-center">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg backdrop-blur-sm border border-gray-300 dark:border-gray-700 p-4 w-full">
        <div className="flex items-center gap-6">
          <div className="text-sm text-gray-800 dark:text-gray-100 whitespace-nowrap">
            Magic fill: Draw over parts of the image you want to change
          </div>
          <div className="flex items-center gap-2">
            <ToolbarButton
              hideLabel={true}
              icon={<Square />}
              label="Rectangle Mask"
              active={activeMaskTool === "rectangle"}
              onClick={() => onMaskToolChange("rectangle")}
              size="sm"
            />
            <ToolbarButton
              hideLabel={true}
              icon={<Lasso />}
              label="Lasso Mask"
              active={activeMaskTool === "lasso"}
              onClick={() => onMaskToolChange("lasso")}
              size="sm"
            />
            <ToolbarButton
              hideLabel={true}
              icon={<Paintbrush />}
              label="Brush Mask"
              active={activeMaskTool === "brush"}
              onClick={() => onMaskToolChange("brush")}
              size="sm"
            />
            <div className="relative">
              <ToolbarButton
                hideLabel={true}
                icon={<ChevronDown />}
                label="Brush Settings"
                active={showBrushSettings}
                onClick={() => setShowBrushSettings(!showBrushSettings)}
                size="sm"
              />
              {showBrushSettings && (
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-4 z-50">
                  <BrushSettings inline={true} />
                </div>
              )}
            </div>
            <ToolbarButton
              hideLabel={true}
              icon={<Eraser />}
              label="Erase Mask"
              active={activeMaskTool === "eraser"}
              onClick={() => onMaskToolChange("eraser")}
              size="sm"
            />
          </div>
          <div className="ml-auto flex items-center gap-2">
            <div className="border-l border-gray-300 dark:border-gray-700 h-8 mx-2"></div>
         
            <Button variant="secondary" size="sm" onClick={onCancelMagicFill}>
              Cancel
            </Button>
            <Button
              variant="gradient-primary"
              size="sm"
              onClick={onProceedMagicFill}
            >
              Next
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
