"use client";

import React, { useEffect } from "react";
import {
  Download,
  <PERSON>,
  MousePointer,
  Save,
  Sparkles,
  Upload,
  Wand2,
} from "lucide-react";
import { CanvasMode, MaskToolMode } from "@/modules/canvas/types/canvas";

import useCanvasStore from "@/modules/canvas/store/canvas-store";
import { ToolbarButton } from "./toolbar-button";
import { MagicFillToolbar } from "./magic-fill-toolbar";
import { Logo } from "@/modules/marketing/components/layout/header/logo";
import Link from "next/link";
import { cn } from "@/modules/ui";

interface MainToolbarProps {
  mode: CanvasMode;
  onModeChange: (mode: CanvasMode) => void;
  onGenerate: () => Promise<void>;
  onUpload: () => void;
  onUndo?: () => void;
  onRedo?: () => void;
  onSave?: () => void;
  onDownload?: () => void;
  canUndo?: boolean;
  canRedo?: boolean;
}

export const MainToolbar: React.FC<MainToolbarProps> = ({
  mode,
  onModeChange,
  onUpload,
  onSave,
  onDownload,
}) => {
  const {
    maskTool,
    setMaskTool,
    invertMask,
    clearLines,
    setMagicFillAreaSelection,
    setSelectionArea,
    setAspectRatio,
    aspectRatio,
    inputImage,
    inputImagePosition,
    inputImageDimensions,
    generatedImages,
    selectionArea,
  } = useCanvasStore();

  // Provide a safe no-op if invertMask is undefined.
  const safeInvertMask = invertMask || (() => {});

  // Create a wrapper function to handle mode changes while preserving aspect ratio
  const handleModeChange = (newMode: CanvasMode) => {
    // Save the current aspect ratio before mode change
    const currentAspectRatio = aspectRatio;
    console.log('[MAIN TOOLBAR] Changing mode to:', newMode, 'with current aspect ratio:', currentAspectRatio);
    
    // For magic fill, we need special handling
    if (newMode === CanvasMode.MagicFill) {
      // Change the mode first, without changing aspect ratio yet
      // This is because magic fill will determine aspect ratio from the selection
      onModeChange(newMode);
      
      // Clear any existing magic fill selections
      setMagicFillAreaSelection(false);
      clearLines();
      
      console.log('[MAIN TOOLBAR] Entered Magic Fill mode, preserving current aspect ratio:', currentAspectRatio);
      return;
    }
    
    // For other modes, use the standard approach with validation
    // IMPORTANT: Ensure we have a valid aspect ratio
    const validAspectRatios = ["1:1", "16:9", "9:16", "4:3", "3:4", "3:2", "2:3", "16:10", "10:16", "3:1", "1:3"];
    
    // If we don't have a valid aspect ratio, default to a reasonable one
    let aspectRatioToUse = currentAspectRatio;
    if (currentAspectRatio === "None" || !validAspectRatios.includes(currentAspectRatio)) {
      aspectRatioToUse = "16:9"; // Default to 16:9 if no valid ratio
      console.log('[MAIN TOOLBAR] No valid aspect ratio, defaulting to 16:9');
      
      // Update the store immediately with the default
      setAspectRatio(aspectRatioToUse);
    }
    
    // CRITICAL: Set aspect ratio before mode change to ensure it's preserved
    if (aspectRatioToUse !== "None") {
      console.log('[MAIN TOOLBAR] Setting aspect ratio before mode change:', aspectRatioToUse);
      setAspectRatio(aspectRatioToUse);
    }
    
    // Change the mode
    onModeChange(newMode);
  };

  // Enhance the handleGenerate function to explicitly preserve aspect ratio
  const handleGenerate = () => {
    // Save the current aspect ratio before mode change
    const currentAspectRatio = aspectRatio;
    console.log('[MAIN TOOLBAR] Starting generation with aspect ratio:', currentAspectRatio);
    
    // IMPORTANT: Ensure we have a valid aspect ratio
    const validAspectRatios = ["1:1", "16:9", "9:16", "4:3", "3:4", "3:2", "2:3", "16:10", "10:16", "3:1", "1:3"];
    
    // If we don't have a valid aspect ratio, default to a reasonable one
    let aspectRatioToUse = currentAspectRatio;
    if (currentAspectRatio === "None" || !validAspectRatios.includes(currentAspectRatio)) {
      aspectRatioToUse = "16:9"; // Default to 16:9 if no valid ratio
      console.log('[MAIN TOOLBAR] No valid aspect ratio for generation, defaulting to 16:9');
      
      // Update the store immediately with the default
      setAspectRatio(aspectRatioToUse);
    }
    
    // CRITICAL: Set aspect ratio before mode change to ensure it's preserved
    console.log('[MAIN TOOLBAR] Setting aspect ratio before generation:', aspectRatioToUse);
    setAspectRatio(aspectRatioToUse);
    
    // Change to generate mode and only clear magic fill selection
    onModeChange(CanvasMode.Generate);
    setMagicFillAreaSelection(false);
  };

  useEffect(() => {
    if (mode !== CanvasMode.MagicFill) {
      setMaskTool("brush");
    }
  }, [mode, setMaskTool]);

  return (
    <div className="flex flex-col items-start h-[100dvh] bg-background">
      {/* Logo with link to dashboard */}
      <div className="w-full flex justify-center items-center py-4 border-b border-border">
        <Logo 
          width={36} 
          height={36} 
          href="/dashboard"
          className={cn("transition-all duration-300 hover:opacity-80")} 
        />
      </div>
      
      <div className="flex flex-col items-start gap-1 px-1 py-2">
        {/* Main action buttons with padding */}
        <div className="space-y-2 w-full">
          <ToolbarButton
            icon={<Wand2 />}
            label="Generate"
            hideLabel={false}
            active={mode === CanvasMode.Generate}
            onClick={handleGenerate}
            size="wide"
            disableActiveBackground={true}
          />
          <div className="">
            <ToolbarButton
              icon={<Sparkles />}
              label="Magic Fill"
              hideLabel={false}
              active={mode === CanvasMode.MagicFill}
              onClick={() => handleModeChange(CanvasMode.MagicFill)}
              size="wide"
              disableActiveBackground={true}
            />
          </div>
          <div className="">
            <ToolbarButton
              icon={<MousePointer />}
              label="Select"
              hideLabel={false}
              active={mode === CanvasMode.Move}
              onClick={() => handleModeChange(CanvasMode.Move)}
              size="wide"
              disableActiveBackground={true}
            />
          </div>
          <div className="">
            <ToolbarButton
              icon={<Hand />}
              label="Hand"
              hideLabel={false}
              active={mode === CanvasMode.Pan}
              onClick={() => handleModeChange(CanvasMode.Pan)}
              size="wide"
              disableActiveBackground={true}
            />
          </div>
          <div className="">
            <ToolbarButton
              icon={<Upload />}
              label="Upload"
              hideLabel={false}
              onClick={onUpload ?? (() => {})}
              size="wide"
              disableActiveBackground={true}
            />
          </div>
          <div className="">
            <ToolbarButton
              icon={<Save />}
              label="Save"
              hideLabel={false}
              onClick={onSave ?? (() => {})}
              size="wide"
              disableActiveBackground={true}
            />
          </div>
          <div className="">
            <ToolbarButton
              icon={<Download />}
              label="Download"
              hideLabel={false}
              onClick={onDownload ?? (() => {})}
              size="wide"
              disableActiveBackground={true}
            />
          </div>
        </div>
      </div>
      {mode === CanvasMode.MagicFill && (
        <MagicFillToolbar
          activeMaskTool={maskTool}
          onMaskToolChange={(tool: MaskToolMode) => setMaskTool(tool)}
          onInvert={safeInvertMask}
          onCancelMagicFill={() => {
            clearLines();
            // The canvas store now preserves aspect ratio during mode changes
            onModeChange(CanvasMode.Move);
            setMagicFillAreaSelection(false);
            setSelectionArea(null);
          }}
          onProceedMagicFill={() => {
            // Since we always want an area selected when proceeding to the next step,
            // check if we need to create a default selection area
            if (!selectionArea) {
              // Try to find any image to use as a basis for the selection
              if (inputImage) {
                // Use input image if available
                setSelectionArea({
                  x: inputImagePosition.x,
                  y: inputImagePosition.y,
                  width: inputImageDimensions?.width || inputImage.width,
                  height: inputImageDimensions?.height || inputImage.height
                });
              } else if (generatedImages.length > 0) {
                // Find first generated image with valid content
                for (const img of generatedImages) {
                  if (img.image || img.src) {
                    setSelectionArea({
                      x: img.position.x,
                      y: img.position.y,
                      width: img.width,
                      height: img.height
                    });
                    break;
                  }
                }
              }
            }
            
            // Now proceed to the prompt input stage
            setMagicFillAreaSelection(true);
          }}
        />
      )}
    </div>
  );
};
