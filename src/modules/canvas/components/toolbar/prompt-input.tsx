"use client";
import React, { useState, useEffect, useRef } from "react";
import { Input } from "@/modules/ui/input";
import { Button } from "@/modules/ui/button";
import {
  Wand2,
  Loader2,
  Sparkles,
  ChevronUp,
  ChevronDown,
  Square,
  Lasso,
  Paintbrush,
  Eraser,
} from "lucide-react";
import useCanvasStore from "@/modules/canvas/store/canvas-store";
import { cn } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";
import { ImageDimensionsCard } from "./image-dimension-card";
import { ToolbarButton } from "./toolbar-button";
import { BrushSettings } from "./brush-settings";


interface PromptInputProps {
  onGenerate: () => Promise<void>;
}

export const PromptInput: React.FC<PromptInputProps> = ({ onGenerate }) => {
  const {
    prompt,
    selectionArea,
    setPrompt,
    isGenerating,
    magicFillAreaSelection,
    setSelectionArea,
    maskTool,
    setMaskTool,
    resolution,
    aspectRatio,
    setAspectRatio,
    dimensions
  } = useCanvasStore();
  
  const [isFocused, setIsFocused] = useState(false);
  const [magicPromptEnabled, setMagicPromptEnabled] = useState(false);
  const [isDimensionsCardOpen, setIsDimensionsCardOpen] = useState(false);
  const [showBrushSettings, setShowBrushSettings] = useState(false);
  
  // Consolidated ref for all UI elements
  const formElementsRef = useRef<HTMLDivElement>(null);

  // Update magic prompt option when the toggle changes
  useEffect(() => {
    useCanvasStore.getState().setMagicPromptOption(magicPromptEnabled ? "On" : "Auto");
  }, [magicPromptEnabled]);

  // Handler for generating images
  const handleGenerate = async () => {
    if (!prompt || isGenerating) return;
    await onGenerate();
  };

  // Simplified mouse event handlers
  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    // Skip if in magic fill mode or interacting with UI elements
    if (magicFillAreaSelection || shouldIgnoreMouseEvent(e)) {
      return;
    }
    
    startSelectionArea(e);
  };
  
  // Helper to check if we should ignore a mouse event
  const shouldIgnoreMouseEvent = (e: React.MouseEvent): boolean => {
    // Check if click is inside form elements container
    if (formElementsRef.current && formElementsRef.current.contains(e.target as Node)) {
      return true;
    }
    
    // Check for any UI element
    const target = e.target as HTMLElement;
    return (
      target.tagName === 'INPUT' || 
      target.tagName === 'BUTTON' || 
      target.tagName === 'SELECT' ||
      target.tagName === 'TEXTAREA' ||
      target.getAttribute('role') === 'button' ||
      !!target.closest('button') || 
      !!target.closest('input') ||
      !!target.closest('[role="button"]') ||
      !!target.closest('.settings-panel') ||
      !!target.closest('.dropdown') ||
      !!target.closest('.ui-element')
    );
  };
  
  // Start selection area creation
  const startSelectionArea = (e: React.MouseEvent) => {
    const stageEl = document.getElementById("canvas-stage");
    if (!stageEl) return;
    
    const stageRect = stageEl.getBoundingClientRect();
    const mouseX = e.clientX - stageRect.left;
    const mouseY = e.clientY - stageRect.top;
    
    // Initialize selection with minimum size
    setSelectionArea({ 
      x: mouseX, 
      y: mouseY, 
      width: 100, 
      height: 100 
    });
    
    // Set up mouse move and mouse up listeners
    const handleMouseMove = (moveEvent: MouseEvent) => {
      const currentX = moveEvent.clientX - stageRect.left;
      const currentY = moveEvent.clientY - stageRect.top;
      
      const width = Math.max(50, Math.abs(currentX - mouseX));
      const height = Math.max(50, Math.abs(currentY - mouseY));
      const x = Math.min(mouseX, currentX);
      const y = Math.min(mouseY, currentY);
      
      setSelectionArea({ x, y, width, height });
    };
    
    const handleMouseUp = () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };
    
    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);
  };

  // Helper function to compute preview rectangle style based on ratio string
  const getAspectStyle = (ratioStr: string) => {
    const parts = ratioStr.split(":");
    if (parts.length === 2) {
      const w = Number(parts[0]);
      const h = Number(parts[1]);
      const aspectValue = w / h;
      if (aspectValue >= 1) {
        return { width: "40px", height: `${Math.round(40 / aspectValue)}px` };
      } else {
        return { height: "40px", width: `${Math.round(40 * aspectValue)}px` };
      }
    }
    return { width: "40px", height: "40px" };
  };

  // Handle dimension changes from the ImageDimensionsCard
  const handleDimensionsChange = (
    width: number, 
    height: number, 
    orientation: "landscape" | "portrait", 
    customEnabled: boolean, 
    newAspectRatio: string
  ) => {
    // Update dimensions and aspect ratio in one operation
    useCanvasStore.getState().updateDimensions(width, height, true);
    
    // Update aspect ratio if valid
    if (newAspectRatio && newAspectRatio !== "None") {
      setAspectRatio(newAspectRatio as any);
    }
  };

  // Handle prompt container click
  const handlePromptContainerClick = (e: React.MouseEvent<HTMLElement>) => {
    // Only handle clicks directly on the container, not on its children
    if ((e.target as HTMLElement).id === "prompt-container") {
      setSelectionArea(null);
    }
  };

  // Stop event propagation
  const stopPropagation = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  return (
    <div 
      onMouseDown={!magicFillAreaSelection ? handleMouseDown : undefined} 
      className="w-full"
    >
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{ duration: 0.2 }}
          className={cn(
            "bg-background rounded-lg shadow-sm",
            "border border-border",
            "hover:shadow-md transition-shadow duration-200",
            isFocused && "ring-1 ring-primary",
            "relative w-full"
          )}
          onClick={handlePromptContainerClick}
          id="prompt-container"
        >
          {/* UI container that stops propagation */}
          <div 
            className="ui-container w-full"
            ref={formElementsRef}
            onMouseDown={stopPropagation}
          >
            <div className="p-2 flex items-center gap-2">
              <div className="flex-1 relative">
                <Input
                  placeholder="Describe what you want to generate..."
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  onFocus={() => setIsFocused(true)}
                  onBlur={() => setIsFocused(false)}
                  className={cn(
                    "h-10 text-base bg-transparent border-input",
                    "transition-all duration-200 ease-in-out",
                    "focus:ring-1 focus:border-primary focus:ring-primary",
                    "w-full rounded-md"
                  )}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && !e.shiftKey) {
                      e.preventDefault();
                      handleGenerate();
                    }
                  }}
                />
                <Sparkles
                  className={cn(
                    "absolute right-3 top-1/2 -translate-y-1/2 w-4 h-4",
                    "text-muted-foreground transition-all duration-200",
                    prompt ? "opacity-100 scale-100" : "opacity-0 scale-95"
                  )}
                />
              </div>
              <Button
                variant="default"
                size="sm"
                onClick={handleGenerate}
                disabled={isGenerating || !prompt}
                className={cn(
                  "h-10 px-3 rounded-md transition-all duration-200 ease-in-out",
                  isGenerating ? "min-w-[120px]" : "min-w-[100px]"
                )}
              >
                {isGenerating ? (
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span>Generating</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <Wand2 className="h-4 w-4" />
                    <span>Generate</span>
                  </div>
                )}
              </Button>
            </div>
            <div className="px-2 py-1 flex flex-wrap gap-2 border-t border-border">
              <Button
                variant={magicPromptEnabled ? "secondary" : "outline"}
                size="sm"
                onClick={() => setMagicPromptEnabled((prev) => !prev)}
                className="h-8 text-xs"
              >
                <Sparkles className="h-3.5 w-3.5 mr-1" />
                Magic Prompt: {magicPromptEnabled ? "On" : "Off"}
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsDimensionsCardOpen(true)}
                className="h-8 text-xs flex items-center gap-1"
              >
                <div
                  className="border border-border rounded flex items-center justify-center bg-background"
                  style={getAspectStyle(aspectRatio)}
                >
                  <span className="text-[10px] text-muted-foreground">{aspectRatio}</span>
                </div>
                <span>Dimensions</span>
                {isDimensionsCardOpen ? (
                  <ChevronUp className="h-3.5 w-3.5" />
                ) : (
                  <ChevronDown className="h-3.5 w-3.5" />
                )}
              </Button>
            </div>
            {magicFillAreaSelection && (
              <>
                <div className="px-2 py-1 border-t border-border bg-amber-50 dark:bg-amber-950/30">
                  <div className="text-sm text-amber-700 dark:text-amber-300 flex items-center justify-center">
                    <Wand2 className="h-4 w-4 mr-2" />
                    Magic Fill Mode: Enter a prompt to describe what to generate in the selected area
                  </div>
                </div>
                <div className="px-2 py-1 flex items-center justify-between gap-2 border-t border-border">
                  <div className="flex items-center gap-1">
                    <ToolbarButton
                      hideLabel={false}
                      icon={<Square className="h-4 w-4" />}
                      label="Rectangle"
                      active={maskTool === "rectangle"}
                      onClick={() => setMaskTool("rectangle")}
                      size="sm"
                    />
                    <ToolbarButton
                      hideLabel={false}
                      icon={<Lasso className="h-4 w-4" />}
                      label="Lasso"
                      active={maskTool === "lasso"}
                      onClick={() => setMaskTool("lasso")}
                      size="sm"
                    />
                    <ToolbarButton
                      hideLabel={false}
                      icon={<Paintbrush className="h-4 w-4" />}
                      label="Brush"
                      active={maskTool === "brush"}
                      onClick={() => setMaskTool("brush")}
                      size="sm"
                    />
                    <ToolbarButton
                      hideLabel={false}
                      icon={<Eraser className="h-4 w-4" />}
                      label="Eraser"
                      active={maskTool === "eraser"}
                      onClick={() => setMaskTool("eraser")}
                      size="sm"
                    />
                  </div>
                  <div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowBrushSettings(!showBrushSettings)}
                      className="h-8 text-xs"
                    >
                      <span>Brush Settings</span>
                      {showBrushSettings ? (
                        <ChevronUp className="h-3.5 w-3.5 ml-1" />
                      ) : (
                        <ChevronDown className="h-3.5 w-3.5 ml-1" />
                      )}
                    </Button>
                  </div>
                </div>
              </>
            )}
            {magicFillAreaSelection && showBrushSettings && (
              <div className="px-2 py-1 border-t border-border">
                <BrushSettings inline={true} />
              </div>
            )}
          </div>
        </motion.div>
      </AnimatePresence>
      {isDimensionsCardOpen && (
        <ImageDimensionsCard
          width={dimensions.width || 1152}
          height={dimensions.height || 704}
          initialAspectRatio={aspectRatio}
          initialCustomMode={resolution !== "None"}
          onChange={handleDimensionsChange}
          onClose={() => setIsDimensionsCardOpen(false)}
        />
      )}
      
    </div>
  );
};
