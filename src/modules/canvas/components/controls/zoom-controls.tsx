"use client";
import React from 'react';
import { Button } from '@/modules/ui/button';
import { Plus, Minus, Maximize2 } from 'lucide-react';

interface ZoomControlsProps {
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetZoom: () => void;
  zoomLevel: number;
  className?: string;
}

/**
 * Controls for adjusting the canvas zoom level
 */
export const ZoomControls: React.FC<ZoomControlsProps> = ({ 
  onZoomIn, 
  onZoomOut, 
  onResetZoom, 
  zoomLevel,
  className = '' 
}) => {
  return (
    <div className={`absolute right-4 top-4 z-10 flex flex-col space-y-2 bg-white dark:bg-gray-800 rounded-md shadow-md p-2 ${className}`}>
      <Button
        variant="outline"
        size="icon"
        onClick={onZoomIn}
        aria-label="Zoom in"
        className="h-8 w-8"
      >
        <Plus className="h-4 w-4" />
      </Button>
      <Button
        variant="outline"
        size="icon"
        onClick={onZoomOut}
        aria-label="Zoom out"
        className="h-8 w-8"
      >
        <Minus className="h-4 w-4" />
      </Button>
      <Button
        variant="outline"
        size="icon"
        onClick={onResetZoom}
        aria-label="Reset zoom"
        className="h-8 w-8"
      >
        <Maximize2 className="h-4 w-4" />
      </Button>
      <div className="text-xs text-center mt-1 font-medium">
        {Math.round(zoomLevel * 100)}%
      </div>
    </div>
  );
}; 