"use client";
import React from 'react';
import { Button } from '@/modules/ui/button';
import { Plus, Minus, Maximize2, Undo2, Redo2 } from 'lucide-react';
import { Separator } from "@/modules/ui/separator";

interface CanvasControlsProps {
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetZoom: () => void;
  onUndo?: () => void;
  onRedo?: () => void;
  zoomLevel: number;
  canUndo?: boolean;
  canRedo?: boolean;
  className?: string;
}

/**
 * Unified controls for adjusting the canvas zoom level and undo/redo operations
 */
export const CanvasControls: React.FC<CanvasControlsProps> = ({ 
  onZoomIn, 
  onZoomOut, 
  onResetZoom, 
  onUndo,
  onRedo,
  zoomLevel,
  canUndo = false,
  canRedo = false,
  className = '' 
}) => {
  return (
    <div className={`absolute right-4 top-4 z-10 flex flex-col space-y-2 bg-white dark:bg-gray-800 rounded-md shadow-md p-2 ${className}`}>
      {/* Zoom controls */}
      <Button
        variant="outline"
        size="icon"
        onClick={onZoomIn}
        aria-label="Zoom in"
        className="h-8 w-8"
      >
        <Plus className="h-4 w-4" />
      </Button>
      <Button
        variant="outline"
        size="icon"
        onClick={onZoomOut}
        aria-label="Zoom out"
        className="h-8 w-8"
      >
        <Minus className="h-4 w-4" />
      </Button>
      <Button
        variant="outline"
        size="icon"
        onClick={onResetZoom}
        aria-label="Reset zoom"
        className="h-8 w-8"
      >
        <Maximize2 className="h-4 w-4" />
      </Button>
      <div className="text-xs text-center mt-1 font-medium">
        {Math.round(zoomLevel * 100)}%
      </div>
      
      {/* Separator between zoom and history controls */}
      <Separator className="my-1" />
      
      {/* History controls */}
      <Button
        variant="outline"
        size="icon"
        onClick={onUndo}
        disabled={!canUndo}
        aria-label="Undo"
        className="h-8 w-8"
      >
        <Undo2 className="h-4 w-4" />
      </Button>
      <Button
        variant="outline"
        size="icon"
        onClick={onRedo}
        disabled={!canRedo}
        aria-label="Redo"
        className="h-8 w-8"
      >
        <Redo2 className="h-4 w-4" />
      </Button>
    </div>
  );
}; 