"use client";
import React, { useState } from 'react';
import { But<PERSON> } from '@/modules/ui/button';
import { Plus, Minus, Maximize2, Undo2, Redo2, Layers, History, Brush, Download, Upload } from 'lucide-react';
import { Separator } from "@/modules/ui/separator";
import { LayersPanel } from "@/modules/canvas/components/panels/layers-panel";
import { HistoryPanel } from "@/modules/canvas/components/panels/history-panel";
import { BrushSettingsPanel } from "@/modules/canvas/components/panels/brush-settings-panel";
import { cn } from "@/lib/utils";

interface CanvasControlsProps {
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetZoom: () => void;
  onUndo?: () => void;
  onRedo?: () => void;
  onExport?: () => void;
  onImport?: () => void;
  zoomLevel: number;
  canUndo?: boolean;
  canRedo?: boolean;
  className?: string;
}

type PanelType = 'layers' | 'history' | 'brush' | null;

/**
 * Unified controls for adjusting the canvas zoom level and undo/redo operations
 */
export const CanvasControls: React.FC<CanvasControlsProps> = ({
  onZoomIn,
  onZoomOut,
  onResetZoom,
  onUndo,
  onRedo,
  onExport,
  onImport,
  zoomLevel,
  canUndo = false,
  canRedo = false,
  className = ''
}) => {
  const [activePanel, setActivePanel] = useState<PanelType>(null);

  const togglePanel = (panel: PanelType) => {
    setActivePanel(activePanel === panel ? null : panel);
  };
  return (
    <>
      <div className={cn(
        "absolute right-4 top-4 z-10 flex flex-col space-y-2 bg-white dark:bg-gray-800 rounded-md shadow-md p-2",
        className
      )}>
        {/* Zoom controls */}
        <Button
          variant="outline"
          size="icon"
          onClick={onZoomIn}
          aria-label="Zoom in"
          className="h-8 w-8"
        >
          <Plus className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="icon"
          onClick={onZoomOut}
          aria-label="Zoom out"
          className="h-8 w-8"
        >
          <Minus className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="icon"
          onClick={onResetZoom}
          aria-label="Reset zoom"
          className="h-8 w-8"
        >
          <Maximize2 className="h-4 w-4" />
        </Button>
        <div className="text-xs text-center mt-1 font-medium">
          {Math.round(zoomLevel * 100)}%
        </div>

        <Separator className="my-1" />

        {/* History controls */}
        <Button
          variant="outline"
          size="icon"
          onClick={onUndo}
          disabled={!canUndo}
          aria-label="Undo"
          className="h-8 w-8"
        >
          <Undo2 className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="icon"
          onClick={onRedo}
          disabled={!canRedo}
          aria-label="Redo"
          className="h-8 w-8"
        >
          <Redo2 className="h-4 w-4" />
        </Button>

        <Separator className="my-1" />

        {/* Panel toggles */}
        <Button
          variant={activePanel === 'layers' ? 'default' : 'outline'}
          size="icon"
          onClick={() => togglePanel('layers')}
          aria-label="Toggle layers panel"
          className="h-8 w-8"
        >
          <Layers className="h-4 w-4" />
        </Button>
        <Button
          variant={activePanel === 'history' ? 'default' : 'outline'}
          size="icon"
          onClick={() => togglePanel('history')}
          aria-label="Toggle history panel"
          className="h-8 w-8"
        >
          <History className="h-4 w-4" />
        </Button>
        <Button
          variant={activePanel === 'brush' ? 'default' : 'outline'}
          size="icon"
          onClick={() => togglePanel('brush')}
          aria-label="Toggle brush settings panel"
          className="h-8 w-8"
        >
          <Brush className="h-4 w-4" />
        </Button>

        <Separator className="my-1" />

        {/* File operations */}
        {onImport && (
          <Button
            variant="outline"
            size="icon"
            onClick={onImport}
            aria-label="Import image"
            className="h-8 w-8"
          >
            <Upload className="h-4 w-4" />
          </Button>
        )}
        {onExport && (
          <Button
            variant="outline"
            size="icon"
            onClick={onExport}
            aria-label="Export canvas"
            className="h-8 w-8"
          >
            <Download className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Floating panels */}
      {activePanel === 'layers' && (
        <div className="absolute right-20 top-4 z-20">
          <LayersPanel />
        </div>
      )}
      {activePanel === 'history' && (
        <div className="absolute right-20 top-4 z-20">
          <HistoryPanel />
        </div>
      )}
      {activePanel === 'brush' && (
        <div className="absolute right-20 top-4 z-20">
          <BrushSettingsPanel />
        </div>
      )}
    </>
  );
};