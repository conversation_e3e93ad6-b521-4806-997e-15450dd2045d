"use client";
import React, { useRef, useState, useEffect } from "react";
import {
  Stage,
  Layer,
  Image as KonvaImage,
  Line,
  Transformer,
  Group,
  Rect,
} from "react-konva";
import Konva from "konva";
import { MainToolbar } from "@/modules/canvas/components/toolbar/main-toolbar";
import { PromptInput } from "@/modules/canvas/components/toolbar/prompt-input";
import { MagicFillToolbar } from "@/modules/canvas/components/toolbar/magic-fill-toolbar";
import { ImageSidebar } from "@/modules/canvas/components/sidebar/image-sidebar";
import { CanvasControls } from "@/modules/canvas/components/controls/canvas-controls";
import useCanvasStore from "@/modules/canvas/store/canvas-store";
import {
  CanvasMode,
  CanvasLine,
  CanvasRect,
  GeneratedImage,
  TransformConfig,
  CanvasPosition,
  CanvasSize,
  MaskToolMode,
} from "@/modules/canvas/types/canvas";
import { useMediaQuery } from "@/modules/hooks/use-media-query";
import { useToast } from "@/modules/ui/use-toast";
import { useCurrentUser } from "@/modules/auth/hooks/use-current-user";
import { cn } from "@/lib/utils";
import { editImage } from "@/modules/canvas/actions/edit-image";
import { MagicFillCompositeLayer } from "@/modules/canvas/hooks/use-brush-composite";
import { SelectionRect } from "@/modules/canvas/components/selection-rectangle";
import { useTheme } from "next-themes";
import { UnifiedCanvasItem } from "@/modules/canvas/components/unified-canvas-item";
import {
  computeAspectRatio,
  findClosestValidResolution,
  getRelativePointerPosition,
  snapToGrid,
  findAvailableCanvasPosition
} from "@/modules/canvas/utils/canvas-utils";
import { Button } from "@/modules/ui/button";
import { Download, Upload, Image, Info } from "lucide-react";
import { PredictionStatus } from "@prisma/client";
import { useChannel } from "ably/react";

// Custom Hooks
import { useCanvasZoom } from "../hooks/use-canvas-zoom";
import { useCanvasOperations } from "../hooks/use-canvas-operations";
import { useCanvasExport } from "../hooks/use-canvas-export";
import { useGenerationTasks } from "../hooks/use-generation-tasks";
import { useMagicFill } from "../hooks/use-magic-fill";
import { useCanvasItems } from "../hooks/use-canvas-items";
import { useFileUpload } from "../hooks/use-file-upload";

// Custom Components
import { CanvasLayers as CanvasLayersComponent } from "./canvas-layers/canvas-layers";
import { PlaceholderLayer } from "./canvas-layers/placeholder-layer";
import { ActiveStrokePreview } from "./canvas-layers/active-stroke-preview";
import { LoadingOverlay } from "./overlays/loading-overlay";

/**
 * CanvasLayers:
 * Renders elements underneath the mask. This includes the input (uploaded) image,
 * generated images (which allow transformation), and the composited brush strokes.
 */
const CanvasLayers: React.FC<{
  inputImage: HTMLImageElement | null;
  inputImageDimensions?: { width: number; height: number } | null;
  inputImagePosition: CanvasPosition;
  generatedImages: GeneratedImage[];
  transformConfig: TransformConfig;
  brushComposite: HTMLImageElement | null;
  mode: CanvasMode;
  onImageClick: (index: number, e: Konva.KonvaEventObject<any>) => void;
  onImageDragEnd: (index: number, e: Konva.KonvaEventObject<any>) => void;
  onTransformEnd: (index: number, e: Konva.KonvaEventObject<any>) => void;
  onImageUpdate: (index: number, updatedImage: GeneratedImage) => void;
  selectedImageRef: React.RefObject<Konva.Image>;
  transformerRef: React.RefObject<Konva.Transformer>;
}> = ({
  inputImage,
  inputImageDimensions,
  inputImagePosition,
  generatedImages,
  transformConfig,
  brushComposite,
  mode,
  onImageClick,
  onImageDragEnd,
  onTransformEnd,
  onImageUpdate,
}) => {
  // Add debug log to track image positions
  useEffect(() => {
    if (generatedImages.length > 0) {
      console.log('Rendering generated images:', generatedImages.map(img => ({
        id: img.id,
        position: img.position,
        dimensions: { width: img.width, height: img.height },
        isNew: img.isNew
      })));
    }
  }, [generatedImages]);

  return (
    <Layer>
      {inputImage && (
        <UnifiedCanvasItem
          type="image"
          x={inputImagePosition.x}
          y={inputImagePosition.y}
          width={inputImageDimensions ? inputImageDimensions.width : inputImage.width}
          height={inputImageDimensions ? inputImageDimensions.height : inputImage.height}
          rotation={0}
          image={{
            image: inputImage,
            position: inputImagePosition,
            width: inputImageDimensions ? inputImageDimensions.width : inputImage.width,
            height: inputImageDimensions ? inputImageDimensions.height : inputImage.height,
            rotation: 0,
            isSelected: transformConfig.selectedImageIndex === -1,
          }}
          isSelected={transformConfig.selectedImageIndex === -1}
          isDraggable={mode === CanvasMode.Move}
          onClick={(e) => onImageClick(-1, e)}
          onDragEnd={(e) => onImageDragEnd(-1, e)}
          onTransformEnd={(e) => onTransformEnd(-1, e)}
        />
      )}
      {generatedImages.map((genImage, index) => (
        <UnifiedCanvasItem
          key={index}
          type="image"
          x={genImage.position.x}
          y={genImage.position.y}
          width={genImage.width}
          height={genImage.height}
          rotation={genImage.rotation}
          image={genImage}
          isSelected={transformConfig.selectedImageIndex === index}
          isDraggable={mode === CanvasMode.Move}
          onClick={(e) => onImageClick(index, e)}
          onDragEnd={(e) => onImageDragEnd(index, e)}
          onTransformEnd={(e) => onTransformEnd(index, e)}
          onImageUpdate={(updatedImage) => onImageUpdate(index, updatedImage)}
        />
      ))}
      {brushComposite && (
        <KonvaImage image={brushComposite} x={0} y={0} listening={false} />
      )}
    </Layer>
  );
};

/**
 * ImageCanvas is the main canvas component that supports drawing masks, 
 * image generation, magic fill and image transformations.
 */
export function ImageCanvas({ className }: { className?: string }) {
  const store = useCanvasStore();
  const stageRef = useRef<Konva.Stage>(null);
  const transformerRef = useRef<Konva.Transformer>(null);
  const selectedImageRef = useRef<Konva.Image>(null);
  const { toast } = useToast();
  const currentUser = useCurrentUser();
  const isMobile = useMediaQuery("(max-width: 768px)");
  const [brushComposite, setBrushComposite] = useState<HTMLImageElement | null>(null);
  const { theme, resolvedTheme } = useTheme();
  const currentTheme = theme === "system" ? resolvedTheme : theme;
  const canvasFill = currentTheme === "dark" ? "#1f2937" : "#f9fafb";
  const VIRTUAL_WIDTH = 10000;
  const VIRTUAL_HEIGHT = 10000;
  const [dimensions, setDimensions] = useState<CanvasSize>({
    width: window.innerWidth,
    height: window.innerHeight,
  });
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  
  // Get the channel for Ably
  const channelName = currentUser.data?.id 
    ? `predictions-${currentUser.data.id}`
    : "";
  
  const { channel } = useChannel(channelName);

  // Initialize canvas operation hooks
  const { 
    isDrawing, 
    setIsDrawing,
    localSelectionRect, 
    setLocalSelectionRect,
    activeLine, 
    setActiveLine,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    handleStageClick: baseHandleStageClick,
    handleMaskToolChange,
    handleCancelMagicFill,
    handleProceedMagicFill,
    captureSelection
  } = useCanvasOperations();

  // Use the hook and its functions
  const { 
    selectedPlaceholderId,
    setSelectedPlaceholderId,
    handleImageClick: baseHandleImageClick,
    handleImageDragEnd,
    handleTransformEnd,
    handleImageUpdate,
    handlePlaceholderClick,
    deselectAll,
    addGeneratedImage,
    deleteSelectedImage
  } = useCanvasItems();

  // Use the canvas zoom hook with options
  const { 
    handleWheel, 
    zoomIn, 
    zoomOut, 
    resetZoom,
    initializeZoom,
    defaultScale
  } = useCanvasZoom({
    minScale: 0.1,
    maxScale: 5,
    scaleFactor: 1.1,
    defaultScale: 0.7
  });

  // Initialize the canvas export functionality
  const { captureSelectionAreaImage, captureAndUploadSelectionArea, exportSelectionAsImage } = useCanvasExport();

  // Initialize the file upload hooks
  const { fileInputRef, triggerFileUpload, handleFileInput } = useFileUpload();

  // Initialize the generation tasks hook
  const { 
    handleGenerate: handleGenerationTask,
    handleChannelUpdate,
    handlePlaceholderDragEnd,
    handlePlaceholderTransform
  } = useGenerationTasks(
    currentUser.data?.id || "", 
    channelName
  );

  // Initialize the canvas with the default scale
  useEffect(() => {
    if (stageRef.current) {
      initializeZoom(stageRef, dimensions);
    }
  }, [dimensions.width, dimensions.height, initializeZoom]);

  // Update dimensions when window is resized
  useEffect(() => {
    const handleResize = () => {
      // Use store.updateDimensions to ensure consistent state management
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      // Update component-local state for stage rendering
      setDimensions({ width, height });
      
      // Don't update the selection area when resizing the window
      store.updateDimensions(width, height, false);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    if (currentUser.data?.id) {
      store.setUserId(currentUser.data.id);
    }
  }, [currentUser.data?.id]);

  // Set up subscription to Ably channel
  useEffect(() => {
    if (!channel) return;
    
    channel.subscribe('prediction-update', handleChannelUpdate);
    
    return () => {
      channel.unsubscribe('prediction-update', handleChannelUpdate);
    };
  }, [channel, handleChannelUpdate]);

  // Filter tasks to show placeholders and tasks with imageUrl
  const visibleTasks = store.generationTasks.filter(task => {
    // Always show tasks with imageUrl
    if (task.imageUrl) return true;
    
    // Don't show completed tasks without an imageUrl
    if (task.status === 'complete' && !task.imageUrl) return false;
    
    // Don't show tasks with imageId but no imageUrl
    if (task.imageId && !task.imageUrl) return false;
    
    // Show all other tasks
    return true;
  });

  // Helper functions for zoom controls using the hook
  const handleZoomIn = () => {
    zoomIn(stageRef, dimensions);
  };
  
  const handleZoomOut = () => {
    zoomOut(stageRef, dimensions);
  };
  
  const handleResetZoom = () => {
    resetZoom(stageRef, dimensions);
  };

  // Wrapper for file input handling
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileInput(e, stageRef, dimensions);
  };

  // Wrapper for generate function
  const handleGenerate = async () => {
    await handleGenerationTask();
  };

  const { handleMagicFill } = useMagicFill(
    currentUser.data?.id || "",
    addGeneratedImage
  );

  const handleInternalMagicFill = async () => {
    await handleMagicFill(stageRef);
  };

  useEffect(() => {
    if (store.mode !== CanvasMode.MagicFill) {
      store.clearLines();
      setBrushComposite(null);
    }
  }, [store.mode]);

  useEffect(() => {
    const transformer = transformerRef.current;
    if (
      store.transformConfig.selectedImageIndex !== null &&
      selectedImageRef.current
    ) {
      transformer?.nodes([selectedImageRef.current]);
      transformer?.getLayer()?.batchDraw();
    } else {
      transformer?.nodes([]);
      transformer?.getLayer()?.batchDraw();
    }
  }, [store.transformConfig.selectedImageIndex, store.generatedImages]);

  // Compute active stroke for real-time feedback
  const currentActiveLine: CanvasLine | null = isDrawing
    ? store.lines[store.lines.length - 1] || null
    : null;

  // Function to export the selected area of the canvas
  const handleExportSelectedArea = async () => {
    const selection = store.selectionArea;
    
    if (!selection || !stageRef.current) {
      toast({
        title: "No selection",
        description: "Please select an area to export first",
        variant: "destructive",
      });
      return;
    }
    
    // Log exact selection area dimensions for debugging
    console.log("Exporting selection area:", selection);
    
    try {
      // Special handling for magic fill mode - hide all status indicators and placeholders
      if (store.mode === CanvasMode.MagicFill) {
        // Find all tasks that match the export area
        const tasksInSelection = store.generationTasks.filter(task => {
          const area = task.selectionArea;
          const overlaps = !(
            area.x > selection.x + selection.width ||
            area.x + area.width < selection.x ||
            area.y > selection.y + selection.height ||
            area.y + area.height < selection.y
          );
          return overlaps;
        });
        
        // Hide tasks or status indicators that are in progress
        const tasksToHide = tasksInSelection.filter(task => 
          task.status === 'generating' || task.status === 'pending'
        );
        
        if (tasksToHide.length > 0) {
          console.log(`Hiding ${tasksToHide.length} tasks that are generating/pending`);
          
          // Find all status indicators and hide them
          const allLayers = stageRef.current.getLayers();
          
          allLayers.forEach(layer => {
            layer.find('.status-text, .generating-indicator').forEach(node => {
              node.visible(false);
            });
          });
          
          // Apply changes
          stageRef.current.batchDraw();
          
          // Small delay to ensure rendering is complete
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      }
      
      // Use the exportSelectionAsImage function directly from the hook
      await exportSelectionAsImage(
        // Ensure we pass the exact selection coordinates and dimensions
        {
          x: selection.x,
          y: selection.y,
          width: selection.width,
          height: selection.height
        },
        stageRef, 
        `selected-area-${Date.now()}.png`
      );
    } catch (error) {
      console.error("Error exporting selection:", error);
      // No need for toast here as exportSelectionAsImage already handles errors with toasts
    }
  };

  // Handler for showing and handling magic fill toolbar cancel
  const handleMagicFillCancel = () => {
    handleCancelMagicFill();
    
    toast({
      title: "Magic Fill Cancelled",
      description: "Magic fill operation has been cancelled",
    });
  };

  // Function to export the entire canvas
  const handleExportEntireCanvas = () => {
    if (!stageRef.current) return;
    
    try {
      // Hide UI elements temporarily
      const allLayers = stageRef.current.getLayers();
      const hiddenElements: Array<{node: Konva.Node, wasVisible: boolean}> = [];
      
      // Find and hide all UI elements
      allLayers.forEach(layer => {
        layer.find('.selection-rect, Transformer, Label, Tag, .ui-control').forEach(node => {
          hiddenElements.push({
            node,
            wasVisible: node.visible()
          });
          node.visible(false);
        });
      });
      
      // Apply changes
      stageRef.current.batchDraw();
      
      // Export the entire canvas
      const dataUrl = stageRef.current.toDataURL({
        pixelRatio: Math.max(2, window.devicePixelRatio || 1),
        mimeType: "image/png",
        quality: 1,
      });
      
      // Create download link
      const link = document.createElement("a");
      link.download = `canvas-full-${Date.now()}.png`;
      link.href = dataUrl;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Restore visibility
      hiddenElements.forEach(item => {
        item.node.visible(item.wasVisible);
      });
      stageRef.current.batchDraw();
      
      toast({
        title: "Export successful",
        description: "The entire canvas has been exported as an image",
      });
    } catch (error) {
      console.error("Error exporting canvas:", error);
      toast({
        title: "Export failed",
        description: "Failed to export the canvas",
        variant: "destructive",
      });
    }
  };

  // Function to capture and upload the selected area
  const handleCaptureAndUploadSelection = async () => {
    const selection = store.selectionArea;
    
    if (!selection || !stageRef.current || !currentUser.data?.id) {
      toast({
        title: "Cannot upload",
        description: "Please select an area and ensure you're signed in",
        variant: "destructive",
      });
      return;
    }
    
    try {
      store.setIsUploadingImage(true);
      const imageUrl = await captureAndUploadSelectionArea(
        selection,
        stageRef,
        currentUser.data.id
      );
      
      // Add the uploaded image to the store as a generated image
      if (imageUrl) {
        // Load the image to get its dimensions
        const img = document.createElement('img');
        img.crossOrigin = "anonymous";
        img.src = imageUrl;
        
        img.onload = () => {
          // Add the image to the canvas using the addGeneratedImage function
          addGeneratedImage({
            id: `uploaded-${Date.now()}`,
            image: img,
            src: imageUrl,
            element: img, 
            position: { 
              x: selection.x, 
              y: selection.y 
            },
            width: selection.width,
            height: selection.height,
            rotation: 0,
            scaleX: 1,
            scaleY: 1,
            isSelected: false,
            isNew: true,
            isUploaded: true
          });
          
          // The captureAndUploadSelectionArea already shows a success toast
          console.log("Image uploaded successfully and added to canvas:", imageUrl);
        };
        
        img.onerror = () => {
          console.error("Failed to load uploaded image:", imageUrl);
          toast({
            title: "Image Loading Failed",
            description: "The uploaded image could not be loaded to the canvas",
            variant: "destructive"
          });
        };
      }
    } catch (error) {
      console.error("Error uploading selection:", error);
      // The captureAndUploadSelectionArea already shows an error toast
    } finally {
      store.setIsUploadingImage(false);
    }
  };

  // Modified deleteSelectedImage that also closes the sidebar
  const handleDeleteSelectedImage = () => {
    deleteSelectedImage();
    setIsSidebarOpen(false);
  };

  // Add keyboard event listener for delete key
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Check if Delete or Backspace key is pressed
      if ((e.key === 'Delete' || e.key === 'Backspace') && 
          // Ensure we're not deleting while typing in an input field
          !['INPUT', 'TEXTAREA'].includes(document.activeElement?.tagName || '')) {
        handleDeleteSelectedImage();
      }
      
      // Close sidebar when Escape key is pressed
      if (e.key === 'Escape' && isSidebarOpen) {
        setIsSidebarOpen(false);
      }
    };
    
    // Add the event listener
    window.addEventListener('keydown', handleKeyDown);
    
    // Clean up
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [deleteSelectedImage, isSidebarOpen]);

  // Extend the handleImageClick function to toggle sidebar
  const handleImageClick = (index: number, e: Konva.KonvaEventObject<any>) => {
    baseHandleImageClick(index, e);
    setIsSidebarOpen(true);
  };

  // Get the currently selected image
  const selectedImage = store.transformConfig.selectedImageIndex !== null && 
                       store.transformConfig.selectedImageIndex >= 0 
    ? store.generatedImages[store.transformConfig.selectedImageIndex] 
    : null;

  // Handle closing the sidebar
  const handleCloseSidebar = () => {
    setIsSidebarOpen(false);
  };

  // Function to duplicate an image
  const handleDuplicateImage = (imageToDuplicate: GeneratedImage) => {
    if (!imageToDuplicate) return;
    
    // Create a copy of the image with a new ID
    const duplicatedImage: GeneratedImage = {
      ...imageToDuplicate,
      id: `${imageToDuplicate.id || 'img'}-copy-${Date.now()}`,
      isSelected: false,
      isNew: true,
      // Offset the position slightly to make it visible it's a duplicate
      position: {
        x: imageToDuplicate.position.x + 20,
        y: imageToDuplicate.position.y + 20
      }
    };
    
    // Add the duplicated image to the canvas
    addGeneratedImage(duplicatedImage);
    
    // Show success message
    toast({
      title: "Image Duplicated",
      description: "A copy of the image has been created",
    });
  };

  // Extend the stage click handler to close sidebar when clicking on empty canvas
  const handleStageClick = (e: Konva.KonvaEventObject<MouseEvent>) => {
    // Call the original handler
    baseHandleStageClick(e);
    
    // Close the sidebar if target is the stage (background) and not an image
    if (e.target === e.currentTarget) {
      setIsSidebarOpen(false);
    }
  };

  // Add debug logging for history in development mode
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Image history setup:', {
        imageHistory: store.imageHistory,
        currentImageIndex: store.currentImageIndex,
        lineHistory: store.lineHistory,
        currentLineIndex: store.currentLineIndex
      });
    }
  }, [store.imageHistory.length, store.currentImageIndex, store.lineHistory.length, store.currentLineIndex]);

  return (
    <div className={cn("relative", className)}>
      {/* Replace ZoomControls with CanvasControls */}
      <CanvasControls
        onZoomIn={handleZoomIn}
        onZoomOut={handleZoomOut}
        onResetZoom={handleResetZoom}
        onUndo={store.undo}
        onRedo={store.redo}
        zoomLevel={store.zoom}
        canUndo={store.currentLineIndex > 0 || store.currentImageIndex > 0}
        canRedo={
          store.currentLineIndex < store.lineHistory.length - 1 ||
          store.currentImageIndex < store.imageHistory.length - 1
        }
      />
      
      {/* Add info button to reopen sidebar when image is selected but sidebar is closed */}
      {store.transformConfig.selectedImageIndex !== null && 
       store.transformConfig.selectedImageIndex >= 0 && 
       !isSidebarOpen && (
        <div className="absolute top-24 right-4 z-50">
          <Button
            variant="secondary"
            size="icon"
            onClick={() => setIsSidebarOpen(true)}
            title="Image details"
            className="h-10 w-10 rounded-full shadow-md"
          >
            <Info className="h-5 w-5" />
          </Button>
        </div>
      )}
      
      <Stage
        ref={stageRef}
        x={0}
        y={0}
        width={dimensions.width}
        height={dimensions.height}
        draggable={store.mode === CanvasMode.Pan}
        onClick={handleStageClick}
        onDragEnd={(e) => {
          if (store.mode === CanvasMode.Pan) {
            // Optionally, update viewport offset
          }
        }}
        onMouseDown={(e) => {
          if (
            store.magicFillAreaSelection &&
            store.mode !== CanvasMode.SelectArea
          )
            return;
          handleMouseDown(e, stageRef);
        }}
        onMouseMove={(e) => handleMouseMove(e, stageRef)}
        onMouseUp={() => handleMouseUp(stageRef)}
        onWheel={(e) => handleWheel(e, stageRef)}
      >
        {/* Background layer */}
        <Layer>
          <Rect
            x={-VIRTUAL_WIDTH }
            y={-VIRTUAL_HEIGHT}
            width={VIRTUAL_WIDTH}
            height={VIRTUAL_HEIGHT}
            fill={canvasFill}
          />
        </Layer>

        {/* Use the CanvasLayersComponent for main content layers */}
        <CanvasLayersComponent
          inputImage={store.inputImage}
          inputImageDimensions={store.inputImageDimensions}
          inputImagePosition={store.inputImagePosition}
          generatedImages={store.generatedImages}
          transformConfig={store.transformConfig}
          brushComposite={brushComposite}
          mode={store.mode}
          onImageClick={handleImageClick}
          onImageDragEnd={handleImageDragEnd}
          onTransformEnd={handleTransformEnd}
          onImageUpdate={handleImageUpdate}
          selectedImageRef={selectedImageRef}
          transformerRef={transformerRef}
        />

        {/* Placeholder task layer */}
        <PlaceholderLayer
          tasks={visibleTasks}
          mode={store.mode}
          selectedPlaceholderId={selectedPlaceholderId}
          onPlaceholderDragEnd={handlePlaceholderDragEnd}
          onPlaceholderTransform={handlePlaceholderTransform}
          onPlaceholderClick={handlePlaceholderClick}
        />

        {/* Active brush strokes layer - must be visible for magic fill */}
        {store.mode === CanvasMode.MagicFill && (
          <Layer name="magic-fill-composite-layer" listening={false}>
            <MagicFillCompositeLayer
              lines={store.lines}
              dimensions={dimensions}
              isDrawing={isDrawing}
              x={0}
              y={0}
            />
          </Layer>
        )}

        {/* Real-time stroke preview - always visible */}
        {currentActiveLine && store.mode === CanvasMode.MagicFill && (
          <Layer name="active-stroke-layer" listening={false}>
            <ActiveStrokePreview currentLine={currentActiveLine} />
          </Layer>
        )}

        {/* Selection rectangle - always on top */}
        {store.magicFillAreaSelection && store.selectionArea && (
          <Layer>
            <SelectionRect
              x={store.selectionArea.x}
              y={store.selectionArea.y}
              width={store.selectionArea.width}
              height={store.selectionArea.height}
              onChange={(attrs) => {
                store.setSelectionArea(attrs);
              }}
              stageRef={stageRef}
            />
          </Layer>
        )}
      </Stage>

      <div className="absolute top-0 left-0 z-50">
        <MainToolbar
          mode={store.mode}
          onModeChange={(newMode) => {
            // Special handling for entering magic fill mode
            if (newMode === CanvasMode.MagicFill) {
              // Reset magic fill state
              store.setMagicFillAreaSelection(false);
              store.clearLines();
              setBrushComposite(null);
            }
            
            // Close sidebar when changing tools
            if (isSidebarOpen) {
              setIsSidebarOpen(false);
            }
            
            // Set the new mode
            store.setMode(newMode);
          }}
          onGenerate={
            store.mode === CanvasMode.Generate
              ? handleGenerate
              : handleInternalMagicFill
          }
          onUpload={triggerFileUpload}
          onSave={() => {}}
          onDownload={() => {
            if (stageRef.current) {
              // Temporarily hide any loading overlays or UI elements that shouldn't be captured
              const loadingOverlays = document.querySelectorAll('.loading-overlay, .status-indicator');
              const originalVisibility: {element: Element, style: string | null}[] = [];
              
              // Store and hide elements
              loadingOverlays.forEach(element => {
                originalVisibility.push({
                  element,
                  style: element.getAttribute('style')
                });
                element.setAttribute('style', 'visibility: hidden; opacity: 0;');
              });
              
              // Wait a tiny bit for the DOM to update
              setTimeout(() => {
                // Check if there's an active selection area
                if (store.selectionArea) {
                  // If there's a selection, use the export function to export just that area
                  exportSelectionAsImage(
                    {
                      x: store.selectionArea.x,
                      y: store.selectionArea.y,
                      width: store.selectionArea.width,
                      height: store.selectionArea.height
                    }, 
                    stageRef, 
                    `canvas-export-${Date.now()}.png`
                  ).then(() => {
                    // Restore visibility
                    originalVisibility.forEach(({element, style}) => {
                      if (style) {
                        element.setAttribute('style', style);
                      } else {
                        element.removeAttribute('style');
                      }
                    });
                  }).catch(err => {
                    console.error("Export failed:", err);
                    // Still restore visibility
                    originalVisibility.forEach(({element, style}) => {
                      if (style) {
                        element.setAttribute('style', style);
                      } else {
                        element.removeAttribute('style');
                      }
                    });
                  });
                } else {
                  // Otherwise export the entire canvas
                  try {
                    handleExportEntireCanvas();
                  } finally {
                    // Restore visibility
                    originalVisibility.forEach(({element, style}) => {
                      if (style) {
                        element.setAttribute('style', style);
                      } else {
                        element.removeAttribute('style');
                      }
                    });
                  }
                }
              }, 50);
            }
          }}
        />
      </div>

      {/* Display Magic Fill Toolbar when in MagicFill mode and NOT in area selection mode */}
      {store.mode === CanvasMode.MagicFill && !store.magicFillAreaSelection && (
        <MagicFillToolbar
          activeMaskTool={store.maskTool}
          onMaskToolChange={handleMaskToolChange}
          onInvert={() => {/* Invert mask implementation */}}
          onCancelMagicFill={handleMagicFillCancel}
          onProceedMagicFill={handleProceedMagicFill}
          onExportSelection={handleExportSelectedArea}
        />
      )}

      {/* Show PromptInput for Generate mode or when a magic fill area is selected */}
      {(store.mode === CanvasMode.Generate ||
        (store.mode === CanvasMode.MagicFill &&
          store.magicFillAreaSelection)) && (
        <div className="fixed top-4 left-0 right-0 mx-auto z-50 w-full max-w-4xl flex justify-center">
          <PromptInput
            onGenerate={
              store.mode === CanvasMode.Generate
                ? handleGenerate
                : handleInternalMagicFill
            }
          />
          {/* Add Export and Upload buttons when in magic fill with area selected */}
          {store.mode === CanvasMode.MagicFill && 
           store.magicFillAreaSelection && 
           store.selectionArea && (
            <div className="absolute right-[-110px] top-1/2 -translate-y-1/2 flex flex-col gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={handleExportSelectedArea}
                className="h-10 w-10 flex items-center justify-center"
                title="Export selected area"
              >
                <Download className="h-4 w-4" />
              </Button>
              
              <Button
                variant="outline"
                size="icon"
                onClick={handleCaptureAndUploadSelection}
                className="h-10 w-10 flex items-center justify-center"
                title="Upload selected area"
                disabled={!currentUser.data?.id}
              >
                <Upload className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      )}

      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        accept="image/*"
        onChange={handleFileInputChange}
      />

      {/* Use LoadingOverlay component for loading states */}
      <LoadingOverlay 
        isLoading={store.isGenerating} 
        message="Generating image..." 
        opacity={0.2}
      />
      
      <LoadingOverlay 
        isLoading={!store.isGenerating && store.isUploadingImage} 
        message="Uploading image..."
        opacity={0.5} 
      />

      {/* Update the ImageSidebar to accept onDelete prop */}
      <ImageSidebar
        selectedImage={selectedImage}
        onClose={handleCloseSidebar}
        onDuplicate={handleDuplicateImage}
        onDelete={handleDeleteSelectedImage}
        isOpen={isSidebarOpen}
        className=""
      />
    </div>
  );
}

export default ImageCanvas;
