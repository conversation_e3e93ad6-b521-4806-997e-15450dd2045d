import React from 'react';
import Konva from 'konva';

import { UnifiedCanvasItemProps } from './canvas-items/canvas-item-types';
import { PlaceholderCanvasItem } from './canvas-items/placeholder-canvas-item';
import { ImageCanvasItem } from './canvas-items/image-canvas-item';

export { ROTATE_ICON } from './canvas-items/canvas-item-types';
export { GradientAnimationManager } from './gradient-animation-manager';
export { BaseCanvasItem } from './canvas-items/base-canvas-item';
export { PlaceholderCanvasItem } from './canvas-items/placeholder-canvas-item';
export { ImageCanvasItem } from './canvas-items/image-canvas-item';
export { TransformerWrapper } from './transformer-wrapper';

export const UnifiedCanvasItem: React.FC<UnifiedCanvasItemProps> = (props) => {
  const { type, taskType, ...commonProps } = props;

  // Render based on the type
  if (type === 'placeholder') {
    return (
      <PlaceholderCanvasItem
        {...commonProps}
        aspectRatio={props.aspectRatio}
        prompt={props.prompt}
        status={props.status}
        isFading={props.isFading}
        imageUrl={props.imageUrl}
        type={taskType || 'generate'}
      />
    );
  } else if (type === 'image' && props.image) {
    return (
      <ImageCanvasItem
        {...commonProps}
        image={props.image}
        onImageUpdate={props.onImageUpdate}
      />
    );
  }

  // Fallback in case of any issues
  return null;
}; 