import React, { useRef, useState, useEffect } from 'react';
import { Group } from 'react-konva';
import Konva from 'konva';
import { KonvaEventObject } from 'konva/lib/Node';
import { BaseCanvasItemProps } from './canvas-item-types';
import { TransformerWrapper } from '../transformer-wrapper';

// Minimum time between click events to prevent double processing
const CLICK_DEBOUNCE_TIME = 200; // ms

// Use correct Konva event type
type KonvaEvent = KonvaEventObject<Event>;

interface BaseCanvasItemComponentProps extends BaseCanvasItemProps {
  boundBoxFunc?: (oldBox: any, newBox: any) => any;
  children: React.ReactNode;
  onTransform?: (activeAnchor: string | null, newBox: any) => void;
}

export const BaseCanvasItem: React.FC<BaseCanvasItemComponentProps> = ({
  x,
  y,
  width,
  height,
  rotation = 0,
  isDraggable = false,
  isSelected = false,
  children,
  boundBoxFunc,
  onDragEnd,
  onTransformEnd,
  onClick,
  onTransform,
}) => {
  const itemRef = useRef<Konva.Group>(null);
  const lastClickRef = useRef<number>(0); // Store last click time
  const [activeAnchor, setActiveAnchor] = useState<string | null>(null);
  
  // Handle clicks with debounce to prevent animation flicker
  const handleInteraction = (e: KonvaEvent) => {
    if (!onClick) return;
    
    const now = performance.now();
    // Don't re-process clicks that are too close together (prevents double animation triggers)
    if (now - lastClickRef.current < CLICK_DEBOUNCE_TIME) return;
    lastClickRef.current = now;
    
    // Call the original click handler
    onClick(e);
  };
  
  // Handle drag end with proper typing
  const handleDragEnd = (e: KonvaEvent) => {
    if (onDragEnd) {
      onDragEnd(e);
    }
  };
  
  // Listen for transform start to capture active anchor
  const handleTransformStart = (e: KonvaEvent) => {
    // This will be updated during transform via the data-active-anchor attribute
    // set by the TransformerWrapper
  };
  
  // Handle transform with active anchor
  const handleTransform = (activeAnchor: string | null, newBox: any) => {
    if (onTransform && activeAnchor) {
      // Store the active anchor as an attribute on this node
      if (itemRef.current) {
        itemRef.current.setAttr('data-active-anchor', activeAnchor);
        console.log(`BaseCanvasItem: Set active anchor attribute: ${activeAnchor}`);
      }
      
      // Pass to parent handler
      onTransform(activeAnchor, newBox);
    }
  };
  
  // Handle transform end with additional data
  const handleTransformEnd = (e: KonvaEvent) => {
    if (onTransformEnd && itemRef.current) {
      const node = itemRef.current;
      const scaleX = node.scaleX();
      const scaleY = node.scaleY();
      const newWidth = Math.round(width * scaleX);
      const newHeight = Math.round(height * scaleY);
      
      // Reset scale to prevent compounding scale factors
      node.scaleX(1);
      node.scaleY(1);
      
      // Get the active anchor directly from the node's attribute
      // This was set by the TransformerWrapper during transform
      const nodeActiveAnchor = node.getAttr('data-active-anchor') || activeAnchor;
      
      let finalWidth = newWidth;
      let finalHeight = newHeight;
      
      // Apply constraints based on which handle was used
      if (nodeActiveAnchor === 'middle-left' || nodeActiveAnchor === 'middle-right') {
        // For side handles, preserve original height
        finalHeight = height;
      } else if (nodeActiveAnchor === 'top-center' || nodeActiveAnchor === 'bottom-center') {
        // For top/bottom handles, preserve original width
        finalWidth = width;
      } else if (nodeActiveAnchor && (nodeActiveAnchor.includes('top-') || nodeActiveAnchor.includes('bottom-'))) {
        // Corner anchors - ensure aspect ratio is maintained
        const originalRatio = width / height;
        const newRatio = newWidth / newHeight;
        
        if (Math.abs(newRatio - originalRatio) > 0.01) {
          // Force exact aspect ratio for corner handles
          finalHeight = Math.round(finalWidth / originalRatio);
        }
      }
      
      // Call handler with enhanced event data including new dimensions and active anchor
      onTransformEnd({
        ...e,
        target: {
          ...e.target,
          x: node.x(),
          y: node.y(),
          width: finalWidth,
          height: finalHeight,
          rotation: node.rotation(),
        },
        activeAnchor: nodeActiveAnchor, 
      });
      
      // Clear the active anchor attribute
      node.setAttr('data-active-anchor', null);
      setActiveAnchor(null);
    }
  };
  
  // Set a selected class on the node when selected
  useEffect(() => {
    if (!itemRef.current) return;
    
    if (isSelected) {
      itemRef.current.setAttr('data-selected', true);
      // Add other visual indicators if needed, like extra shadow
    } else {
      itemRef.current.setAttr('data-selected', false);
    }
  }, [isSelected]);
  
  return (
    <>
      <Group
        x={x}
        y={y}
        width={width}
        height={height}
        rotation={rotation}
        draggable={isDraggable}
        onClick={handleInteraction}
        onTap={handleInteraction}
        ref={itemRef}
        onDragEnd={handleDragEnd}
        onTransformStart={handleTransformStart}
        onTransformEnd={handleTransformEnd}
        perfectDrawEnabled={false}
      >
        {children}
      </Group>
      
      <TransformerWrapper 
        isSelected={isSelected} 
        nodeRef={itemRef} 
        boundBoxFunc={boundBoxFunc}
        onTransform={handleTransform}
      />
    </>
  );
}; 