import React, { useEffect, useRef, useState } from 'react';
import { Image as KonvaImage } from 'react-konva';
import Konva from 'konva';
import { ImageItemProps } from '@/modules/canvas/components/canvas-items/canvas-item-types';
import { BaseCanvasItem } from '@/modules/canvas/components/canvas-items/base-canvas-item';

// Constants for image behavior
const MIN_ASPECT_RATIO_DIFFERENCE = 0.01;

export const ImageCanvasItem: React.FC<ImageItemProps> = (props) => {
  const { 
    width, 
    height,
    x,
    y,
    image,
    onImageUpdate,
    ...baseProps 
  } = props;
  
  // Create a ref for the KonvaImage component
  const imageRef = useRef<Konva.Image>(null);
  
  // Keep track of the original image dimensions and crop information
  const originalDimensionsRef = useRef<{
    width: number;
    height: number;
    imageWidth: number;
    imageHeight: number;
    cropX: number;
    cropY: number;
    cropWidth: number;
    cropHeight: number;
  }>({
    width: width,
    height: height,
    imageWidth: image ? image.width : width,
    imageHeight: image ? image.height : height,
    cropX: 0,
    cropY: 0,
    cropWidth: image ? image.width : width,
    cropHeight: image ? image.height : height,
  });
  
  // Keep track of the current crop state
  const [cropConfig, setCropConfig] = useState({
    cropX: 0,
    cropY: 0,
    cropWidth: image ? image.width : width,
    cropHeight: image ? image.height : height,
  });
  
  // Ref to track current crop configuration for direct manipulation
  // This allows us to bypass React's state update cycle for real-time transformations
  const currentCropRef = useRef(cropConfig);
  
  // Initialize crop config when image changes or when image.cropConfig is updated
  useEffect(() => {
    if (image) {
      let newCropConfig;
      
      // If the image has a crop config, use it
      if (image.cropConfig) {
        newCropConfig = { ...image.cropConfig };
        
        // Validate that crop dimensions aren't larger than actual image
        const imgElement = image.element || image.image;
        const naturalWidth = imgElement.naturalWidth || width;
        const naturalHeight = imgElement.naturalHeight || height;
        
        // If crop dimensions are significantly larger than the image dimensions, reset them
        if (newCropConfig.cropWidth > naturalWidth * 1.5) {
          console.log(`Correcting oversized cropWidth ${newCropConfig.cropWidth} to match natural width ${naturalWidth}`);
          newCropConfig.cropWidth = naturalWidth;
        }
        
        if (newCropConfig.cropHeight > naturalHeight * 1.5) {
          console.log(`Correcting oversized cropHeight ${newCropConfig.cropHeight} to match natural height ${naturalHeight}`);
          newCropConfig.cropHeight = naturalHeight;
        }
      } else {
        // Otherwise initialize with default values
        const imgElement = image.element || image.image;
        const naturalWidth = imgElement.naturalWidth || width;
        const naturalHeight = imgElement.naturalHeight || height;
        
        newCropConfig = {
          cropX: 0,
          cropY: 0,
          cropWidth: naturalWidth,
          cropHeight: naturalHeight,
        };
      }
      
      setCropConfig(newCropConfig);
      currentCropRef.current = newCropConfig;
      
      // Update original dimensions reference
      originalDimensionsRef.current = {
        width: width,
        height: height,
        imageWidth: image.width,
        imageHeight: image.height,
        cropX: newCropConfig.cropX,
        cropY: newCropConfig.cropY,
        cropWidth: newCropConfig.cropWidth,
        cropHeight: newCropConfig.cropHeight,
      };
      
      console.log('Image dimensions initialized:', {
        image: { width: image.width, height: image.height },
        naturalDimensions: {
          width: (image.element || image.image).naturalWidth, 
          height: (image.element || image.image).naturalHeight
        },
        container: { width, height },
        crop: newCropConfig
      });
    }
  }, [image, image?.cropConfig, width, height]);
  
  // Update currentCropRef when cropConfig state changes
  useEffect(() => {
    currentCropRef.current = cropConfig;
  }, [cropConfig]);
  
  // Bound box function to maintain aspect ratio for non-side anchors
  const boundBoxFunc = (oldBox: any, newBox: any) => {
    // Ensure minimum size to prevent collapse
    if (newBox.width < 10 || newBox.height < 10) {
      return oldBox;
    }
    
    // Get the original aspect ratio
    const originalRatio = width / height;
    
    // For corner anchors, maintain aspect ratio
    const newRatio = newBox.width / newBox.height;
    if (Math.abs(newRatio - originalRatio) > MIN_ASPECT_RATIO_DIFFERENCE) {
      // Determine which dimension changed more
      const widthChange = newBox.width / oldBox.width;
      const heightChange = newBox.height / oldBox.height;
      
      if (widthChange > heightChange) {
        // Width changed more, adjust height to match
        newBox.height = newBox.width / originalRatio;
      } else {
        // Height changed more, adjust width to match
        newBox.width = newBox.height * originalRatio;
      }
    }
    
    return newBox;
  };
  
  // Handle transform with enhanced context about the active anchor
  const handleTransform = (activeAnchor: string | null, newBox: any) => {
    if (!activeAnchor || !image || !newBox) return;
    
    // Store the current dimensions for reference
    const currentWidth = width;
    const currentHeight = height;
    const originalRatio = currentWidth / currentHeight;
    
    // Get current crop settings from the ref (not state)
    const currentCrop = currentCropRef.current;
    
    // Different handling based on anchor type
    if (activeAnchor === 'middle-left' || activeAnchor === 'middle-right') {
      // For side handles, only update width, preserve height
      const scaleX = newBox.width / currentWidth;
      
      // Update the crop configuration in the ref - only affect width
      currentCropRef.current = {
        ...currentCrop,
        cropWidth: currentCrop.cropWidth * scaleX,
      };
      
      // If onImageUpdate is provided, call it with the updated dimensions
      if (onImageUpdate) {
        onImageUpdate({
          ...image,
          width: newBox.width,
          height: currentHeight, // Preserve original height
          position: { x: newBox.x, y: newBox.y },
          rotation: newBox.rotation || 0,
        });
      }
    } else if (activeAnchor === 'top-center' || activeAnchor === 'bottom-center') {
      // For top/bottom handles, only update height, preserve width
      const scaleY = newBox.height / currentHeight;
      
      // Update the crop configuration in the ref - only affect height
      currentCropRef.current = {
        ...currentCrop,
        cropHeight: currentCrop.cropHeight * scaleY,
      };
      
      // If onImageUpdate is provided, call it with the updated dimensions
      if (onImageUpdate) {
        onImageUpdate({
          ...image,
          width: currentWidth, // Preserve original width
          height: newBox.height,
          position: { x: newBox.x, y: newBox.y },
          rotation: newBox.rotation || 0,
        });
      }
    } else if (activeAnchor && (activeAnchor.includes('top-') || activeAnchor.includes('bottom-'))) {
      // For corner anchors, ensure aspect ratio is maintained
      const newRatio = newBox.width / newBox.height;
      
      if (Math.abs(newRatio - originalRatio) > MIN_ASPECT_RATIO_DIFFERENCE) {
        if (newBox.width / originalRatio > newBox.height) {
          newBox.width = newBox.height * originalRatio;
        } else {
          newBox.height = newBox.width / originalRatio;
        }
      }
      
      // Update both dimensions for corner anchors
      const scaleX = newBox.width / currentWidth;
      const scaleY = newBox.height / currentHeight;
      
      // Update the crop configuration in the ref
      currentCropRef.current = {
        ...currentCrop,
        cropWidth: currentCrop.cropWidth * scaleX,
        cropHeight: currentCrop.cropHeight * scaleY,
      };
      
      // If onImageUpdate is provided, call it with the updated dimensions
      if (onImageUpdate) {
        onImageUpdate({
          ...image,
          width: newBox.width,
          height: newBox.height,
          position: { x: newBox.x, y: newBox.y },
          rotation: newBox.rotation || 0,
        });
      }
    }
  };
  
  // Handle transform end with enhanced context about the active anchor
  const handleTransformEnd = (e: any) => {
    if (!onImageUpdate || !image) return;
    
    const node = e.target;
    const { x, y, width, height, rotation } = node;
    
    // The activeAnchor is often set on the parent Group node (BaseCanvasItem)
    let activeAnchor: string | null = null;
    
    // Try to get active anchor from various places
    if (node.parent && typeof node.parent.getAttr === 'function') {
      // From parent Group
      activeAnchor = node.parent.getAttr('data-active-anchor');
      console.log('Got activeAnchor from parent:', activeAnchor);
    } else if (node.getAttr && typeof node.getAttr === 'function') {
      // From node itself
      activeAnchor = node.getAttr('data-active-anchor');
      console.log('Got activeAnchor from node:', activeAnchor);
    } else if (node.attrs && node.attrs['data-active-anchor']) {
      // From attrs object
      activeAnchor = node.attrs['data-active-anchor'];
      console.log('Got activeAnchor from attrs:', activeAnchor);
    }
    
    console.log(`Transform end with anchor: ${activeAnchor}, width: ${width}, height: ${height}`);
    
    // Create updated image object with new dimensions, position, and crop information
    const updatedImage = {
      ...image,
      position: { x, y },
      width: width,
      height: height,
      rotation: rotation,
      // Reset scale factors since we're applying actual dimensions
      scaleX: 1,
      scaleY: 1,
      // Add crop information for rendering - use the ref value for up-to-date data
      cropConfig: { ...currentCropRef.current }
    };
    
    // Log the final crop config being saved
    console.log('Final crop config for update:', updatedImage.cropConfig);
    
    // Inform parent of the update
    onImageUpdate(updatedImage);
  };
  
  // Mark newly added images as not-new to prevent continuous animations
  useEffect(() => {
    if (image && onImageUpdate && image.isNew) {
      // After initial render, remove the isNew flag and update position
      const timeoutId = setTimeout(() => {
        onImageUpdate({ 
          ...image, 
          isNew: false,
          position: { x, y },
          width,
          height
        });
      }, 100); // Short delay to ensure the component is fully mounted
      
      return () => clearTimeout(timeoutId);
    }
  }, [image, onImageUpdate, x, y, width, height]);
  
  // If no image is provided, render nothing
  if (!image) return null;
  
  // Calculate the opacity value to use
  const opacity = image.opacity !== undefined ? image.opacity : 1;
  
  // Determine appropriate scaling factors
  const scaleX = image.scaleX !== undefined ? image.scaleX : 1;
  const scaleY = image.scaleY !== undefined ? image.scaleY : 1;
  
  // Get crop config from image if available, otherwise use local state
  const imageCropConfig = image.cropConfig || cropConfig;
  
  return (
    <BaseCanvasItem
      {...baseProps}
      x={x}
      y={y}
      width={width}
      height={height}
      boundBoxFunc={boundBoxFunc}
      onTransformEnd={handleTransformEnd}
      onTransform={handleTransform}
    >
      <KonvaImage
        ref={imageRef}
        image={image?.element || image?.image}
        width={width}
        height={height}
        scaleX={scaleX}
        scaleY={scaleY}
        offsetX={(width * (1 - scaleX)) / 2}
        offsetY={(height * (1 - scaleY)) / 2}
        perfectDrawEnabled={false}
        imageSmoothingEnabled={true}
        opacity={opacity}
        listening={true}
        // Add crop properties
        cropX={imageCropConfig.cropX}
        cropY={imageCropConfig.cropY}
        cropWidth={imageCropConfig.cropWidth}
        cropHeight={imageCropConfig.cropHeight}
      />
    </BaseCanvasItem>
  );
}; 