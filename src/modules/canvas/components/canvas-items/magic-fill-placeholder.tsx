/* Updated MagicFillPlaceholder component to crop the input image and overlay an animated gradient mask */
"use client";

import React from "react";

interface MagicFillPlaceholderProps {
  x: number;
  y: number;
  width: number;
  height: number;
  inputImageSrc?: string;
  aspectRatio?: string;
}

export const MagicFillPlaceholder: React.FC<MagicFillPlaceholderProps> = ({
  x,
  y,
  width,
  height,
  inputImageSrc,
  aspectRatio = "16:10",
}) => {
  const aspectDisplay = aspectRatio || "16:10";
  
  return (
    <div
      style={{
        position: "absolute",
        top: y,
        left: x,
        width: width,
        height: height,
        overflow: "hidden",
        pointerEvents: "none",
      }}
    >
      {inputImageSrc && (
        <img
          src={inputImageSrc}
          alt="Preserved part of input"
          style={{
            position: "absolute",
            left: `-${x}px`,
            top: `-${y}px`,
            objectFit: "none",
          }}
        />
      )}
      <div
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          background:
            "linear-gradient(130deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.8) 50%, rgba(255,255,255,0.2) 100%)",
          backgroundSize: "200% 200%",
          animation: "gradientAnimation 3s ease infinite",
        }}
      />
      <div
        style={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          color: "rgba(0, 0, 0, 0.6)",
          fontWeight: "bold",
          fontSize: "16px",
          padding: "4px 8px",
          borderRadius: "4px",
          background: "rgba(255, 255, 255, 0.7)",
        }}
      >
        {aspectDisplay}
      </div>
      <style jsx>{`
        @keyframes gradientAnimation {
          0% {
            background-position: 0% 50%;
          }
          50% {
            background-position: 100% 50%;
          }
          100% {
            background-position: 0% 50%;
          }
        }
      `}</style>
    </div>
  );
};
