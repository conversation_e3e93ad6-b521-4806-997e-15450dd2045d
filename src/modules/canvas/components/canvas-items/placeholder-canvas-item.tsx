import React, { useEffect, useRef, useState } from 'react';
import { Rect, Text, Image as KonvaImage } from 'react-konva';
import Konva from 'konva';
import { PlaceholderItemProps } from './canvas-item-types';
import { BaseCanvasItem } from './base-canvas-item';
import { GradientAnimationManager } from '../gradient-animation-manager';

// Extract constants for consistent styling
const MAX_PROMPT_LENGTH = 24;
const TEXT_BACKGROUND_COLOR = 'rgba(255, 255, 255, 0.8)';
const TEXT_COLOR = 'rgba(0, 0, 0, 0.7)';
const FONT_SIZE = 14;
const CORNER_RADIUS = 4;
const IMAGE_TRANSITION_DURATION = 200; // ms for image fade-in

export const PlaceholderCanvasItem: React.FC<PlaceholderItemProps> = (props) => {
  const { 
    width, 
    height, 
    prompt,
    status = 'pending',
    imageUrl,
    type = 'generate', // Default to 'generate' if not specified
    ...baseProps 
  } = props;
  
  const rectRef = useRef<Konva.Rect>(null);
  // Generate a stable ID for this component instance
  const idRef = useRef<string>(`placeholder-${Math.random().toString(36).substr(2, 9)}`);
  const [image, setImage] = useState<HTMLImageElement | null>(null);
  const [imageOpacity, setImageOpacity] = useState(0);
  const [placeholderOpacity, setPlaceholderOpacity] = useState(1);
  
  // Load image when imageUrl is provided
  useEffect(() => {
    if (imageUrl) {
      const img = new Image();
      img.crossOrigin = 'Anonymous';
      
      img.onload = () => {
        // Start showing the image with a fade transition
        setImage(img);
        
        // Quick animation to fade in the image and fade out the placeholder
        setImageOpacity(0.2);
        setTimeout(() => {
          setImageOpacity(0.6);
          setPlaceholderOpacity(0.6);
          
          setTimeout(() => {
            setImageOpacity(1);
            setPlaceholderOpacity(0);
            
            // Stop the gradient animation once the image is shown
            if (idRef.current) {
              const animationManager = GradientAnimationManager.getInstance();
              animationManager.stopAnimation(idRef.current);
            }
          }, IMAGE_TRANSITION_DURATION / 3);
        }, IMAGE_TRANSITION_DURATION / 3);
      };
      
      img.onerror = (err) => {
        console.error('Failed to load image:', err);
      };
      
      img.src = imageUrl;
    } else {
      // Reset states if imageUrl is removed
      setImage(null);
      setImageOpacity(0);
      setPlaceholderOpacity(1);
    }
  }, [imageUrl]);
  
  // Bound box function to maintain aspect ratio
  const boundBoxFunc = (oldBox: any, newBox: any) => {
    // Ensure minimum size to prevent collapse
    if (newBox.width < 10 || newBox.height < 10) {
      return oldBox;
    }
    
    return newBox;
  };
  
  // Get gradient colors based on status and type
  const getGradientColors = () => {
    // If this is a magic-fill task, use different colorings
    if (type === 'magic-fill') {
      switch (status) {
        case 'generating':
          return [
            0, 'rgba(218,165,32,0.035)',
            0.1, 'rgba(218,165,32,0.14)',
            0.3, 'rgba(218,165,32,0.35)',
            0.5, 'rgba(218,165,32,0.7)',
            0.7, 'rgba(218,165,32,0.35)',
            0.9, 'rgba(218,165,32,0.14)',
            1, 'rgba(218,165,32,0.035)'
          ];
        case 'complete':
          return [
            0, 'rgba(255,215,0,0.035)',
            0.1, 'rgba(255,215,0,0.14)',
            0.3, 'rgba(255,215,0,0.35)',
            0.5, 'rgba(255,215,0,0.7)',
            0.7, 'rgba(255,215,0,0.35)',
            0.9, 'rgba(255,215,0,0.14)',
            1, 'rgba(255,215,0,0.035)'
          ];
        case 'error':
          return [
            0, 'rgba(255,99,71,0.035)',
            0.1, 'rgba(255,99,71,0.14)',
            0.3, 'rgba(255,99,71,0.35)',
            0.5, 'rgba(255,99,71,0.7)',
            0.7, 'rgba(255,99,71,0.35)',
            0.9, 'rgba(255,99,71,0.14)',
            1, 'rgba(255,99,71,0.035)'
          ];
        case 'pending':
        default:
          return [
            0, 'rgba(255,239,213,0.035)',
            0.1, 'rgba(255,239,213,0.14)',
            0.3, 'rgba(255,239,213,0.35)',
            0.5, 'rgba(255,239,213,0.7)',
            0.7, 'rgba(255,239,213,0.35)',
            0.9, 'rgba(255,239,213,0.14)',
            1, 'rgba(255,239,213,0.035)'
          ];
      }
    }
    
    // Default (generate) colors
    switch (status) {
      case 'generating':
        return [
          0, 'rgba(173,216,230,0.035)',
          0.1, 'rgba(173,216,230,0.14)',
          0.3, 'rgba(173,216,230,0.35)',
          0.5, 'rgba(173,216,230,0.7)',
          0.7, 'rgba(173,216,230,0.35)',
          0.9, 'rgba(173,216,230,0.14)',
          1, 'rgba(173,216,230,0.035)'
        ];
      case 'complete':
        return [
          0, 'rgba(144,238,144,0.035)',
          0.1, 'rgba(144,238,144,0.14)',
          0.3, 'rgba(144,238,144,0.35)',
          0.5, 'rgba(144,238,144,0.7)',
          0.7, 'rgba(144,238,144,0.35)',
          0.9, 'rgba(144,238,144,0.14)',
          1, 'rgba(144,238,144,0.035)'
        ];
      case 'error':
        return [
          0, 'rgba(255,99,71,0.035)',
          0.1, 'rgba(255,99,71,0.14)',
          0.3, 'rgba(255,99,71,0.35)',
          0.5, 'rgba(255,99,71,0.7)',
          0.7, 'rgba(255,99,71,0.35)',
          0.9, 'rgba(255,99,71,0.14)',
          1, 'rgba(255,99,71,0.035)'
        ];
      case 'pending':
      default:
        return [
          0, 'rgba(255,255,255,0.035)',
          0.1, 'rgba(255,255,255,0.14)',
          0.3, 'rgba(255,255,255,0.35)',
          0.5, 'rgba(255,255,255,0.7)',
          0.7, 'rgba(255,255,255,0.35)',
          0.9, 'rgba(255,255,255,0.14)',
          1, 'rgba(255,255,255,0.035)'
        ];
    }
  };
  
  // Get border color based on status and type
  const getBorderColor = () => {
    // Different border colors for magic-fill
    if (type === 'magic-fill') {
      switch (status) {
        case 'generating':
          return 'rgba(218,165,32,0.4)';
        case 'complete':
          return 'rgba(255,215,0,0.4)';
        case 'error':
          return 'rgba(255,0,0,0.3)';
        case 'pending':
        default:
          return 'rgba(255,239,213,0.2)';
      }
    }
    
    // Default (generate) border colors
    switch (status) {
      case 'generating':
        return 'rgba(0,0,255,0.3)';
      case 'complete':
        return 'rgba(0,128,0,0.3)';
      case 'error':
        return 'rgba(255,0,0,0.3)';
      case 'pending':
      default:
        return 'rgba(0,0,0,0.1)';
    }
  };
  
  // Setup gradient animation using our manager
  useEffect(() => {
    const animationManager = GradientAnimationManager.getInstance();
    
    if (rectRef.current && !image) {
      const rect = rectRef.current;
      
      // Wait for the next frame to ensure the layer is available
      requestAnimationFrame(() => {
        if (rect.getLayer()) {
          // Note: The original API doesn't accept speedFactor, so we'll adjust the animation in other ways
          animationManager.startAnimation(rect, width, height, idRef.current);
          
          // If generating, make the animation faster by restarting it with a lower delay
          if (status === 'generating') {
            // Optional: add custom animation speed control here if supported
          }
        }
      });
      
      // Cleanup on unmount
      return () => {
        animationManager.stopAnimation(idRef.current);
      };
    }
  }, [status, width, height, image]); // Run when status changes or image appears
  
  // Handle interaction to ensure animation continues
  const handleClick = (e: any) => {
    if (baseProps.onClick) {
      // Preserve the animation by ensuring it continues running
      requestAnimationFrame(() => {
        if (!image) {
          const animationManager = GradientAnimationManager.getInstance();
          animationManager.ensureAnimationRunning(idRef.current);
        }
        
        // Call the original click handler
        baseProps.onClick!(e);
      });
    }
  };
  
  // Format the prompt text, truncating if too long
  const displayPrompt = prompt && prompt.length > MAX_PROMPT_LENGTH
    ? `${prompt.substring(0, MAX_PROMPT_LENGTH)}...`
    : prompt;
  
  // Calculate text positioning
  const textWidth = displayPrompt ? Math.min(displayPrompt.length * 8, width - 20) : 0;
  
  return (
    <BaseCanvasItem
      {...baseProps}
      width={width}
      height={height}
      boundBoxFunc={boundBoxFunc}
      onClick={handleClick}
    >
      {/* Placeholder gradient background */}
      <Rect
        ref={rectRef}
        width={width}
        height={height}
        fillLinearGradientStartPoint={{ x: -width, y: 0 }}
        fillLinearGradientEndPoint={{ x: width, y: height }}
        fillLinearGradientColorStops={getGradientColors()}
        perfectDrawEnabled={false}
        cornerRadius={CORNER_RADIUS}
        stroke={getBorderColor()}
        strokeWidth={1}
        shadowColor="rgba(0,0,0,0.1)"
        shadowBlur={4}
        shadowOffset={{ x: 0, y: 2 }}
        shadowOpacity={0.5}
        opacity={placeholderOpacity}
      />
      
      {/* Display the image on top when loaded */}
      {image && (
        <KonvaImage
          image={image}
          width={width}
          height={height}
          cornerRadius={CORNER_RADIUS}
          shadowColor="rgba(0,0,0,0.2)"
          shadowBlur={4}
          shadowOffset={{ x: 0, y: 2 }}
          shadowOpacity={0.5}
          opacity={imageOpacity}
          perfectDrawEnabled={true}
          imageSmoothingEnabled={true}
        />
      )}
      
      {displayPrompt && !image && (
        <Text
          text={displayPrompt}
          fontSize={FONT_SIZE}
          fontFamily="Inter, sans-serif"
          fill={TEXT_COLOR}
          x={width / 2}
          y={height / 2 + 20}
          offsetX={textWidth / 2}
          offsetY={-8}
          padding={8}
          backgroundColor={TEXT_BACKGROUND_COLOR}
          cornerRadius={CORNER_RADIUS}
          align="center"
          width={textWidth}
          ellipsis={true}
          opacity={placeholderOpacity}
        />
      )}
      
      {/* Status indicator - only show if we don't have an image */}
      {status && status !== 'pending' && !image && (
        <Text
          text={type === 'magic-fill' ? `${status} (Magic Fill)` : status}
          fontSize={FONT_SIZE - 2}
          fontFamily="Inter, sans-serif"
          fill={status === 'error' ? '#FF0000' : 
               status === 'complete' ? '#008800' : '#0066CC'}
          x={width / 2}
          y={height - 10}
          offsetX={(type === 'magic-fill' ? status.length * 5 : status.length * 3)}
          padding={4}
          backgroundColor={TEXT_BACKGROUND_COLOR}
          cornerRadius={CORNER_RADIUS}
          align="center"
          opacity={placeholderOpacity}
          name="status-text"
          className="status-text generating-indicator"
        />
      )}
    </BaseCanvasItem>
  );
}; 