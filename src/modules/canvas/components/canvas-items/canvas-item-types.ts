import { GeneratedImage } from '@/modules/canvas/types/canvas';

// SVG icon for rotater
export const ROTATE_ICON = "data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iaXNvLTg4NTktMSI/Pg0KPCEtLSBHZW5lcmF0b3I6IEFkb2JlIElsbHVzdHJhdG9yIDE5LjAuMCwgU1ZHIEV4cG9ydCBQbHVnLUluIC4gU1ZHIFZlcnNpb246IDYuMDAgQnVpbGQgMCkgIC0tPg0KPHN2ZyB2ZXJzaW9uPSIxLjEiIGlkPSJDYXBhXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4Ig0KCSB2aWV3Qm94PSIwIDAgNDI2LjY2NyA0MjYuNjY3IiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IDAgMCA0MjYuNjY3IDQyNi42Njc7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4NCjxnPg0KCTxnPg0KCQk8cGF0aCBkPSJNMjEzLjMzMyw4NS4zMzNWMEwxMDYuNjY3LDEwNi42NjdsMTA2LjY2NywxMDYuNjY3VjEyOGM3MC43MiwwLDEyOCw1Ny4yOCwxMjgsMTI4cy01Ny4yOCwxMjgtMTI4LDEyOHMtMTI4LTU3LjI4LTEyOC0xMjgNCgkJCUg0Mi42NjdjMCw5NC4yOTMsNzYuMzczLDE3MC42NjcsMTcwLjY2NywxNzAuNjY3UzM4NCwzNTAuMjkzLDM4NCwyNTZTMzA3LjYyNyw4NS4zMzMsMjEzLjMzMyw4NS4zMzN6Ii8+DQoJPC9nPg0KPC9nPg0KPGc+DQo8L2c+DQo8Zz4NCjwvZz4NCjxnPg0KPC9nPg0KPGc+DQo8L2c+DQo8Zz4NCjwvZz4NCjxnPg0KPC9nPg0KPGc+DQo8L2c+DQo8Zz4NCjwvZz4NCjxnPg0KPC9nPg0KPGc+DQo8L2c+DQo8Zz4NCjwvZz4NCjxnPg0KPC9nPg0KPC9zdmc+DQo=";

// Base props that are common to all canvas items
export interface BaseCanvasItemProps {
  x: number;
  y: number;
  width: number;
  height: number;
  rotation?: number;
  isDraggable?: boolean;
  isSelected?: boolean;
  onDragEnd?: (e: any) => void;
  onTransformEnd?: (e: any) => void;
  onClick?: (e: any) => void;
}

// Props specific to placeholder items
export interface PlaceholderItemProps extends BaseCanvasItemProps {
  aspectRatio?: string;
  prompt?: string;
  status?: 'pending' | 'generating' | 'complete' | 'error';
  isFading?: boolean;
  imageUrl?: string;
  type?: 'generate' | 'magic-fill';
}

// Props specific to image items
export interface ImageItemProps extends BaseCanvasItemProps {
  image: GeneratedImage;
  onImageUpdate?: (image: GeneratedImage) => void;
}

// Combined props type for the unified component
export interface UnifiedCanvasItemProps extends BaseCanvasItemProps {
  // Content type
  type: 'placeholder' | 'image';
  
  // For image type
  image?: GeneratedImage;
  
  // For placeholder type
  aspectRatio?: string;
  prompt?: string;
  status?: 'pending' | 'generating' | 'complete' | 'error';
  isFading?: boolean;
  imageUrl?: string;
  taskType?: 'generate' | 'magic-fill';
  
  // Event handlers specific to image
  onImageUpdate?: (image: GeneratedImage) => void;
} 