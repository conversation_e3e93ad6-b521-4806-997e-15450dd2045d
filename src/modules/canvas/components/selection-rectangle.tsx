import React, { useEffect, useRef, useState } from "react";
import { Rect, Transformer, Group, Text, Label, Tag } from "react-konva";
import Konva from "konva";
import { Download } from "lucide-react";
import { useCanvasExport } from "../hooks/use-canvas-export";

interface SelectionRectProps {
  x: number;
  y: number;
  width: number;
  height: number;
  onChange: (attrs: {
    x: number;
    y: number;
    width: number;
    height: number;
  }) => void;
  stageRef?: React.RefObject<Konva.Stage>;
}

export const SelectionRect: React.FC<SelectionRectProps> = ({
  x,
  y,
  width,
  height,
  onChange,
  stageRef,
}) => {
  const rectRef = useRef<Konva.Rect>(null);
  const trRef = useRef<Konva.Transformer>(null);
  const groupRef = useRef<Konva.Group>(null);
  const [isExporting, setIsExporting] = useState(false);
  const { exportSelectionAsImage } = useCanvasExport();

  // Update the transformer whenever the rectangle's dimensions change.
  useEffect(() => {
    if (trRef.current && rectRef.current) {
      trRef.current.nodes([rectRef.current]);
      trRef.current.getLayer()?.batchDraw();
    }
  }, [x, y, width, height]);

  // This effect ensures the transformer is set up correctly
  useEffect(() => {
    if (trRef.current && rectRef.current) {
      // Attach transformer to the rectangle
      trRef.current.nodes([rectRef.current]);
      
      // Force redraw to immediately show the transformer
      const layer = trRef.current.getLayer();
      if (layer) {
        layer.batchDraw();
      }
    }
  }, []);

  // Function to export the selection area as image
  const exportSelection = async () => {
    if (!stageRef || !stageRef.current) {
      console.error("Stage reference is required for export");
      return;
    }

    setIsExporting(true);

    try {
      console.log("Exporting selection with dimensions:", { x, y, width, height });
      
      // Use the enhanced export functionality
      await exportSelectionAsImage(
        { x, y, width, height }, 
        stageRef, 
        `canvas-selection-${Date.now()}.png`
      );
      
      console.log("Selection exported successfully");
    } catch (error) {
      console.error("Failed to export selection:", error);
    } finally {
      setIsExporting(false);
    }
  };

  // Helper function to format dimensions for display
  const formatDimensions = () => {
    return `${Math.round(width)} × ${Math.round(height)}`;
  };
  
  // Create a download button within the selection rectangle
  const ExportButton = () => (
    <Label
      x={x + width - 30}
      y={y + height - 30}
      onClick={exportSelection}
      onTap={exportSelection}
      name="ui-control"
      className="export-button"
    >
      <Tag
        fill="#6B46C1"
        stroke="#ffffff"
        strokeWidth={1}
        cornerRadius={4}
        pointerDirection="bottom-right"
        pointerWidth={10}
        pointerHeight={10}
        lineJoin="round"
      />
      <Text
        text="Export"
        fontSize={12}
        padding={5}
        fill="#ffffff"
        fontStyle="bold"
      />
    </Label>
  );

  return (
    <>
      <Group ref={groupRef}>
        <Rect
          ref={rectRef}
          x={x}
          y={y}
          width={width}
          height={height}
          fillLinearGradientColorStops={[0, "#8B5CF6", 1, "#4F46E5"]}
          fillLinearGradientStartPoint={{ x: 0, y: 0 }}
          fillLinearGradientEndPoint={{ x: width, y: height }}
          opacity={0.7}
          stroke="#6B46C1"
          strokeWidth={2}
          draggable
          name="selection-rect"
          onDragStart={() => {
            // Disable transformer during drag for better performance
            if (trRef.current) {
              trRef.current.visible(false);
            }
          }}
          onDragEnd={(e) => {
            // Re-enable transformer after drag
            if (trRef.current) {
              trRef.current.visible(true);
            }
            
            const node = rectRef.current;
            if (node) {
              onChange({
                x: node.x(),
                y: node.y(),
                width: node.width(),
                height: node.height(),
              });
            }
          }}
          onTransformStart={() => {
            // Store initial values for reference during transformation
            const node = rectRef.current;
            if (node) {
              node.setAttrs({
                lastWidth: node.width() * node.scaleX(),
                lastHeight: node.height() * node.scaleY(),
              });
            }
          }}
          onTransform={() => {
            // Update gradient during transformation for visual feedback
            const node = rectRef.current;
            if (node) {
              const newWidth = node.width() * node.scaleX();
              const newHeight = node.height() * node.scaleY();
              
              node.fillLinearGradientEndPoint({ 
                x: newWidth, 
                y: newHeight 
              });
            }
          }}
          onTransformEnd={() => {
            const node = rectRef.current;
            if (node) {
              // Get the final scaled dimensions
              const scaleX = node.scaleX();
              const scaleY = node.scaleY();
              
              // Enforce minimum size
              const minSize = 50;
              const newWidth = Math.max(minSize, node.width() * scaleX);
              const newHeight = Math.max(minSize, node.height() * scaleY);
              
              // Reset scale and update dimensions
              node.scaleX(1);
              node.scaleY(1);
              node.width(newWidth);
              node.height(newHeight);
              
              // Update the gradient endpoints for the new size
              node.fillLinearGradientEndPoint({ 
                x: newWidth, 
                y: newHeight 
              });
              
              // Notify parent of changes
              onChange({
                x: node.x(),
                y: node.y(),
                width: newWidth,
                height: newHeight,
              });
            }
          }}
        />
        
        {/* Show dimensions label at top */}
        <Label x={x + width / 2 - 40} y={y - 25}>
          <Tag
            fill="rgba(0,0,0,0.6)"
            cornerRadius={3}
            pointerDirection="bottom"
            pointerWidth={10}
            pointerHeight={5}
          />
          <Text
            text={formatDimensions()}
            padding={5}
            fontSize={12}
            fill="#ffffff"
          />
        </Label>
        
        {/* Export button */}
        {!isExporting && <ExportButton />}
      </Group>
      
      <Transformer 
        ref={trRef} 
        rotateEnabled={false}
        boundBoxFunc={(oldBox, newBox) => {
          // Enforce minimum dimensions
          if (newBox.width < 50 || newBox.height < 50) {
            return oldBox;
          }
          return newBox;
        }}
        enabledAnchors={[
          'top-left', 'top-right', 
          'bottom-left', 'bottom-right',
          'middle-left', 'middle-right',
          'top-center', 'bottom-center'
        ]}
        keepRatio={false}
        padding={5}
        anchorSize={8}
        anchorCornerRadius={4}
        anchorStrokeWidth={2}
        anchorStroke="#6B46C1"
        anchorFill="#ffffff"
        borderStroke="#6B46C1"
        borderStrokeWidth={2}
        borderDash={[5, 5]}
      />
    </>
  );
};
