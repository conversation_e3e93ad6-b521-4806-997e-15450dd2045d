"use client";
import React from 'react';
import { Loader2 } from 'lucide-react';

interface LoadingOverlayProps {
  isLoading: boolean;
  message: string;
  opacity?: number;
}

/**
 * Overlay to display during loading operations
 */
export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({ 
  isLoading, 
  message,
  opacity = 0.2 
}) => {
  if (!isLoading) return null;
  
  return (
    <div className={`absolute inset-0 bg-black/${Math.round(opacity * 100)} flex items-center justify-center z-50`}>
      <div className="flex flex-col items-center gap-4 p-6 rounded-lg bg-black/20">
        <Loader2 className="w-8 h-8 text-white animate-spin" />
        <p className="text-white font-medium">{message}</p>
      </div>
    </div>
  );
}; 