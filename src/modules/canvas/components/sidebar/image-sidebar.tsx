"use client";
import { X, Download, Copy, Trash2 } from "lucide-react";
import { GeneratedImage } from "@/modules/canvas/types/canvas";
import { But<PERSON> } from "@/modules/ui/button";
import { cn } from "@/lib/utils";
import { Badge } from "@/modules/ui/badge";
import { ScrollArea } from "@/modules/ui/scroll-area";
import { Separator } from "@/modules/ui/separator";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/modules/ui/alert-dialog";
import { useState } from "react";

interface ImageSidebarProps {
  selectedImage: GeneratedImage | null;
  onClose: () => void;
  onDelete?: (image: GeneratedImage) => void;
  onDuplicate?: (image: GeneratedImage) => void;
  className?: string;
  isOpen: boolean;
}

export function ImageSidebar({ 
  selectedImage, 
  onClose,
  onDelete,
  onDuplicate,
  className,
  isOpen
}: ImageSidebarProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // Don't render anything when not open or no image selected
  if (!selectedImage || !isOpen) {
    return null;
  }

  const isGenerated = !selectedImage.isUploaded;
  
  // Function to download the selected image
  const handleDownload = () => {
    if (!selectedImage.src && !selectedImage.element) return;
    
    // Get image source
    const imageSrc = selectedImage.src || (selectedImage.element ? selectedImage.element.src : null);
    if (!imageSrc) return;

    try {
      // Create a temporary anchor element
      const link = document.createElement('a');
      link.href = imageSrc;
      link.download = `image-${selectedImage.id || Date.now()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Failed to download image:", error);
    }
  };

  // Function to duplicate the selected image
  const handleDuplicate = () => {
    if (selectedImage && onDuplicate) {
      onDuplicate(selectedImage);
    }
  };

  // Function to delete the selected image
  const handleDelete = () => {
    if (selectedImage && onDelete) {
      onDelete(selectedImage);
      setShowDeleteDialog(false);
    }
  };
  
  return (
    <>
      {/* Semi-transparent overlay on small screens to help with closing the sidebar */}
      <div 
        className="md:hidden fixed inset-0 bg-black/20 z-30 transition-opacity"
        onClick={onClose}
      />
      <div 
        className={cn(
          "fixed right-0 top-0 z-40 h-full bg-background border-l border-border shadow-lg",
          "transform transition-transform duration-300 ease-in-out",
          "w-full sm:w-80 max-w-full", // Full width on mobile, 320px on larger screens
          isOpen ? "translate-x-0" : "translate-x-full",
          className
        )}
      >
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="font-medium text-lg">
            {isGenerated ? "Generated Image" : "Uploaded Image"}
          </h3>
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground hidden sm:inline">ESC to close</span>
            <Button 
              variant="outline" 
              size="icon" 
              onClick={onClose}
              className="h-8 w-8 hover:bg-destructive hover:text-destructive-foreground transition-colors"
              aria-label="Close sidebar"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
        </div>
        
        <ScrollArea className="h-[calc(100vh-64px)] p-4">
          {/* Image preview */}
          <div className="mb-4 overflow-hidden rounded-md border">
            {selectedImage.element && (
              <img 
                src={selectedImage.src || selectedImage.element.src} 
                alt="Selected image"
                className="w-full object-contain"
              />
            )}
          </div>
          
          {/* Image details */}
          <div className="space-y-4">
            {/* Only show prompt info for generated images */}
            {isGenerated && selectedImage.prompt && (
              <div>
                <h4 className="text-sm font-medium mb-2">Prompt</h4>
                <p className="text-sm text-muted-foreground">{selectedImage.prompt}</p>
              </div>
            )}
            
            {/* Image metadata */}
            <div>
              <h4 className="text-sm font-medium mb-2">Details</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Dimensions</span>
                  <span className="text-sm">{selectedImage.width} × {selectedImage.height}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Position</span>
                  <span className="text-sm">x: {Math.round(selectedImage.position.x)}, y: {Math.round(selectedImage.position.y)}</span>
                </div>
                {selectedImage.rotation !== 0 && (
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Rotation</span>
                    <span className="text-sm">{Math.round(selectedImage.rotation)}°</span>
                  </div>
                )}
              </div>
            </div>
            
            {/* Tags/badges section */}
            <div>
              <h4 className="text-sm font-medium mb-2">Properties</h4>
              <div className="flex flex-wrap gap-2">
                <Badge variant={isGenerated ? "default" : "outline"}>
                  {isGenerated ? "AI Generated" : "Uploaded"}
                </Badge>
                {selectedImage.isNew && (
                  <Badge variant="secondary">New</Badge>
                )}
                {selectedImage.model && (
                  <Badge variant="outline">{selectedImage.model}</Badge>
                )}
              </div>
            </div>
            
            {/* Generation parameters if available */}
            {isGenerated && selectedImage.parameters && (
              <div>
                <h4 className="text-sm font-medium mb-2">Generation Parameters</h4>
                <div className="space-y-2">
                  {Object.entries(selectedImage.parameters).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="text-sm text-muted-foreground capitalize">{key.replace('_', ' ')}</span>
                      <span className="text-sm">{value}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            <Separator />
            
            {/* Actions */}
            <div className="pt-2">
              <h4 className="text-sm font-medium mb-2">Actions</h4>
              <div className="flex flex-col gap-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full justify-start"
                  onClick={handleDownload}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full justify-start"
                  onClick={handleDuplicate}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Duplicate
                </Button>
                <Button 
                  variant="destructive" 
                  size="sm" 
                  className="w-full justify-start"
                  onClick={() => setShowDeleteDialog(true)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              </div>
            </div>
          </div>
        </ScrollArea>
      </div>

      {/* Delete confirmation dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete image</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this image? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 