"use client";

import React from "react";
import { ChannelProvider } from "ably/react";
import { useCurrentUser } from "@/modules/auth/hooks/use-current-user";
import { Skeleton } from "@/modules/ui/skeleton";
import { ImageCanvas } from "@/modules/canvas/components/image-canvas";

// Import the CSS file for canvas styles
import "@/modules/canvas/styles/canvas-container.css";

interface CanvasWrapperProps {
  className?: string;
}

const CanvasWrapper: React.FC<CanvasWrapperProps> = ({ className }) => {
  const { data: user, isLoading } = useCurrentUser();

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-12 min-h-screen">
        <div className="flex flex-col items-center justify-center space-y-4">
          <Skeleton className="w-32 h-32 rounded-full" />
          <Skeleton className="w-48 h-6" />
          <Skeleton className="w-64 h-4" />
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-12 min-h-screen text-center">
        <p>Please log in to use the canvas.</p>
      </div>
    );
  }

  const channelName = `predictions-${user.id}`;

  return (
    <ChannelProvider channelName={channelName}>
      <ImageCanvas className={className} />
    </ChannelProvider>
  );
};

export default CanvasWrapper; 