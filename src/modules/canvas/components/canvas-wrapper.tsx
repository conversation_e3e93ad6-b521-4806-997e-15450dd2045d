"use client";

import React from "react";
import { ChannelProvider } from "ably/react";
import { useCurrentUser } from "@/modules/auth/hooks/use-current-user";
import { Skeleton } from "@/modules/ui/skeleton";
import { ImageCanvas } from "@/modules/canvas/components/image-canvas";
import { ErrorBoundary } from "react-error-boundary";
import { Button } from "@/modules/ui/button";
import { RefreshCw } from "lucide-react";

// Import the CSS file for canvas styles
import "@/modules/canvas/styles/canvas-container.css";

// Error fallback component
function CanvasErrorFallback({ error, resetErrorBoundary }: { error: Error; resetErrorBoundary: () => void }) {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-8 text-center">
      <div className="max-w-md mx-auto">
        <h2 className="text-2xl font-bold text-destructive mb-4">Canvas Error</h2>
        <p className="text-muted-foreground mb-6">
          Something went wrong with the canvas. This might be due to a browser compatibility issue or memory limitation.
        </p>
        <details className="mb-6 text-left">
          <summary className="cursor-pointer text-sm font-medium mb-2">Error Details</summary>
          <pre className="text-xs bg-muted p-3 rounded overflow-auto max-h-32">
            {error.message}
          </pre>
        </details>
        <div className="space-y-3">
          <Button onClick={resetErrorBoundary} className="w-full">
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </Button>
          <Button variant="outline" onClick={() => window.location.reload()} className="w-full">
            Reload Page
          </Button>
        </div>
      </div>
    </div>
  );
}

interface CanvasWrapperProps {
  className?: string;
}

const CanvasWrapper: React.FC<CanvasWrapperProps> = ({ className }) => {
  const { data: user, isLoading } = useCurrentUser();

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-12 min-h-screen">
        <div className="flex flex-col items-center justify-center space-y-4">
          <Skeleton className="w-32 h-32 rounded-full" />
          <Skeleton className="w-48 h-6" />
          <Skeleton className="w-64 h-4" />
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-12 min-h-screen text-center">
        <p>Please log in to use the canvas.</p>
      </div>
    );
  }

  const channelName = `predictions-${user.id}`;

  return (
    <ErrorBoundary
      FallbackComponent={CanvasErrorFallback}
      onError={(error, errorInfo) => {
        console.error('Canvas Error:', error, errorInfo);
        // You could send this to an error reporting service
      }}
    >
      <ChannelProvider channelName={channelName}>
        <ImageCanvas className={className} />
      </ChannelProvider>
    </ErrorBoundary>
  );
};

export default CanvasWrapper;