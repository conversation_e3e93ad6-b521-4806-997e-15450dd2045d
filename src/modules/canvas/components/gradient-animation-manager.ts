import Konva from 'konva';

// Singleton animation manager to ensure animations persist across renders
export class GradientAnimationManager {
  private static instance: GradientAnimationManager;
  private animations: Map<string, {
    animation: Konva.Animation,
    offset: number,
    width: number,
    height: number,
    targetOffset: number, // Target position for smoother transitions
    lastFrameTime: number, // Keep track of last frame time for consistent animation speed
    pulsePhase: number, // Phase of the pulse effect (0-1)
    pulseIntensity: number // Current intensity of the pulse effect
  }> = new Map();

  private constructor() {}

  public static getInstance(): GradientAnimationManager {
    if (!GradientAnimationManager.instance) {
      GradientAnimationManager.instance = new GradientAnimationManager();
    }
    return GradientAnimationManager.instance;
  }

  // Add a method to ensure animation is running
  public ensureAnimationRunning(id: string): boolean {
    const animationData = this.animations.get(id);
    if (!animationData) return false;
    
    // Check if animation is running, restart if not
    if (!animationData.animation.isRunning()) {
      // Preserve current state and restart
      animationData.lastFrameTime = performance.now();
      animationData.animation.start();
      return true;
    }
    
    return true;
  }

  public startAnimation(rect: Konva.Rect, width: number, height: number, id: string): void {
    if (!rect || !rect.getLayer()) return;

    // Get existing animation data or create new
    const existingData = this.animations.get(id);
    let offset = 0;
    let targetOffset = 0;
    let lastFrameTime = performance.now();
    let pulsePhase = 0;
    let pulseIntensity = 0.7; // Starting intensity
    
    // If we have existing animation data, preserve its state
    if (existingData) {
      // Stop the existing animation first (but preserve state)
      existingData.animation.stop();
      
      // If dimensions changed significantly, adjust the offsets proportionally
      if (Math.abs(existingData.width - width) > 5 || Math.abs(existingData.height - height) > 5) {
        // Preserve animation progress position relative to new dimensions
        offset = existingData.offset;
        targetOffset = existingData.targetOffset;
      } else {
        // Keep exact same offsets if dimensions are similar
        offset = existingData.offset;
        targetOffset = existingData.targetOffset;
      }
      
      lastFrameTime = existingData.lastFrameTime;
      pulsePhase = existingData.pulsePhase;
      pulseIntensity = existingData.pulseIntensity;
    }

    // Generate initial gradient
    this.updateGradient(rect, pulseIntensity);

    // Apply initial positions
    rect.fillLinearGradientStartPoint({ x: -width * offset, y: 0 });
    rect.fillLinearGradientEndPoint({ x: width * (1 + offset), y: height });

    // Create animation data
    const animationData = {
      animation: new Konva.Animation((frame) => {
        if (!frame) return false;
        
        const now = performance.now();
        const elapsed = Math.min(now - animationData.lastFrameTime, 50); // Cap to prevent huge jumps after inactivity
        animationData.lastFrameTime = now;
        
        // Ensure consistent animation speed regardless of frame rate
        const speedFactor = 0.00015;
        const deltaOffset = elapsed * speedFactor;
        
        // Update target offset (loops between 0 and 1)
        animationData.targetOffset = (animationData.targetOffset + deltaOffset) % 1;
        
        // Smoothly interpolate current offset towards target
        // This creates an easing effect to prevent jumping
        const easeStrength = 0.08; // Lower values = smoother but slower transitions
        animationData.offset += (animationData.targetOffset - animationData.offset) * easeStrength;
        
        // Ensure offset remains in valid range
        if (animationData.offset > 1) animationData.offset %= 1;
        if (animationData.offset < 0) animationData.offset = 0;
        
        // Update pulse phase (different frequency than movement)
        const pulseSpeed = 0.0004; // Controls pulse speed
        animationData.pulsePhase = (animationData.pulsePhase + elapsed * pulseSpeed) % (Math.PI * 2);
        
        // Calculate pulse intensity using a sine wave for smooth pulsing
        // Math.sin produces values between -1 and 1, we remap to 0.6 - 0.9 range
        // for a subtle but noticeable pulse effect
        const pulseFactor = (Math.sin(animationData.pulsePhase) + 1) / 2; // 0 to 1
        animationData.pulseIntensity = 0.6 + (pulseFactor * 0.3); // Range 0.6 - 0.9
        
        // Update the gradient colors based on pulse intensity
        this.updateGradient(rect, animationData.pulseIntensity);
        
        // Apply gradient positions with the smoothed offset
        rect.fillLinearGradientStartPoint({ x: -width * animationData.offset, y: 0 });
        rect.fillLinearGradientEndPoint({ x: width * (1 + animationData.offset), y: height });
        
        return true;
      }, rect.getLayer()),
      offset,
      targetOffset,
      width,
      height,
      lastFrameTime,
      pulsePhase,
      pulseIntensity
    };

    // Store animation data
    this.animations.set(id, animationData);
    
    // Start the animation
    animationData.animation.start();
  }

  // Helper method to update gradient colors based on pulse intensity
  private updateGradient(rect: Konva.Rect, intensity: number): void {
    // Calculate peak intensity (middle of gradient) 
    const peakIntensity = Math.min(1, intensity + 0.2); // Cap at 1.0
    
    rect.fillLinearGradientColorStops([
      0, `rgba(255,255,255,${intensity * 0.05})`,
      0.1, `rgba(255,255,255,${intensity * 0.2})`,
      0.3, `rgba(255,255,255,${intensity * 0.5})`,
      0.5, `rgba(255,255,255,${peakIntensity})`,
      0.7, `rgba(255,255,255,${intensity * 0.5})`,
      0.9, `rgba(255,255,255,${intensity * 0.2})`,
      1, `rgba(255,255,255,${intensity * 0.05})`
    ]);
  }

  public stopAnimation(id: string): void {
    if (this.animations.has(id)) {
      this.animations.get(id)?.animation.stop();
      // Keep the animation data in the map for continuity
    }
  }

  public stopAll(): void {
    this.animations.forEach((data) => {
      data.animation.stop();
    });
  }
} 