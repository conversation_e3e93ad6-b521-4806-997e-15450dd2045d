"use client";
import React from 'react';
import { Layer } from 'react-konva';
import { UnifiedCanvasItem } from '../unified-canvas-item';
import { computeAspectRatio } from '../../utils/canvas-utils';

interface PlaceholderLayerProps {
  tasks: Array<{
    id: string;
    selectionArea: { x: number; y: number; width: number; height: number };
    prompt: string;
    status: 'pending' | 'generating' | 'complete' | 'error';
    imageUrl?: string;
    type?: 'generate' | 'magic-fill';
  }>;
  mode: string;
  selectedPlaceholderId: string | null;
  onPlaceholderDragEnd: (taskId: string, e: any) => void;
  onPlaceholderTransform: (taskId: string, e: any) => void;
  onPlaceholderClick: (taskId: string) => void;
}

/**
 * Component to render task placeholders (generating images, magic fill areas)
 */
export const PlaceholderLayer: React.FC<PlaceholderLayerProps> = ({
  tasks,
  mode,
  selectedPlaceholderId,
  onPlaceholderDragEnd,
  onPlaceholderTransform,
  onPlaceholderClick
}) => {
  return (
    <Layer>
      {tasks.map(task => (
        <UnifiedCanvasItem
          key={task.id}
          type="placeholder"
          x={task.selectionArea.x}
          y={task.selectionArea.y}
          width={task.selectionArea.width}
          height={task.selectionArea.height}
          aspectRatio={computeAspectRatio(task.selectionArea.width, task.selectionArea.height)}
          isDraggable={mode === 'move'}
          isSelected={task.id === selectedPlaceholderId}
          onDragEnd={(e) => onPlaceholderDragEnd(task.id, e)}
          onTransformEnd={(e) => onPlaceholderTransform(task.id, e)}
          onClick={() => onPlaceholderClick(task.id)}
          prompt={task.prompt}
          status={task.status}
          imageUrl={task.imageUrl}
          taskType={task.type || 'generate'}
        />
      ))}
    </Layer>
  );
}; 