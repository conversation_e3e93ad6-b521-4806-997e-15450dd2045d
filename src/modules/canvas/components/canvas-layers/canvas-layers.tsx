"use client";
import React, { useEffect } from 'react';
import { Layer, Image as KonvaImage } from 'react-konva';
import Konva from 'konva';
import { 
  CanvasPosition,
  CanvasMode,
  GeneratedImage,
  TransformConfig
} from '../../types/canvas';
import { UnifiedCanvasItem } from '../unified-canvas-item';

interface CanvasLayersProps {
  inputImage: HTMLImageElement | null;
  inputImageDimensions?: { width: number; height: number } | null;
  inputImagePosition: CanvasPosition;
  generatedImages: GeneratedImage[];
  transformConfig: TransformConfig;
  brushComposite: HTMLImageElement | null;
  mode: CanvasMode;
  onImageClick: (index: number, e: Konva.KonvaEventObject<any>) => void;
  onImageDragEnd: (index: number, e: Konva.KonvaEventObject<any>) => void;
  onTransformEnd: (index: number, e: Konva.KonvaEventObject<any>) => void;
  onImageUpdate: (index: number, updatedImage: GeneratedImage) => void;
  selectedImageRef: React.RefObject<Konva.Image>;
  transformerRef: React.RefObject<Konva.Transformer>;
}

/**
 * Component to render the canvas content layers
 * This includes the input (uploaded) image, generated images, and brush composite
 */
export const CanvasLayers: React.FC<CanvasLayersProps> = ({
  inputImage,
  inputImageDimensions,
  inputImagePosition,
  generatedImages,
  transformConfig,
  brushComposite,
  mode,
  onImageClick,
  onImageDragEnd,
  onTransformEnd,
  onImageUpdate,
  selectedImageRef,
  transformerRef,
}) => {
  // Add debug log to track image positions
  useEffect(() => {
    if (generatedImages.length > 0) {
      console.log('Rendering generated images:', generatedImages.map(img => ({
        id: img.id,
        position: img.position,
        dimensions: { width: img.width, height: img.height },
        isNew: img.isNew
      })));
    }
  }, [generatedImages]);

  return (
    <Layer>
      {inputImage && (
        <UnifiedCanvasItem
          type="image"
          x={inputImagePosition.x}
          y={inputImagePosition.y}
          width={inputImageDimensions ? inputImageDimensions.width : inputImage.width}
          height={inputImageDimensions ? inputImageDimensions.height : inputImage.height}
          rotation={0}
          image={{
            image: inputImage,
            position: inputImagePosition,
            width: inputImageDimensions ? inputImageDimensions.width : inputImage.width,
            height: inputImageDimensions ? inputImageDimensions.height : inputImage.height,
            rotation: 0,
            isSelected: transformConfig.selectedImageIndex === -1,
          }}
          isSelected={transformConfig.selectedImageIndex === -1}
          isDraggable={mode === CanvasMode.Move}
          onClick={(e) => onImageClick(-1, e)}
          onDragEnd={(e) => onImageDragEnd(-1, e)}
          onTransformEnd={(e) => onTransformEnd(-1, e)}
        />
      )}
      {generatedImages.map((genImage, index) => (
        <UnifiedCanvasItem
          key={genImage.id || index}
          type="image"
          x={genImage.position.x}
          y={genImage.position.y}
          width={genImage.width}
          height={genImage.height}
          rotation={genImage.rotation}
          image={genImage}
          isSelected={transformConfig.selectedImageIndex === index}
          isDraggable={mode === CanvasMode.Move}
          onClick={(e) => onImageClick(index, e)}
          onDragEnd={(e) => onImageDragEnd(index, e)}
          onTransformEnd={(e) => onTransformEnd(index, e)}
          onImageUpdate={(updatedImage) => onImageUpdate(index, updatedImage)}
        />
      ))}
      {brushComposite && (
        <KonvaImage image={brushComposite} x={0} y={0} listening={false} />
      )}
    </Layer>
  );
}; 