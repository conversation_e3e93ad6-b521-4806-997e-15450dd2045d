"use client";
import React from 'react';
import { Line } from 'react-konva';
import { CanvasLine } from '../../types/canvas';

interface ActiveStrokePreviewProps {
  currentLine: CanvasLine | null;
}

/**
 * Component for rendering the active stroke preview during drawing operations
 */
export const ActiveStrokePreview: React.FC<ActiveStrokePreviewProps> = ({ 
  currentLine 
}) => {
  if (!currentLine) return null;
  
  if (currentLine.tool === "rectangle" || currentLine.tool === "lasso") {
    // For shapes, we'll render a filled area with gradient
    const xs = currentLine.points.filter((_, i) => i % 2 === 0);
    const ys = currentLine.points.filter((_, i) => i % 2 === 1);
    const minX = Math.min(...xs);
    const minY = Math.min(...ys);
    const maxX = Math.max(...xs);
    const maxY = Math.max(...ys);
    
    return (
      <Line
        points={currentLine.points}
        closed
        fillLinearGradientStartPoint={{ x: minX, y: minY }}
        fillLinearGradientEndPoint={{ x: maxX, y: maxY }}
        fillLinearGradientColorStops={[0, "#8B5CF6", 1, "#4F46E5"]}
        stroke="transparent"
        opacity={0.7}
        name="active-stroke"
        className="active-stroke-preview"
      />
    );
  } else {
    // For brush/eraser, we'll render a stroke
    return (
      <Line
        points={currentLine.points}
        strokeWidth={currentLine.strokeWidth}
        lineCap="round"
        lineJoin="round"
        tension={0.5}
        stroke={currentLine.tool === "brush" ? "#8B5CF6" : "rgba(0,0,0,1)"}
        opacity={currentLine.tool === "eraser" ? 0.5 : 1}
        name="active-stroke"
        className="active-stroke-preview"
      />
    );
  }
}; 