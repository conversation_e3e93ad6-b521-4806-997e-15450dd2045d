"use client";

import React from 'react';
import { Layer, Line } from 'react-konva';

interface CanvasGridProps {
  width: number;
  height: number;
  gridSize: number;
  visible: boolean;
  color?: string;
  opacity?: number;
  scale?: number;
  offset?: { x: number; y: number };
}

/**
 * Canvas grid component for alignment and positioning
 */
export const CanvasGrid: React.FC<CanvasGridProps> = ({
  width,
  height,
  gridSize,
  visible,
  color = '#e5e7eb',
  opacity = 0.5,
  scale = 1,
  offset = { x: 0, y: 0 }
}) => {
  if (!visible) return null;

  // Adjust grid size based on scale for better visibility
  const adjustedGridSize = gridSize * scale;
  
  // Don't render grid if it's too small or too large
  if (adjustedGridSize < 5 || adjustedGridSize > 200) {
    return null;
  }

  const lines: React.ReactNode[] = [];

  // Calculate grid bounds with offset
  const startX = Math.floor(-offset.x / adjustedGridSize) * adjustedGridSize + offset.x;
  const startY = Math.floor(-offset.y / adjustedGridSize) * adjustedGridSize + offset.y;
  const endX = width + Math.abs(offset.x);
  const endY = height + Math.abs(offset.y);

  // Vertical lines
  for (let x = startX; x <= endX; x += adjustedGridSize) {
    lines.push(
      <Line
        key={`v-${x}`}
        points={[x, -Math.abs(offset.y), x, endY]}
        stroke={color}
        strokeWidth={0.5}
        opacity={opacity}
        listening={false}
        perfectDrawEnabled={false}
      />
    );
  }

  // Horizontal lines
  for (let y = startY; y <= endY; y += adjustedGridSize) {
    lines.push(
      <Line
        key={`h-${y}`}
        points={[-Math.abs(offset.x), y, endX, y]}
        stroke={color}
        strokeWidth={0.5}
        opacity={opacity}
        listening={false}
        perfectDrawEnabled={false}
      />
    );
  }

  return (
    <Layer name="grid-layer" listening={false}>
      {lines}
    </Layer>
  );
};

/**
 * Snap a value to the nearest grid point
 */
export const snapToGrid = (value: number, gridSize: number, enabled: boolean = true): number => {
  if (!enabled || gridSize <= 0) return value;
  return Math.round(value / gridSize) * gridSize;
};

/**
 * Snap a position to the nearest grid point
 */
export const snapPositionToGrid = (
  position: { x: number; y: number },
  gridSize: number,
  enabled: boolean = true
): { x: number; y: number } => {
  return {
    x: snapToGrid(position.x, gridSize, enabled),
    y: snapToGrid(position.y, gridSize, enabled)
  };
};

/**
 * Hook for grid functionality
 */
export const useGrid = (initialGridSize: number = 20) => {
  const [gridSize, setGridSize] = React.useState(initialGridSize);
  const [gridVisible, setGridVisible] = React.useState(false);
  const [snapEnabled, setSnapEnabled] = React.useState(false);

  const toggleGrid = React.useCallback(() => {
    setGridVisible(prev => !prev);
  }, []);

  const toggleSnap = React.useCallback(() => {
    setSnapEnabled(prev => !prev);
  }, []);

  const snapToGridPoint = React.useCallback((value: number) => {
    return snapToGrid(value, gridSize, snapEnabled);
  }, [gridSize, snapEnabled]);

  const snapPositionToGridPoint = React.useCallback((position: { x: number; y: number }) => {
    return snapPositionToGrid(position, gridSize, snapEnabled);
  }, [gridSize, snapEnabled]);

  return {
    gridSize,
    setGridSize,
    gridVisible,
    setGridVisible,
    snapEnabled,
    setSnapEnabled,
    toggleGrid,
    toggleSnap,
    snapToGridPoint,
    snapPositionToGridPoint
  };
};
