/* GeneratePlaceholder component: displays an animated gradient placeholder without an image */
"use client";

import React, { useEffect, useRef } from "react";
import { Group, Rect, Text, Transformer } from "react-konva";
import { computeAspectRatio, findClosestAspectRatio } from "@/modules/canvas/utils/canvas-utils";

interface GeneratePlaceholderProps {
  x: number;
  y: number;
  width: number;
  height: number;
  aspectRatio?: string;
  isDraggable?: boolean;
  isSelected?: boolean;
  onDragEnd?: (e: any) => void;
  onTransformEnd?: (e: any) => void;
  onClick?: (e: any) => void;
}

export const GeneratePlaceholder: React.FC<GeneratePlaceholderProps> = ({
  x,
  y,
  width,
  height,
  aspectRatio,
  isDraggable = false,
  isSelected = false,
  onDragEnd,
  onTransformEnd,
  onClick,
}) => {
  // Calculate the aspect ratio from dimensions if not provided
  const calculatedRatio = React.useMemo(() => computeAspectRatio(width, height), [width, height]);
  // Get a simplified approximated ratio that's easier to read
  const displayRatio = React.useMemo(() => {
    // If an aspect ratio was explicitly provided, use it
    if (aspectRatio && aspectRatio !== "None") {
      return aspectRatio;
    }
    
    // Otherwise find the closest standard ratio for display purposes
    const closestStandard = findClosestAspectRatio(width, height);
    
    // If the calculated ratio is very close to a standard ratio, use the standard
    // This makes the display cleaner and more comprehensible
    const standardRatios = ["1:1", "16:9", "9:16", "4:3", "3:4", "3:2", "2:3", "16:10", "10:16", "3:1", "1:3"];
    if (standardRatios.includes(closestStandard)) {
      return closestStandard;
    }
    
    // When not a standard ratio, simplify by showing W×H
    return `${Math.round(width)}×${Math.round(height)}`;
  }, [width, height, aspectRatio, calculatedRatio]);
  
  const [gradientOffset, setGradientOffset] = React.useState(0);
  const groupRef = useRef<any>(null);
  const transformerRef = useRef<any>(null);

  // Animate the gradient continuously
  useEffect(() => {
    let animationFrame: number;
    const animate = () => {
      setGradientOffset((prev) => (prev + 0.005) % 1); // Smaller increment for smoother animation
      animationFrame = requestAnimationFrame(animate);
    };

    animate();
    return () => cancelAnimationFrame(animationFrame);
  }, []);

  // Handle transformer attachment
  useEffect(() => {
    if (isSelected && transformerRef.current && groupRef.current) {
      transformerRef.current.nodes([groupRef.current]);
      transformerRef.current.getLayer()?.batchDraw();
    }
  }, [isSelected]);

  // Maintain aspect ratio during transform
  const boundBoxFunc = (oldBox: any, newBox: any) => {
    // Parse the aspect ratio to get the values to maintain
    let ratioToMaintain = aspectRatio;
    if (!ratioToMaintain || ratioToMaintain === "None") {
      ratioToMaintain = calculatedRatio;
    }
    
    // Only enforce fixed aspect ratio if explicitly provided
    if (aspectRatio && aspectRatio !== "None") {
      const aspectRatioParts = ratioToMaintain.split(':');
      const ratio = parseInt(aspectRatioParts[0]) / parseInt(aspectRatioParts[1]);
      
      // Minimum dimensions
      if (newBox.width < 50 || newBox.height < 50) return oldBox;
      
      // Maximum dimensions
      if (newBox.width > 2000 || newBox.height > 2000) return oldBox;
  
      const newRatio = newBox.width / newBox.height;
      
      if (Math.abs(newRatio - ratio) > 0.1) {
        if (newBox.width / ratio > newBox.height) {
          newBox.width = newBox.height * ratio;
        } else {
          newBox.height = newBox.width / ratio;
        }
      }
    } else {
      // No fixed aspect ratio - just apply min/max constraints
      if (newBox.width < 50 || newBox.height < 50) return oldBox;
      if (newBox.width > 2000 || newBox.height > 2000) return oldBox;
    }
    
    return newBox;
  };

  return (
    <>
      <Group
        ref={groupRef}
        x={x}
        y={y}
        width={width}
        height={height}
        draggable={isDraggable}
        onClick={onClick}
        onTap={onClick}
        onDragEnd={onDragEnd}
        onTransformEnd={(e) => {
          if (onTransformEnd) {
            const node = groupRef.current;
            const scaleX = node.scaleX();
            const scaleY = node.scaleY();
            
            // Reset scale and apply to width/height instead
            node.scaleX(1);
            node.scaleY(1);
            
            onTransformEnd({
              target: {
                x: node.x(),
                y: node.y(),
                width: Math.round(width * scaleX),
                height: Math.round(height * scaleY),
                rotation: node.rotation(),
              },
            });
          }
        }}
      >
        <Rect
          width={width}
          height={height}
          fillLinearGradientStartPoint={{ x: -width * gradientOffset, y: 0 }}
          fillLinearGradientEndPoint={{ x: width * (1 - gradientOffset), y: height }}
          fillLinearGradientColorStops={[
            0, "rgba(255,255,255,0.2)",
            0.25, "rgba(255,255,255,0.8)",
            0.5, "rgba(255,255,255,0.2)",
            0.75, "rgba(255,255,255,0.8)",
            1, "rgba(255,255,255,0.2)",
          ]}
          listening={true}
          perfectDrawEnabled={false}
          cornerRadius={4}
          stroke="rgba(0,0,0,0.1)"
          strokeWidth={1}
        />
        <Text
          text={displayRatio}
          fontSize={16}
          fontFamily="Inter, sans-serif"
          fontStyle="bold"
          fill="rgba(0, 0, 0, 0.7)"
          x={width / 2}
          y={height / 2}
          offsetX={displayRatio.length * 4}
          offsetY={8}
          padding={8}
          backgroundColor="rgba(255, 255, 255, 0.8)"
          cornerRadius={4}
          align="center"
        />
        <Text
          text="Generating..."
          fontSize={16}
          fontFamily="Inter, sans-serif" 
          fill="rgba(0, 0, 0, 0.7)"
          x={width / 2}
          y={height / 2 + 20}
          offsetX={45}
          offsetY={8}
          padding={8}
          backgroundColor="rgba(255, 255, 255, 0.8)"
          cornerRadius={4}
        />
      </Group>
      {isSelected && (
        <Transformer
          ref={transformerRef}
          boundBoxFunc={boundBoxFunc}
          padding={5}
          // Custom anchor styling
          anchorStroke="#4F46E5"
          anchorFill="#fff"
          anchorSize={8}
          anchorCornerRadius={2}
          borderStroke="#4F46E5"
          borderDash={[4, 4]}
          // Enable specific transforms
          enabledAnchors={[
            'top-left',
            'top-right',
            'bottom-left',
            'bottom-right',
            'middle-left',
            'middle-right',
            'top-center',
            'bottom-center',
          ]}
          rotateEnabled={false}
        />
      )}
    </>
  );
};
