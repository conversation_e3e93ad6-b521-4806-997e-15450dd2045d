"use client";

import React from 'react';
import { Button } from '@/modules/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/modules/ui/card';
import { Badge } from '@/modules/ui/badge';
import { Slider } from '@/modules/ui/slider';
import { 
  Eye, 
  EyeOff, 
  Trash2, 
  Move, 
  Image as ImageIcon,
  Layers,
  ChevronUp,
  ChevronDown
} from 'lucide-react';
import useCanvasStore from '@/modules/canvas/store/canvas-store';
import { GeneratedImage } from '@/modules/canvas/types/canvas';
import { cn } from '@/lib/utils';

interface LayersPanelProps {
  className?: string;
}

interface LayerItemProps {
  layer: GeneratedImage;
  index: number;
  isSelected: boolean;
  onSelect: (index: number) => void;
  onToggleVisibility: (index: number) => void;
  onDelete: (index: number) => void;
  onOpacityChange: (index: number, opacity: number) => void;
  onMoveUp: (index: number) => void;
  onMoveDown: (index: number) => void;
}

const LayerItem: React.FC<LayerItemProps> = ({
  layer,
  index,
  isSelected,
  onSelect,
  onToggleVisibility,
  onDelete,
  onOpacityChange,
  onMoveUp,
  onMoveDown
}) => {
  return (
    <div 
      className={cn(
        "border rounded-lg p-3 cursor-pointer transition-colors",
        isSelected ? "border-primary bg-primary/5" : "border-border hover:border-primary/50"
      )}
      onClick={() => onSelect(index)}
    >
      <div className="flex items-center gap-2 mb-2">
        <div className="w-8 h-8 rounded border overflow-hidden bg-muted flex-shrink-0">
          {layer.image ? (
            <img 
              src={layer.image.src} 
              alt={`Layer ${index + 1}`}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <ImageIcon className="w-4 h-4 text-muted-foreground" />
            </div>
          )}
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium truncate">
            {layer.prompt ? `"${layer.prompt.slice(0, 30)}${layer.prompt.length > 30 ? '...' : ''}"` : `Layer ${index + 1}`}
          </div>
          <div className="text-xs text-muted-foreground">
            {layer.image ? `${layer.image.width}×${layer.image.height}` : 'Loading...'}
          </div>
        </div>

        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={(e) => {
              e.stopPropagation();
              onToggleVisibility(index);
            }}
          >
            {layer.visible !== false ? (
              <Eye className="w-3 h-3" />
            ) : (
              <EyeOff className="w-3 h-3 text-muted-foreground" />
            )}
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0 text-destructive hover:text-destructive"
            onClick={(e) => {
              e.stopPropagation();
              onDelete(index);
            }}
          >
            <Trash2 className="w-3 h-3" />
          </Button>
        </div>
      </div>

      {isSelected && (
        <div className="space-y-2 pt-2 border-t">
          <div className="flex items-center gap-2">
            <span className="text-xs text-muted-foreground">Opacity:</span>
            <div className="flex-1">
              <Slider
                value={[layer.opacity || 100]}
                onValueChange={([value]) => onOpacityChange(index, value)}
                max={100}
                min={0}
                step={1}
                className="flex-1"
              />
            </div>
            <span className="text-xs text-muted-foreground w-8">
              {layer.opacity || 100}%
            </span>
          </div>
          
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              className="h-6 px-2 text-xs"
              onClick={(e) => {
                e.stopPropagation();
                onMoveUp(index);
              }}
              disabled={index === 0}
            >
              <ChevronUp className="w-3 h-3 mr-1" />
              Up
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              className="h-6 px-2 text-xs"
              onClick={(e) => {
                e.stopPropagation();
                onMoveDown(index);
              }}
            >
              <ChevronDown className="w-3 h-3 mr-1" />
              Down
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export const LayersPanel: React.FC<LayersPanelProps> = ({ className }) => {
  const store = useCanvasStore();
  
  const handleSelectLayer = (index: number) => {
    // This would need to be implemented in the store
    console.log('Select layer:', index);
  };

  const handleToggleVisibility = (index: number) => {
    const updatedImages = [...store.generatedImages];
    updatedImages[index] = {
      ...updatedImages[index],
      visible: updatedImages[index].visible !== false ? false : true
    };
    store.setGeneratedImages(updatedImages);
  };

  const handleDeleteLayer = (index: number) => {
    const updatedImages = store.generatedImages.filter((_, i) => i !== index);
    store.setGeneratedImages(updatedImages);
  };

  const handleOpacityChange = (index: number, opacity: number) => {
    const updatedImages = [...store.generatedImages];
    updatedImages[index] = {
      ...updatedImages[index],
      opacity
    };
    store.setGeneratedImages(updatedImages);
  };

  const handleMoveUp = (index: number) => {
    if (index === 0) return;
    const updatedImages = [...store.generatedImages];
    [updatedImages[index - 1], updatedImages[index]] = [updatedImages[index], updatedImages[index - 1]];
    store.setGeneratedImages(updatedImages);
  };

  const handleMoveDown = (index: number) => {
    if (index === store.generatedImages.length - 1) return;
    const updatedImages = [...store.generatedImages];
    [updatedImages[index], updatedImages[index + 1]] = [updatedImages[index + 1], updatedImages[index]];
    store.setGeneratedImages(updatedImages);
  };

  return (
    <Card className={cn("w-80", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center gap-2">
          <Layers className="w-4 h-4" />
          Layers
          <Badge variant="secondary" className="ml-auto">
            {store.generatedImages.length}
          </Badge>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-2">
        {store.generatedImages.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Layers className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No layers yet</p>
            <p className="text-xs">Generate images to create layers</p>
          </div>
        ) : (
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {store.generatedImages.map((layer, index) => (
              <LayerItem
                key={`${layer.id}-${index}`}
                layer={layer}
                index={index}
                isSelected={false} // This would need to be tracked in store
                onSelect={handleSelectLayer}
                onToggleVisibility={handleToggleVisibility}
                onDelete={handleDeleteLayer}
                onOpacityChange={handleOpacityChange}
                onMoveUp={handleMoveUp}
                onMoveDown={handleMoveDown}
              />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
