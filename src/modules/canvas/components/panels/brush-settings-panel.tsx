"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/modules/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/modules/ui/card';
import { Slider } from '@/modules/ui/slider';
import { Label } from '@/modules/ui/label';
import { 
  Brush, 
  Eraser,
  Circle,
  Square,
  Palette,
  RotateCcw
} from 'lucide-react';
import useCanvasStore from '@/modules/canvas/store/canvas-store';
import { cn } from '@/lib/utils';

interface BrushSettingsPanelProps {
  className?: string;
}

interface ColorPreset {
  name: string;
  color: string;
}

const colorPresets: ColorPreset[] = [
  { name: 'Black', color: '#000000' },
  { name: 'White', color: '#ffffff' },
  { name: 'Red', color: '#ef4444' },
  { name: 'Green', color: '#22c55e' },
  { name: 'Blue', color: '#3b82f6' },
  { name: 'Yellow', color: '#eab308' },
  { name: 'Purple', color: '#a855f7' },
  { name: 'Pink', color: '#ec4899' },
  { name: 'Orange', color: '#f97316' },
  { name: 'Cyan', color: '#06b6d4' },
  { name: 'Gray', color: '#6b7280' },
  { name: 'Brown', color: '#a3a3a3' },
];

export const BrushSettingsPanel: React.FC<BrushSettingsPanelProps> = ({ className }) => {
  const store = useCanvasStore();
  
  // Local state for brush settings (these would ideally be in the store)
  const [brushSize, setBrushSize] = useState(10);
  const [brushOpacity, setBrushOpacity] = useState(100);
  const [brushHardness, setBrushHardness] = useState(100);
  const [brushColor, setBrushColor] = useState('#000000');
  const [brushShape, setBrushShape] = useState<'round' | 'square'>('round');
  const [isEraser, setIsEraser] = useState(false);

  const handleColorChange = (color: string) => {
    setBrushColor(color);
    setIsEraser(false);
  };

  const handleEraserToggle = () => {
    setIsEraser(!isEraser);
  };

  const handleReset = () => {
    setBrushSize(10);
    setBrushOpacity(100);
    setBrushHardness(100);
    setBrushColor('#000000');
    setBrushShape('round');
    setIsEraser(false);
  };

  return (
    <Card className={cn("w-80", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center gap-2">
          <Brush className="w-4 h-4" />
          Brush Settings
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Brush Type */}
        <div className="space-y-2">
          <Label className="text-xs font-medium">Tool</Label>
          <div className="flex gap-1">
            <Button
              variant={!isEraser ? "default" : "outline"}
              size="sm"
              className="flex-1"
              onClick={() => setIsEraser(false)}
            >
              <Brush className="w-3 h-3 mr-1" />
              Brush
            </Button>
            <Button
              variant={isEraser ? "default" : "outline"}
              size="sm"
              className="flex-1"
              onClick={handleEraserToggle}
            >
              <Eraser className="w-3 h-3 mr-1" />
              Eraser
            </Button>
          </div>
        </div>

        {/* Brush Shape */}
        <div className="space-y-2">
          <Label className="text-xs font-medium">Shape</Label>
          <div className="flex gap-1">
            <Button
              variant={brushShape === 'round' ? "default" : "outline"}
              size="sm"
              className="flex-1"
              onClick={() => setBrushShape('round')}
            >
              <Circle className="w-3 h-3 mr-1" />
              Round
            </Button>
            <Button
              variant={brushShape === 'square' ? "default" : "outline"}
              size="sm"
              className="flex-1"
              onClick={() => setBrushShape('square')}
            >
              <Square className="w-3 h-3 mr-1" />
              Square
            </Button>
          </div>
        </div>

        {/* Brush Size */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium">Size</Label>
            <span className="text-xs text-muted-foreground">{brushSize}px</span>
          </div>
          <Slider
            value={[brushSize]}
            onValueChange={([value]) => setBrushSize(value)}
            max={100}
            min={1}
            step={1}
          />
        </div>

        {/* Brush Opacity */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium">Opacity</Label>
            <span className="text-xs text-muted-foreground">{brushOpacity}%</span>
          </div>
          <Slider
            value={[brushOpacity]}
            onValueChange={([value]) => setBrushOpacity(value)}
            max={100}
            min={1}
            step={1}
          />
        </div>

        {/* Brush Hardness */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium">Hardness</Label>
            <span className="text-xs text-muted-foreground">{brushHardness}%</span>
          </div>
          <Slider
            value={[brushHardness]}
            onValueChange={([value]) => setBrushHardness(value)}
            max={100}
            min={0}
            step={1}
          />
        </div>

        {/* Color Picker */}
        {!isEraser && (
          <div className="space-y-2">
            <Label className="text-xs font-medium flex items-center gap-1">
              <Palette className="w-3 h-3" />
              Color
            </Label>
            
            {/* Current Color */}
            <div className="flex items-center gap-2">
              <div 
                className="w-8 h-8 rounded border-2 border-border"
                style={{ backgroundColor: brushColor }}
              />
              <input
                type="color"
                value={brushColor}
                onChange={(e) => handleColorChange(e.target.value)}
                className="w-full h-8 rounded border border-border cursor-pointer"
              />
            </div>
            
            {/* Color Presets */}
            <div className="grid grid-cols-6 gap-1">
              {colorPresets.map((preset) => (
                <button
                  key={preset.name}
                  className={cn(
                    "w-8 h-8 rounded border-2 transition-all hover:scale-110",
                    brushColor === preset.color 
                      ? "border-primary ring-2 ring-primary/20" 
                      : "border-border hover:border-primary/50"
                  )}
                  style={{ backgroundColor: preset.color }}
                  onClick={() => handleColorChange(preset.color)}
                  title={preset.name}
                />
              ))}
            </div>
          </div>
        )}

        {/* Brush Preview */}
        <div className="space-y-2">
          <Label className="text-xs font-medium">Preview</Label>
          <div className="flex items-center justify-center h-16 bg-muted rounded border">
            <div
              className={cn(
                "transition-all",
                brushShape === 'round' ? 'rounded-full' : 'rounded-none',
                isEraser ? 'border-2 border-dashed border-destructive' : ''
              )}
              style={{
                width: Math.max(brushSize / 2, 8),
                height: Math.max(brushSize / 2, 8),
                backgroundColor: isEraser ? 'transparent' : brushColor,
                opacity: brushOpacity / 100,
                filter: `blur(${(100 - brushHardness) / 20}px)`,
              }}
            />
          </div>
        </div>

        {/* Reset Button */}
        <Button
          variant="outline"
          size="sm"
          className="w-full"
          onClick={handleReset}
        >
          <RotateCcw className="w-3 h-3 mr-1" />
          Reset to Default
        </Button>
      </CardContent>
    </Card>
  );
};
