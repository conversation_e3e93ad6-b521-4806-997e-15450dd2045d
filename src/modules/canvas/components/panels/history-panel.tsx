"use client";

import React from 'react';
import { Button } from '@/modules/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/modules/ui/card';
import { Badge } from '@/modules/ui/badge';
import { 
  History, 
  Undo2, 
  Redo2,
  Image as ImageIcon,
  Brush,
  Move,
  Trash2,
  Upload,
  Wand2
} from 'lucide-react';
import useCanvasStore from '@/modules/canvas/store/canvas-store';
import { cn } from '@/lib/utils';

interface HistoryPanelProps {
  className?: string;
}

interface HistoryAction {
  type: 'image' | 'brush' | 'move' | 'delete' | 'upload' | 'generate';
  description: string;
  timestamp: Date;
  isCurrent?: boolean;
}

const getActionIcon = (type: string) => {
  switch (type) {
    case 'image':
      return <ImageIcon className="w-3 h-3" />;
    case 'brush':
      return <Brush className="w-3 h-3" />;
    case 'move':
      return <Move className="w-3 h-3" />;
    case 'delete':
      return <Trash2 className="w-3 h-3" />;
    case 'upload':
      return <Upload className="w-3 h-3" />;
    case 'generate':
      return <Wand2 className="w-3 h-3" />;
    default:
      return <History className="w-3 h-3" />;
  }
};

const getActionColor = (type: string) => {
  switch (type) {
    case 'image':
      return 'text-blue-500';
    case 'brush':
      return 'text-purple-500';
    case 'move':
      return 'text-green-500';
    case 'delete':
      return 'text-red-500';
    case 'upload':
      return 'text-orange-500';
    case 'generate':
      return 'text-pink-500';
    default:
      return 'text-muted-foreground';
  }
};

export const HistoryPanel: React.FC<HistoryPanelProps> = ({ className }) => {
  const store = useCanvasStore();
  
  // Create history actions from store state
  const historyActions: HistoryAction[] = React.useMemo(() => {
    const actions: HistoryAction[] = [];
    
    // Add image history
    store.imageHistory.forEach((_, index) => {
      actions.push({
        type: 'image',
        description: `Image action ${index + 1}`,
        timestamp: new Date(Date.now() - (store.imageHistory.length - index) * 60000),
        isCurrent: index === store.currentImageIndex
      });
    });
    
    // Add line/brush history
    store.lineHistory.forEach((_, index) => {
      actions.push({
        type: 'brush',
        description: `Brush stroke ${index + 1}`,
        timestamp: new Date(Date.now() - (store.lineHistory.length - index) * 30000),
        isCurrent: index === store.currentLineIndex
      });
    });
    
    // Sort by timestamp
    return actions.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }, [store.imageHistory, store.lineHistory, store.currentImageIndex, store.currentLineIndex]);

  const canUndo = store.currentImageIndex > 0 || store.currentLineIndex > 0;
  const canRedo = store.currentImageIndex < store.imageHistory.length - 1 || 
                  store.currentLineIndex < store.lineHistory.length - 1;

  const handleUndo = () => {
    if (store.currentLineIndex > 0) {
      store.setCurrentLineIndex(store.currentLineIndex - 1);
    } else if (store.currentImageIndex > 0) {
      store.setCurrentImageIndex(store.currentImageIndex - 1);
    }
  };

  const handleRedo = () => {
    if (store.currentImageIndex < store.imageHistory.length - 1) {
      store.setCurrentImageIndex(store.currentImageIndex + 1);
    } else if (store.currentLineIndex < store.lineHistory.length - 1) {
      store.setCurrentLineIndex(store.currentLineIndex + 1);
    }
  };

  const handleJumpToAction = (action: HistoryAction, index: number) => {
    // This would need more sophisticated logic to jump to specific history points
    console.log('Jump to action:', action, index);
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <Card className={cn("w-80", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center gap-2">
          <History className="w-4 h-4" />
          History
          <Badge variant="secondary" className="ml-auto">
            {historyActions.length}
          </Badge>
        </CardTitle>
        
        <div className="flex gap-1">
          <Button
            variant="outline"
            size="sm"
            className="flex-1"
            onClick={handleUndo}
            disabled={!canUndo}
          >
            <Undo2 className="w-3 h-3 mr-1" />
            Undo
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            className="flex-1"
            onClick={handleRedo}
            disabled={!canRedo}
          >
            <Redo2 className="w-3 h-3 mr-1" />
            Redo
          </Button>
        </div>
      </CardHeader>
      
      <CardContent>
        {historyActions.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <History className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No history yet</p>
            <p className="text-xs">Start creating to build history</p>
          </div>
        ) : (
          <div className="space-y-1 max-h-96 overflow-y-auto">
            {historyActions.map((action, index) => (
              <div
                key={index}
                className={cn(
                  "flex items-center gap-2 p-2 rounded cursor-pointer transition-colors text-sm",
                  action.isCurrent 
                    ? "bg-primary/10 border border-primary/20" 
                    : "hover:bg-muted/50"
                )}
                onClick={() => handleJumpToAction(action, index)}
              >
                <div className={cn("flex-shrink-0", getActionColor(action.type))}>
                  {getActionIcon(action.type)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="truncate font-medium">
                    {action.description}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {formatTime(action.timestamp)}
                  </div>
                </div>
                
                {action.isCurrent && (
                  <Badge variant="secondary" className="text-xs">
                    Current
                  </Badge>
                )}
              </div>
            ))}
          </div>
        )}
        
        {historyActions.length > 0 && (
          <div className="mt-3 pt-3 border-t">
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={() => {
                // Clear history
                store.setImageHistory([]);
                store.setLineHistory([]);
                store.setCurrentImageIndex(0);
                store.setCurrentLineIndex(0);
              }}
            >
              <Trash2 className="w-3 h-3 mr-1" />
              Clear History
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
