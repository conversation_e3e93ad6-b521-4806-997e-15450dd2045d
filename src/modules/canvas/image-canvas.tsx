"use client";
import React, { useRef, useState, useEffect, useCallback } from "react";
import {
  Stage,
  Layer,
  Image as KonvaImage,
  Line,
  Transformer,
  Group,
  Rect,
} from "react-konva";
import Konva from "konva";
import { MainToolbar } from "@/modules/canvas/components/toolbar/main-toolbar";
import { PromptInput } from "@/modules/canvas/components/toolbar/prompt-input";
import { MagicFillToolbar } from "@/modules/canvas/components/toolbar/magic-fill-toolbar";
import useCanvasStore from "@/modules/canvas/store/canvas-store";
import {
  CanvasMode,
  CanvasLine,
  CanvasRect,
  GeneratedImage,
  TransformConfig,
  CanvasPosition,
  CanvasSize,
  MaskToolMode,
} from "@/modules/canvas/types/canvas";
import { useMediaQuery } from "@/modules/hooks/use-media-query";
import { useToast } from "@/modules/ui/use-toast";
import { useCurrentUser } from "@/modules/auth/hooks/use-current-user";
import { cn } from "@/lib/utils";
import { editImage } from "@/modules/canvas/actions/edit-image";
import { MagicFillCompositeLayer } from "@/modules/canvas/hooks/use-brush-composite";
import { SelectionRect } from "@/modules/canvas/components/selection-rectangle";
import { useTheme } from "next-themes";
import { UnifiedCanvasItem } from "@/modules/canvas/components/unified-canvas-item";
import {
  computeAspectRatio,
  findClosestValidResolution,
  getRelativePointerPosition,
  snapToGrid,
  findAvailableCanvasPosition
} from "@/modules/canvas/utils/canvas-utils";
import { Button } from "@/modules/ui/button";
import { Download } from "lucide-react";
import { PredictionStatus } from "@prisma/client";
import { useChannel } from "ably/react";

// Custom Hooks
import { useCanvasZoom } from "./hooks/use-canvas-zoom";
import { useCanvasOperations } from "./hooks/use-canvas-operations";
import { useCanvasExport } from "./hooks/use-canvas-export";
import { useGenerationTasks } from "./hooks/use-generation-tasks";
import { useMagicFill } from "./hooks/use-magic-fill";
import { useCanvasItems } from "./hooks/use-canvas-items";
import { useFileUpload } from "./hooks/use-file-upload";

// Custom Components
import { CanvasLayers as CanvasLayersComponent } from "./components/canvas-layers/canvas-layers";
import { PlaceholderLayer } from "./components/canvas-layers/placeholder-layer";
import { ActiveStrokePreview } from "./components/canvas-layers/active-stroke-preview";
import { ZoomControls } from "./components/controls/zoom-controls";
import { LoadingOverlay } from "./components/overlays/loading-overlay";

/**
 * CanvasLayers:
 * Renders elements underneath the mask. This includes the input (uploaded) image,
 * generated images (which allow transformation), and the composited brush strokes.
 */
const CanvasLayers: React.FC<{
  inputImage: HTMLImageElement | null;
  inputImageDimensions?: { width: number; height: number } | null;
  inputImagePosition: CanvasPosition;
  generatedImages: GeneratedImage[];
  transformConfig: TransformConfig;
  brushComposite: HTMLImageElement | null;
  mode: CanvasMode;
  onImageClick: (index: number, e: Konva.KonvaEventObject<any>) => void;
  onImageDragEnd: (index: number, e: Konva.KonvaEventObject<any>) => void;
  onTransformEnd: (index: number, e: Konva.KonvaEventObject<any>) => void;
  onImageUpdate: (index: number, updatedImage: GeneratedImage) => void;
  selectedImageRef: React.RefObject<Konva.Image>;
  transformerRef: React.RefObject<Konva.Transformer>;
}> = ({
  inputImage,
  inputImageDimensions,
  inputImagePosition,
  generatedImages,
  transformConfig,
  brushComposite,
  mode,
  onImageClick,
  onImageDragEnd,
  onTransformEnd,
  onImageUpdate,
}) => {
  // Add debug log to track image positions
  useEffect(() => {
    if (generatedImages.length > 0) {
      console.log('Rendering generated images:', generatedImages.map(img => ({
        id: img.id,
        position: img.position,
        dimensions: { width: img.width, height: img.height },
        isNew: img.isNew
      })));
    }
  }, [generatedImages]);

  return (
    <Layer>
      {inputImage && (
        <UnifiedCanvasItem
          type="image"
          x={inputImagePosition.x}
          y={inputImagePosition.y}
          width={inputImageDimensions ? inputImageDimensions.width : inputImage.width}
          height={inputImageDimensions ? inputImageDimensions.height : inputImage.height}
          rotation={0}
          image={{
            image: inputImage,
            position: inputImagePosition,
            width: inputImageDimensions ? inputImageDimensions.width : inputImage.width,
            height: inputImageDimensions ? inputImageDimensions.height : inputImage.height,
            rotation: 0,
            isSelected: transformConfig.selectedImageIndex === -1,
          }}
          isSelected={transformConfig.selectedImageIndex === -1}
          isDraggable={mode === CanvasMode.Move}
          onClick={(e) => onImageClick(-1, e)}
          onDragEnd={(e) => onImageDragEnd(-1, e)}
          onTransformEnd={(e) => onTransformEnd(-1, e)}
        />
      )}
      {generatedImages.map((genImage, index) => (
        <UnifiedCanvasItem
          key={index}
          type="image"
          x={genImage.position.x}
          y={genImage.position.y}
          width={genImage.width}
          height={genImage.height}
          rotation={genImage.rotation}
          image={genImage}
          isSelected={transformConfig.selectedImageIndex === index}
          isDraggable={mode === CanvasMode.Move}
          onClick={(e) => onImageClick(index, e)}
          onDragEnd={(e) => onImageDragEnd(index, e)}
          onTransformEnd={(e) => onTransformEnd(index, e)}
          onImageUpdate={(updatedImage) => onImageUpdate(index, updatedImage)}
        />
      ))}
      {brushComposite && (
        <KonvaImage image={brushComposite} x={0} y={0} listening={false} />
      )}
    </Layer>
  );
};

// Placeholder handling hook with proper typing and type assertions
const usePlaceholderHandlers = (
  store: any, // Type assertion to avoid TypeScript errors
  setSelectedPlaceholderId: React.Dispatch<React.SetStateAction<string | null>>
) => {
  const handlePlaceholderDragEnd = useCallback((taskId: string, e: Konva.KonvaEventObject<any>) => {
    const task = store.generationTasks.find((t: any) => t.id === taskId);
    if (!task) return;
    
    // Update the task's selection area with the new position
    store.updateGenerationTask(taskId, {
      selectionArea: {
        ...task.selectionArea,
        x: e.target.x(),
        y: e.target.y()
      }
    });
  }, [store]);

  // Placeholder click handler
  const handlePlaceholderClick = useCallback((taskId: string) => {
    // Deselect any selected image
    store.setTransformConfig({
      selectedImageIndex: null,
      isDragging: false,
      isResizing: false,
      isRotating: false
    });
    
    // Select the placeholder
    setSelectedPlaceholderId(taskId);
  }, [store, setSelectedPlaceholderId]);

  // Placeholder transform handler
  const handlePlaceholderTransform = useCallback((taskId: string, e: Konva.KonvaEventObject<any>) => {
    const task = store.generationTasks.find((t: any) => t.id === taskId);
    if (!task) return;
    
    // Update the task with the new dimensions
    store.updateGenerationTask(taskId, {
      selectionArea: {
        ...task.selectionArea,
        x: e.target.x(),
        y: e.target.y(),
        width: e.target.width(),
        height: e.target.height()
      }
    });
  }, [store]);

  return {
    handlePlaceholderDragEnd,
    handlePlaceholderClick,
    handlePlaceholderTransform
  };
};

/**
 * ImageCanvas is the main canvas component that supports drawing masks, 
 * image generation, magic fill and image transformations.
 */
export function ImageCanvas({ className }: { className?: string }) {
  const store = useCanvasStore();
  const stageRef = useRef<Konva.Stage>(null);
  const transformerRef = useRef<Konva.Transformer>(null);
  const selectedImageRef = useRef<Konva.Image>(null);
  const { toast } = useToast();
  const currentUser = useCurrentUser();
  const isMobile = useMediaQuery("(max-width: 768px)");
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { theme, resolvedTheme } = useTheme();
  const currentTheme = theme === "system" ? resolvedTheme : theme;
  const canvasFill = currentTheme === "dark" ? "#1f2937" : "#f9fafb";
  const VIRTUAL_WIDTH = 10000;
  const VIRTUAL_HEIGHT = 10000;
  
  // State managed by component
  const [dimensions, setDimensions] = useState<CanvasSize>({
    width: window.innerWidth,
    height: window.innerHeight,
  });
  const [brushComposite, setBrushComposite] = useState<HTMLImageElement | null>(null);
  const [selectedPlaceholderId, setSelectedPlaceholderId] = useState<string | null>(null);
  const [loadingImages, setLoadingImages] = useState<Set<string>>(new Set());
  const [completedPredictions, setCompletedPredictions] = useState<Set<string>>(new Set());
  
  // Get the channel for Ably
  const channelName = currentUser.data?.id 
    ? `predictions-${currentUser.data.id}`
    : "";
  
  const { channel } = useChannel(channelName);
  
  // Custom wrapper for file input handling
  const onFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = () => {
      const dataURL = reader.result;
      if (typeof dataURL !== "string") return;
      
      const img = new Image();
      img.onload = () => {
        // Get all occupied spaces on the canvas
        const occupiedSpaces = [
          // Add generated images
          ...store.generatedImages.map(img => ({
            x: img.position.x,
            y: img.position.y,
            width: img.width,
            height: img.height
          })),
          // Add generation tasks/placeholders
          ...store.generationTasks.map(task => task.selectionArea)
        ];
        
        // Find a good position for the uploaded image
        const position = findAvailableCanvasPosition(
          img.width, 
          img.height, 
          stageRef.current, 
          dimensions, 
          occupiedSpaces
        );
        
        // Set this as the input image in the store
        store.setInputImage(img);
        store.setInputImagePosition({
          x: position.x,
          y: position.y
        });
        
        // Also add it as a generated image for consistency
        store.addGeneratedImage({
          id: `uploaded-${crypto.randomUUID()}`,
          image: img,
          src: dataURL,
          width: img.width,
          height: img.height,
          position,
          rotation: 0,
          scaleX: 1,
          scaleY: 1,
          isNew: true,
          isSelected: false,
          element: img,
          isUploaded: true
        });
        
        // Toast to confirm upload
        toast({
          title: "Image uploaded",
          description: "Your image has been uploaded and is ready for editing",
        });
      };
      
      img.src = dataURL;
    };
    
    reader.readAsDataURL(file);
    
    // Clear the file input value so the same file can be re-uploaded
    e.target.value = "";
  }, [dimensions, store, toast]);

  // Load custom hooks with proper config
  const zoomOptions = { minScale: 0.1, maxScale: 5, scaleFactor: 1.1, defaultScale: 0.7 };
  const {
    handleWheel: canvasHandleWheel,
    zoomIn,
    zoomOut,
    resetZoom
  } = useCanvasZoom(zoomOptions);
  
  const {
    isDrawing,
    localSelectionRect,
    activeLine,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    handleStageClick,
    handleMaskToolChange,
    handleCancelMagicFill,
    handleProceedMagicFill,
    renderActiveStrokePreview,
    setIsDrawing,
    setLocalSelectionRect,
    captureSelection
  } = useCanvasOperations();
  
  const { 
    captureSelectionAreaImage, 
    captureAndUploadSelectionArea, 
    exportSelectionAsImage 
  } = useCanvasExport();
  
  const { handleMagicFill } = useMagicFill(currentUser.data?.id || "");
  
  // From Canvas Items hook
  const {
    handleImageClick,
    handleImageDragEnd,
    handleTransformEnd,
    handleImageUpdate
  } = useCanvasItems();
  
  // From Placeholder handlers hook
  const {
    handlePlaceholderDragEnd,
    handlePlaceholderClick,
    handlePlaceholderTransform
  } = usePlaceholderHandlers(store, setSelectedPlaceholderId);
  
  // From Generation Tasks hook
  const { handleGenerate } = useGenerationTasks(currentUser.data?.id || "", channelName);

  // Initialize the canvas with the default scale
  useEffect(() => {
    if (stageRef.current) {
      // Initialize the stage with the default scale and center position
      const stage = stageRef.current;
      stage.scale({ x: 0.7, y: 0.7 });
      stage.position({
        x: dimensions.width / 2,
        y: dimensions.height / 2
      });
      stage.batchDraw();
      store.setZoom(0.7);
    }
  }, [dimensions.width, dimensions.height, store]);

  // Update dimensions when window is resized
  useEffect(() => {
    const handleResize = () => {
      // Use store.updateDimensions to ensure consistent state management
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      // Update component-local state for stage rendering
      setDimensions({ width, height });
      
      // Don't update the selection area when resizing the window
      store.updateDimensions(width, height, false);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [store]);

  useEffect(() => {
    if (currentUser.data?.id) {
      store.setUserId(currentUser.data.id);
    }
  }, [currentUser.data?.id, store]);

  // Now handle the wheel event using our custom hook
  const handleWheelEvent = useCallback((e: Konva.KonvaEventObject<WheelEvent>) => {
    if (stageRef.current) {
      // Manually handle wheel since the hook usage pattern needs to be fixed
      e.evt.preventDefault();
      const stage = stageRef.current;
      const oldScale = stage.scaleX();
      const pointer = stage.getPointerPosition();
      if (!pointer) return;
      
      const direction = e.evt.deltaY > 0 ? 1 / zoomOptions.scaleFactor : zoomOptions.scaleFactor;
      let newScale = oldScale * direction;
      
      // Clamp the scale to min/max values
      newScale = Math.max(zoomOptions.minScale, Math.min(newScale, zoomOptions.maxScale));
      
      // Calculate new position to zoom toward mouse pointer
      const mousePointTo = {
        x: (pointer.x - stage.x()) / oldScale,
        y: (pointer.y - stage.y()) / oldScale,
      };
      
      stage.scale({ x: newScale, y: newScale });
      
      const newPos = {
        x: pointer.x - mousePointTo.x * newScale,
        y: pointer.y - mousePointTo.y * newScale,
      };
      
      stage.position(newPos);
      stage.batchDraw();
      
      // Update zoom in the store
      store.setZoom(newScale);
    }
  }, [store, zoomOptions.minScale, zoomOptions.maxScale, zoomOptions.scaleFactor]);

  // Handle zoom controls using our custom hook
  const handleZoomIn = useCallback(() => {
    zoomIn(stageRef, dimensions);
  }, [zoomIn, dimensions]);

  const handleZoomOut = useCallback(() => {
    zoomOut(stageRef, dimensions);
  }, [zoomOut, dimensions]);

  const handleResetZoom = useCallback(() => {
    resetZoom(stageRef, dimensions);
  }, [resetZoom, dimensions]);

  // Set up subscription to Ably channel
  useEffect(() => {
    if (!channel) return;
    
    const handleUpdate = (message: any) => {
      const data = message.data;
      if (!data || !data.id) return;
      
      // Check if we've already processed this prediction to prevent duplicates
      if (completedPredictions.has(data.id)) {
        console.log(`Ignoring duplicate update for prediction: ${data.id}`);
        return;
      }
      
      // Mark this prediction as completed to prevent double processing
      setCompletedPredictions(prev => {
        const newSet = new Set(prev);
        newSet.add(data.id);
        return newSet;
      });
      
      // Find the task either by clientTaskId or predictionId
      let task = null;
      
      if (data.clientTaskId) {
        task = store.generationTasks.find(t => t.id === data.clientTaskId);
      }
      
      // Fall back to predictionId
      if (!task && data.id) {
        task = store.generationTasks.find(t => t.predictionId === data.id);
      }
      
      if (!task) {
        console.log('No matching task found for update:', data);
        return;
      }
      
      const taskId = task.id;
      const taskType = task.type || 'generate';
      
      // Update the predictionId if needed
      if (!task.predictionId && data.id) {
        store.updateGenerationTask(taskId, { predictionId: data.id });
      }
      
      // Process update based on status
      if (data.status === PredictionStatus.SUCCEEDED && data.outputUrl) {
        // Task succeeded, set image information
        store.updateGenerationTask(taskId, { 
          status: 'complete',
          imageId: `${taskType}-${data.id}`,
          imageUrl: data.outputUrl
        });
        
        // If this is a completed magic-fill, also add the generated image to the canvas
        if (taskType === 'magic-fill') {
          // Load the image and add it to the canvas
          const img = new Image();
          img.crossOrigin = "anonymous";
          img.src = data.outputUrl;
          img.onload = () => {
            // Add to generated images using the task's selection area
            store.addGeneratedImage({
              image: img,
              src: data.outputUrl,
              element: img,
              position: { 
                x: task.selectionArea.x, 
                y: task.selectionArea.y 
              },
              width: task.selectionArea.width,
              height: task.selectionArea.height,
              rotation: 0,
              scaleX: 1,
              scaleY: 1,
              isSelected: false,
              isNew: true,
              id: `${taskType}-${data.id}`,
            });
            
            // Only clear lines and brush composite after the image is successfully loaded
            store.clearLines();
            setBrushComposite(null);
            
            // Reset magic fill UI state if still in magic fill mode
            if (store.mode === CanvasMode.MagicFill) {
              store.setMagicFillAreaSelection(false);
              store.setMode(CanvasMode.Move);
            }
            
            toast({
              title: "Magic Fill Complete",
              description: "The image has been successfully generated and added to the canvas",
            });
          };
        }
      } else if (data.status === PredictionStatus.PROCESSING) {
        // Task is still processing
        store.updateGenerationTask(taskId, { status: 'generating' });
      } else if (data.status === PredictionStatus.FAILED) {
        // Task failed, update status and show error toast
        store.updateGenerationTask(taskId, { status: 'error' });
        
        toast({
          title: `${taskType === 'magic-fill' ? 'Magic Fill' : 'Generation'} failed`,
          description: data.error || `An error occurred during ${taskType === 'magic-fill' ? 'magic fill' : 'image generation'}`,
          variant: "destructive"
        });
        
        // Remove error tasks after a delay
        setTimeout(() => {
          store.removeGenerationTask(taskId);
        }, 3000);
      }
    };

    channel.subscribe('prediction-update', handleUpdate);
    
    return () => {
      channel.unsubscribe('prediction-update', handleUpdate);
    };
  }, [channel, toast, store, completedPredictions]);

  // Filter tasks to show placeholders and tasks with imageUrl
  const visibleTasks = store.generationTasks.filter(task => {
    // Always show tasks with imageUrl
    if (task.imageUrl) return true;
    
    // Don't show completed tasks without an imageUrl
    if (task.status === 'complete' && !task.imageUrl) return false;
    
    // Don't show tasks with imageId but no imageUrl
    if (task.imageId && !task.imageUrl) return false;
    
    // Show all other tasks
    return true;
  });

  // Periodic cleanup of stale tasks
  useEffect(() => {
    // Only run cleanup if there are tasks
    if (store.generationTasks.length === 0) return;
    
    const cleanupInterval = setInterval(() => {
      // Find tasks that should be cleaned up
      const tasksToClean = store.generationTasks.filter(task => {
        // Tasks with images are cleaned up gradually
        if (task.imageUrl) {
          return Math.random() < 0.01; // 1% chance for gradual cleanup
        }
        
        // Clean up completed tasks without imageUrl
        if (task.status === 'complete' && !task.imageUrl) return true;
        
        // Clean up tasks with imageId but no imageUrl
        if (task.imageId && !task.imageUrl) return true;
        
        // Clean up error tasks periodically
        if (task.status === 'error') {
          return Math.random() < 0.1; // 10% chance each cycle
        }
        
        return false;
      });
      
      // Remove each task that needs cleanup
      tasksToClean.forEach(task => {
        store.removeGenerationTask(task.id);
      });
    }, 2000); // Run every 2 seconds
    
    return () => clearInterval(cleanupInterval);
  }, [store]);
  
  // Function to export the selected area of the canvas
  const handleExportSelectedArea = async () => {
    const selection = store.selectionArea;
    
    if (!selection || !stageRef.current) {
      toast({
        title: "No selection",
        description: "Please select an area to export first",
        variant: "destructive",
      });
      return;
    }
    
    try {
      // Use our enhanced captureSelection method for accurate and consistent results
      const dataUrl = await captureSelection(stageRef);
      
      if (!dataUrl) {
        throw new Error("Failed to capture selection area");
      }
      
      // Create a download link for the captured image
      const link = document.createElement("a");
      link.download = `canvas-selection-${Date.now()}.png`;
      link.href = dataUrl;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast({
        title: "Export successful",
        description: "The selected area has been exported as an image",
      });
    } catch (error) {
      console.error("Failed to export selection:", error);
      toast({
        title: "Export failed",
        description: "There was an error exporting the selection",
        variant: "destructive",
      });
    }
  };

  // Handler for internal magic fill
  const handleInternalMagicFill = async () => {
    await handleMagicFill(stageRef);
  };

  // Helper function to capture and upload an area of the canvas
  const handleCaptureAndUploadSelectionArea = async (selection: CanvasRect): Promise<string> => {
    return captureAndUploadSelectionArea(selection, stageRef, currentUser.data?.id || "");
  };

  return (
    <div className={cn("relative", className)}>
      {/* Add zoom controls */}
      <ZoomControls
        onZoomIn={handleZoomIn}
        onZoomOut={handleZoomOut}
        onResetZoom={handleResetZoom}
        zoomLevel={store.zoom}
      />
      
      <Stage
        ref={stageRef}
        x={0}
        y={0}
        width={dimensions.width}
        height={dimensions.height}
        draggable={store.mode === CanvasMode.Pan}
        onClick={handleStageClick}
        onDragEnd={(e) => {
          if (store.mode === CanvasMode.Pan) {
            // Optionally, update viewport offset
          }
        }}
        onMouseDown={(e) => {
          if (
            store.magicFillAreaSelection &&
            store.mode !== CanvasMode.SelectArea
          )
            return;
          handleMouseDown(e, stageRef);
        }}
        onMouseMove={(e) => handleMouseMove(e, stageRef)}
        onMouseUp={() => handleMouseUp(stageRef)}
        onWheel={handleWheelEvent}
      >
        {/* Background layer */}
        <Layer>
          <Rect
            x={-VIRTUAL_WIDTH / 2}
            y={-VIRTUAL_HEIGHT / 2}
            width={VIRTUAL_WIDTH}
            height={VIRTUAL_HEIGHT}
            fill={canvasFill}
          />
        </Layer>

        {/* Use the CanvasLayersComponent for main content layers */}
        <CanvasLayersComponent
          inputImage={store.inputImage}
          inputImageDimensions={store.inputImageDimensions}
          inputImagePosition={store.inputImagePosition}
          generatedImages={store.generatedImages}
          transformConfig={store.transformConfig}
          brushComposite={brushComposite}
          mode={store.mode}
          onImageClick={handleImageClick}
          onImageDragEnd={handleImageDragEnd}
          onTransformEnd={handleTransformEnd}
          onImageUpdate={handleImageUpdate}
          selectedImageRef={selectedImageRef}
          transformerRef={transformerRef}
        />

        {/* Placeholder task layer */}
        <PlaceholderLayer
          tasks={visibleTasks}
          mode={store.mode}
          selectedPlaceholderId={selectedPlaceholderId}
          onPlaceholderDragEnd={handlePlaceholderDragEnd}
          onPlaceholderTransform={handlePlaceholderTransform}
          onPlaceholderClick={handlePlaceholderClick}
        />

        {/* Active brush strokes layer - must be visible for magic fill */}
        {store.mode === CanvasMode.MagicFill && (
          <MagicFillCompositeLayer
            lines={store.lines}
            dimensions={dimensions}
            isDrawing={isDrawing}
          />
        )}

        {/* Real-time stroke preview - always visible */}
        {activeLine && (
          <ActiveStrokePreview currentLine={activeLine} />
        )}

        {/* Selection rectangle - always on top */}
        {store.magicFillAreaSelection && store.selectionArea && (
          <Layer>
            <SelectionRect
              x={store.selectionArea.x}
              y={store.selectionArea.y}
              width={store.selectionArea.width}
              height={store.selectionArea.height}
              onChange={(attrs) => {
                store.setSelectionArea(attrs);
              }}
              stageRef={stageRef}
            />
          </Layer>
        )}
      </Stage>

      <div className="absolute top-0 left-0 z-50">
        <MainToolbar
          mode={store.mode}
          onModeChange={(newMode) => {
            // Special handling for entering magic fill mode
            if (newMode === CanvasMode.MagicFill) {
              // Reset magic fill state
              store.setMagicFillAreaSelection(false);
              store.clearLines();
              setBrushComposite(null);
            }
            
            // Set the new mode
            store.setMode(newMode);
          }}
          onGenerate={
            store.mode === CanvasMode.Generate
              ? handleGenerate
              : handleInternalMagicFill
          }
          onUpload={() => fileInputRef.current?.click()}
          onUndo={store.undo}
          onRedo={store.redo}
          onSave={() => {}}
          onDownload={() => {
            if (stageRef.current) {
              const dataUrl = stageRef.current.toDataURL({
                pixelRatio: window.devicePixelRatio || 2,
                mimeType: "image/png",
                quality: 1,
              });
              const link = document.createElement("a");
              link.download = "edited-image.png";
              link.href = dataUrl;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
            }
          }}
          canUndo={store.currentImageIndex > 0}
          canRedo={store.currentImageIndex < store.imageHistory.length - 1}
        />
      </div>

      {/* Display Magic Fill Toolbar when in MagicFill mode and NOT in area selection mode */}
      {store.mode === CanvasMode.MagicFill && !store.magicFillAreaSelection && (
        <MagicFillToolbar
          activeMaskTool={store.maskTool}
          onMaskToolChange={handleMaskToolChange}
          onInvert={() => {}} // Will be implemented if needed
          onCancelMagicFill={handleCancelMagicFill}
          onProceedMagicFill={handleProceedMagicFill}
          onExportSelection={handleExportSelectedArea}
        />
      )}

      {/* Show PromptInput for Generate mode or when a magic fill area is selected */}
      {(store.mode === CanvasMode.Generate ||
        (store.mode === CanvasMode.MagicFill &&
          store.magicFillAreaSelection)) && (
        <div className="fixed top-4 left-0 right-0 mx-auto z-50 w-full max-w-4xl flex justify-center">
          <PromptInput
            onGenerate={
              store.mode === CanvasMode.Generate
                ? handleGenerate
                : handleInternalMagicFill
            }
          />
          {/* Add Export button when in magic fill with area selected */}
          {store.mode === CanvasMode.MagicFill && 
           store.magicFillAreaSelection && 
           store.selectionArea && (
            <Button
              variant="outline"
              size="icon"
              onClick={handleExportSelectedArea}
              className="absolute right-[-60px] top-1/2 -translate-y-1/2 h-10 w-10 flex items-center justify-center"
              title="Export selected area"
            >
              <Download className="h-4 w-4" />
            </Button>
          )}
        </div>
      )}

      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        accept="image/*"
        onChange={onFileChange}
      />

      {/* Use LoadingOverlay component for loading states */}
      <LoadingOverlay 
        isLoading={store.isGenerating} 
        message="Generating image..." 
        opacity={0.2}
      />
      
      <LoadingOverlay 
        isLoading={!store.isGenerating && store.isUploadingImage} 
        message="Uploading image..."
        opacity={0.5} 
      />
    </div>
  );
} 