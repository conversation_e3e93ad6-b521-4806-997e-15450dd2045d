/**
 * Canvas utility functions
 * Single source of truth for all canvas calculations and validations
 */

import { CanvasOrientation, CanvasPosition, CanvasRect, CanvasSize } from "../types/canvas";
import Konva from "konva";

/**
 * Calculate the greatest common divisor of two numbers
 */
export const gcd = (a: number, b: number): number => (b === 0 ? a : gcd(b, a % b));

/**
 * Compute aspect ratio from width and height
 * Returns a string in the format "width:height"
 */
export function computeAspectRatio(width: number, height: number): string {
  const divisor = gcd(width, height);
  return `${width / divisor}:${height / divisor}`;
}

/**
 * Standard aspect ratios used throughout the application
 */
export const STANDARD_ASPECT_RATIOS = [
  "1:1", "16:9", "9:16", "4:3", "3:4", "3:2", "2:3", 
  "16:10", "10:16", "3:1", "1:3"
];

/**
 * Validate if the given string is a standard aspect ratio
 */
export function isValidAspectRatio(ratio: string): ratio is "1:1" | "16:9" | "9:16" | "4:3" | "3:4" | "3:2" | "2:3" | "16:10" | "10:16" | "3:1" | "1:3" {
  return STANDARD_ASPECT_RATIOS.includes(ratio);
}

/**
 * Returns the list of valid resolutions supported by the API
 */
export function getValidResolutions(): string[] {
  return [
    "1024x1024", "1408x704", "704x1408", "1312x736", "736x1312", 
    "1280x800", "800x1280", "1120x896", "896x1120", "1248x832", 
    "832x1248", "1152x864", "864x1152", "1536x512", "512x1536",
    "1408x576", "1472x576", "1536x576", "1344x640", "1408x640", 
    "1472x640", "1536x640", "1152x704", "1216x704", "1280x704", 
    "1344x704", "1472x704", "1088x768", "1216x768", "1280x768", 
    "1344x768", "960x832", "1024x832", "1088x832", "1152x832", 
    "1216x832", "960x896", "1024x896", "1088x896", "1152x896", 
    "832x960", "896x960", "1024x960", "1088x960", "832x1024", 
    "896x1024", "960x1024", "768x1088", "832x1088", "896x1088", 
    "960x1088", "704x1152", "832x1152", "896x1152", "704x1216", 
    "768x1216", "832x1216", "704x1280", "768x1280", "640x1344", 
    "704x1344", "768x1344", "576x1408", "640x1408", "576x1472", 
    "640x1472", "704x1472", "576x1536", "640x1536"
  ];
}

// Cache valid resolutions for better performance
const VALID_RESOLUTIONS = getValidResolutions();

/**
 * Validate if the given string is a supported resolution
 */
export function isValidResolution(resolution: string): boolean {
  return VALID_RESOLUTIONS.includes(resolution);
}

/**
 * Determine orientation based on width and height
 */
export function getOrientation(width: number, height: number): CanvasOrientation {
  return width >= height ? "landscape" : "portrait";
}

/**
 * Format dimensions as a resolution string
 */
export function formatResolution(width: number, height: number): string {
  return `${width}x${height}`;
}

/**
 * Calculate and prepare image generation parameters
 * This centralizes the logic for determining which parameters to use
 */
export function prepareGenerationParameters(
  width: number,
  height: number
): {
  resolution: string;
  aspectRatio: "1:1" | "16:9" | "9:16" | "4:3" | "3:4" | "3:2" | "2:3" | "16:10" | "10:16" | "3:1" | "1:3" | "None";
} {
  const resolutionString = formatResolution(width, height);
  const aspectRatio = computeAspectRatio(width, height);
  
  // Resolution takes priority over aspect ratio in API
  if (isValidResolution(resolutionString)) {
    return {
      resolution: resolutionString,
      // When valid resolution is provided, aspect ratio is not used by API
      // but we still store it for consistency in UI
      aspectRatio: isValidAspectRatio(aspectRatio) 
        ? aspectRatio as any 
        : "None" as any // Use "None" instead of forcing 1:1 for custom aspect ratios
    };
  } else {
    // Find the closest standard aspect ratio if exact match not found
    const closest = findClosestAspectRatio(width, height);
    
    // Fall back to aspect ratio when resolution isn't valid
    return {
      resolution: "None",
      aspectRatio: closest as any // Use the closest match instead of forcing 1:1
    };
  }
}

/**
 * Find the closest standard aspect ratio to the given dimensions
 */
export function findClosestAspectRatio(width: number, height: number): string {
  const standardRatios = {
    "1:1": 1,
    "16:9": 16/9,
    "9:16": 9/16,
    "4:3": 4/3,
    "3:4": 3/4,
    "3:2": 3/2,
    "2:3": 2/3,
    "16:10": 16/10,
    "10:16": 10/16,
    "3:1": 3/1,
    "1:3": 1/3
  };
  
  const targetRatio = width / height;
  
  // Find the ratio with the smallest difference
  let closestRatio = "1:1";
  let smallestDiff = Number.MAX_VALUE;
  
  Object.entries(standardRatios).forEach(([ratio, value]) => {
    const diff = Math.abs(value - targetRatio);
    if (diff < smallestDiff) {
      smallestDiff = diff;
      closestRatio = ratio;
    }
  });
  
  return closestRatio;
}

/**
 * Convert a data URL to a Blob
 */
export function dataURLtoBlob(dataurl: string): Blob {
  const arr = dataurl.split(",");
  const mimeMatch = arr[0].match(/:(.*?);/);
  const mime = mimeMatch ? mimeMatch[1] : "";
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], { type: mime });
}

/**
 * Finds the closest valid resolution to the given width and height
 * using the API's supported resolutions.
 */
export const findClosestValidResolution = (width: number, height: number): string => {
  // Check if the requested resolution is already valid
  const requestedResolution = `${width}x${height}`;
  if (isValidResolution(requestedResolution)) {
    return requestedResolution;
  }

  // If aspect ratio is important, we can provide a weighting
  const targetAspectRatio = width / height;
  
  // Calculate scores for each valid resolution
  const resolutionsWithScores = VALID_RESOLUTIONS.map(resolution => {
    const [w, h] = resolution.split("x").map(Number);
    
    // Calculate distance based on area difference and aspect ratio difference
    const areaDiff = Math.abs((w * h) - (width * height));
    const aspectRatioDiff = Math.abs((w / h) - targetAspectRatio);
    
    // Combine these factors - we weight aspect ratio difference more heavily
    const score = (areaDiff / 1000000) + (aspectRatioDiff * 10);
    
    return { resolution, score };
  });
  
  // Sort by score (lower is better) and get the best match
  resolutionsWithScores.sort((a, b) => a.score - b.score);
  
  if (resolutionsWithScores.length > 0) {
    return resolutionsWithScores[0].resolution;
  }
  
  return "None";
};

/**
 * Get relative pointer position on a Konva stage
 */
export function getRelativePointerPosition(stage: Konva.Stage): { x: number; y: number } | null {
  const pointer = stage.getPointerPosition();
  if (pointer) {
    return {
      x: (pointer.x - stage.x()) / stage.scaleX(),
      y: (pointer.y - stage.y()) / stage.scaleY(),
    };
  }
  return null;
}

/**
 * Snap a position to a grid
 */
export function snapToGrid(pos: { x: number; y: number }, gridSize = 1) {
  return {
    x: Math.round(pos.x / gridSize) * gridSize,
    y: Math.round(pos.y / gridSize) * gridSize,
  };
}

/**
 * Find an available position for placing a new item on the canvas
 */
export function findAvailableCanvasPosition(
  width: number,
  height: number,
  stage: Konva.Stage | null,
  dimensions: CanvasSize,
  occupiedSpaces: CanvasRect[]
): CanvasPosition {
  if (!stage) {
    return { x: 100, y: 100 };
  }
  
  const scale = stage.scaleX();
  
  // Get the actual visible area of the canvas in virtual space
  const visibleAreaTopLeft = {
    x: (0 - stage.x()) / scale,
    y: (0 - stage.y()) / scale
  };
  
  const visibleAreaBottomRight = {
    x: (dimensions.width - stage.x()) / scale,
    y: (dimensions.height - stage.y()) / scale
  };
  
  // Adjust padding for better spacing
  const padding = 20;
  
  // If no existing elements, return a position near the center of the visible area
  if (occupiedSpaces.length === 0) {
    return {
      x: visibleAreaTopLeft.x + (visibleAreaBottomRight.x - visibleAreaTopLeft.x - width) / 2,
      y: visibleAreaTopLeft.y + (visibleAreaBottomRight.y - visibleAreaTopLeft.y - height) / 2
    };
  }
  
  // Helper function to check if rectangles overlap
  const doRectanglesOverlap = (rect1: CanvasRect, rect2: CanvasRect) => {
    return (
      rect1.x < rect2.x + rect2.width &&
      rect1.x + rect1.width > rect2.x &&
      rect1.y < rect2.y + rect2.height &&
      rect1.y + rect1.height > rect2.y
    );
  };
  
  // Define a grid of potential positions to check, constrained to the visible area
  const gridSpacing = 100;
  const gridPositions: CanvasPosition[] = [];
  
  // Create a grid of positions within the visible area
  for (let x = visibleAreaTopLeft.x + padding; 
       x < visibleAreaBottomRight.x - width - padding; 
       x += gridSpacing) {
    for (let y = visibleAreaTopLeft.y + padding; 
         y < visibleAreaBottomRight.y - height - padding; 
         y += gridSpacing) {
      gridPositions.push({ x, y });
    }
  }
  
  // Default position if we can't find a better one (top-left of visible area with padding)
  const defaultPosition = { 
    x: visibleAreaTopLeft.x + padding, 
    y: visibleAreaTopLeft.y + padding 
  };
  
  // If we have no grid positions, return default
  if (gridPositions.length === 0) return defaultPosition;
  
  // Calculate "congestion" at each grid position
  const positionScores = gridPositions.map(pos => {
    const rect = { 
      x: pos.x, 
      y: pos.y, 
      width, 
      height 
    };
    
    // Count overlaps with occupied spaces
    let overlaps = 0;
    for (const space of occupiedSpaces) {
      if (doRectanglesOverlap(rect, space)) {
        overlaps++;
      }
    }
    
    // Calculate minimum distance to visible area edges
    const distToRightEdge = visibleAreaBottomRight.x - (pos.x + width);
    const distToBottomEdge = visibleAreaBottomRight.y - (pos.y + height);
    const distToLeftEdge = pos.x - visibleAreaTopLeft.x;
    const distToTopEdge = pos.y - visibleAreaTopLeft.y;
    const minEdgeDist = Math.min(distToRightEdge, distToBottomEdge, distToLeftEdge, distToTopEdge);
    
    // Calculate score - penalize overlaps heavily, prefer being away from edges but not too far
    const score = (overlaps * -1000) + Math.min(minEdgeDist, 200);
    
    return { pos, score, overlaps };
  });
  
  // Sort positions by score (highest first)
  positionScores.sort((a, b) => b.score - a.score);
  
  // If we found a position with no overlaps, use it
  const bestPosition = positionScores.find(p => p.overlaps === 0);
  if (bestPosition) {
    return bestPosition.pos;
  }
  
  // If all positions overlap, use the first grid position
  return positionScores[0]?.pos || defaultPosition;
} 