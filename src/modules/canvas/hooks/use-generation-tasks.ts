import { useState, useCallback, useEffect } from 'react';
import { PredictionStatus } from '@prisma/client';
import useCanvasStore from '../store/canvas-store';
import { CanvasRect, CanvasMode } from '../types/canvas';
import { useToast } from '@/modules/ui/use-toast';
import { findClosestValidResolution, findClosestAspectRatio, computeAspectRatio } from '../utils/canvas-utils';
import { editImage } from '../actions/edit-image';

// Utility function to ensure correct image dimensions
const calculateCorrectDimensions = (
  selectionArea: { width: number; height: number },
  img: HTMLImageElement
) => {
  // Scale the image to match the selection area exactly
  return {
    width: selectionArea.width,
    height: selectionArea.height
  };
};

/**
 * Hook for managing canvas generation and prediction tasks
 */
export const useGenerationTasks = (userId: string, channelName: string) => {
  const store = useCanvasStore();
  const { toast } = useToast();
  const [loadingImages, setLoadingImages] = useState<Set<string>>(new Set());
  const [completedPredictions, setCompletedPredictions] = useState<Set<string>>(new Set());
  
  /**
   * Generate a new image at the specified area
   */
  const handleGenerate = useCallback(async () => {
    // Validation: we need a user ID to proceed
    if (!userId) {
      toast({
        title: "Authentication required",
        description: "Please sign in to generate images",
        variant: "destructive",
      });
      return;
    }

    // Deselect any currently selected items
    store.setTransformConfig({
      selectedImageIndex: null,
      isDragging: false,
      isResizing: false,
      isRotating: false,
    });

    // Get current aspect ratio, defaulting to 16:9 if not set
    const aspectRatio = store.aspectRatio === "None" 
      ? (localStorage.getItem('lastAspectRatio') || "16:9") 
      : store.aspectRatio;
    
    console.log(`Generation using aspect ratio: ${aspectRatio}`);
    
    // Set fixed base width and calculate height based on aspect ratio
    const baseWidth = 512;
    let width = baseWidth;
    let height = baseWidth;
    
    // Calculate height based on aspect ratio
    if (aspectRatio !== "1:1") {
      const [w, h] = aspectRatio.split(':').map(Number);
      height = Math.round(width * (h / w));
    }
    
    // Find a good position for the new image
    // Get all occupied spaces on the canvas to find a good spot
    const occupiedSpaces = [
      // Add generated images
      ...store.generatedImages.map(img => ({
        x: img.position.x,
        y: img.position.y,
        width: img.width,
        height: img.height
      })),
      // Add generation tasks/placeholders
      ...store.generationTasks.map(task => task.selectionArea)
    ];
    
    // Add the input image if it exists
    if (store.inputImage) {
      occupiedSpaces.push({
        x: store.inputImagePosition.x,
        y: store.inputImagePosition.y,
        width: store.inputImageDimensions?.width || store.inputImage.width,
        height: store.inputImageDimensions?.height || store.inputImage.height
      });
    }
    
    // Simple position calculation
    // Use the dimensions from the store to find a reasonable center point
    let position = { x: 0, y: 0 };
    
    if (store.dimensions && store.dimensions.width > 0 && store.dimensions.height > 0) {
      // Use center of the canvas dimensions with zoom adjustment
      position.x = (store.dimensions.width / 2) / store.zoom - width / 2;
      position.y = (store.dimensions.height / 2) / store.zoom - height / 2;
    } else {
      // Simple offset if dimensions not available
      position = { x: 100, y: 100 };
    }
    
    // Create the generation area directly
    const generationArea = {
      x: position.x,
      y: position.y,
      width,
      height
    };

    console.log("Creating generation with dimensions:", {
      width, height, position, aspectRatio
    });

    // Create a new task with a unique ID
    const taskId = crypto.randomUUID();

    // Check for duplicate tasks to prevent multiple generations in the same area
    const existingTaskWithSameArea = store.generationTasks.find(task => {
      const existingArea = task.selectionArea;
      
      // Check if the areas significantly overlap (more than 90% overlap)
      const isOverlapping = 
        Math.abs(existingArea.x - generationArea.x) < 10 &&
        Math.abs(existingArea.y - generationArea.y) < 10 &&
        Math.abs(existingArea.width - generationArea.width) < 10 &&
        Math.abs(existingArea.height - generationArea.height) < 10;
      
      return isOverlapping && (task.status === 'pending' || task.status === 'generating');
    });
    
    if (existingTaskWithSameArea) {
      toast({
        title: "Generation in progress",
        description: "An image is already being generated in this area",
        variant: "destructive",
      });
      return;
    }

    // Create and add the new task using store methods
    store.addGenerationTask({
      id: taskId,
      selectionArea: generationArea,
      prompt: store.prompt,
      status: 'pending'
    });

    try {
      // Update the task to generating status
      store.updateGenerationTask(taskId, { status: 'generating' });
      
      // Find the closest valid resolution based on our dimensions
      const calculatedResolution = store.resolution === "None" || !store.resolution
        ? findClosestValidResolution(width, height)
        : store.resolution;
      
      // Use the already validated aspect ratio
      const result = await editImage({
        mode: "generate",
        userId: userId,
        prompt: store.prompt,
        negativePrompt: store.negativePrompt,
        style_type: store.styleType,
        magic_prompt_option: store.magicPromptOption,
        seed: store.seed,
        resolution: calculatedResolution,
        aspect_ratio: aspectRatio,
        clientTaskId: taskId
      });

      // Update the task with the prediction ID
      if (result.success && result.predictionId) {
        store.updateGenerationTask(taskId, { predictionId: result.predictionId });
      } else if (!result.success) {
        // Update task to error state
        store.updateGenerationTask(taskId, { status: 'error' });
        
        toast({
          title: "Generation failed",
          description: result.error || "An error occurred during image generation",
          variant: "destructive",
        });
      }
    } catch (error) {
      // Set the task to error state
      store.updateGenerationTask(taskId, { status: 'error' });
      
      console.error("Generation error:", error);
      toast({
        title: "Generation failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    }
  }, [userId, store, toast]);

  /**
   * Handle Ably channel updates for image generation tasks
   */
  const handleChannelUpdate = useCallback((message: any) => {
    const data = message.data;
    if (!data || !data.id) return;
    
    // Check if we've already processed this prediction to prevent duplicates
    if (completedPredictions.has(data.id)) {
      console.log(`Ignoring duplicate update for prediction: ${data.id}`);
      return;
    }
    
    // Mark this prediction as completed to prevent double processing
    setCompletedPredictions(prev => {
      const newSet = new Set(prev);
      newSet.add(data.id);
      return newSet;
    });
    
    // Find the task either by clientTaskId or predictionId
    let task = null;
    
    if (data.clientTaskId) {
      task = store.generationTasks.find(t => t.id === data.clientTaskId);
    }
    
    // Fall back to predictionId
    if (!task && data.id) {
      task = store.generationTasks.find(t => t.predictionId === data.id);
    }
    
    if (!task) {
      console.log('No matching task found for update:', data);
      return;
    }
    
    const taskId = task.id;
    const taskType = task.type || 'generate';
    
    // Update the predictionId if needed
    if (!task.predictionId && data.id) {
      store.updateGenerationTask(taskId, { predictionId: data.id });
    }
    
    // Process update based on status
    if (data.status === PredictionStatus.SUCCEEDED && data.outputUrl) {
      // Task succeeded, set image information
      // For magic-fill tasks, we need to set this to complete ASAP to ensure the placeholder is properly updated
      if (taskType === 'magic-fill') {
        console.log(`Marking magic-fill task ${taskId} as complete immediately`);
        store.updateGenerationTask(taskId, { 
          status: 'complete',
          imageId: `${taskType}-${data.id}`,
          imageUrl: data.outputUrl
        });
      } else {
        // For regular generation tasks, follow normal flow
        store.updateGenerationTask(taskId, { 
          status: 'complete',
          imageId: `${taskType}-${data.id}`,
          imageUrl: data.outputUrl
        });
      }
      
      // Track this image as loading to prevent duplicate processing
      setLoadingImages(prev => {
        const newSet = new Set(prev);
        newSet.add(data.id);
        return newSet;
      });
      
      // If this is a completed task with an image, add it to the canvas
      const img = new Image();
      img.crossOrigin = "anonymous";
      img.src = data.outputUrl;
      
      // Save original selection area before any modifications
      const originalSelectionArea = {...task.selectionArea};
      
      img.onload = () => {
        console.log(`Successfully loaded ${taskType} image: ${data.outputUrl}`);
        
        // Check if the image is already in the generated images list
        const existingImage = store.generatedImages.find(img => img.src === data.outputUrl);
        
        if (existingImage) {
          console.log('This image already exists in generatedImages, not adding again');
          // Just update the task to complete
          store.updateGenerationTask(taskId, { 
            status: 'complete',
            imageUrl: data.outputUrl,
            imageId: existingImage.id
          });
        } else {
          // Generate a unique ID for this image
          const imageId = `${taskType}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
          
          // Log the dimensions for debugging
          console.log('Adding generated image with dimensions:', {
            selectionArea: originalSelectionArea,
            imageNaturalSize: { width: img.naturalWidth, height: img.naturalHeight }
          });
          
          // Calculate dimensions that match the selection area exactly
          const dimensions = calculateCorrectDimensions(originalSelectionArea, img);
          
          // Add to generated images using the task's selection area
          store.addGeneratedImage({
            image: img,
            src: data.outputUrl,
            element: img,
            position: { 
              x: originalSelectionArea.x, 
              y: originalSelectionArea.y 
            },
            width: dimensions.width,
            height: dimensions.height,
            rotation: 0,
            scaleX: 1,
            scaleY: 1,
            isSelected: false,
            isNew: true,
            id: imageId,
          });
          
          // Update the task with the image ID
          store.updateGenerationTask(taskId, { 
            status: 'complete',
            imageUrl: data.outputUrl,
            imageId: imageId
          });
        }
        
        // If this is a magic fill operation, perform additional cleanup
        if (taskType === 'magic-fill') {
          // Clear lines after the image is successfully loaded
          store.clearLines();
          
          // Reset magic fill UI state if still in magic fill mode
          if (store.mode === CanvasMode.MagicFill) {
            store.setMagicFillAreaSelection(false);
            store.setMode(CanvasMode.Move);
          }
          
          toast({
            title: "Magic Fill Complete",
            description: "The image has been successfully generated and added to the canvas",
          });
        } else {
          toast({
            title: "Generation Complete",
            description: "The image has been successfully generated and added to the canvas",
          });
        }
        
        // Remove this image from loading set
        setLoadingImages(prev => {
          const newSet = new Set(prev);
          newSet.delete(data.id);
          return newSet;
        });
      };
      
      img.onerror = () => {
        console.error(`Failed to load image: ${data.outputUrl}`);
        toast({
          title: "Image Loading Failed",
          description: "The generated image could not be loaded",
          variant: "destructive"
        });
        
        // Remove this image from loading set
        setLoadingImages(prev => {
          const newSet = new Set(prev);
          newSet.delete(data.id);
          return newSet;
        });
        
        // Update task status to error
        store.updateGenerationTask(taskId, { status: 'error' });
      };
    } else if (data.status === PredictionStatus.PROCESSING) {
      // Task is still processing
      store.updateGenerationTask(taskId, { status: 'generating' });
    } else if (data.status === PredictionStatus.FAILED) {
      // Task failed, update status and show error toast
      store.updateGenerationTask(taskId, { status: 'error' });
      
      toast({
        title: `${taskType === 'magic-fill' ? 'Magic Fill' : 'Generation'} failed`,
        description: data.error || `An error occurred during ${taskType === 'magic-fill' ? 'magic fill' : 'image generation'}`,
        variant: "destructive"
      });
      
      // Remove error tasks after a delay
      setTimeout(() => {
        store.removeGenerationTask(taskId);
      }, 3000);
    }
  }, [store, toast, completedPredictions, setLoadingImages]);

  /**
   * Cleanup stale generation tasks periodically
   */
  useEffect(() => {
    if (store.generationTasks.length === 0) return;
    
    const cleanupInterval = setInterval(() => {
      // Find tasks that should be cleaned up
      const tasksToClean = store.generationTasks.filter(task => {
        // Tasks with images are cleaned up gradually
        if (task.imageUrl) {
          return Math.random() < 0.01; // 1% chance for gradual cleanup
        }
        
        // Clean up completed tasks without imageUrl
        if (task.status === 'complete' && !task.imageUrl) return true;
        
        // Clean up tasks with imageId but no imageUrl
        if (task.imageId && !task.imageUrl) return true;
        
        // Clean up error tasks periodically
        if (task.status === 'error') {
          return Math.random() < 0.1; // 10% chance each cycle
        }
        
        return false;
      });
      
      // Remove each task that needs cleanup
      tasksToClean.forEach(task => {
        store.removeGenerationTask(task.id);
      });
    }, 2000); // Run every 2 seconds
    
    return () => clearInterval(cleanupInterval);
  }, [store]);

  /**
   * Handle placeholder item interactions
   */
  const handlePlaceholderDragEnd = useCallback((taskId: string, e: any) => {
    const task = store.generationTasks.find(t => t.id === taskId);
    if (!task) return;
    
    // Update the task's selection area with the new position
    store.updateGenerationTask(taskId, {
      selectionArea: {
        ...task.selectionArea,
        x: e.target.x(),
        y: e.target.y()
      }
    });
  }, [store]);

  const handlePlaceholderTransform = useCallback((taskId: string, e: any) => {
    const task = store.generationTasks.find(t => t.id === taskId);
    if (!task) return;
    
    // Update the task with the new dimensions
    // Use attrs property to access position and dimensions
    // as the target might not have x(), y(), etc. methods
    const node = e.target;
    const attrs = node.attrs || node;
    
    store.updateGenerationTask(taskId, {
      selectionArea: {
        ...task.selectionArea,
        x: typeof node.x === 'function' ? node.x() : attrs.x,
        y: typeof node.y === 'function' ? node.y() : attrs.y,
        width: typeof node.width === 'function' ? node.width() : attrs.width,
        height: typeof node.height === 'function' ? node.height() : attrs.height
      }
    });
  }, [store]);

  return {
    handleGenerate,
    handleChannelUpdate,
    loadingImages,
    setLoadingImages,
    completedPredictions,
    setCompletedPredictions,
    handlePlaceholderDragEnd,
    handlePlaceholderTransform
  };
}; 