import { useCallback } from 'react';

/**
 * Hook for optimizing images for canvas performance
 */
export const useImageOptimization = () => {
  /**
   * Compress an image to reduce memory usage
   */
  const compressImage = useCallback((
    image: HTMLImageElement,
    maxWidth: number = 2048,
    maxHeight: number = 2048,
    quality: number = 0.8
  ): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        if (!ctx) {
          reject(new Error('Failed to get canvas context'));
          return;
        }

        // Calculate new dimensions while maintaining aspect ratio
        let { width, height } = image;
        
        if (width > maxWidth || height > maxHeight) {
          const aspectRatio = width / height;
          
          if (width > height) {
            width = maxWidth;
            height = width / aspectRatio;
          } else {
            height = maxHeight;
            width = height * aspectRatio;
          }
        }

        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx.drawImage(image, 0, 0, width, height);
        
        // Convert to blob and back to image
        canvas.toBlob((blob) => {
          if (!blob) {
            reject(new Error('Failed to compress image'));
            return;
          }
          
          const compressedImage = new Image();
          compressedImage.crossOrigin = 'anonymous';
          compressedImage.onload = () => resolve(compressedImage);
          compressedImage.onerror = () => reject(new Error('Failed to load compressed image'));
          compressedImage.src = URL.createObjectURL(blob);
        }, 'image/jpeg', quality);
      } catch (error) {
        reject(error);
      }
    });
  }, []);

  /**
   * Check if an image needs compression based on size
   */
  const shouldCompress = useCallback((image: HTMLImageElement): boolean => {
    const maxPixels = 4 * 1024 * 1024; // 4MP
    const pixels = image.width * image.height;
    return pixels > maxPixels;
  }, []);

  /**
   * Get memory usage estimate for an image
   */
  const getImageMemoryUsage = useCallback((image: HTMLImageElement): number => {
    // Rough estimate: width * height * 4 bytes (RGBA)
    return image.width * image.height * 4;
  }, []);

  /**
   * Create a thumbnail version of an image
   */
  const createThumbnail = useCallback((
    image: HTMLImageElement,
    size: number = 200
  ): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        if (!ctx) {
          reject(new Error('Failed to get canvas context'));
          return;
        }

        // Calculate thumbnail dimensions
        const aspectRatio = image.width / image.height;
        let thumbWidth, thumbHeight;
        
        if (aspectRatio > 1) {
          thumbWidth = size;
          thumbHeight = size / aspectRatio;
        } else {
          thumbHeight = size;
          thumbWidth = size * aspectRatio;
        }

        canvas.width = thumbWidth;
        canvas.height = thumbHeight;

        // Draw thumbnail
        ctx.drawImage(image, 0, 0, thumbWidth, thumbHeight);
        
        // Convert to image
        canvas.toBlob((blob) => {
          if (!blob) {
            reject(new Error('Failed to create thumbnail'));
            return;
          }
          
          const thumbnail = new Image();
          thumbnail.crossOrigin = 'anonymous';
          thumbnail.onload = () => resolve(thumbnail);
          thumbnail.onerror = () => reject(new Error('Failed to load thumbnail'));
          thumbnail.src = URL.createObjectURL(blob);
        }, 'image/jpeg', 0.7);
      } catch (error) {
        reject(error);
      }
    });
  }, []);

  return {
    compressImage,
    shouldCompress,
    getImageMemoryUsage,
    createThumbnail,
  };
};
