import { useCallback } from 'react';
import useCanvasStore from '../store/canvas-store';
import Konva from 'konva';

interface UseCanvasZoomOptions {
  minScale?: number;
  maxScale?: number;
  scaleFactor?: number;
  defaultScale?: number;
}

/**
 * Hook for managing canvas zoom functionality
 */
export const useCanvasZoom = (options: UseCanvasZoomOptions = {}) => {
  const { 
    minScale = 0.1, 
    maxScale = 5, 
    scaleFactor = 1.1,
    defaultScale = 0.7
  } = options;
  
  const setZoom = useCanvasStore(state => state.setZoom);
  
  /**
   * Handle mouse wheel events for zooming
   */
  const handleWheel = useCallback((e: Konva.KonvaEventObject<WheelEvent>, stageRef: React.RefObject<Konva.Stage>) => {
    e.evt.preventDefault();
    const stage = stageRef.current;
    if (!stage) return;
    
    const oldScale = stage.scaleX();
    const pointer = stage.getPointerPosition();
    if (!pointer) return;
    
    const direction = e.evt.deltaY > 0 ? 1 / scaleFactor : scaleFactor;
    let newScale = oldScale * direction;
    
    // Clamp the scale to min/max values
    newScale = Math.max(minScale, Math.min(newScale, maxScale));
    
    // Calculate new position to zoom toward mouse pointer
    const mousePointTo = {
      x: (pointer.x - stage.x()) / oldScale,
      y: (pointer.y - stage.y()) / oldScale,
    };
    
    stage.scale({ x: newScale, y: newScale });
    
    const newPos = {
      x: pointer.x - mousePointTo.x * newScale,
      y: pointer.y - mousePointTo.y * newScale,
    };
    
    stage.position(newPos);
    stage.batchDraw();
    
    // Update zoom in the store
    setZoom(newScale);
  }, [minScale, maxScale, scaleFactor, setZoom]);
  
  /**
   * Zoom in centered on viewport
   */
  const zoomIn = useCallback((stageRef: React.RefObject<Konva.Stage>, dimensions: { width: number; height: number }) => {
    if (!stageRef.current) return;
    
    const stage = stageRef.current;
    const oldScale = stage.scaleX();
    let newScale = oldScale * scaleFactor;
    newScale = Math.min(newScale, maxScale);
    
    const centerX = dimensions.width / 2;
    const centerY = dimensions.height / 2;
    
    const mousePointTo = {
      x: (centerX - stage.x()) / oldScale,
      y: (centerY - stage.y()) / oldScale,
    };
    
    stage.scale({ x: newScale, y: newScale });
    
    const newPos = {
      x: centerX - mousePointTo.x * newScale,
      y: centerY - mousePointTo.y * newScale,
    };
    
    stage.position(newPos);
    stage.batchDraw();
    setZoom(newScale);
  }, [scaleFactor, maxScale, setZoom]);
  
  /**
   * Zoom out centered on viewport
   */
  const zoomOut = useCallback((stageRef: React.RefObject<Konva.Stage>, dimensions: { width: number; height: number }) => {
    if (!stageRef.current) return;
    
    const stage = stageRef.current;
    const oldScale = stage.scaleX();
    let newScale = oldScale / scaleFactor;
    newScale = Math.max(minScale, newScale);
    
    const centerX = dimensions.width / 2;
    const centerY = dimensions.height / 2;
    
    const mousePointTo = {
      x: (centerX - stage.x()) / oldScale,
      y: (centerY - stage.y()) / oldScale,
    };
    
    stage.scale({ x: newScale, y: newScale });
    
    const newPos = {
      x: centerX - mousePointTo.x * newScale,
      y: centerY - mousePointTo.y * newScale,
    };
    
    stage.position(newPos);
    stage.batchDraw();
    setZoom(newScale);
  }, [minScale, scaleFactor, setZoom]);
  
  /**
   * Reset zoom to default scale and center the stage
   */
  const resetZoom = useCallback((stageRef: React.RefObject<Konva.Stage>, dimensions: { width: number; height: number }) => {
    if (!stageRef.current) return;
    
    const stage = stageRef.current;
    
    stage.scale({ x: defaultScale, y: defaultScale });
    stage.position({
      x: dimensions.width / 2,
      y: dimensions.height / 2
    });
    stage.batchDraw();
    setZoom(defaultScale);
  }, [defaultScale, setZoom]);
  
  /**
   * Initialize stage with default scale and centered position
   */
  const initializeZoom = useCallback((stageRef: React.RefObject<Konva.Stage>, dimensions: { width: number; height: number }) => {
    if (!stageRef.current) return;
    
    const stage = stageRef.current;
    
    // Set scale
    stage.scale({ x: defaultScale, y: defaultScale });
    
    // Center the stage to show the origin at the center of the viewport
    stage.position({
      x: dimensions.width / 2,
      y: dimensions.height / 2
    });
    
    stage.batchDraw();
    setZoom(defaultScale);
  }, [defaultScale, setZoom]);
  
  return {
    handleWheel,
    zoomIn,
    zoomOut,
    resetZoom,
    initializeZoom,
    minScale,
    maxScale,
    defaultScale
  };
}; 