import React from "react";
import { Group, Shape } from "react-konva";
import Konva from "konva";
import { CanvasLine, CanvasSize } from "../types/canvas";

export interface MagicFillCompositeLayerProps {
  lines: CanvasLine[];
  dimensions: CanvasSize;
  isDrawing: boolean;
  x?: number;
  y?: number;
}

/**
 * MagicFillCompositeLayer combines all mask strokes into two layers:
 * 1. Visual feedback layer with purple gradient for user interaction
 * 2. Pure black and white mask layer for actual mask data
 * 
 * The mask layer is invisible during drawing but used for the final mask generation.
 * It contains only the stroke data in pure black and white, with no image data.
 */
export const MagicFillCompositeLayer: React.FC<MagicFillCompositeLayerProps> = ({ 
  lines, 
  dimensions, 
  isDrawing,
  x = 0,
  y = 0
}) => {
  // Exclude the active (in-progress) stroke
  const finalizedLines = isDrawing ? lines.slice(0, lines.length - 1) : lines;

  const drawPath = (context: Konva.Context, line: CanvasLine) => {
    context.beginPath();
    context.moveTo(line.points[0], line.points[1]);
    
    if (line.points.length >= 4) {
      for (let i = 2; i < line.points.length - 2; i += 2) {
        const x = line.points[i];
        const y = line.points[i + 1];
        const nextX = line.points[i + 2];
        const nextY = line.points[i + 3];
        const midX = (x + nextX) / 2;
        const midY = (y + nextY) / 2;
        context.quadraticCurveTo(x, y, midX, midY);
      }
      context.lineTo(
        line.points[line.points.length - 2],
        line.points[line.points.length - 1]
      );
    } else {
      for (let i = 2; i < line.points.length; i += 2) {
        context.lineTo(line.points[i], line.points[i + 1]);
      }
    }

    if (line.tool === "rectangle" || (line.tool === "lasso" && line.points.length > 4)) {
      context.closePath();
    }
  };

  return (
    <Group name="magic-fill-group" className="magic-fill-composite">
      {/* Visual feedback shape with purple gradient */}
      <Shape
        x={x}
        y={y}
        sceneFunc={(context, shape) => {
          context.clearRect(0, 0, dimensions.width, dimensions.height);

          finalizedLines.forEach((line) => {
            context.save();

            if (line.tool === "rectangle" || line.tool === "lasso") {
              drawPath(context, line);
              
              // Create gradient for visual feedback
              const xs = line.points.filter((_, i) => i % 2 === 0);
              const ys = line.points.filter((_, i) => i % 2 === 1);
              const minX = Math.min(...xs);
              const minY = Math.min(...ys);
              const maxX = Math.max(...xs);
              const maxY = Math.max(...ys);
              const gradient = context.createLinearGradient(minX, minY, maxX, maxY);
              gradient.addColorStop(0, "#8B5CF6");
              gradient.addColorStop(1, "#4F46E5");
              context.fillStyle = gradient;
              context.globalAlpha = 0.5;
              context.fill();
            } else {
              drawPath(context, line);
              
              context.lineWidth = line.strokeWidth;
              context.lineCap = "round";
              context.lineJoin = "round";
              
              if (line.tool === "eraser") {
                context.globalCompositeOperation = "destination-out";
                context.strokeStyle = "rgba(0,0,0,1)";
              } else {
                context.globalCompositeOperation = "source-over";
                // Create gradient for brush strokes
                const gradient = context.createLinearGradient(
                  line.points[0],
                  line.points[1],
                  line.points[line.points.length - 2],
                  line.points[line.points.length - 1]
                );
                gradient.addColorStop(0, "#8B5CF6");
                gradient.addColorStop(1, "#4F46E5");
                context.strokeStyle = gradient;
                context.globalAlpha = 0.5;
              }
              
              context.stroke();
            }
            
            context.restore();
          });
        }}
        fill="transparent"
        name="magic-fill-visual"
        className="magic-fill-visual"
        listening={false}
      />

      {/* Pure black and white mask shape */}
      <Shape
        x={x}
        y={y}
        opacity={0}  // Hide this shape as it's only for the mask
        sceneFunc={(context, shape) => {
          // Start with a white canvas (unmasked area)
          context.fillStyle = "white";
          context.fillRect(0, 0, dimensions.width, dimensions.height);

          finalizedLines.forEach((line) => {
            context.save();
            
            // Set up common properties for all strokes
            context.fillStyle = "black";
            context.strokeStyle = "black";
            context.lineWidth = line.strokeWidth;
            context.lineCap = "round";
            context.lineJoin = "round";

            // Handle eraser tool
            if (line.tool === "eraser") {
              context.globalCompositeOperation = "destination-out";
            } else {
              context.globalCompositeOperation = "source-over";
            }

            // Draw the path
            drawPath(context, line);

            // Fill or stroke based on tool
            if (line.tool === "rectangle" || line.tool === "lasso") {
              context.fill();
            } else {
              context.stroke();
            }
            
            context.restore();
          });
        }}
        name="magic-fill-mask"
        className="magic-fill-mask"
        listening={false}
      />
    </Group>
  );
};

export default MagicFillCompositeLayer;
