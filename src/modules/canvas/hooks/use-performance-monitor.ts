import { useEffect, useRef, useCallback } from 'react';

interface PerformanceMetrics {
  memoryUsage: number;
  renderTime: number;
  frameRate: number;
  canvasSize: { width: number; height: number };
}

interface PerformanceMonitorOptions {
  onPerformanceWarning?: (metrics: PerformanceMetrics) => void;
  memoryThreshold?: number; // MB
  frameRateThreshold?: number; // FPS
  enableLogging?: boolean;
}

/**
 * Hook for monitoring canvas performance and memory usage
 */
export const usePerformanceMonitor = (options: PerformanceMonitorOptions = {}) => {
  const {
    onPerformanceWarning,
    memoryThreshold = 100, // 100MB
    frameRateThreshold = 30, // 30 FPS
    enableLogging = false
  } = options;

  const frameCountRef = useRef(0);
  const lastTimeRef = useRef(performance.now());
  const renderTimesRef = useRef<number[]>([]);
  const performanceIntervalRef = useRef<NodeJS.Timeout>();

  /**
   * Get current memory usage estimate
   */
  const getMemoryUsage = useCallback((): number => {
    if ('memory' in performance) {
      // Chrome/Edge specific
      const memory = (performance as any).memory;
      return memory.usedJSHeapSize / 1024 / 1024; // Convert to MB
    }
    
    // Fallback estimation based on canvas elements
    const canvases = document.querySelectorAll('canvas');
    let estimatedMemory = 0;
    
    canvases.forEach(canvas => {
      const pixels = canvas.width * canvas.height;
      estimatedMemory += pixels * 4; // 4 bytes per pixel (RGBA)
    });
    
    return estimatedMemory / 1024 / 1024; // Convert to MB
  }, []);

  /**
   * Calculate current frame rate
   */
  const calculateFrameRate = useCallback((): number => {
    const now = performance.now();
    const deltaTime = now - lastTimeRef.current;
    
    if (deltaTime >= 1000) { // Calculate every second
      const fps = (frameCountRef.current * 1000) / deltaTime;
      frameCountRef.current = 0;
      lastTimeRef.current = now;
      return fps;
    }
    
    frameCountRef.current++;
    return 0; // Return 0 if not ready to calculate
  }, []);

  /**
   * Record render time
   */
  const recordRenderTime = useCallback((renderTime: number) => {
    renderTimesRef.current.push(renderTime);
    
    // Keep only last 60 render times (for averaging)
    if (renderTimesRef.current.length > 60) {
      renderTimesRef.current.shift();
    }
  }, []);

  /**
   * Get average render time
   */
  const getAverageRenderTime = useCallback((): number => {
    if (renderTimesRef.current.length === 0) return 0;
    
    const sum = renderTimesRef.current.reduce((a, b) => a + b, 0);
    return sum / renderTimesRef.current.length;
  }, []);

  /**
   * Get current performance metrics
   */
  const getPerformanceMetrics = useCallback((): PerformanceMetrics => {
    const canvases = document.querySelectorAll('canvas');
    const mainCanvas = canvases[0]; // Assume first canvas is main
    
    return {
      memoryUsage: getMemoryUsage(),
      renderTime: getAverageRenderTime(),
      frameRate: calculateFrameRate(),
      canvasSize: {
        width: mainCanvas?.width || 0,
        height: mainCanvas?.height || 0
      }
    };
  }, [getMemoryUsage, getAverageRenderTime, calculateFrameRate]);

  /**
   * Check if performance is degraded
   */
  const checkPerformance = useCallback(() => {
    const metrics = getPerformanceMetrics();
    
    const isMemoryHigh = metrics.memoryUsage > memoryThreshold;
    const isFrameRateLow = metrics.frameRate > 0 && metrics.frameRate < frameRateThreshold;
    const isRenderTimeSlow = metrics.renderTime > 16.67; // 60 FPS = 16.67ms per frame
    
    if (enableLogging) {
      console.log('Performance Metrics:', {
        memory: `${metrics.memoryUsage.toFixed(2)}MB`,
        frameRate: `${metrics.frameRate.toFixed(1)}FPS`,
        renderTime: `${metrics.renderTime.toFixed(2)}ms`,
        canvasSize: `${metrics.canvasSize.width}x${metrics.canvasSize.height}`
      });
    }
    
    if ((isMemoryHigh || isFrameRateLow || isRenderTimeSlow) && onPerformanceWarning) {
      onPerformanceWarning(metrics);
    }
  }, [getPerformanceMetrics, memoryThreshold, frameRateThreshold, enableLogging, onPerformanceWarning]);

  /**
   * Start performance monitoring
   */
  const startMonitoring = useCallback(() => {
    if (performanceIntervalRef.current) {
      clearInterval(performanceIntervalRef.current);
    }
    
    performanceIntervalRef.current = setInterval(checkPerformance, 2000); // Check every 2 seconds
  }, [checkPerformance]);

  /**
   * Stop performance monitoring
   */
  const stopMonitoring = useCallback(() => {
    if (performanceIntervalRef.current) {
      clearInterval(performanceIntervalRef.current);
      performanceIntervalRef.current = undefined;
    }
  }, []);

  /**
   * Optimize canvas performance
   */
  const optimizePerformance = useCallback(() => {
    // Force garbage collection if available
    if ('gc' in window && typeof (window as any).gc === 'function') {
      (window as any).gc();
    }
    
    // Clear render time history
    renderTimesRef.current = [];
    
    // Reset frame counting
    frameCountRef.current = 0;
    lastTimeRef.current = performance.now();
    
    if (enableLogging) {
      console.log('Performance optimization triggered');
    }
  }, [enableLogging]);

  /**
   * Get performance recommendations
   */
  const getRecommendations = useCallback((): string[] => {
    const metrics = getPerformanceMetrics();
    const recommendations: string[] = [];
    
    if (metrics.memoryUsage > memoryThreshold) {
      recommendations.push('Consider reducing image resolution or clearing unused images');
    }
    
    if (metrics.frameRate > 0 && metrics.frameRate < frameRateThreshold) {
      recommendations.push('Frame rate is low - consider reducing canvas complexity');
    }
    
    if (metrics.renderTime > 16.67) {
      recommendations.push('Render time is slow - consider optimizing drawing operations');
    }
    
    if (metrics.canvasSize.width * metrics.canvasSize.height > 4000000) { // 4MP
      recommendations.push('Canvas size is large - consider using lower resolution for better performance');
    }
    
    return recommendations;
  }, [getPerformanceMetrics, memoryThreshold, frameRateThreshold]);

  // Start monitoring on mount
  useEffect(() => {
    startMonitoring();
    return stopMonitoring;
  }, [startMonitoring, stopMonitoring]);

  return {
    getPerformanceMetrics,
    recordRenderTime,
    startMonitoring,
    stopMonitoring,
    optimizePerformance,
    getRecommendations,
    checkPerformance
  };
};
