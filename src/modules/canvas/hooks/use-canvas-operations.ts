import React, { useState, useRef, useCallback } from 'react';
import Konva from 'konva';
import { 
  CanvasLine, 
  CanvasMode, 
  CanvasRect,
  CanvasPosition,
  MaskToolMode
} from '../types/canvas';
import useCanvasStore from '../store/canvas-store';
import { getRelativePointerPosition } from '../utils/canvas-utils';
import { useCanvasExport } from './use-canvas-export';

/**
 * Normalize a rectangle to ensure positive width and height
 */
const normalizeRect = (rect: CanvasRect): CanvasRect => {
  let { x, y, width, height } = rect;
  
  if (width < 0) {
    x += width;
    width = Math.abs(width);
  }
  
  if (height < 0) {
    y += height;
    height = Math.abs(height);
  }
  
  return { x, y, width, height };
};

/**
 * Hook for handling canvas drawing operations, selection, and mouse events
 */
export const useCanvasOperations = () => {
  const store = useCanvasStore();
  const [isDrawing, setIsDrawing] = useState(false);
  const [localSelectionRect, setLocalSelectionRect] = useState<CanvasRect | null>(null);
  const [activeLine, setActiveLine] = useState<CanvasLine | null>(null);
  const startPointRef = useRef<CanvasPosition | null>(null);
  const { captureSelectionAreaImage } = useCanvasExport();

  /**
   * Handle mouse down events for drawing and selection
   */
  const handleMouseDown = useCallback((e: Konva.KonvaEventObject<MouseEvent>, stageRef: React.RefObject<Konva.Stage>) => {
    const stage = e.target.getStage();
    if (!stage) return;
    const pos = getRelativePointerPosition(stage);
    if (!pos) return;

    if (store.mode === CanvasMode.MagicFill) {
      const tool = store.maskTool;
      const basePos = pos;
      setIsDrawing(true);
      startPointRef.current = basePos;
      const strokeWidth =
        tool === "brush" || tool === "eraser" ? store.brushSize : 2;
      const initialPoints =
        tool === "rectangle"
          ? [basePos.x, basePos.y, basePos.x, basePos.y]
          : [basePos.x, basePos.y];
      const newLine: CanvasLine = {
        tool:
          tool === "eraser"
            ? "eraser"
            : tool === "rectangle"
            ? "rectangle"
            : tool === "lasso"
            ? "lasso"
            : "brush",
        points: initialPoints,
        strokeWidth,
      };
      setActiveLine(newLine);
      store.startNewLine(newLine);
    } else if (store.mode === CanvasMode.SelectArea) {
      setIsDrawing(true);
      setLocalSelectionRect({ x: pos.x, y: pos.y, width: 0, height: 0 });
    }
  }, [store]);

  /**
   * Handle mouse move events for drawing and selection
   */
  const handleMouseMove = useCallback((e: Konva.KonvaEventObject<MouseEvent>, stageRef: React.RefObject<Konva.Stage>) => {
    const stage = e.target.getStage();
    if (!stage) return;
    const pos = getRelativePointerPosition(stage);
    if (!pos) return;

    if (
      store.mode === CanvasMode.MagicFill &&
      isDrawing &&
      startPointRef.current
    ) {
      const tool = store.maskTool;
      let currentLine = { ...store.lines[store.lines.length - 1] };
      if (tool === "rectangle") {
        const x1 = startPointRef.current.x;
        const y1 = startPointRef.current.y;
        const x2 = pos.x;
        const y2 = pos.y;
        currentLine.points = [
          Math.min(x1, x2),
          Math.min(y1, y2),
          Math.max(x1, x2),
          Math.min(y1, y2),
          Math.max(x1, x2),
          Math.max(y1, y2),
          Math.min(x1, x2),
          Math.max(y1, y2),
        ];
      } else if (tool === "lasso") {
        currentLine.points = [...currentLine.points, pos.x, pos.y];
      } else {
        currentLine.points = [...currentLine.points, pos.x, pos.y];
      }
      setActiveLine(currentLine);
      store.updateActiveLine(currentLine);
    } else if (store.mode === CanvasMode.SelectArea && localSelectionRect) {
      const newWidth = pos.x - localSelectionRect.x;
      const newHeight = pos.y - localSelectionRect.y;
      setLocalSelectionRect({
        ...localSelectionRect,
        width: newWidth,
        height: newHeight,
      });
    }
  }, [isDrawing, localSelectionRect, store]);

  /**
   * Handle mouse up events for drawing and selection
   */
  const handleMouseUp = useCallback((stageRef: React.RefObject<Konva.Stage>) => {
    if (store.mode === CanvasMode.MagicFill && isDrawing) {
      const tool = store.maskTool;
      if (tool === "rectangle" && stageRef.current && startPointRef.current) {
        const finalPos = getRelativePointerPosition(stageRef.current);
        if (finalPos) {
          const x1 = startPointRef.current.x;
          const y1 = startPointRef.current.y;
          const x2 = finalPos.x;
          const y2 = finalPos.y;
          let currentLine = { ...store.lines[store.lines.length - 1] };
          currentLine.points = [
            Math.min(x1, x2),
            Math.min(y1, y2),
            Math.max(x1, x2),
            Math.min(y1, y2),
            Math.max(x1, x2),
            Math.max(y1, y2),
            Math.min(x1, x2),
            Math.max(y1, y2),
          ];
          setActiveLine(currentLine);
          store.updateActiveLine(currentLine);
        }
      } else if (tool === "lasso") {
        let currentLine = { ...store.lines[store.lines.length - 1] };
        if (currentLine.points.length >= 4) {
          currentLine.points.push(currentLine.points[0], currentLine.points[1]);
          setActiveLine(currentLine);
          store.updateActiveLine(currentLine);
        }
      }
      setIsDrawing(false);
      startPointRef.current = null;
    } else if (store.mode === CanvasMode.SelectArea) {
      setIsDrawing(false);
      if (localSelectionRect) {
        // Normalize selection rectangle (handle negative width/height)
        const normalizedRect = normalizeRect(localSelectionRect);
        
        // Enforce minimum size for selection rectangle
        if (normalizedRect.width < 50 || normalizedRect.height < 50) {
          // Too small, use minimum dimensions with original position
          store.setSelectionArea({
            x: normalizedRect.x,
            y: normalizedRect.y,
            width: Math.max(50, normalizedRect.width),
            height: Math.max(50, normalizedRect.height),
          });
        } else {
          store.setSelectionArea(normalizedRect);
        }
      }
    }
  }, [isDrawing, localSelectionRect, store]);

  /**
   * Handle mask tool changes for magic fill
   */
  const handleMaskToolChange = useCallback((tool: MaskToolMode) => {
    store.setMaskTool(tool);
  }, [store]);

  /**
   * Cancel current magic fill operation
   */
  const handleCancelMagicFill = useCallback(() => {
    store.setMode(CanvasMode.Move);
    store.setMagicFillAreaSelection(false);
    store.clearLines();
    store.setSelectionArea(null);
  }, [store]);

  /**
   * Proceed with magic fill after drawing mask
   */
  const handleProceedMagicFill = useCallback(() => {
    // Create or update the selection area based on drawn lines
    if (store.lines && store.lines.length > 0) {
      // Compute selection area based on lines with some padding
      let minX = Infinity,
        minY = Infinity,
        maxX = -Infinity,
        maxY = -Infinity;
      store.lines.forEach((line) => {
        for (let i = 0; i < line.points.length; i += 2) {
          const x = line.points[i],
            y = line.points[i + 1];
          minX = Math.min(minX, x);
          minY = Math.min(minY, y);
          maxX = Math.max(maxX, x);
          maxY = Math.max(maxY, y);
        }
      });
      
      // Add some padding around the mask
      const padding = 20;
      store.setSelectionArea({
        x: minX - padding,
        y: minY - padding,
        width: (maxX - minX) + (padding * 2),
        height: (maxY - minY) + (padding * 2),
      });
    }
    
    // Set the magicFillAreaSelection to true to show the prompt input
    store.setMagicFillAreaSelection(true);
  }, [store]);

  /**
   * Prepare and capture the current selection area
   */
  const captureSelection = useCallback(async (stageRef: React.RefObject<Konva.Stage>) => {
    const selection = store.selectionArea;
    if (!selection || !stageRef.current) {
      console.error('No selection area available to capture');
      return null;
    }
    
    try {
      // Temporarily hide UI elements during capture
      const allLayers = stageRef.current.getLayers();
      const uiElements: Map<Konva.Node, boolean> = new Map();
      
      // First hide DOM elements
      const domElements = document.querySelectorAll(
        '.loading-status, .status-indicator, .generating-indicator, .magic-fill-status, .placeholder, .task-indicator'
      );
      const originalDomStyles: {element: Element, style: string | null}[] = [];
      domElements.forEach(element => {
        originalDomStyles.push({
          element,
          style: element.getAttribute('style')
        });
        element.setAttribute('style', 'visibility: hidden; opacity: 0; display: none;');
      });
      
      // Find the placeholder layer if it exists
      let placeholderLayer = null;
      allLayers.forEach((layer, i) => {
        // Check if this is a placeholder layer
        const hasPlaceholders = layer.find('.placeholder, .task-placeholder').length > 0 || 
                               (layer.children && layer.children.some(child => 
                                 child.name && (
                                   child.name().includes('placeholder') || 
                                   child.name().includes('task')
                                 )
                               ));
        
        if (hasPlaceholders) {
          placeholderLayer = layer;
        }
      });
      
      // Hide specific UI elements and placeholders in all layers
      allLayers.forEach(layer => {
        // Use expanded selector for better element coverage
        layer.find(
          '.selection-rect, Transformer, Tag, Label, .ui-control, Text, .placeholder, ' + 
          '.loading-overlay, .status-text, .task-placeholder, .generating-indicator, ' +
          '.magic-fill-placeholder, .task-indicator'
        ).forEach(node => {
          uiElements.set(node, node.visible());
          node.visible(false);
        });
        
        // Look for grouped placeholders
        layer.find('Group').forEach(node => {
          // Check if this is a placeholder by properties
          if (node.attrs && node.attrs.isPlaceholder) {
            uiElements.set(node, node.visible());
            node.visible(false);
          }
          
          // Check by name
          const name = node.name();
          if (name && (
            name.includes('placeholder') || 
            name.includes('task') ||
            name.includes('generating')
          )) {
            uiElements.set(node, node.visible());
            node.visible(false);
          }
        });
        
        // Hide any nodes with UI-related names
        layer.find('*').forEach(node => {
          const name = node.name();
          if (name && (
            name.includes('ui') || 
            name.includes('interface') || 
            name.includes('status') || 
            name.includes('placeholder') ||
            name.includes('loading') ||
            name.includes('overlay') ||
            name.includes('task') ||
            name.includes('generating') ||
            name.includes('magic-fill-task')
          )) {
            uiElements.set(node, node.visible());
            node.visible(false);
          }
        });
      });
      
      // Let the stage update
      stageRef.current.batchDraw();
      await new Promise(resolve => setTimeout(resolve, 50));
      
      // Use our enhanced capture method
      const imageDataUrl = await captureSelectionAreaImage(selection, stageRef);
      
      // Restore visibility of UI elements
      uiElements.forEach((originalVisibility, node) => {
        node.visible(originalVisibility);
      });
      
      // Restore DOM elements
      originalDomStyles.forEach(({element, style}) => {
        if (style) {
          element.setAttribute('style', style);
        } else {
          element.removeAttribute('style');
        }
      });
      
      stageRef.current.batchDraw();
      
      return imageDataUrl;
    } catch (error) {
      console.error('Error capturing selection:', error);
      return null;
    }
  }, [store.selectionArea, captureSelectionAreaImage]);

  /**
   * Handle clicks on the stage (for deselection)
   */
  const handleStageClick = useCallback((
    e: Konva.KonvaEventObject<MouseEvent>
  ) => {
    // Only proceed with deselection in Move mode
    if (store.mode === CanvasMode.Move) {
      const target = e.target;
      const isStage = target === e.currentTarget;
      const isBackground = target instanceof Konva.Rect && target.width() > 1000 && target.height() > 1000;
      
      if (isStage || isBackground) {
        // Clear all selections
        store.setTransformConfig({
          selectedImageIndex: null,
          isDragging: false,
          isResizing: false,
          isRotating: false
        });
      }
    }
  }, [store.mode, store]);

  /**
   * Helper to render active stroke preview
   */
  const renderActiveStrokePreview = useCallback(() => {
    const currentActiveLine = isDrawing ? store.lines[store.lines.length - 1] || null : null;
    
    if (!currentActiveLine) return null;
    
    if (
      currentActiveLine.tool === "rectangle" ||
      currentActiveLine.tool === "lasso"
    ) {
      const xs = currentActiveLine.points.filter((_, i) => i % 2 === 0);
      const ys = currentActiveLine.points.filter((_, i) => i % 2 === 1);
      const minX = Math.min(...xs);
      const minY = Math.min(...ys);
      const maxX = Math.max(...xs);
      const maxY = Math.max(...ys);
      
      return {
        type: 'shape',
        points: currentActiveLine.points,
        gradient: {
          startPoint: { x: minX, y: minY },
          endPoint: { x: maxX, y: maxY },
          colorStops: [0, "#8B5CF6", 1, "#4F46E5"],
        }
      };
    } else {
      return {
        type: 'line',
        points: currentActiveLine.points,
        strokeWidth: currentActiveLine.strokeWidth,
        stroke: currentActiveLine.tool === "brush" ? "#8B5CF6" : "rgba(0,0,0,1)",
        opacity: currentActiveLine.tool === "eraser" ? 0.5 : 1
      };
    }
  }, [isDrawing, store.lines]);

  /**
   * Reset canvas operations state
   */
  const resetState = useCallback(() => {
    setIsDrawing(false);
    setLocalSelectionRect(null);
    setActiveLine(null);
    startPointRef.current = null;
  }, []);

  return {
    isDrawing,
    localSelectionRect,
    activeLine,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    handleStageClick,
    handleMaskToolChange,
    handleCancelMagicFill,
    handleProceedMagicFill,
    renderActiveStrokePreview,
    resetState,
    setIsDrawing,
    setLocalSelectionRect,
    setActiveLine,
    captureSelection
  };
}; 