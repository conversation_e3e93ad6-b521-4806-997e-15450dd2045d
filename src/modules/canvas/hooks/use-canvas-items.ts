import { useState, useCallback, useRef } from 'react';
import Konva from 'konva';
import useCanvasStore from '../store/canvas-store';
import { CanvasMode, CanvasPosition, GeneratedImage } from '../types/canvas';
import { useToast } from '@/modules/ui/use-toast';

/**
 * Hook for managing canvas item selection and transformations
 */
export const useCanvasItems = () => {
  const store = useCanvasStore();
  const { toast } = useToast();
  const [selectedPlaceholderId, setSelectedPlaceholderId] = useState<string | null>(null);
  // Add a ref to track processed image IDs to prevent duplicates
  const processedImageIds = useRef<Set<string>>(new Set());
  
  /**
   * Handle clicking on generated images
   */
  const handleImageClick = useCallback((index: number, e: Konva.KonvaEventObject<any>) => {
    if (store.mode === CanvasMode.Move) {
      e.cancelBubble = true;
      
      // Clear placeholder selection when selecting an image
      setSelectedPlaceholderId(null);
      
      // Update the transform config to select the image
      store.setTransformConfig({
        selectedImageIndex: index,
        isDragging: false,
        isResizing: false,
        isRotating: false
      });
    }
  }, [store, setSelectedPlaceholderId]);

  /**
   * Handle dragging images
   */
  const handleImageDragEnd = useCallback((
    index: number,
    e: Konva.KonvaEventObject<any>
  ) => {
    const newPosition: CanvasPosition = { x: e.target.x(), y: e.target.y() };
    
    // Use the store method to update image position
    store.setImagePosition(index, newPosition);
  }, [store]);

  /**
   * Handle transformations of images (resize, rotate)
   */
  const handleTransformEnd = useCallback((
    index: number,
    e: Konva.KonvaEventObject<any>
  ) => {
    // Get the current image from the store
    const image = store.generatedImages[index];
    if (!image) return;
    
    try {
      // Extract transformation values
      const width = typeof e.target.width === 'number' ? Math.round(e.target.width) : image.width;
      const height = typeof e.target.height === 'number' ? Math.round(e.target.height) : image.height;
      const x = typeof e.target.x === 'number' ? e.target.x : image.position.x;
      const y = typeof e.target.y === 'number' ? e.target.y : image.position.y;
      const rotation = typeof e.target.rotation === 'number' ? e.target.rotation : image.rotation;
      
      // Get the active anchor from the node's attributes
      // This is set by the TransformerWrapper during transform
      let activeAnchor: string | null = null;
      
      if (e.target && typeof e.target.getAttr === 'function') {
        activeAnchor = e.target.getAttr('data-active-anchor');
        console.log('Got activeAnchor from node attributes:', activeAnchor);
      } else if (e.target && e.target.attrs && e.target.attrs['data-active-anchor']) {
        activeAnchor = e.target.attrs['data-active-anchor'];
        console.log('Got activeAnchor from attrs object:', activeAnchor);
      }
      
      console.log(`Transform end with anchor: ${activeAnchor}, width: ${width}, height: ${height}`);
      
      // We'll respect the crop configuration that was updated during the transform
      // If crop configuration exists, we'll let it handle aspect adjustments
      // Otherwise, we'll manually calculate dimensions as before
      
      // Create the update object with all required properties
      const updates: Partial<GeneratedImage> = {
        position: { x, y },
        width,
        height,
        rotation,
        isSelected: true,
      };
      
      // If the image has a cropConfig from transform operations, make sure to include it
      if (image.cropConfig) {
        console.log('Preserving crop configuration from transform:', image.cropConfig);
        updates.cropConfig = image.cropConfig;
      }
      
      // Update the image using the store's method
      store.updateImageTransform(index, updates);
    } catch (error) {
      console.error('Error in handleTransformEnd:', error);
    }
  }, [store]);

  /**
   * Handle updating an image's properties
   */
  const handleImageUpdate = useCallback((index: number, updatedImage: GeneratedImage) => {
    console.log(`Updating image at index ${index}:`, updatedImage);
    
    // Check if we have crop configuration in the update
    if (updatedImage.cropConfig) {
      console.log('Image update includes crop config:', updatedImage.cropConfig);
    }
    
    // Update the image in the store
    store.updateImageTransform(index, updatedImage);
  }, [store]);

  /**
   * Add a generated image to the canvas
   */
  const addGeneratedImage = useCallback((newImage: GeneratedImage) => {
    // Generate a unique ID if one is not provided
    const imageId = newImage.id || `gen-${Date.now()}`;
    
    // Skip if we've already processed this image ID
    if (processedImageIds.current.has(imageId)) {
      return;
    }
    
    // Mark this image as processed
    processedImageIds.current.add(imageId);
    
    // Add the image to the store
    store.addGeneratedImage(newImage);
    
    // Set this image as selected after a delay
    setTimeout(() => {
      const newIndex = store.generatedImages.findIndex(img => img.id === imageId);
      if (newIndex !== -1) {
        store.setTransformConfig({
          selectedImageIndex: newIndex,
          isDragging: false,
          isResizing: false,
          isRotating: false
        });
      }
    }, 100);
  }, [store]);

  /**
   * Handle placeholder item click
   */
  const handlePlaceholderClick = useCallback((taskId: string) => {
    // Deselect any selected image
    store.setTransformConfig({
      selectedImageIndex: null,
      isDragging: false,
      isResizing: false,
      isRotating: false
    });
    
    // Select the placeholder
    setSelectedPlaceholderId(taskId);
  }, [store, setSelectedPlaceholderId]);

  /**
   * Deselect all items
   */
  const deselectAll = useCallback(() => {
    setSelectedPlaceholderId(null);
    store.setTransformConfig({
      selectedImageIndex: null,
      isDragging: false,
      isResizing: false,
      isRotating: false
    });
  }, [store, setSelectedPlaceholderId]);

  /**
   * Delete the currently selected image
   */
  const deleteSelectedImage = useCallback(() => {
    // Get the currently selected image index
    const selectedIndex = store.transformConfig.selectedImageIndex;
    
    // Check if an image is selected
    if (selectedIndex === null || selectedIndex < 0) {
      // No image selected or input image selected (index -1)
      return;
    }
    
    // Remove the image from generated images array
    const updatedImages = [...store.generatedImages];
    const deletedImage = updatedImages[selectedIndex];
    
    // Only proceed if we found the image
    if (deletedImage) {
      updatedImages.splice(selectedIndex, 1);
      
      // Use setGeneratedImages instead of direct state updates
      // This ensures our deletion is properly tracked in the history stack
      store.setGeneratedImages(updatedImages);
      
      // Reset selection
      store.setTransformConfig({
        ...store.transformConfig,
        selectedImageIndex: null,
      });
      
      // Show confirmation toast
      toast({
        title: "Image deleted",
        description: "The selected image was removed from the canvas",
      });
    }
  }, [store, toast]);

  return {
    selectedPlaceholderId,
    setSelectedPlaceholderId,
    handleImageClick,
    handleImageDragEnd,
    handleTransformEnd,
    handleImageUpdate,
    handlePlaceholderClick,
    deselectAll,
    addGeneratedImage,
    deleteSelectedImage
  };
}; 