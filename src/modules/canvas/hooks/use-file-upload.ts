import { useRef, useCallback } from 'react';
import { findAvailableCanvasPosition } from '../utils/canvas-utils';
import useCanvasStore from '../store/canvas-store';
import { useToast } from '@/modules/ui/use-toast';
import Konva from 'konva';
import { CanvasSize } from '../types/canvas';

/**
 * Hook for handling file uploads to the canvas
 */
export const useFileUpload = () => {
  const store = useCanvasStore();
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  /**
   * Trigger file input click
   */
  const triggerFileUpload = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  /**
   * Handle file input change
   */
  const handleFileInput = useCallback((
    e: React.ChangeEvent<HTMLInputElement>, 
    stageRef: React.RefObject<Konva.Stage>,
    dimensions: CanvasSize
  ) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    const reader = new FileReader();
    reader.onload = () => {
      const dataURL = reader.result;
      if (typeof dataURL !== "string") return;
      
      const img = new Image();
      img.onload = () => {
        // Get all occupied spaces on the canvas
        const occupiedSpaces = [
          // Add generated images
          ...store.generatedImages.map(img => ({
            x: img.position.x,
            y: img.position.y,
            width: img.width,
            height: img.height
          })),
          // Add generation tasks/placeholders
          ...store.generationTasks.map(task => task.selectionArea)
        ];
        
        // Find a good position for the uploaded image
        const position = findAvailableCanvasPosition(
          img.width, 
          img.height, 
          stageRef.current, 
          dimensions, 
          occupiedSpaces
        );
        
        // Add the image as a generated image only (prevent duplication)
        store.addGeneratedImage({
          id: `uploaded-${crypto.randomUUID()}`,
          image: img,
          src: dataURL,
          width: img.width,
          height: img.height,
          position,
          rotation: 0,
          scaleX: 1,
          scaleY: 1,
          isNew: true,
          isSelected: false,
          isUploaded: true
        });
        
        // Toast to confirm upload
        toast({
          title: "Image uploaded",
          description: "Your image has been uploaded and is ready for editing",
        });
      };
      
      img.src = dataURL;
    };
    
    reader.readAsDataURL(file);
    
    // Clear the file input value so the same file can be re-uploaded
    e.target.value = "";
  }, [store, toast]);

  return {
    fileInputRef,
    triggerFileUpload,
    handleFileInput
  };
}; 