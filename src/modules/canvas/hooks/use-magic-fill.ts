import { useCallback } from 'react';
import Kon<PERSON> from 'konva';
import { CanvasMode, CanvasRect, MaskToolMode, GeneratedImage } from '../types/canvas';
import useCanvasStore from '../store/canvas-store';
import { useToast } from '@/modules/ui/use-toast';
import { editImage } from '../actions/edit-image';
import { computeAspectRatio, findClosestAspectRatio, isValidResolution, formatResolution, dataURLtoBlob } from '../utils/canvas-utils';
import { useCanvasExport } from './use-canvas-export';
import { handleImageUpload } from '../actions/upload-image';

/**
 * Hook for handling magic fill operations
 */
export const useMagicFill = (
  userId: string,
  addGeneratedImageFn?: (image: GeneratedImage) => void
) => {
  const store = useCanvasStore();
  const { toast } = useToast();
  const { captureSelectionAreaImage } = useCanvasExport();

  /**
   * Handle magic fill operation
   */
  const handleMagicFill = useCallback(async (stageRef: React.RefObject<Konva.Stage>) => {
    let selection = store.selectionArea;
    
    // Validation: we need a user ID to proceed
    if (!userId) {
      toast({
        title: "Authentication required",
        description: "Please sign in to use magic fill",
        variant: "destructive",
      });
      return;
    }
    
    // Validate the selection area
    if (!selection) {
      toast({
        title: "Selection required",
        description: "Please select an area to apply magic fill",
        variant: "destructive",
      });
      return;
    }
    
    if (!store.prompt || store.prompt.trim() === '') {
      toast({
        title: "Prompt required",
        description: "Please enter a prompt describing what to generate",
        variant: "destructive",
      });
      return;
    }

    // Create a unique task ID for this magic fill operation
    const taskId = `magic-fill-${crypto.randomUUID()}`;
    
    try {
      // IMPORTANT: First capture and upload the selection area image BEFORE adding the task
      // This prevents the placeholder from appearing in the screenshot
      console.log("Magic Fill - Capturing selection area image...");
      
      try {
        // Capture the selection area with our improved method
        // This handles all visibility management automatically
        const dataUrl = await captureSelectionAreaImage(selection, stageRef);
        
        // Convert to blob for upload
        const blob = dataURLtoBlob(dataUrl);
        
        // Upload the captured image
        const formData = new FormData();
        formData.append("file", blob, "magic-fill-input.png");
        
        const uploadResult = await handleImageUpload(formData);
        
        if ("error" in uploadResult) {
          throw new Error(`Upload failed: ${uploadResult.error.message}`);
        }
        
        const uploadedImageUrl = uploadResult.url;
        console.log('Canvas area captured and uploaded successfully:', uploadedImageUrl);
        
        // NOW add the magic fill task to the store AFTER the image has been captured and uploaded
        store.addGenerationTask({
          id: taskId,
          selectionArea: {...selection}, // Clone selection to avoid reference issues
          prompt: store.prompt,
          status: 'pending',
          type: 'magic-fill'
        });
        
        // Update task status to generating
        store.updateGenerationTask(taskId, { status: 'generating' });
        
        // Proceed with magic fill processing
        await processMagicFill(uploadedImageUrl, selection, taskId);
      } catch (error) {
        console.error("Error in magic fill capture:", error);
        // Only add the task to the store with an error status if the capture fails
        store.addGenerationTask({
          id: taskId,
          selectionArea: {...selection},
          prompt: store.prompt,
          status: 'error',
          type: 'magic-fill'
        });
        toast({
          title: "Magic Fill Failed",
          description: error instanceof Error ? error.message : "Failed to capture the selection area",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Error in magic fill:", error);
      // Only add the task with error status if it doesn't exist yet
      if (!store.generationTasks.find(task => task.id === taskId)) {
        store.addGenerationTask({
          id: taskId,
          selectionArea: {...selection},
          prompt: store.prompt,
          status: 'error',
          type: 'magic-fill'
        });
      } else {
        store.updateGenerationTask(taskId, { status: 'error' });
      }
      toast({
        title: "Magic Fill Failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive"
      });
    }
  }, [userId, store, toast, captureSelectionAreaImage]);

  /**
   * Process the magic fill operation with the uploaded image
   */
  const processMagicFill = async (uploadedImageUrl: string, selection: CanvasRect, taskId: string) => {
    try {
      // Determine the aspect ratio from the selection dimensions
      const selectionAspectRatio = computeAspectRatio(selection.width, selection.height);
      console.log("Magic Fill - Selection area aspect ratio:", selectionAspectRatio);
      
      // Find the closest standard aspect ratio to the selection
      const validAspectRatios = ["1:1", "16:9", "9:16", "4:3", "3:4", "3:2", "2:3", "16:10", "10:16", "3:1", "1:3"];
      let finalAspectRatio;
      if (validAspectRatios.includes(selectionAspectRatio)) {
        finalAspectRatio = selectionAspectRatio;
      } else {
        finalAspectRatio = findClosestAspectRatio(selection.width, selection.height);
      }
      console.log("Magic Fill - Final aspect ratio for API call:", finalAspectRatio);
      
      // Check if the resolution string matches one of the predefined values
      const resolutionString = formatResolution(selection.width, selection.height);
      const finalResolution = isValidResolution(resolutionString) 
        ? resolutionString 
        : "None";
      console.log("Magic Fill - Resolution for API call:", finalResolution);
      
      // Keep track of the original aspect ratio to restore after API call
      const originalAspectRatio = store.aspectRatio;
      
      try {
        // Step 2: Now create the mask immediately before calling the API
        // This ensures the mask is created using the most current state
        console.log("Magic Fill - Creating mask image");
        
        // First, download the input image to analyze for transparency
        // We need to load the uploaded image first to ensure dimensions match exactly
        console.log("Magic Fill - Loading uploaded input image for analysis");
        const inputImg = new Image();
        inputImg.crossOrigin = "anonymous";
        inputImg.src = uploadedImageUrl;
        
        // Wait for the input image to load
        await new Promise<void>((resolve, reject) => {
          inputImg.onload = () => resolve();
          inputImg.onerror = () => reject(new Error("Failed to load uploaded input image"));
          // Set a timeout in case the image doesn't load
          setTimeout(() => reject(new Error("Timeout loading input image")), 10000);
        });
        
        console.log("Magic Fill - Input image loaded with dimensions:", {
          width: inputImg.width,
          height: inputImg.height
        });
        
        // Create a mask canvas with EXACTLY the same dimensions as the input image
        const maskCanvas = document.createElement("canvas");
        maskCanvas.width = inputImg.width;
        maskCanvas.height = inputImg.height;
        const maskCtx = maskCanvas.getContext("2d", { willReadFrequently: true });
        
        if (!maskCtx) throw new Error("Failed to get context for mask");
        
        // Step 3: Start with a white background (unmasked areas)
        maskCtx.fillStyle = "white";
        maskCtx.fillRect(0, 0, maskCanvas.width, maskCanvas.height);
        
        // Analyze the input image for transparent areas
        // First create a temporary canvas to hold the input image
        const analyzeCanvas = document.createElement("canvas");
        analyzeCanvas.width = inputImg.width;
        analyzeCanvas.height = inputImg.height;
        const analyzeCtx = analyzeCanvas.getContext("2d", { willReadFrequently: true });
        
        if (!analyzeCtx) throw new Error("Failed to get context for analysis");
        
        // Draw the input image for analysis
        analyzeCtx.drawImage(inputImg, 0, 0);
        
        // Get the pixel data to check for transparency
        const imageData = analyzeCtx.getImageData(0, 0, analyzeCanvas.width, analyzeCanvas.height);
        const data = imageData.data;
        
        // Array to track which pixels are transparent or empty
        const emptyPixels = new Uint8Array(maskCanvas.width * maskCanvas.height);
        
        // Find transparent and empty areas in the input image
        let emptyCount = 0;
        for (let y = 0; y < maskCanvas.height; y++) {
          for (let x = 0; x < maskCanvas.width; x++) {
            const idx = (y * maskCanvas.width + x) * 4;
            // Consider a pixel empty if:
            // 1. It's transparent (alpha < 20)
            // 2. It's nearly white (all RGB values > 240)
            // 3. It's at the edge of the image (for outpainting)
            if (
              data[idx + 3] < 20 || // Transparent
              (data[idx] > 240 && data[idx + 1] > 240 && data[idx + 2] > 240) || // White
              x === 0 || x === maskCanvas.width - 1 || // Left/Right edges
              y === 0 || y === maskCanvas.height - 1    // Top/Bottom edges
            ) {
              emptyPixels[y * maskCanvas.width + x] = 1;
              emptyCount++;
            }
          }
        }
        
        console.log(`Magic Fill - Found ${emptyCount} empty/transparent pixels in input image`);
        
        // Step 5: Now draw the mask from user-drawn lines
        maskCtx.fillStyle = "black";
        maskCtx.strokeStyle = "black";
        maskCtx.lineJoin = "round";
        maskCtx.lineCap = "round";
        
        console.log("Magic Fill - Processing mask lines:", store.lines.length);
        
        // Calculate the scale ratio between the canvas coordinates and the uploaded image
        const scaleX = maskCanvas.width / selection.width;
        const scaleY = maskCanvas.height / selection.height;
        
        console.log("Magic Fill - Coordinate scaling:", { scaleX, scaleY });
        
        // Process each line drawn by the user
        for (const line of store.lines) {
          if (line.points.length < 2) {
            console.log("Magic Fill - Skipping line with insufficient points");
            continue;
          }
          
          console.log(`Magic Fill - Processing line with tool "${line.tool}" and ${line.points.length / 2} points`);
          
          if (line.tool === "rectangle") {
            // For rectangle, fill a solid shape
            maskCtx.beginPath();
            const pts = line.points;
            
            // We need to offset all points by the selection area origin and scale to match the mask canvas
            const x0 = (pts[0] - selection.x) * scaleX;
            const y0 = (pts[1] - selection.y) * scaleY;
            maskCtx.moveTo(x0, y0);
            
            for (let i = 2; i < pts.length; i += 2) {
              const x = (pts[i] - selection.x) * scaleX;
              const y = (pts[i+1] - selection.y) * scaleY;
              maskCtx.lineTo(x, y);
            }
            
            maskCtx.closePath();
            maskCtx.fill();
          } else if (line.tool === "lasso") {
            // For lasso, fill the enclosed shape
            maskCtx.beginPath();
            
            // Apply offset to first point and scale
            const x0 = (line.points[0] - selection.x) * scaleX;
            const y0 = (line.points[1] - selection.y) * scaleY;
            maskCtx.moveTo(x0, y0);
            
            for (let i = 2; i < line.points.length; i += 2) {
              // Apply offset to all points and scale
              const x = (line.points[i] - selection.x) * scaleX;
              const y = (line.points[i + 1] - selection.y) * scaleY;
              maskCtx.lineTo(x, y);
            }
            
            maskCtx.closePath();
            maskCtx.fill();
          } else {
            // For brush strokes, use the line width
            maskCtx.beginPath();
            
            // Apply offset to first point and scale
            const x0 = (line.points[0] - selection.x) * scaleX;
            const y0 = (line.points[1] - selection.y) * scaleY;
            maskCtx.moveTo(x0, y0);
            
            for (let i = 2; i < line.points.length; i += 2) {
              // Apply offset to all points and scale
              const x = (line.points[i] - selection.x) * scaleX;
              const y = (line.points[i + 1] - selection.y) * scaleY;
              maskCtx.lineTo(x, y);
            }
            
            // Scale the stroke width to match the mask canvas
            maskCtx.lineWidth = line.strokeWidth * Math.max(scaleX, scaleY);
            maskCtx.stroke();
          }
        }
        
        // Step 6: Add empty/transparent pixels from the input image to the mask
        if (emptyCount > 0) {
          console.log("Magic Fill - Adding empty/transparent areas to mask");
          maskCtx.fillStyle = "black";
          
          for (let y = 0; y < maskCanvas.height; y++) {
            for (let x = 0; x < maskCanvas.width; x++) {
              if (emptyPixels[y * maskCanvas.width + x] === 1) {
                maskCtx.fillRect(x, y, 1, 1);
              }
            }
          }
        }
        
        // Step 7: Add a black border around the entire mask for better outpainting
        maskCtx.strokeStyle = "black";
        maskCtx.lineWidth = 2;
        maskCtx.strokeRect(0, 0, maskCanvas.width, maskCanvas.height);
        
        // Convert the mask canvas to a data URL
        const maskDataUrl = maskCanvas.toDataURL("image/png");
        console.log("Magic Fill - Mask created with dimensions:", {
          width: maskCanvas.width,
          height: maskCanvas.height
        });
        
        // Upload the mask
        const maskBlob = dataURLtoBlob(maskDataUrl);
        const maskFormData = new FormData();
        maskFormData.append("file", maskBlob, "maskCrop.png");
        
        // Upload the mask using handleImageUpload
        console.log("Magic Fill - Uploading mask...");
        const uploadMaskResult = await handleImageUpload(maskFormData);
        
        // Check for upload errors
        if ("error" in uploadMaskResult) {
          throw new Error(`Failed to upload mask: ${uploadMaskResult.error.message || "Unknown error"}`);
        }
        
        console.log("Magic Fill - Mask uploaded:", uploadMaskResult.url);
        
        // Call the API to generate the filled image
        const result = await editImage({
          mode: "replace",
          userId: userId,
          prompt: store.prompt,
          negativePrompt: store.negativePrompt,
          image: uploadedImageUrl,
          mask: uploadMaskResult.url,
          resolution: finalResolution,
          style_type: "None",
          // Always provide explicit aspect ratio
          aspect_ratio: finalAspectRatio,
          clientTaskId: taskId
        });
        
        console.log("Magic Fill - API result:", result);
        
        if (result.success) {
          // Update task with the prediction ID if available
          if (result.predictionId) {
            store.updateGenerationTask(taskId, { 
              predictionId: result.predictionId
            });
          }
          
          // If we already have the output URL, update immediately
          if (result.outputUrl) {
            console.log('Magic fill successful, loading result image:', result.outputUrl);
            
            // First check if we already have this image in generatedImages to avoid duplicates
            const existingImage = store.generatedImages.find(img => img.src === result.outputUrl);
            
            if (existingImage) {
              console.log('This image already exists in generatedImages, not adding again');
              // Just update the task to complete
              store.updateGenerationTask(taskId, { 
                status: 'complete',
                imageUrl: result.outputUrl,
                imageId: existingImage.id
              });
            } else {
              // Load and add the new image
              const img = new Image();
              img.crossOrigin = "anonymous";
              img.src = result.outputUrl;
              
              const imageId = `magic-fill-${Date.now()}`;
              
              img.onload = () => {
                console.log('Result image loaded, adding to canvas');
                
                // Create the image object
                const newImage = {
                  image: img,
                  src: result.outputUrl,
                  element: img,
                  position: { x: selection.x, y: selection.y },
                  width: selection.width,
                  height: selection.height,
                  rotation: 0,
                  scaleX: 1,
                  scaleY: 1,
                  isSelected: false,
                  isNew: true,
                  id: imageId,
                };
                
                // Use addGeneratedImageFn if provided, otherwise fall back to store method
                if (addGeneratedImageFn) {
                  addGeneratedImageFn(newImage);
                } else {
                  store.addGeneratedImage(newImage);
                }
                
                // Update the task status to complete
                store.updateGenerationTask(taskId, { 
                  status: 'complete',
                  imageUrl: result.outputUrl,
                  imageId: imageId
                });
              };
            }
            
            // Reset magic fill UI state
            store.setMagicFillAreaSelection(false);
            store.setMode(CanvasMode.Move);
            
            // Clear lines and brush composite to avoid re-using the same mask
            store.clearLines();
            
            toast({
              title: "Magic Fill Complete",
              description: "The image has been generated and added to your canvas"
            });
          }
        } else {
          console.error('Magic fill failed:', result);
          store.updateGenerationTask(taskId, { status: 'error' });
          toast({
            title: "Magic Fill Failed",
            description: result.error || "An error occurred during image generation",
            variant: "destructive"
          });
        }
      } finally {
        // Ensure the original aspect ratio is restored if needed
        // Only restore it if it's a valid aspect ratio
        if (originalAspectRatio !== "None" && validAspectRatios.includes(originalAspectRatio)) {
          console.log('Restoring aspect ratio after magic fill API call:', originalAspectRatio);
          store.setAspectRatio(originalAspectRatio as any);
        }
      }
    } catch (error) {
      console.error("Error processing magic fill:", error);
      store.updateGenerationTask(taskId, { status: 'error' });
      throw error; // Propagate the error
    }
  };

  /**
   * Handles changing mask tools (brush, eraser, etc)
   */
  const handleMaskToolChange = useCallback((tool: MaskToolMode) => {
    store.setMaskTool(tool);
  }, [store]);

  /**
   * Handle cancel magic fill operation
   */
  const handleCancelMagicFill = useCallback(() => {
    // Full reset of the magic fill state
    store.setMode(CanvasMode.Move);
    store.setMagicFillAreaSelection(false);
    store.clearLines();
    store.setSelectionArea(null);
    
    toast({
      title: "Magic Fill Cancelled",
      description: "Magic fill operation has been cancelled",
    });
  }, [store, toast]);

  /**
   * Handle proceed with magic fill after mask is drawn
   */
  const handleProceedMagicFill = useCallback(() => {
    // Create or update the selection area based on drawn lines
    if (store.lines && store.lines.length > 0) {
      // Compute selection area based on lines with some padding
      let minX = Infinity,
        minY = Infinity,
        maxX = -Infinity,
        maxY = -Infinity;
      store.lines.forEach((line) => {
        for (let i = 0; i < line.points.length; i += 2) {
          const x = line.points[i],
            y = line.points[i + 1];
          minX = Math.min(minX, x);
          minY = Math.min(minY, y);
          maxX = Math.max(maxX, x);
          maxY = Math.max(maxY, y);
        }
      });
      
      // Add some padding around the mask
      const padding = 20;
      store.setSelectionArea({
        x: minX - padding,
        y: minY - padding,
        width: (maxX - minX) + (padding * 2),
        height: (maxY - minY) + (padding * 2),
      });
    } else if (store.inputImage) {
      // Default to entire input image if no lines are drawn
      store.setSelectionArea({
        x: store.inputImagePosition.x,
        y: store.inputImagePosition.y,
        width: store.inputImageDimensions ? store.inputImageDimensions.width : store.inputImage.width,
        height: store.inputImageDimensions ? store.inputImageDimensions.height : store.inputImage.height,
      });
    } else if (store.generatedImages.length > 0) {
      // If no input image but we have generated images, use the first valid one
      for (const img of store.generatedImages) {
        if (img.image || img.src) {
          store.setSelectionArea({
            x: img.position.x,
            y: img.position.y,
            width: img.width,
            height: img.height,
          });
          break;
        }
      }
    }
    
    // Set the magicFillAreaSelection to true to show the prompt input
    store.setMagicFillAreaSelection(true);
  }, [store]);

  /**
   * Handle invert mask operation
   */
  const handleInvertMask = useCallback(() => {
    store.invertMask();
  }, [store]);

  return {
    handleMagicFill,
    handleMaskToolChange,
    handleCancelMagicFill,
    handleProceedMagicFill,
    handleInvertMask
  };
}; 