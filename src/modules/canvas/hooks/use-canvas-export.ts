import { useCallback } from 'react';
import Konva from 'konva';
import { CanvasRect } from '../types/canvas';
import { dataURLtoBlob } from '../utils/canvas-utils';
import { handleImageUpload } from '../actions/upload-image';
import { useToast } from '@/modules/ui/use-toast';

/**
 * Helper function to create a checkerboard pattern for transparent areas
 */
const createCheckerboardPattern = () => {
  const patternCanvas = document.createElement('canvas');
  const size = 10;
  patternCanvas.width = size * 2;
  patternCanvas.height = size * 2;
  const patternContext = patternCanvas.getContext('2d');
  
  if (patternContext) {
    patternContext.fillStyle = '#f0f0f0';
    patternContext.fillRect(0, 0, size * 2, size * 2);
    patternContext.fillStyle = '#cccccc';
    patternContext.fillRect(0, 0, size, size);
    patternContext.fillRect(size, size, size, size);
  }
  
  return patternCanvas;
};

/**
 * Hook to handle canvas image exports, selection captures, and image uploads
 */
export const useCanvasExport = () => {
  const { toast } = useToast();

  /**
   * Helper to load an image from a data URL
   */
  const loadImage = useCallback((dataUrl: string): Promise<HTMLImageElement> => {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = (e) => reject(new Error("Failed to load image: " + e));
      img.src = dataUrl;
    });
  }, []);
  
  /**
   * Verify that a captured image has meaningful content
   */
  const verifyImageHasContent = useCallback(async (dataUrl: string): Promise<boolean> => {
    return new Promise((resolve) => {
      const img = new Image();
      
      img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d', { willReadFrequently: true });
        
        if (!ctx) {
          console.warn("Could not get canvas context for image verification");
          resolve(true); // Assume it's good if we can't verify
          return;
        }
        
        ctx.drawImage(img, 0, 0);
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;
        
        // Count non-transparent pixels
        let nonTransparentCount = 0;
        const totalPixels = data.length / 4;
        const minRequiredPixels = Math.min(100, totalPixels * 0.01); // At least 1% or 100 pixels
        
        for (let i = 3; i < data.length; i += 4) {
          if (data[i] > 20) { // Alpha > 20
            nonTransparentCount++;
            if (nonTransparentCount > minRequiredPixels) {
              resolve(true);
              return;
            }
          }
        }
        
        console.warn(`Image verification: Found only ${nonTransparentCount} non-transparent pixels`);
        resolve(false);
      };
      
      img.onerror = () => {
        console.warn("Could not load image for verification");
        resolve(false);
      };
      
      img.src = dataUrl;
    });
  }, []);
  
  /**
   * Create a fallback image when capture fails
   */
  const createFallbackImage = useCallback((selection: CanvasRect, message: string): string => {
    const canvas = document.createElement('canvas');
    canvas.width = selection.width;
    canvas.height = selection.height;
    const ctx = canvas.getContext('2d');
    
    if (ctx) {
      // Create a checkerboard pattern background
      const pattern = ctx.createPattern(createCheckerboardPattern(), 'repeat');
      if (pattern) {
        ctx.fillStyle = pattern;
      } else {
        ctx.fillStyle = "#f9fafb";
      }
      ctx.fillRect(0, 0, selection.width, selection.height);
      
      // Add a border
      ctx.strokeStyle = "#6B46C1";
      ctx.lineWidth = 4;
      ctx.strokeRect(2, 2, selection.width - 4, selection.height - 4);
      
      // Add informative text
      ctx.font = "16px sans-serif";
      ctx.fillStyle = "#1f2937";
      ctx.textAlign = "center";
      ctx.fillText(message, selection.width / 2, selection.height / 2 - 10);
      ctx.font = "14px sans-serif";
      ctx.fillText(`${Math.round(selection.width)} × ${Math.round(selection.height)}`, 
                   selection.width / 2, selection.height / 2 + 20);
    }
    
    return canvas.toDataURL('image/png');
  }, []);

  /**
   * Helper to check if an image is mostly empty (transparent or white)
   */
  const checkIfImageIsEmpty = useCallback(async (dataUrl: string): Promise<boolean> => {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext('2d', { willReadFrequently: true });
        
        if (!ctx) {
          console.warn("Could not get canvas context for image check");
          resolve(false);
          return;
        }
        
        ctx.drawImage(img, 0, 0);
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;
        
        // Count non-empty pixels (not transparent, not white)
        let nonEmptyPixels = 0;
        const pixelCount = data.length / 4;
        
        for (let i = 0; i < data.length; i += 4) {
          // Skip transparent pixels
          if (data[i + 3] < 20) continue;
          
          // Skip nearly white pixels
          if (data[i] > 240 && data[i + 1] > 240 && data[i + 2] > 240) continue;
          
          nonEmptyPixels++;
          
          // Early exit if we found sufficient content
          if (nonEmptyPixels > pixelCount * 0.01) { // More than 1% has content
            resolve(false);
            return;
          }
        }
        
        // If less than 1% of pixels have content, consider it empty
        resolve(nonEmptyPixels <= pixelCount * 0.01);
      };
      
      img.onerror = () => {
        console.warn("Could not load image for emptiness check");
        resolve(false);
      };
      
      img.src = dataUrl;
    });
  }, []);

  /**
   * Captures the selected area of the stage as a data URL
   * Enhanced with improved visibility management and positioning
   */
  const captureSelectionAreaImage = useCallback(async (
    selection: CanvasRect,
    stageRef: React.RefObject<Konva.Stage>
  ): Promise<string> => {
    if (!stageRef.current) {
      throw new Error("Canvas stage is not available");
    }
    
    // Get the stage
    const stage = stageRef.current;
    
    // Log detailed information for debugging
    console.log("Capturing selection area:", selection, "Stage:", {
      width: stage.width(),
      height: stage.height(),
      scale: stage.scaleX(),
      position: stage.position()
    });
    
    // Find background color - try to detect from background rect or use default white
    let backgroundColor = '#FFFFFF';
    try {
      // Look for the background rectangle in the first layer
      const bgLayer = stage.getLayers()[0];
      if (bgLayer) {
        const bgRect = bgLayer.find('Rect')[0] as Konva.Rect;
        if (bgRect && typeof bgRect.fill === 'function') {
          const fill = bgRect.fill();
          if (typeof fill === 'string') {
            backgroundColor = fill;
          }
        }
      }
    } catch (e) {
      console.warn("Could not detect background color:", e);
    }
    
    // Hide DOM elements that might show in the canvas (like placeholders with status)
    const domElements = document.querySelectorAll(
      '.loading-status, .status-indicator, .generating-indicator, .magic-fill-status, .placeholder, .task-indicator'
    );
    const originalDomStyles: {element: Element, style: string | null}[] = [];
    domElements.forEach(element => {
      originalDomStyles.push({
        element,
        style: element.getAttribute('style')
      });
      element.setAttribute('style', 'visibility: hidden; opacity: 0; display: none;');
    });
    
    // Make sure all brush strokes and components are fully rendered
    stage.batchDraw();
    
    // Wait to ensure everything is rendered
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Save current stage settings for restoration later
    const originalScale = { x: stage.scaleX(), y: stage.scaleY() };
    const originalPosition = { x: stage.x(), y: stage.y() };
    
    try {
      // APPROACH 1: DIRECT CAPTURE OF SELECTION AREA
      // This is the most reliable method but requires stage manipulation
      
      // Hide UI elements that shouldn't be exported
      const elementsToHide: Map<Konva.Node, boolean> = new Map();
      const allLayers = stage.getLayers();
      
      // First, try to find and temporarily hide the entire placeholder layer
      let placeholderLayer = null;
      allLayers.forEach((layer, i) => {
        // Check if this is a placeholder layer by examining its children
        const hasPlaceholders = layer.find('.placeholder, .task-placeholder').length > 0 || 
                               (layer.children && layer.children.some(child => 
                                 child.name && (
                                   child.name().includes('placeholder') || 
                                   child.name().includes('task')
                                 )
                               ));
        
        if (hasPlaceholders) {
          console.log("Found potential placeholder layer:", i);
          placeholderLayer = layer;
        }
      });
      
      // Now hide all UI elements and placeholders in all layers
      allLayers.forEach(layer => {
        // First, check if this is a magic fill layer and hide it entirely
        if (layer.name() === 'magic-fill-composite-layer' || 
            layer.name() === 'magic-fill-layer' ||
            layer.className === 'magic-fill-composite') {
          elementsToHide.set(layer, layer.visible());
          layer.visible(false);
          return;
        }

        // Expanded selector for UI elements, placeholders, and status indicators
        layer.find(
          '.selection-rect, Transformer, Tag, Label, .ui-control, Text, .placeholder, ' + 
          '.loading-overlay, .status-text, .task-placeholder, .generating-indicator, ' +
          '.magic-fill-placeholder, .task-indicator, .magic-fill-mask, Line, .brush-stroke, ' +
          '.magic-fill-composite, .magic-fill-line, .magic-fill-area, .magic-fill-preview, ' +
          '.active-stroke-preview, .magic-fill-stroke'
        ).forEach(node => {
          elementsToHide.set(node, node.visible());
          node.visible(false);
        });
        
        // Look for any UnifiedCanvasItem nodes that might be placeholders or magic fill elements
        layer.find('Group, Line, Shape').forEach(node => {
          // Check if this is a placeholder task or magic fill element
          if (node.attrs && (
            node.attrs.isPlaceholder || 
            node.attrs.isMagicFill || 
            node.attrs.isMask ||
            node.attrs.tool === 'magic-fill'
          )) {
            elementsToHide.set(node, node.visible());
            node.visible(false);
          }
          
          // Also check its name and className
          const name = node.name();
          const className = node.className;
          if (
            (name && (
              name.includes('magic-fill') || 
              name.includes('mask') || 
              name.includes('brush') ||
              name.includes('stroke')
            )) ||
            (className && (
              className.includes('magic-fill') ||
              className.includes('mask') ||
              className.includes('brush') ||
              className.includes('stroke')
            ))
          ) {
            elementsToHide.set(node, node.visible());
            node.visible(false);
          }
        });
      });
      
      // Apply changes
      stage.batchDraw();
      
      // Calculate the pixel ratio for high-quality export
      const pixelRatio = Math.max(2, window.devicePixelRatio || 1);
      
      // Calculate the absolute position of the selection in the stage
      // accounting for stage scaling and position
      const absoluteX = selection.x;
      const absoluteY = selection.y;
      
      // Store original viewport
      const originalViewport = {
        scale: stage.scaleX(),
        x: stage.x(),
        y: stage.y()
      };
      
      // Position the stage viewport to center exactly on our selection
      stage.scale({ x: 1, y: 1 });
      stage.position({
        x: -absoluteX + (stage.width() - selection.width) / 2,
        y: -absoluteY + (stage.height() - selection.height) / 2
      });
      
      // Ensure changes are applied
      stage.batchDraw();
      
      // Short pause to ensure rendering is complete
      await new Promise(resolve => setTimeout(resolve, 50));
      
      // Capture the selection area with transparent background
      const transparentDataUrl = stage.toDataURL({
        x: (stage.width() - selection.width) / 2,
        y: (stage.height() - selection.height) / 2,
        width: selection.width,
        height: selection.height,
        pixelRatio: pixelRatio,
        mimeType: "image/png"
      });
      
      // Create a canvas to apply background color if needed
      const canvas = document.createElement('canvas');
      canvas.width = selection.width * pixelRatio;
      canvas.height = selection.height * pixelRatio;
      const ctx = canvas.getContext('2d');
      
      // Load the transparent image
      const img = await loadImage(transparentDataUrl);
      
      if (ctx) {
        // Fill with background color
        ctx.fillStyle = backgroundColor;
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // Draw the image on top
        ctx.drawImage(img, 0, 0);
        
        // Get the final image with background
        const dataUrl = canvas.toDataURL('image/png');
        
        // Restore stage viewport
        stage.scale({ x: originalViewport.scale, y: originalViewport.scale });
        stage.position({ x: originalViewport.x, y: originalViewport.y });
        
        // Restore UI element visibility
        elementsToHide.forEach((wasVisible, node) => {
          node.visible(wasVisible);
        });
        
        // Apply changes
        stage.batchDraw();
        
        // Verify the image has content
        const hasContent = await verifyImageHasContent(dataUrl);
        
        if (hasContent) {
          return dataUrl;
        }
      }
      
      // APPROACH 2: MANUAL OFFSCREEN CANVAS RENDERING
      // A fallback approach that manually renders each layer
      
      console.log("First approach failed, trying manual rendering...");
      
      // Create an offscreen canvas for manual rendering
      const offscreenCanvas = document.createElement('canvas');
      offscreenCanvas.width = selection.width * pixelRatio;
      offscreenCanvas.height = selection.height * pixelRatio;
      const offCtx = offscreenCanvas.getContext('2d');
      
      if (!offCtx) {
        throw new Error("Could not create canvas context");
      }
      
      // Set scale to match pixel ratio
      offCtx.scale(pixelRatio, pixelRatio);
      
      // Set background
      offCtx.fillStyle = backgroundColor;
      offCtx.fillRect(0, 0, selection.width, selection.height);
      
      // Position the stage to center on selection (without changing the DOM)
      const tempStage = new Konva.Stage({
        container: document.createElement('div'),
        width: selection.width,
        height: selection.height
      });
      
      // Clone all visible content from the original stage
      allLayers.forEach(originalLayer => {
        const clonedLayer = originalLayer.clone();
        clonedLayer.x(-absoluteX);
        clonedLayer.y(-absoluteY);
        
        // Remove UI elements from the cloned layer - use expanded selectors
        clonedLayer.find(
          '.selection-rect, Transformer, Tag, Label, .ui-control, Text, .placeholder, ' + 
          '.loading-overlay, .status-text, .task-placeholder, .generating-indicator, ' +
          '.magic-fill-placeholder, .task-indicator'
        ).forEach(node => {
          node.destroy();
        });
        
        // Look for any UnifiedCanvasItem nodes that might be placeholders
        clonedLayer.find('Group').forEach(node => {
          // Check if this is a placeholder task by examining its properties
          if (node.attrs && node.attrs.isPlaceholder) {
            node.destroy();
          }
          
          // Also check its name
          const name = node.name();
          if (name && (
            name.includes('placeholder') || 
            name.includes('task') ||
            name.includes('generating')
          )) {
            node.destroy();
          }
        });
        
        // Also remove any nodes with UI-related names
        clonedLayer.find('*').forEach(node => {
          const name = node.name();
          if (name && (
            name.includes('ui') || 
            name.includes('interface') || 
            name.includes('status') || 
            name.includes('placeholder') ||
            name.includes('loading') ||
            name.includes('overlay') ||
            name.includes('task') ||
            name.includes('generating') ||
            name.includes('magic-fill-task')
          )) {
            node.destroy();
          }
        });
        
        tempStage.add(clonedLayer);
      });
      
      // Convert to data URL
      const fallbackDataUrl = tempStage.toDataURL({
        pixelRatio: pixelRatio,
        mimeType: "image/png"
      });
      
      // Clean up
      tempStage.destroy();
      
      // Verify the image has content
      const fallbackHasContent = await verifyImageHasContent(fallbackDataUrl);
      
      if (fallbackHasContent) {
        return fallbackDataUrl;
      }
      
      // If all approaches fail, return a fallback image
      console.warn("All capture approaches failed, creating fallback image");
      return createFallbackImage(selection, "Image capture failed");
      
    } catch (error) {
      console.error("Error during selection capture:", error);
      
      // Restore original stage state in case of error
      stage.scale(originalScale);
      stage.position(originalPosition);
      stage.batchDraw();
      
      // Restore DOM elements
      originalDomStyles.forEach(({element, style}) => {
        if (style) {
          element.setAttribute('style', style);
        } else {
          element.removeAttribute('style');
        }
      });
      
      // Create a fallback image if capture fails
      return createFallbackImage(selection, "Capture failed");
    }
  }, [loadImage, verifyImageHasContent, createFallbackImage]);

  /**
   * Captures and uploads a selection area to the server
   */
  const captureAndUploadSelectionArea = useCallback(async (
    selection: CanvasRect,
    stageRef: React.RefObject<Konva.Stage>,
    userId: string
  ): Promise<string> => {
    try {
      // Capture the selection area
      const dataUrl = await captureSelectionAreaImage(selection, stageRef);
      
      // Check if the image is empty before uploading
      const isEmpty = await checkIfImageIsEmpty(dataUrl);
      if (isEmpty) {
        throw new Error("The selected area is empty");
      }
      
      // Convert data URL to blob for upload
      const blob = dataURLtoBlob(dataUrl);
      
      // Upload the image
      const formData = new FormData();
      formData.append("file", blob, "selection.png");
      const result = await handleImageUpload(formData);
      
      if ("error" in result) {
        throw new Error(`Upload failed: ${result.error.message}`);
      }
      
      toast({
        title: "Upload successful", 
        description: "The image has been uploaded successfully",
      });
      
      return result.url;
    } catch (error) {
      console.error("Error uploading selection:", error);
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "Failed to upload the selected area",
        variant: "destructive",
      });
      throw error;
    }
  }, [captureSelectionAreaImage, checkIfImageIsEmpty, toast]);

  /**
   * Exports the current selection area directly to the user's device
   */
  const exportSelectionAsImage = useCallback(async (
    selection: CanvasRect,
    stageRef: React.RefObject<Konva.Stage>,
    fileName?: string
  ): Promise<void> => {
    try {
      // Capture the selected area as a data URL
      const dataUrl = await captureSelectionAreaImage(selection, stageRef);
      
      // Create download link
      const link = document.createElement("a");
      link.download = fileName || `selected-area-${Date.now()}.png`;
      link.href = dataUrl;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      toast({
        title: "Export successful",
        description: "The selected area has been exported as an image",
      });
    } catch (error) {
      console.error("Error exporting selection:", error);
      toast({
        title: "Export failed",
        description: "Failed to export the selected area",
        variant: "destructive",
      });
    }
  }, [captureSelectionAreaImage, toast]);

  return {
    captureSelectionAreaImage,
    captureAndUploadSelectionArea,
    exportSelectionAsImage
  };
}; 