"use server";

export type UploadError = {
  code:
    | "INVALID_FILE_TYPE"
    | "FILE_TOO_LARGE"
    | "UPLOAD_FAILED"
    | "INVALID_CREDENTIALS"
    | "NETWORK_ERROR";
  message: string;
};

// Helper function to handle Cloudflare response
async function handleCloudflareResponse(response: Response) {
  if (!response.ok) {
    const errorText = await response.text();
    console.error("Upload response error:", {
      status: response.status,
      statusText: response.statusText,
      error: errorText,
    });

    return {
      error: {
        code: "UPLOAD_FAILED" as const,
        message:
          response.status === 413
            ? "Image size is too large"
            : "Failed to upload image. Please try again.",
      },
    };
  }

  const result = await response.json();
  console.log("Cloudflare API response:", JSON.stringify(result, null, 2));

  if (!result.success || !result.result?.variants?.[0]) {
    console.error("Invalid upload response:", result);
    return {
      error: {
        code: "UPLOAD_FAILED" as const,
        message: "Failed to process image. Please try again.",
      },
    };
  }

  // Extract image ID and construct CDN URL
  const imageId = result.result.id;
  const cdnUrl = `https://renovaitor.com/cdn-cgi/imagedelivery/QnAHiMddHGu5qJ9DvFs5Lw/${imageId}/quality=100`;
  console.log("Generated CDN URL:", cdnUrl);

  return { id: imageId, url: cdnUrl };
}

// Helper function to check credentials
function checkCredentials():
  | { accountId: string; apiToken: string }
  | { error: UploadError } {
  const accountId = process.env.CLOUDFLARE_ACCOUNT_ID;
  const apiToken = process.env.CLOUDFLARE_API_TOKEN;

  if (!accountId || !apiToken) {
    return {
      error: {
        code: "INVALID_CREDENTIALS",
        message: "Unable to connect to image service. Please try again later.",
      },
    };
  }

  return { accountId, apiToken };
}

export async function handleImageUpload(
  formData: FormData
): Promise<{ id: string; url: string } | { error: UploadError }> {
  try {
    const credentials = checkCredentials();
    if ("error" in credentials) return credentials;

    const uploadResponse = await fetch(
      `https://api.cloudflare.com/client/v4/accounts/${credentials.accountId}/images/v1`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${credentials.apiToken}`,
        },
        body: formData,
      }
    );

    return handleCloudflareResponse(uploadResponse);
  } catch (error) {
    console.error("Image upload error:", error);
    return {
      error: {
        code: "NETWORK_ERROR",
        message:
          "Network error occurred. Please check your connection and try again.",
      },
    };
  }
}

export async function handleUrlUpload(imageUrl: string, folderPath?: string) {
  try {
    const credentials = checkCredentials();
    if ("error" in credentials) return credentials;

    const formData = new FormData();
    formData.append("url", imageUrl);

    if (folderPath) {
      formData.append("metadata", JSON.stringify({ folder: folderPath }));
    }
    formData.append("requireSignedURLs", "false");

    const uploadResponse = await fetch(
      `https://api.cloudflare.com/client/v4/accounts/${credentials.accountId}/images/v1`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${credentials.apiToken}`,
        },
        body: formData,
      }
    );

    return handleCloudflareResponse(uploadResponse);
  } catch (error) {
    console.error("URL upload error:", error);
    return {
      error: {
        code: "NETWORK_ERROR",
        message:
          "Network error occurred. Please check your connection and try again.",
      },
    };
  }
}

function getFileExtension(contentType: string): string {
  switch (contentType) {
    case "image/jpeg":
      return ".jpg";
    case "image/png":
      return ".png";
    case "image/gif":
      return ".gif";
    case "image/webp":
      return ".webp";
    case "image/svg+xml":
      return ".svg";
    default:
      return ".png"; // fallback
  }
} 