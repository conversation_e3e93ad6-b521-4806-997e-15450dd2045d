"use server";

import { z } from "zod";
import Replicate from "replicate";
import {
  EditImageParams,
  EditImageParamsSchema,
  EditImageResult,
  RemoveEditImageParams,
  ReplaceEditImageParams,
  GenerateEditImageParams,
} from "../types/edit-image";
import prisma from "@/lib/prisma/prisma";
import { PredictionStatus } from "../types/base";
import { handleImageUpload } from "../actions/upload-image";

const REPLICATE_API_TOKEN = process.env.REPLICATE_API_TOKEN;
const WEBHOOK_HOST = process.env.WEBHOOK_HOST;  // Use the same environment variable as virtual staging
const WEBHOOK_URL = WEBHOOK_HOST
  ? `${WEBHOOK_HOST}/api/webhook/replicate/image-generator`
  : undefined;  // Make it undefined when WEBHOOK_HOST is not set

// Constants for Replicate models
const REPLICATE_MODELS = {
  LAMA: "meta/lama:985047a1d35cc0d14c2b09d1580e9c3ca304d5c4f6f4028c0a3e77efd0a3d202",
  IDEOGRAM: "ideogram-ai/ideogram-v2-turbo",
} as const;

// Database operations
async function checkUserCredits(userId: string): Promise<{ user: { imageCredits: number } | null; error?: string }> {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { imageCredits: true },
  });

  if (!user || user.imageCredits < 1) {
    return {
      user,
      error: "You need 1 credit to edit an image. Please upgrade to get more credits.",
    };
  }

  return { user };
}

async function deductUserCredits(userId: string): Promise<{ imageCredits: number }> {
  return prisma.user.update({
    where: { id: userId },
    data: { imageCredits: { decrement: 1 } },
  });
}

async function saveEditImageInitialState(params: {
  userId: string;
  editId: string;
  inputImage?: string;
  maskImage?: string;
  prompt?: string;
  clientTaskId?: string;
}): Promise<any> {
  return prisma.editImage.create({
    data: {
      replicate_id: params.editId,
      userId: params.userId,
      status: PredictionStatus.PROCESSING,
      inputImage: params.inputImage || "",
      maskImage: params.maskImage || "",
      prompt: params.prompt || "",
      clientTaskId: params.clientTaskId, // Store the client task ID for matching
    } as any, // Type assertion to work around type issue
  });
}

// Type guards
function isRemoveMode(params: EditImageParams): params is RemoveEditImageParams {
  return params.mode === "remove";
}

function isReplaceMode(params: EditImageParams): params is ReplaceEditImageParams {
  return params.mode === "replace";
}

function isGenerateMode(params: EditImageParams): params is GenerateEditImageParams {
  return params.mode === "generate";
}

// Enhanced function for webhook-based API calls
async function processRemoveMode(replicate: Replicate, params: RemoveEditImageParams): Promise<any> {
  return replicate.predictions.create({
    model: REPLICATE_MODELS.LAMA,
    input: {
      image: params.image,
      mask: params.mask,
      resize_mode: "scale",
      return_mask: false,
    },
    webhook: WEBHOOK_URL,
    webhook_events_filter: ["completed"] 
  });
}

async function processReplaceMode(replicate: Replicate, params: ReplaceEditImageParams): Promise<any> {
  // Generate a random seed if not provided
  const seed = params.seed || Math.floor(Math.random() * 1000000);
  
  return replicate.predictions.create({
    model: "ideogram-ai/ideogram-v2-turbo",
    input: {
      prompt: params.prompt,
      image: params.image,
      mask: params.mask,
      negative_prompt: params.negativePrompt || "",
      resolution: params.resolution || "None",
      style_type: params.style_type || "None",
      aspect_ratio: params.aspect_ratio || "1:1",
      magic_prompt_option: params.magic_prompt_option || "Auto",
      seed: seed,
    },
    webhook: WEBHOOK_URL,
    webhook_events_filter: ["completed"] 
  });
}

async function processGenerateMode(replicate: Replicate, params: GenerateEditImageParams): Promise<any> {
  // Define valid resolutions from the API error message
  const validResolutions = [
    "None", "512x1536", "576x1408", "576x1472", "576x1536", "640x1024", 
    "640x1344", "640x1408", "640x1472", "640x1536", "704x1152", "704x1216", 
    "704x1280", "704x1344", "704x1408", "704x1472", "720x1280", "736x1312", 
    "768x1024", "768x1088", "768x1152", "768x1216", "768x1232", "768x1280", 
    "768x1344", "832x960", "832x1024", "832x1088", "832x1152", "832x1216", 
    "832x1248", "864x1152", "896x960", "896x1024", "896x1088", "896x1120", 
    "896x1152", "960x832", "960x896", "960x1024", "960x1088", "1024x640", 
    "1024x768", "1024x832", "1024x896", "1024x960", "1024x1024", "1088x768", 
    "1088x832", "1088x896", "1088x960", "1120x896", "1152x704", "1152x768", 
    "1152x832", "1152x864", "1152x896", "1216x704", "1216x768", "1216x832", 
    "1232x768", "1248x832", "1280x704", "1280x720", "1280x768", "1280x800", 
    "1312x736", "1344x640", "1344x704", "1344x768", "1408x576", "1408x640", 
    "1408x704", "1472x576", "1472x640", "1472x704", "1536x512", "1536x576", "1536x640"
  ];

  // Enhanced validation for resolution and aspect_ratio
  let resolution = params.resolution;
  let aspect_ratio = params.aspect_ratio;
  
  // CRITICAL: Make sure resolution is always a valid value
  // First check if the provided resolution is valid
  if (resolution && resolution !== "None" && !validResolutions.includes(resolution)) {
    console.warn(`Invalid resolution "${resolution}" provided, defaulting to "None"`);
    resolution = "None";
  }
  
  // If resolution is 'None' or not provided
  if (!resolution || resolution === 'None') {
    // If explicit aspect_ratio is provided, use it
    if (aspect_ratio) {
      console.log('Using explicit aspect ratio:', aspect_ratio);
      // Set resolution to "None" when using aspect ratio
      resolution = "None";
    } else {
      // Only default to 1:1 if NO aspect ratio is provided
      console.log('No aspect ratio provided, defaulting to 1:1');
      aspect_ratio = '1:1';
      resolution = "None";
    }
  } else {
    // If valid resolution is provided, API will ignore aspect_ratio
    // Only use default if aspect_ratio is completely missing
    if (!aspect_ratio) {
      aspect_ratio = '1:1';
    }
  }
  
  console.log('Final Generation API parameters:', {
    prompt: params.prompt,
    negative_prompt: params.negativePrompt || "",
    resolution: resolution,
    aspect_ratio: aspect_ratio,
    style_type: params.style_type || "None",
    magic_prompt_option: params.magic_prompt_option || "Auto",
    seed: params.seed || "undefined (random)",
  });
  
  const apiInputs = {
    prompt: params.prompt,
    negative_prompt: params.negativePrompt || "",
    resolution: resolution, // Always ensure it's a valid string value
    style_type: params.style_type || "None",
    aspect_ratio: aspect_ratio,
    magic_prompt_option: params.magic_prompt_option || "Auto",
    seed: params.seed || Math.floor(Math.random() * 1000000), // Match virtual-staging random seed pattern
  };
  
  console.log('Raw API input object:', apiInputs);
  
  try {
    // Match the virtual staging implementation exactly
    return replicate.predictions.create({
      model: "ideogram-ai/ideogram-v2-turbo",
      input: apiInputs,
      webhook: WEBHOOK_URL,
      webhook_events_filter: ["completed"]
    });
  } catch (error: any) {
    console.error('Replicate API error:', error);
    throw new Error(`Image generation failed: ${error.message || 'Unknown error'}`);
  }
}

// ----- Helper Functions for Uploading Non-URI Data -----

function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch (_) {
    return false;
  }
}

function dataURLtoBlob(dataurl: string): Blob {
  const parts = dataurl.split(',');
  const mimeMatch = parts[0].match(/:(.*?);/);
  const mime = mimeMatch ? mimeMatch[1] : 'image/png';
  const bstr = Buffer.from(parts[1], 'base64');
  return new Blob([bstr], { type: mime });
}

async function uploadIfNotUrl(field: string | undefined, fieldName: string): Promise<string> {
  if (!field) throw new Error(`${fieldName} is required`);
  if (isValidUrl(field)) return field;
  // Assume field is a data URL (e.g. base64); convert and upload it.
  const blob = dataURLtoBlob(field);
  const formData = new FormData();
  formData.append("file", blob, `${fieldName}.png`);
  const uploadResult = await handleImageUpload(formData);
  if ("error" in uploadResult) {
    throw new Error(`Failed to upload ${fieldName}: ${uploadResult.error.message}`);
  }
  return uploadResult.url;
}

// Update to include clientTaskId and return predictionId
export interface ExtendedEditImageResult extends EditImageResult {
  predictionId?: string;
  clientTaskId?: string;
}

export async function editImage(params: EditImageParams & { clientTaskId?: string }): Promise<ExtendedEditImageResult> {
  try {
    // Log the incoming parameters for debugging
    console.log('editImage called with params:', {
      mode: params.mode,
      userId: params.userId,
      prompt: 'prompt' in params ? params.prompt : undefined,
      resolution: 'resolution' in params ? params.resolution : undefined,
      aspect_ratio: 'aspect_ratio' in params ? params.aspect_ratio : undefined,
      clientTaskId: params.clientTaskId,
    });
    
    // Early validation of userId before the Zod schema validation
    if (!params.userId || typeof params.userId !== 'string' || params.userId.trim() === '') {
      console.error('Invalid userId received:', params.userId);
      return {
        success: false,
        error: "User ID is missing or invalid. Please log in again.",
        type: "INSUFFICIENT_CREDITS" as any, // Type casting to fix incompatibility
        updatedCredits: 0, // Added to fix error
      };
    }
    
    try {
      // Schema validation
      const validatedParams = EditImageParamsSchema.parse(params);
      
      // Check user credits
      const { user, error } = await checkUserCredits(validatedParams.userId);
      if (error) {
        return {
          success: false,
          error,
          updatedCredits: user?.imageCredits || 0,
          type: "INSUFFICIENT_CREDITS",
        };
      }
      
      if (!user) {
        console.error(`User not found for ID: ${validatedParams.userId}`);
        return {
          success: false,
          error: "User not found. Please log in again.",
          type: "INSUFFICIENT_CREDITS" as any, // Type casting to fix incompatibility
          updatedCredits: 0, // Added to fix error
        };
      }

      // Initialize Replicate
      const replicate = new Replicate({ auth: REPLICATE_API_TOKEN });

      // For modes that require image/mask inputs, ensure they are valid URIs.
      let prediction: any;
      try {
        if (isRemoveMode(validatedParams)) {
          validatedParams.image = await uploadIfNotUrl(validatedParams.image, "image");
          validatedParams.mask = await uploadIfNotUrl(validatedParams.mask, "mask");
          prediction = await processRemoveMode(replicate, validatedParams);
        } else if (isReplaceMode(validatedParams)) {
          validatedParams.image = await uploadIfNotUrl(validatedParams.image, "image");
          validatedParams.mask = await uploadIfNotUrl(validatedParams.mask, "mask");
          prediction = await processReplaceMode(replicate, validatedParams);
        } else if (isGenerateMode(validatedParams)) {
          prediction = await processGenerateMode(replicate, validatedParams);
        } else {
          throw new Error("Invalid mode specified");
        }
      } catch (processingError) {
        console.error('Error during image processing:', processingError);
        throw processingError;
      }

      if (!prediction || !prediction.id) {
        throw new Error("Failed to create prediction");
      }

      // Save initial state to database
      await saveEditImageInitialState({
        userId: validatedParams.userId,
        editId: prediction.id,
        inputImage: "image" in validatedParams ? validatedParams.image : undefined,
        maskImage: "mask" in validatedParams ? validatedParams.mask : undefined,
        prompt: "prompt" in validatedParams ? validatedParams.prompt : undefined,
        clientTaskId: params.clientTaskId,
      });

      // Deduct credits immediately
      const updatedUser = await deductUserCredits(validatedParams.userId);

      return {
        success: true,
        predictionId: prediction.id,
        clientTaskId: params.clientTaskId,
        updatedCredits: updatedUser.imageCredits,
      };
    } catch (validationError) {
      console.error("Validation error with params:", validationError);
      return {
        success: false,
        error: validationError instanceof Error ? validationError.message : "Invalid parameters provided",
        type: "INSUFFICIENT_CREDITS" as any, // Type casting to fix incompatibility
        updatedCredits: 0, // Added to fix error
      };
    }
  } catch (error) {
    console.error("Error editing image:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unknown error occurred",
      type: "INSUFFICIENT_CREDITS" as any, // Type casting to fix incompatibility
      updatedCredits: 0, // Added to fix error
    };
  }
} 