export interface AspectRatioOption {
  label: string;
  value: string;
  width: number;
  height: number;
}

export const aspectRatioOptions: AspectRatioOption[] = [
  { label: "1:3", value: "1:3", width: 576, height: 1728 },
  { label: "1:2", value: "1:2", width: 768, height: 1536 },
  { label: "9:16", value: "9:16", width: 576, height: 1024 },
  { label: "10:16", value: "10:16", width: 640, height: 1024 },
  { label: "2:3", value: "2:3", width: 768, height: 1152 },
  { label: "3:4", value: "3:4", width: 768, height: 1024 },
  { label: "4:5", value: "4:5", width: 832, height: 1040 },
  { label: "1:1", value: "1:1", width: 1024, height: 1024 },
  { label: "5:4", value: "5:4", width: 1040, height: 832 },
  { label: "4:3", value: "4:3", width: 1024, height: 768 },
  { label: "3:2", value: "3:2", width: 1152, height: 768 },
  { label: "16:10", value: "16:10", width: 1024, height: 640 },
  { label: "16:9", value: "16:9", width: 1024, height: 576 },
  { label: "2:1", value: "2:1", width: 1536, height: 768 },
  { label: "3:1", value: "3:1", width: 1728, height: 576 },
];

export interface ImageDimensionsState {
  customResolutionEnabled: boolean;
  selectedRatio: string;
  width: number;
  height: number;
  orientation: "landscape" | "portrait";
}

export interface ImageDimensionsCardProps {
  onClose: () => void;
  initialState?: Partial<ImageDimensionsState>;
  onChange?: (state: ImageDimensionsState) => void;
} 