"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/modules/ui/dialog";
import { Button } from "@/modules/ui/button";
import { RadioGroup, RadioGroupItem } from "@/modules/ui/radio-group";
import { Label } from "@/modules/ui/label";
import { Textarea } from "@/modules/ui/textarea";

interface FeedbackDialogProps {
  open: boolean;
  onClose: () => void;
}

const FEEDBACK_OPTIONS = [
  { id: "too_expensive", label: "Too expensive" },
  { id: "not_sure_about_quality", label: "Not sure about the quality" },
  { id: "will_buy_later", label: "Planning to buy later" },
  { id: "using_other_service", label: "Using another service" },
  { id: "other", label: "Other reason" },
];

export function FeedbackDialog({ open, onClose }: FeedbackDialogProps) {
  const [selectedOption, setSelectedOption] = useState<string>("");
  const [otherFeedback, setOtherFeedback] = useState("");

  const handleSubmit = async () => {
    try {
      await fetch("/api/feedback", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          type: "paywall_dismissal",
          reason: selectedOption,
          additionalFeedback: otherFeedback,
        }),
      });
    } catch (error) {
      console.error("Failed to submit feedback:", error);
    } finally {
      onClose();
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Help Us Improve</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <RadioGroup value={selectedOption} onValueChange={setSelectedOption}>
            {FEEDBACK_OPTIONS.map((option) => (
              <div key={option.id} className="flex items-center space-x-2">
                <RadioGroupItem value={option.id} id={option.id} />
                <Label htmlFor={option.id}>{option.label}</Label>
              </div>
            ))}
          </RadioGroup>

          {selectedOption === "other" && (
            <Textarea
              placeholder="Please tell us more..."
              value={otherFeedback}
              onChange={(e) => setOtherFeedback(e.target.value)}
              className="mt-2"
            />
          )}

          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={onClose}>
              Skip
            </Button>
            <Button onClick={handleSubmit}>Submit</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
