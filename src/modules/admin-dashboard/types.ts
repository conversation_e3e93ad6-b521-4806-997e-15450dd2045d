import { PredictionStatus, Role, SubscriptionStatus } from "@prisma/client";

export interface AdminUser {
  id: string;
  name: string | null;
  email: string | null;
  image: string | null;
  role: string;
  imageCredits: number;
  lastCreditReset: Date | null;
  subscription: {
    packageName: string;
    paymentStatus: string;
    endDate: Date;
  } | null;
  _count: {
    interiorDesigns: number;
    exteriorDesigns: number;
    virtualStagings: number;
    editImages: number;
  };
  industry: string | null;
  source: string | null;
  totalReferrals: number;
  referralCredits: number;
}

export interface DashboardStats {
  users: {
    total: number;
    totalCredits: number;
  };
  subscriptions: Array<{
    status: SubscriptionStatus;
    count: number;
  }>;
  transactions: {
    totalAmount: number;
    totalCredits: number;
    count: number;
    byPackage: Array<{
      packageName: string;
      amount: number;
      baseAmount: number;
      taxAmount: number;
      credits: number;
      count: number;
    }>;
  };
  generations: {
    interior: number;
    exterior: number;
    virtualStaging: number;
    editImage: number;
  };
}

export type AdminSubscriptionSortField =
  | "startDate"
  | "endDate"
  | "packageName"
  | "paymentStatus"
  | "user.name"
  | "user.email"
  | "user.imageCredits";

export interface SubscriptionWithUser {
  id: string;
  userId: string;
  packageName: string;
  paymentStatus: SubscriptionStatus;
  startDate: Date;
  endDate: Date;
  user: {
    name: string;
    email: string;
    imageCredits: number;
  };
}

export interface Transaction {
  id: string;
  packageName: string;
  amount: number;
  credits: number;
  status: string;
  user: {
    name: string;
    email: string;
  };
  createdAt: Date;
}

export interface BaseGeneration {
  id: string;
  createdAt: Date;
  inputImage: string;
  prompt?: string;
  style?: string;
  room?: string;
  status: PredictionStatus;
}

export interface InteriorDesign extends BaseGeneration {
  outputImages: string[];
}

export interface ExteriorDesign extends BaseGeneration {
  outputImages: string[];
}

export interface VirtualStaging extends BaseGeneration {
  outputImage: string;
}

export interface EditImage extends BaseGeneration {
  outputImage: string;
}

export interface UserGenerations {
  interiorDesigns: InteriorDesign[];
  exteriorDesigns: ExteriorDesign[];
  virtualStagings: VirtualStaging[];
  editImages: EditImage[];
}

export type EmailTemplate =
  | "announcement"
  | "notification"
  | "marketing"
  | "welcome"
  | "incident"
  | "custom"
  | "feedback"
  | "inactivity-feedback";

export interface UpdateUserCreditsResponse {
  id: string;
  name: string | null;
  email: string | null;
  imageCredits: number;
}
