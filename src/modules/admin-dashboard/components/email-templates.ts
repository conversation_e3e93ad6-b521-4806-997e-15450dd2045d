import type { EmailTemplate } from "@/modules/admin-dashboard/types";

export interface EmailTemplateConfig {
  id: EmailTemplate;
  name: string;
  subject: string;
  description: string;
  preview?: React.ReactNode;
}

export const EMAIL_TEMPLATES: EmailTemplateConfig[] = [
  {
    id: "announcement",
    name: "Announcement",
    subject: "Important Update from Renovaitor",
    description:
      "Send important announcements about service updates or changes",
  },
  {
    id: "notification",
    name: "Notification",
    subject: "New Notification from Renovaitor",
    description: "Send notifications about account activity or updates",
  },
  {
    id: "marketing",
    name: "Marketing",
    subject: "Special Offer from Renovaitor",
    description: "Send marketing messages about special offers or promotions",
  },
  {
    id: "welcome",
    name: "Welcome Email",
    subject: "Welcome to Renovaitor!",
    description: "Send a welcome message to new users",
  },
  {
    id: "custom",
    name: "Custom Email",
    subject: "Message from Renovaitor",
    description: "Send a custom message with your own content",
  },
  {
    id: "incident",
    name: "Incident Email",
    subject: "We apologize for the inconvenience",
    description: "Send an apology email for an incident",
  },
  {
    id: "feedback",
    name: "Feedback Survey",
    subject: "We'd Love Your Feedback!",
    description:
      "Send a feedback survey to gather user opinions and suggestions",
  },
  {
    id: "inactivity-feedback",
    name: "Inactivity Feedback",
    description:
      "Ask inactive users for feedback and encourage them to try the service",
    subject: "We'd Love to Help You Get Started! 🎨",
  },
];
