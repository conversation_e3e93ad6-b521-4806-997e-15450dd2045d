"use client";

import { useEffect, useState } from "react";
import { formatDistanceToNow } from "date-fns";

interface Activity {
  id: string;
  type: string;
  user: {
    name: string;
    email: string;
  };
  createdAt: Date;
}

export const RecentActivities = () => {
  const [activities, setActivities] = useState<Activity[]>([]);

  useEffect(() => {
    // Fetch activities
    const fetchActivities = async () => {
      // Implementation pending - will fetch from a new API endpoint
    };

    fetchActivities();
  }, []);

  return (
    <div>
      <h3 className="text-lg font-medium text-foreground mb-4">
        Recent Activity
      </h3>
      <div className="flow-root">
        <ul className="-mb-8">
          {activities.map((activity, idx) => (
            <li key={activity.id}>
              <div className="relative pb-8">
                {idx !== activities.length - 1 && (
                  <span
                    className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-border"
                    aria-hidden="true"
                  />
                )}
                <div className="relative flex space-x-3">
                  <div>
                    <span className="h-8 w-8 rounded-full bg-primary flex items-center justify-center ring-8 ring-card">
                      {/* Icon based on activity type */}
                    </span>
                  </div>
                  <div className="flex min-w-0 flex-1 justify-between space-x-4 pt-1.5">
                    <div>
                      <p className="text-sm text-muted-foreground">
                        {activity.user.name} {activity.type}
                      </p>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {formatDistanceToNow(new Date(activity.createdAt), {
                        addSuffix: true,
                      })}
                    </div>
                  </div>
                </div>
              </div>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};
