"use client";

import { useState, useEffect } from "react";
import { getSubscriptions, updateSubscriptionStatus } from "../actions";
import { useTransition } from "react";
import { Button } from "@/modules/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/modules/ui/table";
import { SubscriptionWithUser } from "../types";

export const SubscriptionsTable = () => {
  const [isPending, startTransition] = useTransition();
  const [subscriptions, setSubscriptions] = useState<SubscriptionWithUser[]>(
    []
  );

  useEffect(() => {
    const loadSubscriptions = async () => {
      const result = await getSubscriptions();
      setSubscriptions(result.subscriptions);
    };
    loadSubscriptions();
  }, []);

  const handleStatusUpdate = async (id: string, status: string) => {
    startTransition(async () => {
      await updateSubscriptionStatus(id, status as any);
      const result = await getSubscriptions();
      setSubscriptions(result.subscriptions);
    });
  };

  return (
    <div className="rounded-md border border-border bg-card">
      <Table>
        <TableHeader>
          <TableRow className="border-border hover:bg-muted/50">
            <TableHead className="text-muted-foreground">User</TableHead>
            <TableHead className="text-muted-foreground">Package</TableHead>
            <TableHead className="text-muted-foreground">Status</TableHead>
            <TableHead className="text-muted-foreground">Start Date</TableHead>
            <TableHead className="text-muted-foreground">End Date</TableHead>
            <TableHead className="text-muted-foreground">Credits</TableHead>
            <TableHead className="text-muted-foreground">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {subscriptions?.map((sub) => (
            <TableRow key={sub.id} className="border-border hover:bg-muted/50">
              <TableCell>
                <div>
                  <p className="font-medium text-foreground">{sub.user.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {sub.user.email}
                  </p>
                </div>
              </TableCell>
              <TableCell className="text-muted-foreground">
                {sub.packageName}
              </TableCell>
              <TableCell className="text-muted-foreground">
                {sub.paymentStatus}
              </TableCell>
              <TableCell className="text-muted-foreground">
                {new Date(sub.startDate).toLocaleDateString()}
              </TableCell>
              <TableCell className="text-muted-foreground">
                {new Date(sub.endDate).toLocaleDateString()}
              </TableCell>
              <TableCell className="text-muted-foreground">
                {sub.user.imageCredits}
              </TableCell>
              <TableCell className="space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleStatusUpdate(sub.id, "PAUSED")}
                  disabled={sub.paymentStatus === "PAUSED"}
                  className="hover:bg-muted/50 border-border"
                >
                  Pause
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleStatusUpdate(sub.id, "CANCELED")}
                  disabled={sub.paymentStatus === "CANCELED"}
                  className="hover:bg-muted/50 border-border"
                >
                  Cancel
                </Button>
              </TableCell>
            </TableRow>
          ))}
          {!subscriptions?.length && (
            <TableRow>
              <TableCell
                colSpan={7}
                className="h-24 text-center text-muted-foreground"
              >
                No subscriptions found.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
};
