"use client";

import { formatCurrency } from "@/lib/utils";

interface DashboardStatsProps {
  stats: {
    users: {
      total: number;
      totalCredits: number;
    };
    subscriptions: Array<{
      status: string;
      count: number;
    }>;
    transactions: {
      totalAmount: number;
      totalCredits: number;
      count: number;
      byPackage: Array<{
        packageName: string;
        amount: number;
        baseAmount: number;
        taxAmount: number;
        credits: number;
        count: number;
      }>;
    };
    generations: {
      interior: number;
      exterior: number;
      virtualStaging: number;
      editImage: number;
    };
  };
}

const creditProducts = [
  {
    credits: 50,
    price: 9.0,
    variantId: "619715",
    pricePerCredit: 0.18,
  },
  {
    credits: 200,
    price: 19.0,
    variantId: "135060",
    pricePerCredit: 0.095,
  },
  {
    credits: 500,
    price: 39.0,
    variantId: "619716",
    pricePerCredit: 0.078,
  },
  {
    credits: 1000,
    price: 69.0,
    variantId: "619721",
    pricePerCredit: 0.069,
  },
  {
    credits: 1500,
    price: 99.0,
    variantId: "619722",
    pricePerCredit: 0.066,
    isBestValue: true,
  },
];

const getCreditAmountFromPackageName = (packageName: string): number | null => {
  const match = packageName.match(/(\d+)\s*Design Credits/i);
  return match ? parseInt(match[1], 10) : null;
};

export const DashboardStats = ({ stats }: DashboardStatsProps) => {
  const activeSubscriptions =
    stats.subscriptions.find((s) => s.status === "ACTIVE")?.count || 0;

  // Calculate totals only for paid transactions
  const totals = stats.transactions.byPackage.reduce(
    (acc, pkg) => {
      // Skip zero amount transactions
      if (pkg.amount <= 0) return acc;

      // Handle credit packages differently from subscription packages
      if (pkg.packageName.toLowerCase().includes("design credits")) {
        const creditAmount = getCreditAmountFromPackageName(pkg.packageName);
        const creditProduct = creditProducts.find(
          (p) => p.credits === creditAmount
        );

        if (!creditProduct) {
          console.warn(
            `No matching credit product found for ${pkg.packageName} (${creditAmount} credits)`
          );
          return acc;
        }

        const basePrice = creditProduct.price;
        const baseAmount = pkg.count * basePrice;
        const taxAmount = pkg.amount - baseAmount;

        return {
          baseAmount: acc.baseAmount + baseAmount,
          taxAmount: acc.taxAmount + taxAmount,
          totalAmount: acc.totalAmount + pkg.amount,
          totalCredits: acc.totalCredits + pkg.credits,
        };
      } else {
        // For subscription packages, use the provided baseAmount and taxAmount
        return {
          baseAmount: acc.baseAmount + pkg.baseAmount,
          taxAmount: acc.taxAmount + pkg.taxAmount,
          totalAmount: acc.totalAmount + pkg.amount,
          totalCredits: acc.totalCredits + pkg.credits,
        };
      }
    },
    { baseAmount: 0, taxAmount: 0, totalAmount: 0, totalCredits: 0 }
  );

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <StatsCard
          title="Total Users"
          value={stats.users.total.toLocaleString()}
          description="Active accounts"
        />
        <StatsCard
          title="Active Subscriptions"
          value={activeSubscriptions.toLocaleString()}
          description="Current subscribers"
        />
        <StatsCard
          title="Total Revenue"
          value={formatCurrency(totals.totalAmount)}
          description={`Net: ${formatCurrency(
            totals.baseAmount
          )} + Tax: ${formatCurrency(totals.taxAmount)}`}
        />
        <StatsCard
          title="Credits Issued"
          value={totals.totalCredits.toLocaleString()}
          description="All time"
        />
      </div>

      <div className="bg-card rounded-lg border border-border shadow-sm p-6">
        <h3 className="text-lg font-medium text-foreground mb-4">
          Transaction Details
        </h3>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-border">
            <thead>
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                  Package
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider">
                  Count
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider">
                  Base Amount
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider">
                  Tax
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider">
                  Total
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider">
                  Credits
                </th>
              </tr>
            </thead>
            <tbody className="bg-card divide-y divide-border">
              {stats.transactions.byPackage.map((pkg, idx) => {
                // Skip packages with zero amount
                if (pkg.amount <= 0) {
                  return null;
                }

                // Handle credit packages
                if (pkg.packageName.toLowerCase().includes("design credits")) {
                  const creditAmount = getCreditAmountFromPackageName(
                    pkg.packageName
                  );
                  const creditProduct = creditProducts.find(
                    (p) => p.credits === creditAmount
                  );

                  if (!creditProduct) {
                    console.warn(
                      `No matching credit product found for ${pkg.packageName} (${creditAmount} credits)`
                    );
                    return null;
                  }

                  const basePrice = creditProduct.price;
                  const baseAmount = pkg.count * basePrice;
                  const taxAmount = pkg.amount - baseAmount;

                  return (
                    <tr key={pkg.packageName + idx}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                        {pkg.packageName}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground text-right">
                        {pkg.count.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground text-right">
                        {formatCurrency(baseAmount)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground text-right">
                        {formatCurrency(taxAmount)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground text-right">
                        {formatCurrency(pkg.amount)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground text-right">
                        {pkg.credits.toLocaleString()}
                      </td>
                    </tr>
                  );
                }

                // Handle subscription packages (using existing code)
                return (
                  <tr key={pkg.packageName + idx}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                      {pkg.packageName}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground text-right">
                      {pkg.count.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground text-right">
                      {formatCurrency(pkg.baseAmount)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground text-right">
                      {formatCurrency(pkg.taxAmount)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground text-right">
                      {formatCurrency(pkg.amount)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground text-right">
                      {pkg.credits.toLocaleString()}
                    </td>
                  </tr>
                );
              })}
              {/* Totals row */}
              <tr className="bg-muted font-medium">
                <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                  Totals
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground text-right">
                  {stats.transactions.byPackage
                    .reduce(
                      (sum, pkg) => sum + (pkg.amount > 0 ? pkg.count : 0),
                      0
                    )
                    .toLocaleString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground text-right">
                  {formatCurrency(totals.baseAmount)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground text-right">
                  {formatCurrency(totals.taxAmount)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground text-right">
                  {formatCurrency(totals.totalAmount)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground text-right">
                  {totals.totalCredits.toLocaleString()}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

const StatsCard = ({
  title,
  value,
  description,
}: {
  title: string;
  value: string | number;
  description: string;
}) => {
  return (
    <div className="bg-card border border-border rounded-lg shadow-sm">
      <div className="p-5">
        <div className="flex items-center">
          <div className="flex-1">
            <p className="text-sm font-medium text-muted-foreground truncate">
              {title}
            </p>
            <p className="mt-1 text-3xl font-semibold text-foreground">
              {value}
            </p>
          </div>
        </div>
        <p className="mt-4 text-sm text-muted-foreground">{description}</p>
      </div>
    </div>
  );
};
