"use server";

import prisma from "@/lib/prisma/prisma";
import { DashboardStats, SubscriptionWithUser, AdminUser } from "./types";
import { PredictionStatus, SubscriptionStatus } from "@prisma/client";
import { <PERSON>rism<PERSON>, Role } from "@prisma/client";
import { sendEmail } from "@/lib/email/send-email";
import { currentUser } from "@/modules/auth/actions/user-actions";
import { revalidatePath } from "next/cache";
import type { EmailTemplate } from "@/modules/admin-dashboard/types";
import CustomEmail from "@/lib/auth/email-templates/custom-email";
import NotificationEmail from "@/lib/auth/email-templates/notification-email";
import AnnouncementEmail from "@/lib/auth/email-templates/announcement-email";
import MarketingEmail from "@/lib/auth/email-templates/marketing-email";
import WelcomeEmail from "@/lib/auth/email-templates/welcome-email";
import IncidentEmail from "@/lib/auth/email-templates/incident-email";
import FeedbackSurveyEmail from "@/lib/auth/email-templates/feedback-survey-email";
import { EmailTemplateConfig } from "./components/email-templates";
import { EmailFiltersState } from "@/modules/admin-dashboard/types/email";
import { FeedbackInactivityEmail } from "@/lib/auth/email-templates/feedback-inactivity-email";
import { type UpdateUserCreditsResponse } from "./types";

// Define base types
type UserSortField =
  | "name"
  | "email"
  | "role"
  | "imageCredits"
  | "lastCreditReset"
  | "industry"
  | "source";

type UserSubscriptionSortField = "packageName" | "paymentStatus" | "endDate";

export type SortOrder = "asc" | "desc";

export type SortField =
  | UserSortField
  | `subscription.${UserSubscriptionSortField}`
  | "generationCount";

interface GetUsersParams {
  page?: number;
  limit?: number;
  sortBy?: SortField;
  sortOrder?: SortOrder;
  search?: string;
  role?: string;
  subscriptionStatus?: SubscriptionStatus;
}

export async function getDashboardStats(): Promise<DashboardStats> {
  const [userStats, subscriptionStats, transactionStats] = await Promise.all([
    prisma.user.aggregate({
      _count: { id: true },
      _sum: { imageCredits: true },
    }),
    prisma.subscription.groupBy({
      by: ["paymentStatus"],
      _count: { id: true },
    }),
    prisma.transaction.aggregate({
      _sum: {
        amount: true,
        credits: true,
      },
      _count: { id: true },
      where: {
        amount: {
          gt: 0,
        },
      },
    }),
  ]);

  // Get package stats with proper grouping and counting
  const packageStats = await prisma.transaction.groupBy({
    by: ["packageName"],
    _sum: {
      amount: true,
      credits: true,
    },
    _count: {
      id: true,
    },
    where: {
      amount: {
        gt: 0,
      },
    },
    orderBy: {
      _sum: {
        amount: "desc",
      },
    },
  });

  // Process package stats with tax calculations
  const processedPackageStats = packageStats.map((stat) => {
    const amountInCents = stat._sum?.amount || 0;
    const amount = amountInCents / 100; // Convert cents to dollars
    const basePrice = stat.packageName?.toLowerCase().includes("pro")
      ? 39.0 // Pro package base price
      : 19.0; // Regular package base price
    const baseAmount = basePrice * (stat._count?.id || 0);
    const taxAmount = amount - baseAmount;

    return {
      packageName: stat.packageName || "Unknown",
      amount,
      baseAmount,
      taxAmount,
      credits: stat._sum?.credits || 0,
      count: stat._count.id,
    };
  });

  // Get generation counts from different models
  const [interior, exterior, virtualStaging, editImage] = await Promise.all([
    prisma.interiorDesign.count(),
    prisma.exteriorDesign.count(),
    prisma.virtualStaging.count(),
    prisma.editImage.count(),
  ]);

  return {
    users: {
      total: userStats._count.id,
      totalCredits: userStats._sum.imageCredits || 0,
    },
    subscriptions: subscriptionStats.map((stat) => ({
      status: stat.paymentStatus,
      count: stat._count.id,
    })),
    transactions: {
      totalAmount: (transactionStats._sum.amount || 0) / 100, // Convert cents to dollars
      totalCredits: transactionStats._sum.credits || 0,
      count: transactionStats._count.id,
      byPackage: processedPackageStats,
    },
    generations: {
      interior,
      exterior,
      virtualStaging,
      editImage,
    },
  };
}

// Add type for complex sorting
type ComplexOrderBy = {
  [key: string]: {
    [key: string]: "asc" | "desc";
  };
};

// Improved buildOrderBy function with better type safety and null handling
function buildOrderBy(
  sortBy: SortField,
  sortOrder: SortOrder
): Prisma.UserOrderByWithRelationInput[] {
  // Handle nested subscription sorting
  if (sortBy.startsWith("subscription.")) {
    const field = sortBy.split(".")[1];
    return [
      {
        subscription: {
          [field]: sortOrder,
        },
      },
      { id: "asc" }, // Ensure consistent ordering
    ];
  }

  // Handle generation count sorting
  if (sortBy === "generationCount") {
    return [
      {
        interiorDesigns: { _count: sortOrder },
      },
      {
        exteriorDesigns: { _count: sortOrder },
      },
      {
        virtualStagings: { _count: sortOrder },
      },
      {
        editImages: { _count: sortOrder },
      },
      { id: "asc" }, // Ensure consistent ordering
    ];
  }

  // Handle basic field sorting
  return [
    { [sortBy]: sortOrder },
    { id: "asc" }, // Ensure consistent ordering
  ];
}

export async function getUsers({
  page = 1,
  limit = 10,
  sortBy = "lastCreditReset",
  sortOrder = "desc",
  search,
  role,
  subscriptionStatus,
}: {
  page?: number;
  limit?: number;
  sortBy?: SortField;
  sortOrder?: SortOrder;
  search?: string;
  role?: string;
  subscriptionStatus?: SubscriptionStatus;
}) {
  const skip = (page - 1) * limit;

  // Handle different sorting cases
  let orderBy: Prisma.UserOrderByWithRelationInput[] = [];

  if (sortBy.includes("subscription.")) {
    // Handle subscription related sorting
    const field = sortBy.split(".")[1];
    orderBy = [
      {
        subscription: {
          [field]: sortOrder,
        },
      },
      { id: "asc" }, // Secondary sort for consistency
    ];
  } else if (sortBy === "generationCount") {
    // Handle generation count sorting
    orderBy = [
      {
        interiorDesigns: {
          _count: sortOrder,
        },
      },
      {
        exteriorDesigns: {
          _count: sortOrder,
        },
      },
      {
        virtualStagings: {
          _count: sortOrder,
        },
      },
      {
        editImages: {
          _count: sortOrder,
        },
      },
      { id: "asc" }, // Secondary sort for consistency
    ];
  } else if (sortBy === "industry" || sortBy === "source") {
    // Handle nullable string fields
    orderBy = [
      {
        [sortBy]: {
          sort: sortOrder,
          nulls: "last",
        },
      },
      { id: "asc" }, // Secondary sort for consistency
    ];
  } else {
    // Handle regular field sorting
    orderBy = [
      { [sortBy]: sortOrder },
      { id: "asc" }, // Secondary sort for consistency
    ];
  }

  const where: any = {
    ...(search && {
      OR: [
        { name: { contains: search, mode: "insensitive" } },
        { email: { contains: search, mode: "insensitive" } },
      ],
    }),
    ...(role && { role }),
    ...(subscriptionStatus && {
      subscription: {
        paymentStatus: subscriptionStatus,
      },
    }),
  };

  // First, get all users for the current page with their counts
  const [users, total] = await Promise.all([
    prisma.user.findMany({
      where,
      orderBy,
      take: limit,
      skip,
      include: {
        subscription: true,
        _count: {
          select: {
            interiorDesigns: true,
            exteriorDesigns: true,
            virtualStagings: true,
            editImages: true,
          },
        },
      },
    }),
    prisma.user.count({ where }),
  ]);

  return {
    users,
    pagination: {
      total,
      pageCount: Math.ceil(total / limit),
    },
  };
}

// Add types for transactions and subscriptions sorting
export type TransactionSortField =
  | "amount"
  | "credits"
  | "createdAt"
  | "packageName"
  | "user.name"
  | "user.email";

export type AdminSubscriptionSortField =
  | "startDate"
  | "endDate"
  | "packageName"
  | "paymentStatus"
  | "user.name"
  | "user.email"
  | "user.imageCredits";

interface GetTransactionsParams {
  page?: number;
  limit?: number;
  sortBy?: TransactionSortField;
  sortOrder?: SortOrder;
  search?: string;
  packageName?: string;
  minAmount?: number;
  maxAmount?: number;
}

interface GetSubscriptionsParams {
  page?: number;
  limit?: number;
  sortBy?: AdminSubscriptionSortField;
  sortOrder?: SortOrder;
  search?: string;
  status?: SubscriptionStatus;
  packageName?: string;
}

// Add helper functions for building orderBy clauses
function buildTransactionOrderBy(
  sortBy: TransactionSortField,
  sortOrder: SortOrder
):
  | Prisma.TransactionOrderByWithRelationInput
  | Prisma.TransactionOrderByWithRelationInput[] {
  if (sortBy.startsWith("user.")) {
    const field = sortBy.split(".")[1];
    return [
      {
        user: {
          [field]: sortOrder,
        },
      },
      { createdAt: "desc" },
      { id: "asc" },
    ];
  }

  return [{ [sortBy]: sortOrder }, { createdAt: "desc" }, { id: "asc" }];
}

function buildSubscriptionOrderBy(
  sortBy: AdminSubscriptionSortField,
  sortOrder: SortOrder
): Prisma.SubscriptionOrderByWithRelationInput {
  // Handle user-related fields
  if (sortBy.startsWith("user.")) {
    const field = sortBy.split(".")[1];
    return {
      user: {
        [field]: sortOrder,
      },
    };
  }

  // Handle direct subscription fields
  return {
    [sortBy]: sortOrder,
  };
}

export async function getTransactions({
  page = 1,
  limit = 10,
  sortBy = "createdAt",
  sortOrder = "desc",
  search = "",
  packageName,
  minAmount,
  maxAmount,
}: GetTransactionsParams = {}) {
  const skip = (page - 1) * limit;

  const where: Prisma.TransactionWhereInput = {
    AND: [
      search
        ? {
            OR: [
              { user: { name: { contains: search, mode: "insensitive" } } },
              { user: { email: { contains: search, mode: "insensitive" } } },
              { packageName: { contains: search, mode: "insensitive" } },
            ],
          }
        : {},
      packageName ? { packageName } : {},
      minAmount ? { amount: { gte: minAmount } } : {},
      maxAmount ? { amount: { lte: maxAmount } } : {},
    ],
  };

  const total = await prisma.transaction.count({ where });
  const orderBy = buildTransactionOrderBy(
    sortBy as TransactionSortField,
    sortOrder
  );

  const transactions = await prisma.transaction.findMany({
    where,
    orderBy,
    skip,
    take: limit,
    select: {
      id: true,
      packageName: true,
      amount: true,
      credits: true,
      createdAt: true,
      user: {
        select: {
          name: true,
          email: true,
        },
      },
    },
  });

  return {
    transactions: transactions.map((t) => ({
      id: t.id,
      packageName: t.packageName || "Unknown",
      amount: t.amount,
      credits: t.credits,
      status: "COMPLETED",
      user: {
        name: t.user.name || "",
        email: t.user.email || "",
      },
      createdAt: t.createdAt,
    })),
    pagination: {
      total,
      pageCount: Math.ceil(total / limit),
      currentPage: page,
    },
  };
}

export async function getSubscriptions({
  page = 1,
  limit = 10,
  sortBy = "endDate",
  sortOrder = "desc",
  search = "",
  status,
  packageName,
}: GetSubscriptionsParams = {}) {
  const skip = (page - 1) * limit;

  const where: Prisma.SubscriptionWhereInput = {
    AND: [
      search
        ? {
            OR: [
              { user: { name: { contains: search, mode: "insensitive" } } },
              { user: { email: { contains: search, mode: "insensitive" } } },
              { packageName: { contains: search, mode: "insensitive" } },
            ],
          }
        : {},
      status ? { paymentStatus: status } : {},
      packageName ? { packageName } : {},
    ],
  };

  let orderBy: Prisma.SubscriptionOrderByWithRelationInput = {};

  // Handle user-related fields
  if (sortBy.startsWith("user.")) {
    const field = sortBy.split(".")[1];
    orderBy = {
      user: {
        [field]: sortOrder,
      },
    };
  } else {
    // Handle direct subscription fields
    orderBy = {
      [sortBy]: sortOrder,
    };
  }

  const [total, subscriptions] = await Promise.all([
    prisma.subscription.count({ where }),
    prisma.subscription.findMany({
      where,
      orderBy,
      skip,
      take: limit,
      include: {
        user: {
          select: {
            name: true,
            email: true,
            imageCredits: true,
          },
        },
      },
    }),
  ]);

  return {
    subscriptions: subscriptions.map((sub) => ({
      ...sub,
      user: {
        name: sub.user.name || "",
        email: sub.user.email || "",
        imageCredits: sub.user.imageCredits,
      },
    })),
    pagination: {
      total,
      pageCount: Math.ceil(total / limit),
      currentPage: page,
    },
  };
}

export async function updateSubscriptionStatus(
  id: string,
  status: SubscriptionStatus
) {
  return prisma.subscription.update({
    where: { id },
    data: { paymentStatus: status },
  });
}

// Helper function to map PredictionStatus to Generation status

interface BulkEmailFilters {
  roles?: Role[];
  subscriptionStatus?: SubscriptionStatus[];
  lastActiveWithin?: number;
  minCredits?: number;
  maxCredits?: number;
  minGenerations?: number;
  maxGenerations?: number;
  hasSubscription?: boolean;
}

// Update the EmailTemplateData type to include feedback template
interface EmailTemplateData {
  template: EmailTemplate;
  subject: string;
  message?: string;
  discountCode?: string;
  expiryDate?: Date;
  actionUrl?: string;
  actionText?: string;
  fromEmail?: string;
  fromName?: string;
}

// Constants for email sending
const BATCH_SIZE = 20; // Process 20 emails at a time
const BATCH_DELAY = 2000; // 2 seconds between batches
const RETRY_ATTEMPTS = 3; // Number of retries for failed emails
const RETRY_DELAY = 1000; // 1 second between retries

interface EmailSendingProgress {
  total: number;
  sent: number;
  failed: number;
  currentBatch: number;
  totalBatches: number;
}

async function sendEmailWithRetry(
  to: string,
  subject: string,
  template: EmailTemplate,
  props: any,
  fromEmail?: string,
  fromName?: string,
  retryCount = 0
): Promise<boolean> {
  try {
    await sendEmail({
      to,
      subject,
      template,
      props,
      config: fromEmail ? { fromEmail, fromName } : undefined,
    });
    return true;
  } catch (error) {
    if (retryCount < RETRY_ATTEMPTS) {
      console.log(
        `Retrying email to ${to} (Attempt ${retryCount + 1}/${RETRY_ATTEMPTS})`
      );
      await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY));
      return sendEmailWithRetry(
        to,
        subject,
        template,
        props,
        fromEmail,
        fromName,
        retryCount + 1
      );
    }
    throw error;
  }
}

export async function sendBulkEmailWithFilters(
  filters: EmailFiltersState,
  {
    template,
    subject,
    message,
    discountCode,
    expiryDate,
    actionUrl,
    actionText,
    fromEmail,
    fromName,
    creditAmount,
    incidentTitle,
    incidentMessage,
    discountPercentage,
    customFooterMessage,
  }: {
    template: EmailTemplate;
    subject: string;
    message?: string;
    discountCode?: string;
    expiryDate?: Date;
    actionUrl?: string;
    actionText?: string;
    fromEmail?: string;
    fromName?: string;
    creditAmount?: number;
    incidentTitle?: string;
    incidentMessage?: string;
    discountPercentage?: number;
    customFooterMessage?: string;
  }
) {
  try {
    const admin = await currentUser();
    if (!admin || admin.role !== "ADMIN") {
      throw new Error("Unauthorized access");
    }

    const filteredUsers = await getFilteredUsers(filters);

    if (filteredUsers.length === 0) {
      throw new Error("No users match the selected filters");
    }

    let successCount = 0;
    let failureCount = 0;

    for (let i = 0; i < filteredUsers.length; i++) {
      const user = filteredUsers[i];
      if (!user.email) {
        failureCount++;
        continue;
      }

      try {
        const success = await sendEmailWithRetry(
          user.email,
          subject,
          template,
          {
            name: user.name || "Designer",
            ...(template === "custom" && {
              message: message,
              customFooterMessage,
            }),
            ...(template === "marketing" && {
              offerTitle: subject,
              offerDescription: message,
              discountPercentage,
              customFooterMessage,
            }),
            ...(template === "announcement" && {
              title: subject,
              message,
              actionUrl,
              actionText,
              customFooterMessage,
            }),
            ...(template === "incident" && {
              discountCode: discountCode || "SORRY20", // Provide default value
              expiryDate: expiryDate?.toLocaleDateString("en-US", {
                year: "numeric",
                month: "long",
                day: "numeric",
              }) || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString("en-US", {
                year: "numeric",
                month: "long",
                day: "numeric",
              }), // Default to 7 days from now
              incidentTitle: incidentTitle || subject || "We Apologize for the Inconvenience",
              incidentMessage: incidentMessage || message || "We noticed an issue with your account. We sincerely apologize for this and want to make it right.",
              creditAmount: creditAmount || 10,
              discountPercentage: discountPercentage || 20,
              customFooterMessage,
            }),
            ...(template === "feedback" && {
              message,
              discountCode,
              expiryDate: expiryDate?.toLocaleDateString("en-US", {
                year: "numeric",
                month: "long",
                day: "numeric",
              }),
              title: subject,
              customFooterMessage,
            }),
            ...(template === "inactivity-feedback" && {
              tryNowUrl: actionUrl || "https://renovaitor.com/dashboard",
              message,
              title: subject,
              customFooterMessage,
            }),
          },
          fromEmail,
          fromName
        );

        if (success) {
          successCount++;
        } else {
          failureCount++;
        }
      } catch (error) {
        console.error(`Failed to send email to ${user.email}:`, error);
        failureCount++;
      }
    }

    return {
      success: true,
      emailsSent: successCount,
      emailsFailed: failureCount,
      totalAttempted: filteredUsers.length,
      matchedUsers: filteredUsers.length,
    };
  } catch (error) {
    console.error("Error sending bulk emails:", error);
    throw error;
  }
}

// Helper function to get filtered users
async function getFilteredUsers(filters: BulkEmailFilters) {
  const whereConditions: Prisma.UserWhereInput[] = [];

  // Add role filter
  if (filters.roles?.length) {
    whereConditions.push({ role: { in: filters.roles } });
  }

  // Add subscription status filter
  if (filters.subscriptionStatus?.length) {
    whereConditions.push({
      subscription: {
        paymentStatus: { in: filters.subscriptionStatus },
      },
    });
  }

  // Add last active filter
  if (filters.lastActiveWithin) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - filters.lastActiveWithin);
    whereConditions.push({
      lastCreditReset: {
        gte: cutoffDate,
      },
    });
  }

  // Add credit filters
  if (filters.minCredits !== undefined) {
    whereConditions.push({ imageCredits: { gte: filters.minCredits } });
  }
  if (filters.maxCredits !== undefined) {
    whereConditions.push({ imageCredits: { lte: filters.maxCredits } });
  }

  // Add subscription existence filter
  if (filters.hasSubscription !== undefined) {
    whereConditions.push({
      subscription: filters.hasSubscription ? { isNot: null } : { is: null },
    });
  }

  // Get users with their generation counts
  const users = await prisma.user.findMany({
    where: {
      AND: whereConditions,
      email: { not: null }, // Ensure users have email
    },
    select: {
      id: true,
      email: true,
      name: true,
      _count: {
        select: {
          interiorDesigns: true,
          exteriorDesigns: true,
          virtualStagings: true,
          editImages: true,
        },
      },
    },
  });

  // Filter by generation counts if specified
  if (
    filters.minGenerations !== undefined ||
    filters.maxGenerations !== undefined
  ) {
    return users.filter((user) => {
      const totalGenerations =
        user._count.interiorDesigns +
        user._count.exteriorDesigns +
        user._count.virtualStagings +
        user._count.editImages;

      const meetsMinRequirement =
        filters.minGenerations === undefined ||
        totalGenerations >= filters.minGenerations;

      const meetsMaxRequirement =
        filters.maxGenerations === undefined ||
        totalGenerations <= filters.maxGenerations;

      return meetsMinRequirement && meetsMaxRequirement;
    });
  }

  return users;
}

// Add export to the interface
export interface AdvancedUserFilters {
  roles?: string[];
  subscriptionStatus?: SubscriptionStatus[];
  lastActiveWithin?: number;
  minCredits?: number;
  maxCredits?: number;
  minGenerations?: number;
  maxGenerations?: number;
  hasSubscription?: boolean;
  search?: string;
}

// Add export to the function
export async function getUsersForBulkEmail(filters: AdvancedUserFilters) {
  const whereConditions: Prisma.UserWhereInput[] = [];

  // Add all filter conditions
  if (filters.roles?.length) {
    whereConditions.push({ role: { in: filters.roles as Role[] } });
  }

  if (filters.subscriptionStatus?.length) {
    whereConditions.push({
      subscription: {
        paymentStatus: { in: filters.subscriptionStatus },
      },
    });
  }

  if (filters.lastActiveWithin) {
    whereConditions.push({
      lastCreditReset: {
        gte: new Date(
          Date.now() - filters.lastActiveWithin * 24 * 60 * 60 * 1000
        ),
      },
    });
  }

  if (filters.minCredits !== undefined) {
    whereConditions.push({ imageCredits: { gte: filters.minCredits } });
  }

  if (filters.maxCredits !== undefined) {
    whereConditions.push({ imageCredits: { lte: filters.maxCredits } });
  }

  if (filters.hasSubscription !== undefined) {
    whereConditions.push({
      subscription: filters.hasSubscription ? { isNot: null } : { is: null },
    });
  }

  if (filters.search) {
    whereConditions.push({
      OR: [
        { name: { contains: filters.search, mode: "insensitive" } },
        { email: { contains: filters.search, mode: "insensitive" } },
      ],
    });
  }

  // Get users with counts for filtering
  const users = await prisma.user.findMany({
    where: {
      AND: whereConditions,
      email: { not: null },
    },
    select: {
      id: true,
      email: true,
      name: true,
      role: true,
      imageCredits: true,
      lastCreditReset: true,
      subscription: {
        select: {
          packageName: true,
          paymentStatus: true,
          endDate: true,
        },
      },
      _count: {
        select: {
          interiorDesigns: true,
          exteriorDesigns: true,
          virtualStagings: true,
          editImages: true,
        },
      },
    },
  });

  // Apply generation filters in memory
  let filteredUsers = users;
  if (
    filters.minGenerations !== undefined ||
    filters.maxGenerations !== undefined
  ) {
    filteredUsers = users.filter((user) => {
      const totalGenerations =
        user._count.interiorDesigns +
        user._count.exteriorDesigns +
        user._count.virtualStagings +
        user._count.editImages;

      const meetsMinRequirement =
        filters.minGenerations === undefined ||
        totalGenerations >= filters.minGenerations;

      const meetsMaxRequirement =
        filters.maxGenerations === undefined ||
        totalGenerations <= filters.maxGenerations;

      return meetsMinRequirement && meetsMaxRequirement;
    });
  }

  return {
    users: filteredUsers,
    total: filteredUsers.length,
  };
}

// Update the getEmailComponent function
const getEmailComponent = (template?: EmailTemplate) => {
  switch (template || "custom") {
    case "announcement":
      return AnnouncementEmail;
    case "notification":
      return NotificationEmail;
    case "marketing":
      return MarketingEmail;
    case "welcome":
      return WelcomeEmail;
    case "incident":
      return IncidentEmail;
    case "feedback":
      return FeedbackSurveyEmail;
    case "inactivity-feedback":
      return FeedbackInactivityEmail;
    case "custom":
    default:
      return CustomEmail;
  }
};

// Update the sendEmailToUser function signature
export async function sendEmailToUser(
  userId: string,
  subject: string,
  message?: string,
  discountCode?: string,
  expiryDate?: Date,
  template: EmailTemplate = "custom",
  {
    fromEmail,
    fromName,
    creditAmount,
    incidentTitle,
    incidentMessage,
    discountPercentage,
    customFooterMessage,
    actionUrl,
    actionText,
  }: {
    fromEmail?: string;
    fromName?: string;
    creditAmount?: number;
    incidentTitle?: string;
    incidentMessage?: string;
    discountPercentage?: number;
    customFooterMessage?: string;
    actionUrl?: string;
    actionText?: string;
  } = {}
) {
  try {
    const admin = await currentUser();
    if (!admin || admin.role !== "ADMIN") {
      throw new Error("Unauthorized access");
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { email: true, name: true },
    });

    if (!user || !user.email) {
      throw new Error("User not found or has no email");
    }

    if (template === "custom" && !message?.trim()) {
      throw new Error("Custom message is required for custom template");
    }

    const baseProps = {
      name: user.name || "Designer",
      customFooterMessage,
    };

    const templateProps = (() => {
      switch (template) {
        case "custom":
          return {
            ...baseProps,
            message,
          };
        case "marketing":
          return {
            ...baseProps,
            offerTitle: subject,
            offerDescription: message,
            discountCode,
            discountPercentage,
            expiryDate: expiryDate?.toLocaleDateString("en-US", {
              year: "numeric",
              month: "long",
              day: "numeric",
            }),
            actionUrl: actionUrl || "https://renovaitor.com/pricing",
            actionText: actionText || "View Offer",
          };
        case "announcement":
          return {
            ...baseProps,
            title: subject,
            message,
            actionUrl: actionUrl || "https://renovaitor.com",
            actionText: actionText || "Learn More",
          };
        case "notification":
          return {
            ...baseProps,
            title: subject,
            message,
            actionUrl: actionUrl || "https://renovaitor.com/dashboard",
            actionText: actionText || "View Details",
          };
        case "incident":
          return {
            ...baseProps,
            title: incidentTitle || subject,
            message: incidentMessage || message,
            discountCode,
            creditAmount,
            discountPercentage,
            expiryDate: expiryDate?.toLocaleDateString("en-US", {
              year: "numeric",
              month: "long",
              day: "numeric",
            }),
            actionUrl: actionUrl || "https://renovaitor.com/dashboard",
            actionText: actionText || "Go to Dashboard",
          };
        case "feedback":
          return {
            ...baseProps,
            title: subject,
            message,
            discountCode,
            discountPercentage,
            expiryDate: expiryDate?.toLocaleDateString("en-US", {
              year: "numeric",
              month: "long",
              day: "numeric",
            }),
            surveyUrl: actionUrl || "https://renovaitor.com/feedback",
          };
        case "inactivity-feedback":
          return {
            ...baseProps,
            title: subject,
            message,
            discountCode,
            discountPercentage,
            expiryDate: expiryDate?.toLocaleDateString("en-US", {
              year: "numeric",
              month: "long",
              day: "numeric",
            }),
            tryNowUrl: actionUrl || "https://renovaitor.com/dashboard",
          };
        default:
          return { ...baseProps, message };
      }
    })();

    await sendEmail({
      to: user.email,
      subject,
      template,
      props: templateProps,
      config: {
        fromEmail,
        fromName,
      },
    });

    revalidatePath("/admin-dashboard/users");
    return { success: true };
  } catch (error) {
    console.error("Error sending email to user:", error);
    throw error;
  }
}

// Update the validateEmailParams function
const validateEmailParams = (params: BulkEmailParams) => {
  if (!params.userIds.length) {
    throw new Error("No users selected");
  }
  if (!params.subject.trim()) {
    throw new Error("Email subject is required");
  }
  if (
    ![
      "announcement",
      "notification",
      "marketing",
      "welcome",
      "incident",
      "custom",
      "feedback",
      "inactivity-feedback",
    ].includes(params.template)
  ) {
    throw new Error("Invalid template selected");
  }
  if (params.template === "custom" && !params.message?.trim()) {
    throw new Error("Custom message is required for custom template");
  }
};

// Add missing BulkEmailParams interface
interface BulkEmailParams {
  userIds: string[];
  subject: string;
  template: EmailTemplate;
  message?: string;
  discountCode?: string;
  expiryDate?: Date;
}

export async function updateUserCredits(
  userId: string,
  credits: number
): Promise<UpdateUserCreditsResponse> {
  try {
    const admin = await currentUser();
    if (!admin || admin.role !== "ADMIN") {
      throw new Error("Unauthorized access");
    }

    if (credits < 0) {
      throw new Error("Credits cannot be negative");
    }

    const user = await prisma.user.update({
      where: { id: userId },
      data: {
        imageCredits: credits,
        lastCreditReset: new Date(),
      },
      select: {
        id: true,
        name: true,
        email: true,
        imageCredits: true,
      },
    });

    revalidatePath("/admin-dashboard/users");
    return user as UpdateUserCreditsResponse;
  } catch (error) {
    console.error("Error updating user credits:", error);
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      throw new Error("Database error while updating credits");
    }
    throw error;
  }
}

interface BulkUpdateResult {
  success: boolean;
  updatedUsers: number;
  matchedUsers: number;
}

export async function updateBulkUserCredits(
  filters: BulkEmailFilters,
  operation: "set" | "add" | "subtract",
  amount: number
): Promise<BulkUpdateResult> {
  try {
    const admin = await currentUser();
    if (!admin || admin.role !== "ADMIN") {
      throw new Error("Unauthorized access");
    }

    const filteredUsers = await getFilteredUsers(filters);

    const result = await prisma.user.updateMany({
      where: {
        id: { in: filteredUsers.map((user) => user.id) },
      },
      data: {
        imageCredits:
          operation === "set"
            ? amount
            : {
                [operation === "add" ? "increment" : "decrement"]: amount,
              },
      },
    });

    return {
      success: true,
      updatedUsers: result.count,
      matchedUsers: filteredUsers.length,
    };
  } catch (error) {
    console.error("Error updating bulk user credits:", error);
    throw error;
  }
}
