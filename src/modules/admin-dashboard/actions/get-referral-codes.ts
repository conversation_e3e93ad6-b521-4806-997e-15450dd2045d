import prisma from "@/lib/prisma/prisma";
import { ReferralCodeRow } from "@/app/(admin)/admin-dashboard/referral-codes/columns";
import { Prisma } from "@prisma/client";

interface GetReferralCodesOptions {
  page: number;
  limit: number;
  search?: string;
}

export async function getReferralCodes({
  page,
  limit,
  search,
}: GetReferralCodesOptions) {
  const skip = (page - 1) * limit;

  const where = search
    ? {
        OR: [
          { code: { contains: search, mode: Prisma.QueryMode.insensitive } },
          {
            user: {
              name: { contains: search, mode: Prisma.QueryMode.insensitive },
            },
          },
          {
            user: {
              email: { contains: search, mode: Prisma.QueryMode.insensitive },
            },
          },
        ],
      }
    : {};

  const [referralCodes, total] = await Promise.all([
    prisma.referralCode.findMany({
      where,
      skip,
      take: limit,
      orderBy: { createdAt: "desc" },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        referrals: {
          select: {
            id: true,
            status: true,
            creditedAt: true,
            referred: {
              select: {
                name: true,
                email: true,
              },
            },
          },
        },
      },
    }),
    prisma.referralCode.count({ where }),
  ]);

  return {
    referralCodes: referralCodes as ReferralCodeRow[],
    pagination: {
      total,
      pageCount: Math.ceil(total / limit),
    },
  };
}
