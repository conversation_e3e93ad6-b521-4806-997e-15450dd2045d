import prisma from "@/lib/prisma/prisma";
import { PredictionStatus } from "@/modules/dashboard/main-features/types";
import {
  Generation,
  GenerationType,
} from "@/app/(admin)/admin-dashboard/generations/columns";

export type GenerationsFilter = {
  type?: GenerationType;
  status?: "COMPLETED" | "PROCESSING" | "FAILED";
  search?: string;
  dateFrom?: Date;
  dateTo?: Date;
  sort?: {
    field: string;
    direction: "asc" | "desc";
  };
};

export type GenerationsPagination = {
  pageSize: number;
  pageIndex: number;
};

function mapPredictionStatus(
  status: PredictionStatus
): "COMPLETED" | "PROCESSING" | "FAILED" {
  switch (status) {
    case "SUCCEEDED":
      return "COMPLETED";
    case "FAILED":
      return "FAILED";
    default:
      return "PROCESSING";
  }
}

function getOrderBy(sort?: GenerationsFilter["sort"]) {
  if (!sort) return { createdAt: "desc" as const };

  switch (sort.field) {
    case "createdAt":
      return { createdAt: sort.direction };
    case "status":
      return { status: sort.direction };
    case "user":
      return { user: { name: sort.direction } };
    default:
      return { createdAt: "desc" as const };
  }
}

// First, let's add a type for the whereClause
type WhereClause = {
  OR?: Array<{
    user?: {
      is?: {
        OR: Array<{
          name?: { contains: string; mode: "insensitive" };
          email?: { contains: string; mode: "insensitive" };
        }>;
      };
    };
    prompt?: { contains: string; mode: "insensitive" };
  }>;
  createdAt?: { gte?: Date; lte?: Date };
  status?: PredictionStatus;
};

// First, let's define the proper types for our generation results
type PrismaGenerationWithUser = {
  id: string;
  status: PredictionStatus;
  prompt: string | null;
  outputImages?: string[];
  outputImage?: string | null;
  createdAt: Date;
  style?: string | null;
  room?: string | null;
  building?: string | null;
  upscaleAmount?: number | null;
  creativity?: number | null;
  user: {
    name: string | null;
    email: string | null;
  };
};

// Update the whereClause function to handle different models
function createWhereClause(
  filters: GenerationsFilter | undefined,
  hasPrompt: boolean
) {
  const baseClause = {
    ...(filters?.search && {
      OR: [
        {
          user: {
            is: {
              OR: [
                {
                  name: {
                    contains: filters.search,
                    mode: "insensitive" as const,
                  },
                },
                {
                  email: {
                    contains: filters.search,
                    mode: "insensitive" as const,
                  },
                },
              ],
            },
          },
        },
        ...(hasPrompt
          ? [
              {
                prompt: {
                  contains: filters.search,
                  mode: "insensitive" as const,
                },
              },
            ]
          : []),
      ],
    }),
    ...(filters?.dateFrom && { createdAt: { gte: filters.dateFrom } }),
    ...(filters?.dateTo && { createdAt: { lte: filters.dateTo } }),
    ...(filters?.status && {
      status:
        filters.status === "COMPLETED"
          ? ("SUCCEEDED" as PredictionStatus)
          : (filters.status as PredictionStatus),
    }),
  } as const;

  return baseClause;
}

// Add typeStats to the return type
type GetGenerationsReturn = {
  data: Generation[];
  pageCount: number;
  totalCount: number;
  typeStats: Record<GenerationType, number>;
};

export async function getGenerations(
  filters?: GenerationsFilter,
  pagination?: GenerationsPagination
): Promise<GetGenerationsReturn> {
  const { pageSize = 10, pageIndex = 0 } = pagination || {};
  const skip = pageIndex * pageSize;
  const orderBy = getOrderBy(filters?.sort);

  // Get counts with proper where clauses
  const [
    interiorsCount,
    exteriorsCount,
    virtualStagingsCount,
    backgroundRemovalsCount,
    upscaleImagesCount,
  ] = await Promise.all([
    prisma.interiorDesign.count({
      where: createWhereClause(filters, true),
    }),
    prisma.exteriorDesign.count({
      where: createWhereClause(filters, true),
    }),
    prisma.virtualStaging.count({
      where: createWhereClause(filters, true),
    }),
    prisma.backgroundRemoval.count({
      where: createWhereClause(filters, false),
    }),
    prisma.upscaleImage.count({
      where: createWhereClause(filters, false),
    }),
  ]);

  const totalCount =
    interiorsCount +
    exteriorsCount +
    virtualStagingsCount +
    backgroundRemovalsCount +
    upscaleImagesCount;

  // Calculate pagination parameters
  let itemsPerType = pageSize;
  let skipValues = {
    interior: 0,
    exterior: 0,
    virtualStaging: 0,
    backgroundRemoval: 0,
    upscaleImage: 0,
  };

  if (filters?.type) {
    // If filtering by type, use simple pagination
    skipValues[filters.type] = skip;
  } else {
    // If showing all types, take all items and handle pagination after combining
    itemsPerType = Math.ceil(pageSize * 3); // Take more items to ensure we have enough after sorting
  }

  // Fetch data with updated pagination
  const [
    interiors,
    exteriors,
    virtualStagings,
    backgroundRemovals,
    upscaleImages,
  ] = await Promise.all([
    !filters?.type || filters.type === "interior"
      ? prisma.interiorDesign.findMany({
          where: createWhereClause(filters, true) as any,
          orderBy,
          take: itemsPerType,
          skip: skipValues.interior,
          include: {
            user: {
              select: {
                name: true,
                email: true,
              },
            },
          },
        })
      : [],
    !filters?.type || filters.type === "exterior"
      ? prisma.exteriorDesign.findMany({
          where: createWhereClause(filters, true) as any,
          orderBy,
          take: itemsPerType,
          skip: skipValues.exterior,
          include: {
            user: {
              select: {
                name: true,
                email: true,
              },
            },
          },
        })
      : [],
    !filters?.type || filters.type === "virtualStaging"
      ? prisma.virtualStaging.findMany({
          where: createWhereClause(filters, true) as any,
          orderBy,
          take: itemsPerType,
          skip: skipValues.virtualStaging,
          include: {
            user: {
              select: {
                name: true,
                email: true,
              },
            },
          },
        })
      : [],
    !filters?.type || filters.type === "backgroundRemoval"
      ? prisma.backgroundRemoval.findMany({
          where: createWhereClause(filters, false) as any,
          orderBy,
          take: itemsPerType,
          skip: skipValues.backgroundRemoval,
          include: {
            user: {
              select: {
                name: true,
                email: true,
              },
            },
          },
        })
      : [],
    !filters?.type || filters.type === "upscaleImage"
      ? prisma.upscaleImage.findMany({
          where: createWhereClause(filters, false) as any,
          orderBy,
          take: itemsPerType,
          skip: skipValues.upscaleImage,
          include: {
            user: {
              select: {
                name: true,
                email: true,
              },
            },
          },
        })
      : [],
  ]);

  // Map and combine all results
  const mappedResults = [
    ...interiors.map((gen) => ({
      id: gen.id,
      type: "interior" as GenerationType,
      prompt: gen.prompt ?? undefined,
      outputUrl: gen.outputImages?.[0] || "",
      status: mapPredictionStatus(gen.status as PredictionStatus),
      user: {
        name: gen.user?.name || "",
        email: gen.user?.email || "",
      },
      metadata: {
        style: gen.style ?? undefined,
        room: gen.room ?? undefined,
      },
      createdAt: gen.createdAt,
    })),
    ...exteriors.map((gen) => ({
      id: gen.id,
      type: "exterior" as GenerationType,
      prompt: gen.prompt ?? undefined,
      outputUrl: gen.outputImages?.[0] || "",
      status: mapPredictionStatus(gen.status as PredictionStatus),
      user: {
        name: gen.user?.name || "",
        email: gen.user?.email || "",
      },
      metadata: {
        style: gen.style ?? undefined,
        building: gen.building ?? undefined,
      },
      createdAt: gen.createdAt,
    })),
    ...virtualStagings.map((gen) => ({
      id: gen.id,
      type: "virtualStaging" as GenerationType,
      prompt: gen.prompt ?? undefined,
      outputUrl: gen.outputImage || "",
      status: mapPredictionStatus(gen.status as PredictionStatus),
      user: {
        name: gen.user?.name || "",
        email: gen.user?.email || "",
      },
      metadata: {
        style: gen.style ?? undefined,
        room: gen.room ?? undefined,
      },
      createdAt: gen.createdAt,
    })),
    ...backgroundRemovals.map((gen) => ({
      id: gen.id,
      type: "backgroundRemoval" as GenerationType,
      outputUrl: gen.outputImage || "",
      status: mapPredictionStatus(gen.status as PredictionStatus),
      user: {
        name: gen.user?.name || "",
        email: gen.user?.email || "",
      },
      createdAt: gen.createdAt,
    })),
    ...upscaleImages.map((gen) => ({
      id: gen.id,
      type: "upscaleImage" as GenerationType,
      outputUrl: gen.outputImage || "",
      status: mapPredictionStatus(gen.status as PredictionStatus),
      user: {
        name: gen.user?.name || "",
        email: gen.user?.email || "",
      },
      metadata: {
        upscaleAmount: gen.upscaleAmount ?? undefined,
        creativity: gen.creativity ?? undefined,
      },
      createdAt: gen.createdAt,
    })),
  ];

  // Sort combined results and apply pagination
  const sortedResults = filters?.type
    ? mappedResults
    : mappedResults
        .sort((a, b) => {
          if (!filters?.sort)
            return b.createdAt.getTime() - a.createdAt.getTime();

          const { field, direction } = filters.sort;
          const multiplier = direction === "desc" ? -1 : 1;

          switch (field) {
            case "createdAt":
              return (
                multiplier * (b.createdAt.getTime() - a.createdAt.getTime())
              );
            case "status":
              return multiplier * a.status.localeCompare(b.status);
            case "user":
              return multiplier * a.user.name.localeCompare(b.user.name);
            default:
              return b.createdAt.getTime() - a.createdAt.getTime();
          }
        })
        .slice(skip, skip + pageSize); // Apply pagination after sorting

  const pageCount = Math.ceil(totalCount / pageSize);

  // Calculate type stats from the count results instead of mapped results
  const typeStats: Record<GenerationType, number> = {
    interior: interiorsCount,
    exterior: exteriorsCount,
    virtualStaging: virtualStagingsCount,
    backgroundRemoval: backgroundRemovalsCount,
    upscaleImage: upscaleImagesCount,
  };

  return {
    data: sortedResults,
    pageCount,
    totalCount,
    typeStats,
  };
}
