import { Role, SubscriptionStatus } from "@prisma/client";
import { EmailTemplateConfig } from "../components/email-templates";

export interface EmailFiltersState {
  roles: Role[];
  subscriptionStatus: SubscriptionStatus[];
  lastActiveWithin?: number;
  minCredits?: number;
  maxCredits?: number;
  minGenerations?: number;
  maxGenerations?: number;
  hasSubscription?: boolean;
}

export const AVAILABLE_ROLES: Role[] = ["USER", "ADMIN"];

export interface EmailTemplateData {
  template: EmailTemplateConfig["id"];
  subject: string;
  message?: string;
  discountCode?: string;
  expiryDate?: Date;
  actionUrl?: string;
  actionText?: string;
  fromEmail?: string;
  fromName?: string;
}
