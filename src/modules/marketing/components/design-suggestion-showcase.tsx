"use client";

import {
  Wand2,
  <PERSON><PERSON><PERSON>,
  Image as ImageIcon,
  Scan<PERSON><PERSON>ch,
  SwatchBook,
  Layout,
} from "lucide-react";
import { But<PERSON> } from "@/modules/ui/button";
import Link from "next/link";
import { useRef } from "react";
import { useInView } from "react-intersection-observer";
import { cn } from "@/modules/ui";

interface FeatureHighlight {
  title: string;
  description: string;
  Icon: React.ElementType;
}

const features: FeatureHighlight[] = [
  {
    title: "Smart Room Analysis",
    description: "Our AI analyzes your room's unique features and potential",
    Icon: ScanSearch,
  },
  {
    title: "Style Discovery",
    description: "Get curated design ideas that match your personal taste",
    Icon: SwatchBook,
  },
  {
    title: "Instant Previews",
    description: "See how new design ideas will look in your space",
    Icon: Layout,
  },
];

export function DesignSuggestionShowcase() {
  const containerRef = useRef<HTMLDivElement>(null);
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  return (
    <section
      id="design-suggestions"
      aria-labelledby="design-suggestions-heading"
      className="relative px-4 sm:px-24 md:px-48 overflow-hidden will-change-transform"
      ref={containerRef}
    >
      <div
        ref={ref}
        className={cn(
          "grid gap-8 lg:grid-cols-2 lg:gap-16 items-center",
          "opacity-0 translate-y-4",
          inView && "animate-fade-in-up"
        )}
      >
        {/* Content Section */}
        <div className="space-y-6">
          <div
            className={cn(
              "inline-flex items-center gap-2 rounded-lg bg-primary/5 border border-primary/10 px-3 py-1 text-sm",
              "opacity-0",
              inView && "animate-fade-in delay-100"
            )}
          >
            <Wand2 className="h-4 w-4 text-primary" aria-hidden="true" />
            <span className="text-primary">AI Interior Design Assistant</span>
          </div>

          <h2
            id="design-suggestions-heading"
            className={cn(
              "text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold tracking-tighter p-2",
              "bg-clip-text text-transparent bg-gradient-to-r from-foreground via-foreground/90 to-primary",
              "opacity-0",
              inView && "animate-fade-in delay-200"
            )}
          >
            Get Personalized Interior Design Ideas in Seconds
          </h2>

          <p
            className={cn(
              "text-base sm:text-lg text-muted-foreground max-w-prose",
              "opacity-0",
              inView && "animate-fade-in delay-300"
            )}
          >
            Discover your perfect interior design style with AI. Simply upload a
            photo of your room and get tailored design recommendations that
            bring your vision to life.
          </p>

          <div
            className={cn(
              "flex flex-col sm:flex-row gap-4",
              "opacity-0",
              inView && "animate-fade-in delay-400"
            )}
          >
            <Link
              href="/dashboard/design-suggestions/new"
              className="w-full sm:w-auto"
            >
              <Button
                variant="gradient-primary"
                size="lg"
                className="w-full sm:w-auto group relative overflow-hidden"
              >
                <span className="flex items-center gap-2 group-hover:translate-x-1 transition-transform duration-200">
                  Get Free Design Ideas Now
                  <ArrowRight className="w-4 h-4" />
                </span>
              </Button>
            </Link>
          </div>
        </div>

        {/* Image Section */}
        <div
          className={cn(
            "relative w-full aspect-[4/3] lg:aspect-[16/10] transform-gpu",
            "opacity-0 translate-y-4",
            inView && "animate-fade-in-up delay-200"
          )}
        >
          <div
            className="relative rounded-xl overflow-hidden shadow-2xl 
                       border border-primary/10 bg-background/50 backdrop-blur-sm
                       transform-gpu"
          >
            <video
              autoPlay
              muted
              loop
              playsInline
              className="w-full h-full object-cover"
            >
              <source src="/videos/design-suggestion.mp4" type="video/mp4" />
              Your browser does not support the video tag.
            </video>
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
          </div>

          <div
            className={cn(
              "absolute -bottom-6 -right-6 bg-background/80 backdrop-blur-sm",
              "rounded-lg p-4 shadow-lg border border-primary/10",
              "hidden sm:flex",
              "opacity-0",
              inView && "animate-fade-in delay-500"
            )}
          >
            <div className="flex items-center gap-3">
              <ImageIcon className="h-5 w-5 text-primary" />
              <span className="text-sm font-medium bg-clip-text text-transparent bg-gradient-to-r from-foreground to-primary">
                See Your Room&apos;s Potential
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Feature Highlights */}
      <div
        className={cn(
          "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 mt-16",
          "opacity-0 translate-y-4",
          inView && "animate-fade-in-up"
        )}
        role="list"
        aria-label="Key features"
      >
        {features.map((feature, i) => (
          <div
            key={feature.title}
            className={cn(
              "group relative p-6 rounded-xl",
              "bg-card/90 dark:bg-card/90",
              "border border-primary/10 dark:border-primary/20",
              "shadow-sm transition-all duration-300",
              "hover:shadow-md hover:border-primary/30",
              "opacity-0",
              inView && `animate-fade-in delay-${(i + 1) * 100}`
            )}
            role="listitem"
          >
            <div className="relative z-10">
              <feature.Icon
                className="h-12 w-12 text-primary mb-4 
                        transition-transform duration-300
                        group-hover:scale-110"
                aria-hidden="true"
              />
              <h3
                className="font-semibold mb-2 bg-gradient-to-br from-foreground to-foreground/70 
                        bg-clip-text text-transparent"
              >
                {feature.title}
              </h3>
              <p className="text-sm text-foreground/80 dark:text-foreground/70">
                {feature.description}
              </p>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}
