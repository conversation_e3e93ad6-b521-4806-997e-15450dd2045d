const HeroSkeleton = () => {
  return (
    <div className="min-h-[90vh] w-full animate-pulse">
      <div className="relative w-full pt-10 md:pt-20 px-4 sm:px-6 lg:px-8 bg-background dark:bg-background">
        <div className="relative max-w-7xl mx-auto pt-20 pb-24">
          <div className="flex flex-col items-center text-center max-w-4xl mx-auto mb-16">
            {/* Heading skeleton */}
            <div className="space-y-4 w-full">
              <div className="h-12 bg-card/70 dark:bg-card/70 rounded-lg w-3/4 mx-auto shadow-sm" />
              <div className="h-12 bg-card/70 dark:bg-card/70 rounded-lg w-2/4 mx-auto shadow-sm" />

              {/* Description skeleton */}
              <div className="mt-6 space-y-4">
                <div className="h-6 bg-card/70 dark:bg-card/70 rounded-lg w-2/3 mx-auto shadow-sm" />
                <div className="h-4 bg-card/70 dark:bg-card/70 rounded-lg w-1/2 mx-auto shadow-sm" />
              </div>

              {/* Tool badges skeleton */}
              <div className="flex flex-wrap justify-center gap-3 mt-8">
                {[...Array(6)].map((_, i) => (
                  <div
                    key={i}
                    className="h-10 w-32 bg-card/70 dark:bg-card/70 rounded-full shadow-sm border border-primary/10 dark:border-primary/20"
                  />
                ))}
              </div>
            </div>

            {/* CTA buttons skeleton */}
            <div className="flex flex-col sm:flex-row gap-4 mt-8 sm:mt-12">
              <div className="h-14 w-48 bg-card/70 dark:bg-card/70 rounded-lg shadow-sm" />
              <div className="h-14 w-48 bg-card/70 dark:bg-card/70 rounded-lg shadow-sm" />
            </div>
          </div>

          {/* Cards section skeleton - improved with better visual structure */}
          <div className="mt-20 space-y-12">
            {[...Array(3)].map((_, i) => (
              <div
                key={i}
                className="h-96 bg-card/70 dark:bg-card/70 rounded-2xl w-full shadow-md border border-primary/10 dark:border-primary/20 overflow-hidden"
              >
                <div className="flex flex-col md:flex-row h-full">
                  <div className="flex-1 p-8">
                    {/* Title and description area */}
                    <div className="flex items-start gap-4 mb-8">
                      <div className="w-16 h-16 rounded-xl bg-foreground/10 animate-pulse"></div>
                      <div className="space-y-2">
                        <div className="h-6 w-48 bg-foreground/10 rounded-md animate-pulse"></div>
                        <div className="h-4 w-64 bg-foreground/10 rounded-md animate-pulse"></div>
                      </div>
                    </div>

                    {/* Benefits grid */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {[...Array(4)].map((_, j) => (
                        <div key={j} className="flex items-center gap-3">
                          <div className="w-8 h-8 rounded-lg bg-foreground/10 animate-pulse"></div>
                          <div className="h-4 w-32 bg-foreground/10 rounded-md animate-pulse"></div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Video/image area */}
                  <div className="flex-1 bg-foreground/5 animate-pulse"></div>
                </div>
              </div>
            ))}

            {/* Professional tools card skeleton */}
            <div className="flex justify-center w-full px-4 sm:px-6 lg:px-8">
              <div className="w-full max-w-sm mx-auto">
                <div className="h-64 bg-card/70 dark:bg-card/70 rounded-xl w-full shadow-sm border border-primary/10 dark:border-primary/20 p-6">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="w-12 h-12 rounded-lg bg-foreground/10 animate-pulse"></div>
                    <div className="space-y-2">
                      <div className="h-5 w-40 bg-foreground/10 rounded-md animate-pulse"></div>
                      <div className="h-4 w-48 bg-foreground/10 rounded-md animate-pulse"></div>
                    </div>
                  </div>

                  <div className="space-y-3 mt-4">
                    {[...Array(3)].map((_, j) => (
                      <div key={j} className="flex items-center gap-3">
                        <div className="w-5 h-5 rounded-md bg-foreground/10 animate-pulse"></div>
                        <div className="h-4 w-32 bg-foreground/10 rounded-md animate-pulse"></div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSkeleton;
