"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { Lens } from "@/modules/ui/lens";
import { Maximize2, <PERSON>rk<PERSON>, Zap, ArrowRight } from "lucide-react";
import { GradientHeading, H3, BodyText, Lead } from "@/modules/ui/typography";
import { Button } from "@/modules/ui/button";
import Link from "next/link";

const features = [
  {
    icon: Maximize2,
    title: "4x Resolution Increase",
    description: "Transform low-resolution images into crystal-clear visuals",
  },
  {
    icon: Sparkles,
    title: "Enhanced Details",
    description: "Bring out the finest details in textures and materials",
  },
  {
    icon: Zap,
    title: "Optimized Colors",
    description: "Perfect color balance and contrast for stunning results",
  },
];

export default function ImageEnhancementShowcase() {
  const [hovering, setHovering] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  return (
    <section
      id="image-enhancement"
      aria-labelledby="enhancement-heading"
      className="bg-background dark:bg-background"
    >
      <div className=" mx-auto px-4 sm:px-6 lg:px-8">
        <GradientHeading
          id="enhancement-heading"
          className="text-center mb-6 md:mb-8 text-3xl md:text-4xl lg:text-5xl"
        >
          Experience Ultra-Clear Visuals
        </GradientHeading>

        <motion.div
          className="text-center max-w-2xl mx-auto mb-8 sm:mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <Lead className="text-base sm:text-lg md:text-xl text-foreground/80 dark:text-foreground/70">
            Transform your images with our advanced AI enhancement technology
          </Lead>
        </motion.div>

        <motion.div
          className=" mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <div
            className="relative aspect-video sm:aspect-[16/9] rounded-xl overflow-hidden"
            role="img"
            aria-label="Interactive image enhancement demo"
          >
            <Lens
              zoomFactor={2}
              lensSize={200}
              hovering={hovering}
              setHovering={setHovering}
            >
              <motion.img
                src="https://replicate.delivery/yhqm/gwh2Ww66BgaiMZP1G13jAVR5dXQ1RVzylVfhmmbBLPjQVM1JA/1337-91982160-92f8-11ef-9d10-7631d2862520.png"
                alt="Enhanced clarity, details lighting, and resolution"
                className={`w-full h-full object-cover rounded-xl shadow-lg ${
                  !imageLoaded ? "invisible" : ""
                }`}
                onLoad={() => setImageLoaded(true)}
                loading="lazy"
              />
            </Lens>
            {!imageLoaded && (
              <div
                className="absolute inset-0 animate-pulse bg-primary/5 rounded-xl"
                aria-hidden="true"
              />
            )}
            <div
              className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"
              aria-hidden="true"
            />
          </div>
          <BodyText className="text-center mt-6 text-base md:text-lg text-muted-foreground">
            Hover or tap to see the enhanced clarity and details
          </BodyText>
        </motion.div>

        <div
          className="mt-12 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 sm:gap-8 lg:max-w-5xl mx-auto px-0 sm:px-4"
          role="list"
          aria-label="Enhancement features"
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              className="p-6 rounded-xl
                       bg-card/90 dark:bg-card/90
                       border border-primary/10 dark:border-primary/20
                       hover:border-primary/30
                       transition-all duration-300
                       shadow-sm hover:shadow-md
                       group"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 + 0.6 }}
              role="listitem"
            >
              <feature.icon
                className="w-8 h-8 text-primary mb-4
                        group-hover:scale-110 transition-transform duration-300"
                aria-hidden="true"
              />
              <H3 className="!mb-3 text-xl md:text-2xl font-semibold text-foreground">
                {feature.title}
              </H3>
              <BodyText className="text-base text-foreground/80 dark:text-foreground/70">
                {feature.description}
              </BodyText>
            </motion.div>
          ))}
        </div>

        <motion.div
          className="mt-16 text-center max-w-3xl mx-auto flex flex-col items-center px-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.9 }}
        >
          <H3 className="mb-4 text-xl md:text-2xl lg:text-3xl font-semibold text-foreground">
            Instant Results, Professional Quality
          </H3>
          <BodyText className="text-base md:text-lg mb-8 text-foreground/80 dark:text-foreground/70">
            Our AI-powered enhancement technology automatically detects and
            improves every aspect of your images. Get professional-grade results
            in seconds, not hours.
          </BodyText>

          <Link
            href="/dashboard/tools/increase-resolution"
            aria-label="Try image enhancement tool"
          >
            <Button
              variant="gradient-primary"
              size="lg"
              shine
              glow
              className="text-[15px] sm:text-base px-6 sm:px-8 py-5 sm:py-6 group"
            >
              <span className="flex items-center gap-2 relative z-10">
                Try Image Enhancement
                <ArrowRight
                  className="w-4 h-4 group-hover:translate-x-1 transition-transform"
                  aria-hidden="true"
                />
              </span>
            </Button>
          </Link>
        </motion.div>
      </div>
    </section>
  );
}
