"use client";
import React from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/modules/ui/button";
import { cn } from "@/lib/utils";

const LoginButton: React.FC = () => {
  return (
    <Link href="/login">
      <div className="flex justify-center items-center flex-col">
        <Button className={cn(
          "bg-primary/90 hover:bg-primary/95",
          "border border-primary/20",
          "shadow-sm hover:shadow-md",
          "transition-all duration-300"
        )}>
          Login
        </Button>
      </div>
    </Link>
  );
};

export default LoginButton;
