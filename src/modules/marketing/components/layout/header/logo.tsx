"use client";
import React from "react";
import Image from "next/image";
import Link from "next/link";
import { ROUTES } from "@/lib/constants/routes";
import { useTheme } from "next-themes";

interface LogoProps {
  className?: string;
  width?: number;
  height?: number;
  href?: string;
}

export const Logo = ({ className, width = 40, height = 40, href }: LogoProps) => {
  const { resolvedTheme } = useTheme();
  const logoSrc =
    resolvedTheme === "dark"
      ? "/images/renovaitor-logo-gradient-dark.png"
      : "/images/renovaitor-logo-gradient-light.png";

  return (
    <Link
      href={href || ROUTES.HOME}
      prefetch={false}
      className={`flex items-center justify-center h-10 ${className || ''}`}
    >
      <div className="flex items-center justify-center w-10 h-10">
        <Image
          src={logoSrc}
          alt="Renovaitor Logo"
          width={width}
          height={height}
          priority
          className="object-contain"
        />
      </div>
    </Link>
  );
};

Logo.displayName = "Logo";
