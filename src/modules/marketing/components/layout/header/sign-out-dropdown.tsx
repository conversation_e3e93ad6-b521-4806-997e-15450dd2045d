"use client";
import React from "react";
import { useRouter } from "next/navigation";
import { DropdownMenuItem } from "@/modules/ui/dropdown-menu";
import { Check, LogOut } from "@/modules/ui/icons";
import { useToast } from "@/modules/ui/use-toast";
import { signOut } from "next-auth/react";

// Define a custom type for toast options
type CustomToastOptions = {
  title: React.ReactNode;
  description: string;
};

const SignOutDropdown = () => {
  const router = useRouter();
  const { toast } = useToast();

  const handleSignOut = async () => {
    await signOut({ redirect: false });

    // Use the custom type for toast options
    const toastOptions: CustomToastOptions = {
      title: (
        <div className="flex items-center">
          <Check className="mr-2 h-5 w-5 text-green-500 dark:text-green-400" />
          <span>Logout successful</span>
        </div>
      ),
      description: "You have been securely logged out from your account",
    };

    // Use type assertion to call toast with custom options
    (toast as (options: CustomToastOptions) => void)(toastOptions);

    router.push("/");
  };

  return (
    <DropdownMenuItem 
      onSelect={handleSignOut} 
      className="text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-950/20"
    >
      <LogOut className="mr-2 h-5 w-5" />
      Logout
    </DropdownMenuItem>
  );
};

SignOutDropdown.displayName = "SignOutDropdown";

export default SignOutDropdown;
