"use client";
import Link from "next/link";
import { motion } from "framer-motion";
import {
  NavigationMenu,
  NavigationMenuList,
  NavigationMenuLink,
  NavigationMenuItem,
} from "@/modules/ui/navigation-menu";
import { cn } from "@/lib/utils";
import { usePathname } from "next/navigation";

const navigationItems = [
  { href: "/dashboard", label: "Tools" },
  { href: "/pricing", label: "Pricing" },
  { href: "/#designs", label: "Designs" },
];

export default function Navigation() {
  const pathname = usePathname();

  return (
    <div className="flex w-full justify-center z-[10]">
      <NavigationMenu className="hidden lg:flex">
        <NavigationMenuList className="gap-2">
          {navigationItems.map((item) => (
            <NavigationMenuItem key={item.href}>
              <NavigationMenuLink asChild>
                <Link
                  href={item.href}
                  className={cn(
                    "group relative px-5 py-2 text-sm font-medium",
                    "transition-all duration-300 ease-out",
                    "rounded-full",
                    "border border-transparent",
                    pathname === item.href
                      ? "text-primary bg-primary/10 border-primary/30"
                      : "text-foreground/70 hover:text-foreground hover:bg-primary/5 hover:border-primary/20"
                  )}
                  prefetch={false}
                >
                  {/* Background glow effect */}
                  <motion.div
                    className={cn(
                      "absolute inset-0 rounded-full opacity-0",
                      "bg-gradient-to-r from-primary/15 via-secondary/10 to-primary/15",
                      "group-hover:opacity-100 transition-opacity duration-300",
                      "blur-sm"
                    )}
                    layoutId="nav-glow"
                  />

                  {/* Inner gradient border */}
                  <motion.div
                    className={cn(
                      "absolute inset-0 rounded-full opacity-0",
                      "group-hover:opacity-100 transition-opacity duration-300",
                      pathname === item.href ? "opacity-100" : ""
                    )}
                    style={{
                      background: `
                        linear-gradient(to right, transparent, rgba(var(--primary), 0.15), transparent)
                      `,
                    }}
                    layoutId="nav-border"
                  />

                  {/* Text content with hover effect */}
                  <span className="relative z-10 inline-flex items-center">
                    {item.label}

                    {/* Animated dot for active state */}
                    {pathname === item.href && (
                      <motion.span
                        className="ml-2 h-1.5 w-1.5 rounded-full bg-primary"
                        layoutId="nav-dot"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{
                          type: "spring",
                          stiffness: 300,
                          damping: 30,
                        }}
                      />
                    )}
                  </span>

                  {/* Hover shine effect */}
                  <motion.div
                    className="absolute inset-0 -z-10 rounded-full opacity-0 
                             group-hover:opacity-100 transition-opacity duration-300"
                    style={{
                      background: `
                        linear-gradient(
                          45deg,
                          transparent 25%,
                          rgba(var(--primary), 0.08) 50%,
                          transparent 75%
                        )
                      `,
                      backgroundSize: "200% 200%",
                    }}
                    animate={{
                      backgroundPosition: ["0% 0%", "200% 200%"],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "linear",
                    }}
                  />
                </Link>
              </NavigationMenuLink>
            </NavigationMenuItem>
          ))}
        </NavigationMenuList>
      </NavigationMenu>
    </div>
  );
}

Navigation.displayName = "Navigation";
