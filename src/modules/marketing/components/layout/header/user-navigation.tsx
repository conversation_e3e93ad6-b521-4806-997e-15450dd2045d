"use client";

import { useSession } from "next-auth/react";
import Link from "next/link";
import { motion } from "framer-motion";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuLabel,
} from "@/modules/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/modules/ui/avatar";
import { signOut } from "next-auth/react";
import {
  Monitor,
  Sun,
  Moon,
  Laptop,
  UserCircle,
  Settings,
  LogOut,
  Zap,
  LayoutDashboard,
  Crown,
  ImagePlus,
} from "lucide-react";
import { useTheme } from "next-themes";
import { cn } from "@/lib/utils";
import { useSubscription } from "@/modules/payments/hooks/use-subscription";
import { PaywallDialog } from "@/modules/payments/components/paywall-dialog";

const UserNavigation = () => {
  const { data: session, status } = useSession();
  const { setTheme, theme } = useTheme();
  const { isSubscribed, subscription, isLoading } = useSubscription();

  const hasSubscription = isSubscribed || subscription;

  if (status === "loading" || isLoading) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="h-10 w-10 rounded-full bg-primary/10 animate-pulse"
      />
    );
  }

  if (status === "unauthenticated") {
    return (
      <Link
        href="/login"
        className={cn(
          "text-sm font-medium",
          "px-5 py-2.5 rounded-full",
          "bg-primary/10 hover:bg-primary/15",
          "text-foreground/90 hover:text-foreground",
          "transition-all duration-200",
          "border border-primary/20 hover:border-primary/30"
        )}
      >
        Sign in
      </Link>
    );
  }

  return (
    <div className="flex items-center gap-2">
      {/* User Menu */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className={cn(
              "focus:outline-none",
              "rounded-full p-1",
              "hover:border-primary/30",
              "transition-all duration-200",
            )}
          >
            <Avatar className="h-8 w-8 transition-all duration-200">
              <AvatarImage
                src={session?.user?.image ?? ""}
                alt={session?.user?.name ?? "User avatar"}
                className="object-cover"
              />
              <AvatarFallback className="bg-primary/10 text-foreground/90">
                {session?.user?.name?.charAt(0).toUpperCase() ?? "U"}
              </AvatarFallback>
            </Avatar>
          </motion.button>
        </DropdownMenuTrigger>

        <DropdownMenuContent
          align="end"
          className="w-56 p-2 mt-2 border border-primary/20"
          sideOffset={8}
        >
          <DropdownMenuLabel className="font-normal">
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none">
                {session?.user?.name}
              </p>
              <p className="text-xs leading-none text-muted-foreground">
                {session?.user?.email}
              </p>
              {hasSubscription && (
                <div className="flex flex-col gap-1 mt-1">
                  <span className="inline-flex items-center gap-1 text-xs font-medium text-primary bg-primary/5 px-2 py-0.5 rounded-full">
                    <Crown className="w-3 h-3" />
                    Pro Member
                  </span>
                </div>
              )}
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator className="bg-primary/10" />

          <DropdownMenuItem asChild>
            <Link href="/dashboard" className="flex items-center">
              <LayoutDashboard className="mr-2 h-4 w-4" />
              Dashboard
            </Link>
          </DropdownMenuItem>

          <DropdownMenuItem asChild>
            <Link href="/dashboard/designs" className="flex items-center">
              <ImagePlus className="mr-2 h-4 w-4" />
              My Designs
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link href="/dashboard/profile" className="flex items-center">
              <UserCircle className="mr-2 h-4 w-4" />
              Profile
            </Link>
          </DropdownMenuItem>
          <DropdownMenuSeparator className="bg-primary/10" />

          <DropdownMenuSub>
            <DropdownMenuSubTrigger className="flex items-center">
              <Monitor className="mr-2 h-4 w-4" />
              Theme
            </DropdownMenuSubTrigger>
            <DropdownMenuSubContent className="border border-primary/20">
              {[
                { value: "light", label: "Light", icon: Sun },
                { value: "dark", label: "Dark", icon: Moon },
                { value: "system", label: "System", icon: Laptop },
              ].map(({ value, label, icon: Icon }) => (
                <DropdownMenuItem
                  key={value}
                  onClick={() => setTheme(value)}
                  className={cn(
                    "flex items-center",
                    theme === value && "bg-primary/10"
                  )}
                >
                  <Icon className="mr-2 h-4 w-4" />
                  {label}
                </DropdownMenuItem>
              ))}
            </DropdownMenuSubContent>
          </DropdownMenuSub>

          <DropdownMenuSeparator className="bg-primary/10" />

          <DropdownMenuItem
            onClick={() => signOut()}
            className="flex items-center text-red-600 focus:text-red-600 dark:text-red-400 dark:focus:text-red-400"
          >
            <LogOut className="mr-2 h-4 w-4" />
            Sign out
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default UserNavigation;
