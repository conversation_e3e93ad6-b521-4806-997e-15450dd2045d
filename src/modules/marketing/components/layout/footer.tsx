"use client";
import * as React from "react";
import { Logo } from "@/modules/marketing/components/layout/header/logo";
import { cn } from "@/modules/ui/utils/cn";
import { ModeToggle } from "../mode-toggle";
import Link from "next/link";

const currentYear = new Date().getFullYear();
const copyrightText = `Copyright © ${currentYear} renovaitor.com - All rights reserved`;

const Footer: React.FC<{ className?: string }> = React.memo(({ className }) => {
  return (
    <footer
      className={cn(
        className,
        "relative bg-background/90 backdrop-blur-sm border-t border-primary/15"
      )}
    >
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-background/70 to-background" />

      <div className="container relative z-10 flex flex-col items-center justify-between gap-4 py-10 md:h-24 md:flex-row md:py-0">
        <div className="flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0">
          <Logo />
          <p className="text-center text-sm leading-loose md:text-left text-foreground/80">
            {copyrightText}
          </p>
        </div>

        <nav className="flex gap-4">
          <Link
            href="/privacy"
            className="text-sm text-foreground/80 hover:text-primary 
                     transition-colors duration-300"
          >
            Privacy Policy
          </Link>
          <Link
            href="/terms"
            className="text-sm text-foreground/80 hover:text-primary 
                     transition-colors duration-300"
          >
            Terms of Service
          </Link>
        </nav>

        <div
          className="p-1 rounded-lg bg-background/60 backdrop-blur-sm 
                      border border-primary/15 hover:border-primary/25 
                      transition-colors duration-300"
        >
          <ModeToggle />
        </div>
      </div>
    </footer>
  );
});

Footer.displayName = "Footer";

export default Footer;
