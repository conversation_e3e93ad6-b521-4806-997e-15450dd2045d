"use client";

import React, { useEffect, useRef } from "react";
import Navigation from "@/modules/marketing/components/layout/header/navigation";
import UserNavigation from "@/modules/marketing/components/layout/header/user-navigation";
import { Logo } from "@/modules/marketing/components/layout/header/logo";
import { cn } from "@/lib/utils";
import { Button } from "@/modules/ui/button";
import { useSession } from "next-auth/react";
import Link from "next/link";

const Header = () => {
  const [hasScrolled, setHasScrolled] = React.useState(false);
  const { status } = useSession();
  const observerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setHasScrolled(!entry.isIntersecting);
      },
      { threshold: 1.0 }
    );

    if (observerRef.current) {
      observer.observe(observerRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const renderAuthButton = () => {
    switch (status) {
      case "authenticated":
        return (
          <div className="bg-primary/10 rounded-full border border-primary/20 p-1">
            <UserNavigation />
          </div>
        );
      case "unauthenticated":
        return (
          <Link href="/login" className="flex items-center gap-2">
            <Button
              variant="gradient-primary"
              size="sm"
              className="px-6 py-2 transform-gpu hover:scale-[1.02] active:scale-[0.98] transition-all duration-300 ease-out shadow-sm"
            >
              Login
            </Button>
          </Link>
        );
      default:
        return (
          <div className="w-[100px] h-9 bg-primary/5 animate-pulse rounded-full" />
        );
    }
  };

  return (
    <>
      <div ref={observerRef} className="absolute top-0 h-1" />
      <header
        className={cn(
          "fixed left-0 right-0 z-40 w-full transform-gpu flex flex-col",
          "transition-all duration-300 ease-in-out",
          "top-0"
        )}
      >
        {/* Main Header */}
        <div
          className={cn(
            "w-full transform-gpu bg-background/90 backdrop-blur-xl",
            "transition-all duration-300 ease-in-out relative",
            hasScrolled ? "shadow-sm " : ""
          )}
        >
          <div className="absolute inset-x-0 -bottom-px h-px bg-gradient-to-r from-transparent via-primary/10 to-transparent opacity-60" />

          <div
            className={cn(
              "flex items-center w-full px-4 md:px-6 lg:px-8 relative transform-gpu",
              "transition-all duration-300 ease-in-out",
              hasScrolled ? "h-14" : "h-16"
            )}
          >
            {/* Left Section - Logo */}
            <div className="flex-1 flex justify-start items-center">
              <div className="h-10 flex items-center">
                <Logo
                  width={40}
                  height={40}
                  className="transition-all duration-300"
                />
              </div>
            </div>

            {/* Center Section - Navigation (desktop only) */}
            <div className="hidden lg:flex flex-1 justify-center">
              <div
                className={cn(
                  "transform-gpu transition-all duration-300 ease-in-out",
                  "backdrop-blur-sm px-4 py-2 rounded-full ",
                  hasScrolled ? "scale-95" : "scale-100"
                )}
              >
                <Navigation />
              </div>
            </div>

            {/* Right Section - Auth Button */}
            <div className="flex-1 flex justify-end items-center space-x-4">
              {renderAuthButton()}
            </div>
          </div>
        </div>
      </header>
    </>
  );
};

export default Header;
