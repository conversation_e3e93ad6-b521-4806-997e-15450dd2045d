"use client";
import React, { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/modules/ui/tabs";
import { Button } from "@/modules/ui/button";
import { Compare } from "@/modules/ui/compare";
import { getPublicDesigns } from "@/modules/dashboard/main-features/actions/get-public-designs";
import {
  H1,
  H2,
  H3,
  BodyText,
  GradientHeading,
  Small,
  Lead,
} from "@/modules/ui/typography";
import { Shuffle } from "lucide-react";
import { GradientBackground } from "@/modules/ui/gradient-background";
import Link from "next/link";
import type { DesignType } from "@/modules/dashboard/main-features/actions/get-public-designs";

interface PublicDesign {
  id: string;
  inputImage: string;
  outputImage: string;
  type: string;
  createdAt: Date;
  averageRating?: number | null;
  totalRatings?: number | null;
}

interface FeatureContentProps {
  activeTab: string;
  activeDesign: PublicDesign | null;
  isLoading: boolean;
  handleShuffle: () => void;
  designsByType: Record<string, PublicDesign[]>;
}

const features = [
  {
    title: "Virtual Staging",
    description:
      "Transform empty spaces into perfectly furnished rooms. Our AI understands your space and automatically suggests optimal furniture placement.",
    video: "/api/placeholder/640/360",
    benefits: [
      "Perfect furniture placement",
      "Maintains structural elements",
      "Multiple style options",
      "Realistic lighting & shadows",
    ],
    queryType: "Virtual Staging",
  },
  {
    title: "Interior Design",
    description:
      "Turn sketches and basic 3D models into photorealistic interiors. Ideal for visualizing design concepts quickly.",
    video: "/api/placeholder/640/360",
    benefits: [
      "Sketch to reality conversion",
      "Texture enhancement",
      "Lighting simulation",
      "Material visualization",
    ],
    queryType: "Interior Design",
  },
  {
    title: "Exterior Design",
    description:
      "Transform architectural sketches and models into stunning exterior visualizations with precise detail.",
    video: "/api/placeholder/640/360",
    benefits: [
      "Architectural accuracy",
      "Environmental integration",
      "Material rendering",
      "Landscape visualization",
    ],
    queryType: "Exterior Design",
  },
  {
    title: "Upscale Image",
    description:
      "Add clarity and detail to your images while increasing resolution for crystal-clear results.",
    video: "/api/placeholder/640/360",
    benefits: [
      "4x resolution increase",
      "Detail enhancement",
      "Noise reduction",
      "Color optimization",
    ],
    queryType: "Upscale Image",
  },
];

const ratingOrderBy = [
  { averageRating: { sort: 'desc', nulls: 'last' } },
  { totalRatings: 'desc' },
  { createdAt: 'desc' },
];

const fallbackOrderBy = { totalRatings: "desc" };
const finalFallbackOrderBy = { createdAt: "desc" };

const DESIGNS_PER_FEATURE = 10;

export default function FeatureShowcase() {
  const [designsByType, setDesignsByType] = useState<
    Record<string, PublicDesign[]>
  >({});
  const [activeDesign, setActiveDesign] = useState<PublicDesign | null>(null);
  const [activeTab, setActiveTab] = useState(features[0].title);
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>(
    features.reduce((acc, feature) => {
      acc[feature.title] = true;
      return acc;
    }, {} as Record<string, boolean>)
  );
  const [mobileDesignIndices, setMobileDesignIndices] = useState<
    Record<string, number>
  >({});

  const fetchFeatureDesigns = useCallback(async (featureType: string) => {
    const feature = features.find(f => f.title === featureType);
    if (!feature) {
      console.error(`Feature not found: ${featureType}`);
      setLoadingStates(prev => ({ ...prev, [featureType]: false }));
      return;
    }

    console.log(`Fetching designs for ${featureType}...`);
    setLoadingStates(prev => ({ ...prev, [featureType]: true }));

    try {
      const designs = await getPublicDesigns({
        type: feature.queryType as DesignType,
        limit: DESIGNS_PER_FEATURE,
        orderBy: { createdAt: 'desc' }
      });

      console.log(`Fetched ${designs.length} designs for ${featureType}`);
      setDesignsByType(prev => ({ ...prev, [featureType]: designs }));

      setMobileDesignIndices(prev => ({
        ...prev,
        [featureType]: prev[featureType] ?? 0
      }));

      if (featureType === activeTab && designs.length > 0 && !activeDesign) {
         setActiveDesign(designs[0]);
      } else if (featureType === activeTab && designs.length === 0) {
         setActiveDesign(null);
      }

    } catch (error) {
      console.error(`Error fetching designs for ${featureType}:`, error);
      setDesignsByType(prev => ({ ...prev, [featureType]: [] }));
    } finally {
      setLoadingStates(prev => ({ ...prev, [featureType]: false }));
    }
  }, [activeTab, activeDesign]);

  useEffect(() => {
    const initialFeatureTitle = features[0].title;
    if (loadingStates[initialFeatureTitle]) {
        fetchFeatureDesigns(initialFeatureTitle);
    }
  }, []);

  useEffect(() => {
    const designsForActiveTab = designsByType[activeTab];
    if (designsForActiveTab && designsForActiveTab.length > 0) {
      const currentActiveDesignStillValid = designsForActiveTab.some(d => d.id === activeDesign?.id);
      if (!currentActiveDesignStillValid || !activeDesign) {
          const currentMobileIndex = mobileDesignIndices[activeTab] ?? 0;
          const designToShow = designsForActiveTab[currentMobileIndex] ?? designsForActiveTab[0];
          setActiveDesign(designToShow);
      }
    } else if (!loadingStates[activeTab]) {
        setActiveDesign(null);
    }
  }, [activeTab, designsByType, activeDesign, mobileDesignIndices, loadingStates]);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    if (!designsByType[value] && loadingStates[value] !== false) {
      fetchFeatureDesigns(value);
    }
  };

  const handleShuffle = useCallback(() => {
    const currentDesigns = designsByType[activeTab];
    const isLoading = loadingStates[activeTab];

    if (isLoading || !currentDesigns || currentDesigns.length <= 1) {
      console.log("Cannot shuffle:", { isLoading, currentDesignsLength: currentDesigns?.length });
      return;
    }

    let newIndex;
    const currentIndex = currentDesigns.findIndex(d => d.id === activeDesign?.id);

    if (currentIndex === -1 || currentIndex === currentDesigns.length - 1) {
      newIndex = 0;
    } else {
      newIndex = currentIndex + 1;
    }

    console.log(`Shuffling ${activeTab}: index ${currentIndex} -> ${newIndex}`);
    setActiveDesign(currentDesigns[newIndex]);

    setMobileDesignIndices(prev => ({ ...prev, [activeTab]: newIndex }));

  }, [activeTab, activeDesign, designsByType, loadingStates]);

  const handleMobileShuffle = useCallback((featureType: string) => {
    const currentDesigns = designsByType[featureType];
    const isLoading = loadingStates[featureType];

    if (isLoading || !currentDesigns || currentDesigns.length <= 1) {
       console.log("Cannot mobile shuffle:", { isLoading, currentDesignsLength: currentDesigns?.length });
      return;
    }

    setMobileDesignIndices(prevIndices => {
      const currentIndex = prevIndices[featureType] ?? 0;
      const nextIndex = (currentIndex + 1) % currentDesigns.length;
      console.log(`Mobile shuffle ${featureType}: index ${currentIndex} -> ${nextIndex}`);

      if (featureType === activeTab) {
          setActiveDesign(currentDesigns[nextIndex]);
      }

      return { ...prevIndices, [featureType]: nextIndex };
    });

  }, [designsByType, loadingStates, activeTab]);

  const isFeatureLoading = (featureType: string) => loadingStates[featureType] ?? true;

  return (
    <section
      id="designs"
      aria-labelledby="designs-heading"
      className="relative w-full px-4 sm:px-6 lg:px-8 max-w-7xl py-4 sm:py-12 md:py-20 mx-auto"
    >
      <GradientHeading
        id="designs-heading"
        className="text-center mb-6 md:mb-8 text-3xl md:text-4xl lg:text-5xl"
      >
        See AI Transformations in Real-Time
      </GradientHeading>

      <Lead className="text-center max-w-2xl mx-auto mb-12 text-base sm:text-lg md:text-xl text-foreground/80">
        Watch our AI transform spaces instantly. Slide to compare before and
        after results from real projects.
      </Lead>

      <div className="hidden lg:block">
        <Tabs
          value={activeTab}
          onValueChange={handleTabChange}
          aria-label="Feature showcase tabs"
        >
          <TabsList
            className="grid w-full grid-cols-4 mb-8
                     bg-card dark:bg-card/90 shadow-md
                     backdrop-blur-xl border border-primary/10 dark:border-primary/20
                     rounded-2xl h-14"
            aria-label="Feature categories"
          >
            {features.map((feature) => (
              <TabsTrigger
                key={feature.title}
                value={feature.title}
                className="relative h-full data-[state=active]:text-primary data-[state=active]:font-medium dark:data-[state=active]:text-primary/90 dark:hover:text-primary/80 transition-colors"
                aria-label={`Show ${feature.title.toLowerCase()} examples`}
              >
                {feature.title}
              </TabsTrigger>
            ))}
          </TabsList>
          <FeatureContent
            activeTab={activeTab}
            activeDesign={activeDesign}
            isLoading={isFeatureLoading(activeTab)}
            handleShuffle={handleShuffle}
            designsByType={designsByType}
          />
        </Tabs>
      </div>

      <div className="lg:hidden space-y-12" role="tablist">
        {features.map((feature) => {
          const featureDesigns = designsByType[feature.title] || [];
          const designIndex = mobileDesignIndices[feature.title] ?? 0;
          const safeIndex = designIndex < featureDesigns.length ? designIndex : 0;
          const displayDesign = featureDesigns.length > 0 ? featureDesigns[safeIndex] : null;

          const currentFeatureLoading = isFeatureLoading(feature.title);

          return (
            <motion.div
              key={displayDesign?.id || feature.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="space-y-6"
              role="tabpanel"
              aria-label={feature.title}
            >
              <H3 className="text-xl md:text-2xl">{feature.title}</H3>
              <div className="space-y-4">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={displayDesign?.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="relative aspect-video rounded-xl overflow-hidden bg-gradient-to-b from-background/90 to-background/70 shadow-lg"
                  >
                    {displayDesign && displayDesign.inputImage && displayDesign.outputImage ? (
                      <Compare
                        firstImage={displayDesign.inputImage}
                        secondImage={displayDesign.outputImage}
                        className="w-full h-full"
                        firstImageClassName="object-cover"
                        secondImageClassName="object-cover"
                        slideMode="hover"
                        showHandlebar={true}
                        aria-label={`Comparison slider for ${feature.title}. ${currentFeatureLoading ? 'Loading example.' : 'Example loaded.'}`}
                      >
                      </Compare>
                    ) : (
                      <div
                        className="flex items-center justify-center h-full bg-muted"
                        aria-label={currentFeatureLoading ? "Loading example" : "No example available"}
                      >
                        <p className="text-muted-foreground">
                          {currentFeatureLoading ? "Loading..." : "No examples available yet"}
                        </p>
                      </div>
                    )}
                  </motion.div>
                </AnimatePresence>

                <Button
                  variant="gradient-subtle"
                  size="sm"
                  shine
                  onClick={() => handleMobileShuffle(feature.title)}
                  disabled={currentFeatureLoading || (featureDesigns.length ?? 0) <= 1}
                  className="w-full bg-background/80 backdrop-blur-sm border border-primary/10 shadow-lg hover:bg-background/90"
                  aria-label={`Show another ${feature.title.toLowerCase()} example`}
                >
                  <Shuffle className="w-4 h-4 mr-2" aria-hidden="true" />
                  {currentFeatureLoading ? "Loading..." : "Show Another Example"}
                </Button>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="space-y-4 pt-2"
                >
                  <BodyText className="text-muted-foreground">
                    {feature.description}
                  </BodyText>
                  <div
                    className="grid grid-cols-2 gap-2"
                    role="list"
                    aria-label={`${feature.title} benefits`}
                  >
                    {feature.benefits.map((benefit, index) => (
                      <motion.div
                        key={index}
                        role="listitem"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3, delay: 0.4 + index * 0.1 }}
                        className="flex items-center gap-2"
                      >
                        <div
                          className="w-1.5 h-1.5 rounded-full bg-primary/80"
                          aria-hidden="true"
                        />
                        <span className="text-sm text-foreground/80">
                          {benefit}
                        </span>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              </div>
            </motion.div>
          );
        })}
      </div>

      <div className="mt-16 flex flex-col items-center justify-center">
        <motion.div
          className="relative group"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <Link href="/dashboard" aria-label="Try free AI design generation">
            <Button
              variant="gradient-primary"
              size="lg"
              shine
              glow
              className="relative px-8 py-6 text-lg font-medium group shadow-lg hover:shadow-xl transition-shadow duration-300"
            >
              Try Free Generation
            </Button>
          </Link>
        </motion.div>

        <Small className="mt-4 text-sm text-muted-foreground/60 px-4 py-2">
          No credit card required • See results in seconds
        </Small>
      </div>
    </section>
  );
}

const FeatureContent: React.FC<FeatureContentProps> = ({
  activeTab,
  activeDesign,
  isLoading,
  handleShuffle,
  designsByType,
}) => {
  const [sliderPosition, setSliderPosition] = useState(50);

  const isShuffleDisabled = isLoading || !activeDesign || (designsByType[activeTab]?.length ?? 0) <= 1;

  return (
    <div
      className="relative grid grid-cols-1 md:grid-cols-2 gap-8 p-6 lg:p-8
                    bg-card dark:bg-card/90 shadow-lg
                    backdrop-blur-xl rounded-3xl border border-primary/10 dark:border-primary/20"
      role="tabpanel"
      aria-label={`${activeTab} showcase`}
    >
      <div className="relative space-y-4">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeDesign?.id || 'no-design'}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="relative w-full aspect-video rounded-xl overflow-hidden
                       bg-gradient-to-b from-background/90 to-background/70
                       shadow-lg"
          >
            {activeDesign && activeDesign.inputImage && activeDesign.outputImage ? (
              <div className="relative w-full h-full">
                <Compare
                  firstImage={activeDesign.inputImage}
                  secondImage={activeDesign.outputImage}
                  className="w-full h-full"
                  firstImageClassName="object-cover object-center"
                  secondImageClassName="object-cover object-center"
                  slideMode="hover"
                  showHandlebar={true}
                  autoplay={false}
                  onSliderChange={setSliderPosition}
                  aria-label={`Before and after comparison for ${activeTab}`}
                />
                <div
                  className="absolute top-2 left-2 z-[60] bg-black/50 text-white
                            px-2 py-1 rounded transition-opacity duration-200
                            text-sm font-medium"
                  style={{ opacity: sliderPosition < 25 ? 0 : 1 }}
                  aria-hidden="true"
                >
                  Original
                </div>
                <div
                  className="absolute top-2 right-2 z-[60] bg-black/50 text-white
                            px-2 py-1 rounded transition-opacity duration-200
                            text-sm font-medium"
                  style={{ opacity: sliderPosition > 75 ? 0 : 1 }}
                  aria-hidden="true"
                >
                  Generated
                </div>
              </div>
            ) : (
              <div
                className="flex items-center justify-center h-full bg-muted"
                aria-label={isLoading ? "Loading example" : "No example available"}
              >
                <p className="text-muted-foreground">
                  {isLoading ? "Loading..." : "No examples available yet"}
                </p>
              </div>
            )}
          </motion.div>
        </AnimatePresence>

        <Button
          variant="gradient-subtle"
          shine
          onClick={handleShuffle}
          disabled={isShuffleDisabled}
          className="w-full relative group hover:opacity-90 transition-opacity"
          aria-label={`Show another ${activeTab.toLowerCase()} example`}
        >
          <Shuffle className="w-4 h-4 mr-2" aria-hidden="true" />
          <span className="font-medium">
            {isLoading && !activeDesign ? "Loading..." : "Show Another Example"}
          </span>
        </Button>
      </div>

      <div className="space-y-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="space-y-6"
        >
          <div>
            <GradientHeading className="text-2xl font-bold mb-3">
              {activeTab}
            </GradientHeading>
            <BodyText className="text-muted-foreground/90 leading-relaxed">
              {features.find((f) => f.title === activeTab)?.description}
            </BodyText>
          </div>

          <div
            className="space-y-3 pt-4"
            role="list"
            aria-label={`${activeTab} benefits`}
          >
            {features
              .find((f) => f.title === activeTab)
              ?.benefits.map((benefit, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="flex items-center gap-3 group"
                  role="listitem"
                >
                  <div
                    className="w-1.5 h-1.5 rounded-full bg-primary/80
                                group-hover:scale-125 transition-all duration-300"
                    aria-hidden="true"
                  />
                  <span
                    className="text-foreground/80 group-hover:text-primary
                                 transition-colors duration-300"
                  >
                    {benefit}
                  </span>
                </motion.div>
              ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
};
