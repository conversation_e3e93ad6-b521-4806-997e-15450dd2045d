"use client";
import React from "react";
import Link from "next/link";
import { ArrowRight } from "lucide-react";
import { <PERSON><PERSON> } from "@/modules/ui/button";
import { H1, BodyText } from "@/modules/ui/typography";
import { DesignSuggestionShowcase } from "./design-suggestion-showcase";
import { GradientBackground } from "@/modules/ui/gradient-background";
import { Suspense } from "react";
import HeroCards from "./hero-cards";

export default function Hero() {
  return (
    <>
      <section
        id="hero"
        aria-labelledby="hero-heading"
        className="relative w-full pt-10 md:pt-20 px-4 sm:px-6 lg:px-8 bg-background dark:bg-background"
      >
        {/* Hero background pattern */}
        <div className="absolute inset-0 bg-grid-white/[0.02] bg-grid-black/[0.02] mix-blend-multiply opacity-50"></div>

        {/* Subtle gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/[0.03] via-transparent to-secondary/[0.03]"></div>

        <div className="relative max-w-7xl mx-auto pt-20 pb-24">
          <div className="flex flex-col items-center text-center max-w-4xl mx-auto mb-16">
            <div className="space-y-4 animate-fade-in transform-gpu">
              <H1
                id="hero-heading"
                className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight"
              >
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary via-purple-500 to-pink-500">
                  Transform Any Space
                </span>
                <br />
                <span className="text-foreground">
                  into Something Extraordinary
                </span>
              </H1>

              <div className="mt-6 space-y-4">
                <BodyText className="text-base sm:text-lg md:text-xl max-w-2xl mx-auto text-foreground/80 font-medium">
                  Your complete AI design suite for professional space
                  transformation
                </BodyText>
                <BodyText className="text-sm sm:text-base max-w-xl mx-auto text-muted-foreground">
                  Stage, design, edit, and enhance any space with our powerful
                  AI tools
                </BodyText>
              </div>

              {/* Tool badges */}
              <div className="flex flex-wrap justify-center gap-3 mt-8">
                {[
                  {
                    name: "Virtual Staging",
                    icon: "🏡",
                  },
                  {
                    name: "Interior Design",
                    icon: "⚡️",
                  },
                  {
                    name: "Exterior Design",
                    icon: "🏘️",
                  },
                  {
                    name: "Style Transfer",
                    icon: "🎨",
                  },
                  {
                    name: "Smart Editing",
                    icon: "✨",
                  },
                  {
                    name: "Background Removal",
                    icon: "✂️",
                  },
                  {
                    name: "Upscaling",
                    icon: "🔍",
                  },
                ].map((tool, index) => (
                  <span
                    key={tool.name}
                    role="listitem"
                    className="text-sm px-4 py-2 rounded-full
                             bg-card/70 dark:bg-card/90
                             hover:bg-primary/10 dark:hover:bg-primary/20
                             border border-primary/10 dark:border-primary/20 hover:border-primary/30
                             shadow-sm hover:shadow-md
                             backdrop-blur-sm
                             transition-colors duration-300 ease-in-out
                             flex items-center gap-2 group"
                  >
                    <span
                      className="transition-colors duration-200 ease-out"
                      aria-hidden="true"
                    >
                      {tool.icon}
                    </span>
                    <span className="text-foreground/90 group-hover:text-foreground font-medium transition-colors duration-200">
                      {tool.name}
                    </span>
                  </span>
                ))}
              </div>

              {/* Trust Indicators */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-3xl mx-auto mt-12">
                <h2 className="sr-only">Why Choose Renovaitor</h2>
                <div className="flex items-start gap-3 bg-card shadow-sm rounded-xl p-4 border border-primary/10 hover:border-primary/20 hover:shadow-md transition-all duration-300">
                  <div className="flex-shrink-0 w-8 h-8 rounded-lg bg-primary/15 flex items-center justify-center">
                    <span className="text-lg">👥</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-sm text-foreground">
                      Professional Results
                    </h4>
                    <p className="text-xs text-muted-foreground mt-1">
                      Photorealistic renders with ultra detail and clarity
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3 bg-card shadow-sm rounded-xl p-4 border border-primary/10 hover:border-primary/20 hover:shadow-md transition-all duration-300">
                  <div className="flex-shrink-0 w-8 h-8 rounded-lg bg-primary/15 flex items-center justify-center">
                    <span className="text-lg">💫</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-sm text-foreground">
                      Multiple Styles
                    </h4>
                    <p className="text-xs text-muted-foreground mt-1">
                      Modern, Luxury, Minimal & more
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3 bg-card shadow-sm rounded-xl p-4 border border-primary/10 hover:border-primary/20 hover:shadow-md transition-all duration-300">
                  <div className="flex-shrink-0 w-8 h-8 rounded-lg bg-primary/15 flex items-center justify-center">
                    <span className="text-lg">⚡️</span>
                  </div>
                  <div>
                    <h4 className="font-medium text-sm text-foreground">
                      AI-Powered Speed
                    </h4>
                    <p className="text-xs text-muted-foreground mt-1">
                      Transform spaces in seconds
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 mt-8 sm:mt-12">
              <Link
                href="/dashboard"
                className="flex items-center gap-2 relative z-10"
                aria-label="Get started with transforming your space"
              >
                <Button
                  variant="gradient-primary"
                  size="lg"
                  shine
                  glow
                  className="text-[15px] sm:text-base px-6 sm:px-8 py-5 sm:py-6 group transform-gpu hover:scale-[1.02] active:scale-[0.98] transition duration-300"
                >
                  Transform Your Space
                  <ArrowRight
                    className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300"
                    aria-hidden="true"
                  />
                </Button>
              </Link>
              <Link
                href="#designs"
                className="flex items-center gap-2 relative z-10"
                aria-label="View example transformations"
              >
                <Button
                  variant="gradient-secondary"
                  size="lg"
                  shine
                  className="text-[15px] sm:text-base px-6 sm:px-8 py-5 sm:py-6 group transform-gpu hover:scale-[1.02] active:scale-[0.98] transition duration-300"
                >
                  See Live Transformations
                  <span
                    className="ml-2 group-hover:rotate-12 transition-transform inline-block"
                    aria-hidden="true"
                  >
                    ✨
                  </span>
                </Button>
              </Link>
            </div>
          </div>

          <Suspense
            fallback={
              <div
                className="h-[600px] rounded-2xl animate-pulse"
                aria-label="Loading hero cards..."
              />
            }
          >
            <div className="relative z-10 mt-8">
              <HeroCards />
            </div>
          </Suspense>
        </div>
      </section>
      <Suspense
        fallback={
          <div
            className="h-96 bg-foreground/5 animate-pulse"
            aria-label="Loading design showcase..."
          />
        }
      >
        <DesignSuggestionShowcase />
      </Suspense>
    </>
  );
}
