"use client";
import React from "react";
import { motion } from "framer-motion";
import { Camera, ImagePlus, Layers } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/modules/ui/card";
import { useInView } from "react-intersection-observer";

const benefits = [
  {
    icon: Camera,
    title: "Ultra-Clear Virtual Staging",
    description:
      "Transform empty spaces into furnished showrooms with stunning detail",
    gradient: "from-neutral-500 to-neutral-600",
    image: "/images/benefits/virtual-staging.jpg",
  },
  {
    icon: ImagePlus,
    title: "High-Definition Interior Visualization",
    description:
      "Bring your design concepts to life with photorealistic, crystal-clear renders",
    gradient: "from-neutral-500 to-neutral-600",
    image: "/images/benefits/interior-visualization.jpg",
  },
  {
    icon: Layers,
    title: "Upscale Mode for Maximum Detail",
    description:
      "Enhance your visuals with our advanced upscale mode for unparalleled clarity",
    gradient: "from-neutral-500 to-neutral-600",
    image: "/images/benefits/upscale-detail.jpg",
  },
];

export const BenefitsSection = () => {
  const { ref, inView } = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  return (
    <section
      id="benefits"
      aria-labelledby="benefits-heading"
      ref={ref}
      className="mt-24 space-y-8"
    >
      <h2
        id="benefits-heading"
        className="text-4xl md:text-5xl lg:text-6xl 
                 font-bold text-center
                 leading-[1.2] md:leading-[1.2] lg:leading-[1.2]
                 text-foreground
                 mb-6 md:mb-8"
      >
        Revolutionize Your Design Process with Unmatched Clarity
      </h2>

      <div
        className="grid grid-cols-1 md:grid-cols-3 gap-8"
        role="list"
        aria-label="Key benefits"
      >
        {benefits.map((benefit, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 * index }}
            whileHover={{ scale: 1.02 }}
            className="transition-all duration-300"
            role="listitem"
          >
            <Card
              className="group hover:shadow-md h-full 
                        bg-card/50
                        backdrop-blur-sm border border-border/50
                        transition-all duration-300"
            >
              <CardHeader>
                <CardTitle className="text-lg md:text-xl lg:text-2xl font-semibold leading-tight flex items-center gap-3">
                  <benefit.icon
                    className="w-6 h-6 text-foreground/80
                             group-hover:scale-110 transition-transform duration-300"
                    aria-hidden="true"
                  />
                  {benefit.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-base md:text-lg text-muted-foreground leading-relaxed">
                  {benefit.description}
                </p>
                <img
                  src={benefit.image}
                  alt={`Example of ${benefit.title}`}
                  className="w-full h-48 object-cover rounded-lg 
                           transform group-hover:scale-[1.02] transition-transform duration-300"
                />
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    </section>
  );
};

export default BenefitsSection;
