"use client";

import React from "react";
import {
  Accordion,
  AccordionContent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from "@/modules/ui/accordion";
import { GradientHeading, BodyText, Lead } from "@/modules/ui/typography";
import { Button } from "@/modules/ui/button";
import Link from "next/link";

// Group questions by category for better organization
const faqSections = [
  {
    category: "Getting Started",
    questions: [
      {
        id: "how-it-works",
        question: "How does your app work?",
        answer: `Renovaitor works in 3 simple steps:

1. Upload a photo of your space
2. Choose your preferred design style or use our advanced customization options
3. Let our AI transform your space with stunning results

You can use our various tools including:
• Interior & exterior design generation
• Virtual staging with furniture
• Magic image editor
• Image upscaling`,
      },
      {
        id: "try-before-purchase",
        question: "Can I try the app before I purchase?",
        answer: `Absolutely! We offer a free trial that includes:

• 10 free design credits
• Access to all design tools
• Full-resolution downloads
• No credit card required

This allows you to test all features before making any commitment.`,
      },
    ],
  },
  {
    category: "Features & Capabilities",
    questions: [
      {
        id: "features",
        question: "What features does Renovaitor offer?",
        answer: `Our platform includes several powerful features:

1. AI Design Generation
   • Interior and exterior designs
   • Multiple style options
   • Real-time generation

2. Virtual Staging
   • AI-suggested furniture placement
   • Decor recommendations
   • Non-destructive editing

3. Magic Image Editor
   • Object removal
   • Object addition
   • Background editing

4. Image Enhancement
   • HD upscaling
   • Detail enhancement`,
      },
      {
        id: "virtual-staging",
        question: "What is virtual staging?",
        answer: `Virtual staging is our AI-powered tool that helps you:

• Visualize furniture and decor in your empty spaces
• Experiment with different design styles
• Keep structural elements unchanged
• Generate multiple variations quickly

Perfect for:
• Real estate listings
• Interior design planning
• Room layout visualization
• Design concept testing`,
      },
      {
        id: "image-editing",
        question: "What image editing features are available?",
        answer: `Our magic image editor includes:

1. Object Manipulation
   • Remove unwanted items
   • Add new furniture or decor
   • Adjust object positions

2. Image Enhancement
   • Increase resolution
   • Enhance details
   • Improve lighting`,
      },
      {
        id: "design-styles",
        question: "What design styles are available?",
        answer: `We offer a wide range of pre-defined styles:

Popular Styles:
• Modern
• Contemporary
• Traditional
• Minimalist
• Industrial
• Scandinavian
• Bohemian
• Mid-century Modern

Custom Options:
• Advanced mode for custom prompts
• Style mixing capabilities
• Personalized preferences`,
      },
    ],
  },
  {
    category: "Technical Details",
    questions: [
      {
        id: "image-processing",
        question: "How long does image processing take?",
        answer: `Processing times vary by feature:

Basic Operations:
• Design generation: ~7 seconds
• Object removal: 5-15 seconds

Complex Operations:
• Virtual staging: 10-20 seconds
• Detailed editing: 15-30 seconds

You'll receive real-time progress updates for all operations.`,
      },
      {
        id: "data-security",
        question: "How secure is my data?",
        answer: `We maintain the highest standards of security:

Data Storage:
• US-based secure servers
• Enterprise-grade encryption
• Regular security audits

Technology Partners:
• Google Cloud Platform
• Microsoft Azure
• Replicate Inc.

Payment Security:
• Secure processing via Lemon Squeezy
• No credit card storage
• SSL/TLS encryption`,
      },
    ],
  },
  {
    category: "About Us",
    questions: [
      {
        id: "who-built",
        question: "Who built Renovaitor?",
        answer: `Hey there! I'm Ugurcan, the founder of Renovaitor.

Our Mission:
• Democratize interior and exterior design
• Make professional design accessible to everyone
• Leverage cutting-edge AI technology

Why Renovaitor:
• User-friendly platform
• Professional-grade results
• Affordable design solutions
• Continuous innovation

Join our growing community of users who are already transforming their spaces with Renovaitor!`,
      },
    ],
  },
];

export default function FAQ() {
  return (
    <section
      id="faq"
      aria-labelledby="faq-heading"
      className="py-4 sm:py-12 md:py-20 w-full bg-gradient-to-b from-background/95 to-background/50"
    >
      <div className="w-full px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto">
        <div className="space-y-16 animate-fade-in-up">
          <div className="text-center space-y-4">
            <GradientHeading
              id="faq-heading"
              className="text-3xl md:text-4xl lg:text-5xl font-bold"
            >
              Frequently Asked Questions
            </GradientHeading>
            <Lead className="text-base sm:text-lg md:text-xl text-muted-foreground/80 max-w-2xl mx-auto">
              Everything you need to know about Renovaitor
            </Lead>
          </div>

          <div className="space-y-12" role="feed" aria-label="FAQ Sections">
            {faqSections.map((section, index) => (
              <div
                key={section.category}
                className="space-y-6 animate-fade-in-up"
                style={{ "--delay": `${index * 0.1}s` } as React.CSSProperties}
              >
                <h3
                  id={`${section.category
                    .toLowerCase()
                    .replace(/\s+/g, "-")}-section`}
                  className="text-xl font-semibold text-primary dark:text-primary/90 border-b border-primary/20 pb-2 mb-4"
                >
                  {section.category}
                </h3>
                <Accordion
                  type="single"
                  collapsible
                  className="w-full space-y-4"
                  aria-labelledby={`${section.category
                    .toLowerCase()
                    .replace(/\s+/g, "-")}-section`}
                  defaultValue={section.questions[0].id}
                >
                  {section.questions.map((item) => (
                    <AccordionItem
                      key={item.id}
                      value={item.id}
                      className="group border border-primary/10 dark:border-primary/20 rounded-xl
                               bg-card/90 dark:bg-card/90
                               backdrop-blur-xl shadow-sm hover:shadow-md
                               hover:border-primary/30
                               transition-all duration-300
                               motion-reduce:transform-none"
                    >
                      <AccordionTrigger
                        className="text-base sm:text-lg
                                 font-medium text-left
                                 text-foreground/90 group-hover:text-foreground
                                 py-6 px-6
                                 transition-colors duration-200
                                 data-[state=open]:text-primary data-[state=open]:font-semibold
                                 focus-visible:outline-none focus-visible:ring-2
                                 focus-visible:ring-primary/50 rounded-t-xl
                                 hover:bg-primary/5 dark:hover:bg-primary/10"
                        aria-controls={`content-${item.id}`}
                        id={`trigger-${item.id}`}
                      >
                        {item.question}
                      </AccordionTrigger>
                      <AccordionContent
                        className="px-6 pb-6 pt-2 text-foreground/90 dark:text-foreground/80"
                        id={`content-${item.id}`}
                        role="region"
                        aria-labelledby={`trigger-${item.id}`}
                      >
                        <div className="prose prose-gray dark:prose-invert max-w-none">
                          {item.answer.split("\n\n").map((paragraph, idx) => {
                            if (paragraph.includes("•")) {
                              const [title, ...items] = paragraph.split("\n");
                              return (
                                <div key={idx} className="space-y-2">
                                  {title && (
                                    <p className="font-medium text-foreground/90">
                                      {title}
                                    </p>
                                  )}
                                  <ul
                                    className="list-none space-y-1.5 text-muted-foreground/90"
                                    role="list"
                                  >
                                    {items.map((item, itemIdx) => (
                                      <li
                                        key={itemIdx}
                                        className="flex items-start"
                                        role="listitem"
                                      >
                                        <span
                                          className="text-primary mr-2"
                                          aria-hidden="true"
                                        >
                                          •
                                        </span>
                                        <span>{item.replace("• ", "")}</span>
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              );
                            } else if (paragraph.match(/^\d\./)) {
                              const [title, ...items] = paragraph.split("\n");
                              return (
                                <div key={idx} className="space-y-2">
                                  {title && (
                                    <p className="font-medium text-foreground/90">
                                      {title}
                                    </p>
                                  )}
                                  <ol
                                    className="list-none space-y-1.5 text-muted-foreground/90"
                                    role="list"
                                  >
                                    {items.map((item, itemIdx) => (
                                      <li
                                        key={itemIdx}
                                        className="flex items-start"
                                        role="listitem"
                                      >
                                        <span
                                          className="text-primary mr-2"
                                          aria-hidden="true"
                                        >
                                          •
                                        </span>
                                        <span>{item.replace("   • ", "")}</span>
                                      </li>
                                    ))}
                                  </ol>
                                </div>
                              );
                            }
                            return (
                              <p
                                key={idx}
                                className="text-base text-muted-foreground/90 leading-relaxed"
                              >
                                {paragraph}
                              </p>
                            );
                          })}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
            ))}
          </div>

          <div
            className="text-center py-8 animate-fade-in-up"
            style={{ "--delay": "0.5s" } as React.CSSProperties}
          >
            <Lead className="text-center mb-6 text-base sm:text-lg text-muted-foreground/80">
              Still have questions?
            </Lead>
            <Link
              href="mailto:<EMAIL>"
              className="text-base font-medium px-8 py-2.5"
              aria-label="Contact us via email"
            >
              <Button variant="gradient-subtle" shine className="mt-4" asChild>
                Contact us
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
