"use client";

import * as React from "react";
import { useTheme } from "next-themes";
import { Button } from "@/modules/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/modules/ui/dropdown-menu";
import * as Icons from "@/modules/ui/icons";

export default function ThemeToggle(props: {
  align?: "center" | "start" | "end";
  side?: "top" | "bottom";
}) {
  const { setTheme, theme } = useTheme();

  const icons = {
    light: <Icons.Sun className="h-5 w-5" />,
    dark: <Icons.Moon className="h-5 w-5" />,
    system: <Icons.System className="h-5 w-5" />,
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="group relative px-3 py-2
                     bg-background/50 hover:bg-primary/10
                     border border-primary/10 hover:border-primary/20
                     backdrop-blur-sm transition-all duration-300
                     text-foreground/70 hover:text-foreground"
        >
          {icons[theme as keyof typeof icons]}
          <span className="ml-2 capitalize">{theme}</span>
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align={props.align}
        side={props.side}
        className="bg-background/80 backdrop-blur-md border border-primary/10"
      >
        <DropdownMenuItem
          onClick={() => setTheme("light")}
          className="hover:bg-primary/10 focus:bg-primary/10 cursor-pointer"
        >
          <Icons.Sun className="mr-2 h-4 w-4 text-foreground/70" />
          <span>Light</span>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setTheme("dark")}
          className="hover:bg-primary/10 focus:bg-primary/10 cursor-pointer"
        >
          <Icons.Moon className="mr-2 h-4 w-4 text-foreground/70" />
          <span>Dark</span>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setTheme("system")}
          className="hover:bg-primary/10 focus:bg-primary/10 cursor-pointer"
        >
          <Icons.System className="mr-2 h-4 w-4 text-foreground/70" />
          <span>System</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
