"use client";
import React from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { Button } from "@/modules/ui/button";
import { useMediaQuery } from "@/modules/hooks/use-media-query";
import { GradientHeading, BodyText, Small } from "@/modules/ui/typography";
import {  Gift } from "lucide-react";

const CTASection = () => {
  const isMobile = useMediaQuery("(max-width: 768px)");

  return (
    <section
      id="cta"
      aria-labelledby="cta-heading"
      className="w-full px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto text-center"
    >
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.4 }}
      >
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4, ease: "easeOut" }}
        >
          <GradientHeading
            id="cta-heading"
            className="text-center mb-6 md:mb-8 text-2xl sm:text-3xl md:text-4xl lg:text-5xl"
          >
            Start Creating Beautiful Spaces
            <br className="hidden sm:block" /> with AI-Powered Design
          </GradientHeading>
        </motion.div>

        <BodyText className="max-w-2xl mx-auto mb-8 text-sm sm:text-base md:text-lg lg:text-xl">
          Sign up now and get free credits to experience the future of interior
          design
        </BodyText>

        <div className="mt-8 sm:mt-12 space-y-4">
          <Link href="/login" aria-label="Sign up and get free credits">
            <Button
              variant="gradient-primary"
              size="lg"
              shine
              glow
              className="text-base sm:text-lg px-6 sm:px-8 py-2.5 sm:py-3 group"
            >
              <Gift className="w-5 h-5 mr-2" aria-hidden="true" />
              Get Your Free Credits
            </Button>
          </Link>
          <div className="space-y-2" aria-label="Offer details">
            <Small className="block text-primary font-medium">
              Limited Time Launch Offer
            </Small>
            <Small className="block text-muted-foreground">
              <span className="font-semibold">10 free credits</span> • No credit
              card required • Start designing instantly
            </Small>
          </div>
        </div>
      </motion.div>
    </section>
  );
};

export default CTASection;
