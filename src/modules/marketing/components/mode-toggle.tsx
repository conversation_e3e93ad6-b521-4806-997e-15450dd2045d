"use client";

import * as React from "react";
import { useTheme } from "next-themes";

import { But<PERSON> } from "@/modules/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/modules/ui/dropdown-menu";
import * as Icons from "@/modules/ui/icons";

export function ModeToggle() {
  const { setTheme, theme } = useTheme();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="w-9 h-9 px-0 relative group
                     bg-background/50 hover:bg-primary/10
                     border border-primary/10 hover:border-primary/20
                     backdrop-blur-sm transition-all duration-300"
        >
          <Icons.Sun
            className="h-5 w-5 rotate-0 scale-100 transition-transform duration-300
                               dark:-rotate-90 dark:scale-0
                               text-foreground/70 group-hover:text-foreground"
          />
          <Icons.Moon
            className="absolute h-5 w-5 rotate-90 scale-0 transition-transform duration-300
                                dark:rotate-0 dark:scale-100
                                text-foreground/70 group-hover:text-foreground"
          />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="bg-background/80 backdrop-blur-md border border-primary/10"
      >
        <DropdownMenuItem
          onClick={() => setTheme("light")}
          className="hover:bg-primary/10 focus:bg-primary/10 cursor-pointer"
        >
          <Icons.Sun className="mr-2 h-4 w-4 text-foreground/70" />
          <span>Light</span>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setTheme("dark")}
          className="hover:bg-primary/10 focus:bg-primary/10 cursor-pointer"
        >
          <Icons.Moon className="mr-2 h-4 w-4 text-foreground/70" />
          <span>Dark</span>
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setTheme("system")}
          className="hover:bg-primary/10 focus:bg-primary/10 cursor-pointer"
        >
          <Icons.Laptop className="mr-2 h-4 w-4 text-foreground/70" />
          <span>System</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
