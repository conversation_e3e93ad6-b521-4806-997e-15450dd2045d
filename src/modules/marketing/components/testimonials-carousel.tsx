"use client";
import React from "react";
import { motion } from "framer-motion";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay } from "swiper/modules";
import { GradientHeading, BodyText, H3, Small } from "@/modules/ui/typography";
import Image from "next/image";

// Import Swiper styles
import "swiper/css";
import "swiper/css/pagination";
import "swiper/css/autoplay";

const testimonials = [
  {
    name: "<PERSON><PERSON>",
    role: "CEO, Unio Exhibition",
    content: {
      highlight: "Revolutionized our exhibition design process",
      details:
        "The AI-powered sketches to renders feature is mind-blowing. It's become indispensable for our creative team.",
      impact:
        "Reduced design time by 70% and increased client satisfaction by 40%",
    },
    avatar: "/images/testimonials/sercan-soydan.png",
  },
];

const TestimonialsCarousel = () => {
  return (
    <section id="testimonials" aria-labelledby="testimonials-heading">
      <div className="container mx-auto px-4">
        <GradientHeading
          id="testimonials-heading"
          className="text-center mb-6 md:mb-8"
        >
          See the Difference Ultra-Clear Visuals Make
        </GradientHeading>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <Swiper
            modules={[Pagination, Autoplay]}
            spaceBetween={30}
            slidesPerView={1}
            pagination={{
              clickable: true,
              bulletActiveClass:
                "swiper-pagination-bullet-active custom-bullet-active",
            }}
            autoplay={{ delay: 5000, disableOnInteraction: false }}
            loop={true}
            className="custom-swiper"
            aria-label="Customer testimonials"
          >
            {testimonials.map((testimonial, index) => (
              <SwiperSlide
                key={index}
                role="group"
                aria-roledescription="slide"
                aria-label={`Testimonial from ${testimonial.name}`}
              >
                <div
                  className="bg-card/90 dark:bg-card/90 backdrop-blur-sm
                            border border-primary/10 dark:border-primary/20 rounded-lg shadow-sm
                            p-8 text-center max-w-2xl mx-auto
                            hover:shadow-md transition-shadow duration-300"
                >
                  <Image
                    src={testimonial.avatar}
                    alt={`${testimonial.name}'s profile picture`}
                    width={96}
                    height={96}
                    className="rounded-full mx-auto mb-6
                             border-4 border-primary/40 dark:border-primary/50 shadow-md
                             transform hover:scale-105 transition-transform duration-300
                             object-contain"
                  />
                  <BodyText
                    variant="large"
                    className="font-semibold mb-4 text-foreground"
                  >
                    &quot;{testimonial.content.highlight}&quot;
                  </BodyText>

                  <BodyText className="mb-4 text-foreground/90">
                    {testimonial.content.details}
                  </BodyText>

                  <BodyText className="font-medium mb-6 text-primary">
                    {testimonial.content.impact}
                  </BodyText>

                  <H3 className="!mb-2 text-foreground">{testimonial.name}</H3>
                  <Small className="text-primary font-medium">
                    {testimonial.role}
                  </Small>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </motion.div>
      </div>
      <style jsx global>{`
        .custom-swiper .swiper-pagination-bullet {
          background-color: hsl(var(--primary));
          opacity: 0.5;
          width: 12px;
          height: 12px;
          transition: all 0.3s ease;
          border: 1px solid rgba(var(--primary-rgb), 0.2);
        }
        .custom-swiper .swiper-pagination-bullet-active.custom-bullet-active {
          background-color: hsl(var(--primary));
          opacity: 1;
          width: 24px;
          border-radius: 6px;
        }
        .custom-swiper {
          padding-bottom: 3rem !important;
        }
        .custom-swiper .swiper-pagination {
          bottom: 0 !important;
        }
      `}</style>
    </section>
  );
};

export default TestimonialsCarousel;
