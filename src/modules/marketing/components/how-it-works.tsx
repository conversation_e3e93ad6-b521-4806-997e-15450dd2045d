"use client";
import React from "react";
import { motion } from "framer-motion";
import Image from "next/image";
import {
  Wand2,
  Upload,
  Palette,
  Spark<PERSON>,
  Settings,
  Share2,
} from "lucide-react";

interface Step {
  id: number;
  title: string;
  description: string;
  Icon: React.ElementType;
}

const steps: Step[] = [
  {
    id: 1,
    title: "Upload Your Space",
    description: "Take a photo of your room or upload an existing image.",
    Icon: Upload,
  },
  {
    id: 2,
    title: "Choose Your Style",
    description: "Select from various design styles or create a custom look.",
    Icon: Palette,
  },
  {
    id: 3,
    title: "AI Transformation",
    description: "Our AI generates multiple design options in seconds.",
    Icon: Sparkles,
  },
  {
    id: 4,
    title: "Refine and Customize",
    description: "Adjust details and make the design uniquely yours.",
    Icon: Settings,
  },
  {
    id: 5,
    title: "Download and Share",
    description: "Get your new design in high resolution, ready to use.",
    Icon: Share2,
  },
  {
    id: 6,
    title: "Get AI Design Suggestions",
    description:
      "Upload a photo of your room and receive personalized design recommendations instantly.",
    Icon: Wand2,
  },
];

const StepCard = ({ step }: { step: Step }) => (
  <motion.div
    className="relative group bg-card hover:bg-accent/5 p-6 rounded-xl border border-border/50 shadow-sm
               hover:shadow-lg transition-all duration-300 hover:scale-[1.02]"
    whileHover={{ y: -5 }}
    transition={{ type: "spring", stiffness: 300 }}
  >
    <div
      className="absolute inset-0 bg-gradient-to-r from-primary/10 via-accent/10 to-secondary/10 opacity-0 
                    group-hover:opacity-100 transition-opacity duration-500 rounded-xl"
    />
    <div className="relative z-10">
      <step.Icon className="w-10 h-10 text-primary mb-4" />
      <h3 className="text-xl font-semibold mb-2 bg-gradient-to-br from-foreground to-foreground/70 bg-clip-text text-transparent">
        {step.title}
      </h3>
      <p className="text-muted-foreground text-sm leading-relaxed">
        {step.description}
      </p>
    </div>
  </motion.div>
);

export function HowItWorks() {
  return (
    <section className="py-20 relative overflow-hidden">
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-grid-black/[0.02]" />
      <div className="relative container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-5xl font-bold bg-gradient-to-r from-primary via-accent to-secondary bg-clip-text text-transparent mb-4">
            How Renovaitor Works
          </h2>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Transform your space in minutes with our intuitive AI-powered
            process
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {steps.map((step) => (
            <StepCard key={step.id} step={step} />
          ))}
        </div>
      </div>
    </section>
  );
}

export default HowItWorks;
