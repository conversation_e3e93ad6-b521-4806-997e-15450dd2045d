"use client";
import React, { useState, useRef } from "react";
import Image from "next/image";
import { motion } from "framer-motion";
import { Compare } from "@/modules/ui/compare";
import ImageEnhancementShowcase from "./image-enhancement-showcase";
import {
  H2,
  H3,
  <PERSON>Text,
  <PERSON>rad<PERSON>Heading,
  Lead,
} from "@/modules/ui/typography";
import { Play } from "lucide-react";
import { DollarSign, Zap, Users, Trophy, Wand2 } from "lucide-react";

const features = [
  {
    title: "AI-Powered Design Suggestions",
    description:
      "Get instant, personalized interior design recommendations for any room. Upload a photo, specify your style, and let our AI create the perfect design concept.",
    icon: Wand2,
    highlight: true,
  },
  // ... other features
];

export function FeaturesGrid() {
  return (
    <section className="w-full">
      <div className="w-full px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        <ImageEnhancementShowcase />
      </div>
    </section>
  );
}

export default FeaturesGrid;
