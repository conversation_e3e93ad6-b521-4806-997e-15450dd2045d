"use client";

export const ChristmasBanner = () => {
  return (
    <div className="w-full bg-gradient-to-r from-[#165B33] via-[#BB2528] to-[#165B33] text-white py-2 text-center text-sm font-medium relative overflow-hidden">
      <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCI+CiAgPHBhdGggZD0iTTAgMGg2MHY2MEgweiIgZmlsbD0ibm9uZSIvPgogIDxwYXRoIGQ9Ik0zMCAyMHYyME0yMCAzMGgyMCIgc3Ryb2tlPSIjZmZmIiBzdHJva2Utb3BhY2l0eT0iMC4xIiBzdHJva2Utd2lkdGg9IjAuNSIvPgo8L3N2Zz4=')] opacity-10" />

      <div className="relative flex flex-col sm:flex-row items-center justify-center gap-1.5 sm:gap-3 px-3 sm:px-4 max-w-4xl mx-auto">
        <span className="text-base sm:text-lg transform-gpu animate-tree-pulse">
          🎄
        </span>
        <div className="flex flex-col sm:flex-row items-center gap-1 sm:gap-3">
          <span className="text-[#F8B229] font-medium whitespace-nowrap">
            Holiday Special:
          </span>
          <span className="text-white/90 text-xs sm:text-sm">
            Get bonus credits when your referred friends make their first
            design!
          </span>
        </div>
        <span className="text-base sm:text-lg transform-gpu animate-santa-bounce hidden sm:inline-block">
          🎅
        </span>
      </div>

      {/* Optimized decorative elements */}
      <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
        <div className="absolute top-0 left-1/4 w-1.5 h-1.5 bg-[#F8B229] rounded-full opacity-60 animate-optimized-snow-1 transform-gpu" />
        <div className="absolute top-0 right-1/3 w-1 h-1 bg-white rounded-full opacity-40 animate-optimized-snow-2 transform-gpu" />
        <div className="absolute top-0 left-2/3 w-1 h-1 bg-[#F8B229] rounded-full opacity-60 animate-optimized-snow-3 transform-gpu" />
      </div>

      {/* Subtle gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-white/5 via-transparent to-white/5" />
    </div>
  );
};
