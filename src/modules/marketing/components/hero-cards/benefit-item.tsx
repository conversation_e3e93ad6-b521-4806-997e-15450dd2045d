import { LucideIcon } from "lucide-react";
import { Small } from "@/modules/ui/typography";
import { cn } from "@/modules/ui";

interface BenefitItemProps {
  icon: LucideIcon;
  text: string;
  color: string;
}

export const BenefitItem = ({ icon: Icon, text, color }: BenefitItemProps) => {
  return (
    <div
      className={cn(
        "flex items-center gap-3 group/benefit",
        "hover:translate-x-1 hover:scale-[1.01] transition-all duration-200"
      )}
    >
      <div
        className={cn(
          "p-2 rounded-xl",
          "bg-card/90 dark:bg-card/90",
          "border border-primary/10 dark:border-primary/20",
          "group-hover/benefit:border-primary/30",
          "shadow-sm group-hover/benefit:shadow-md",
          "transition-all duration-300",
          color
        )}
      >
        <Icon className="w-3.5 h-3.5" />
      </div>
      <Small
        className="text-foreground/90 dark:text-foreground/80 group-hover/benefit:text-foreground
                  transition-colors duration-300 text-sm md:text-base font-medium"
      >
        {text}
      </Small>
    </div>
  );
};
