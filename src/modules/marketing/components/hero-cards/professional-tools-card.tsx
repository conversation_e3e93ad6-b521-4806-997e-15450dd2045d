import Image from "next/image";
import { H3, BodyText } from "@/modules/ui/typography";
import { cn } from "@/modules/ui";
import { LucideIcon } from "lucide-react";
import { BenefitItem } from "./benefit-item";

interface Benefit {
  icon: LucideIcon;
  text: string;
  color: string;
}

interface ProfessionalToolsCardProps {
  imageSrc: string;
  text: string;
  subtext: string;
  benefits: Benefit[];
  delay: number;
}

export const ProfessionalToolsCard = ({
  imageSrc,
  text,
  subtext,
  benefits,
  delay,
}: ProfessionalToolsCardProps) => {
  return (
    <article
      className={cn(
        "relative overflow-hidden rounded-2xl",
        "bg-card/90 dark:bg-card backdrop-blur-xl border border-primary/10 dark:border-primary/20",
        "hover:border-primary/30 transition-all duration-500",
        "shadow-md hover:shadow-lg",
        "hover:z-10 group",
        "opacity-0 translate-y-4",
        "animate-fade-in-up"
      )}
      style={{ "--delay": `${delay}s` } as React.CSSProperties}
    >
      <div className="absolute inset-0 bg-gradient-to-br from-primary/[0.08] via-transparent to-secondary/[0.08] dark:from-primary/[0.15] dark:to-secondary/[0.15] opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

      <div className="p-8 relative z-10">
        <div className="flex flex-col">
          <div className="flex items-start gap-8 mb-8">
            <div
              className={cn(
                "relative flex-shrink-0 w-20 h-20 rounded-2xl overflow-hidden mb-6",
                "before:absolute before:inset-0 before:bg-gradient-to-br before:from-primary/15 before:to-secondary/15 before:opacity-0 before:hover:opacity-100 before:transition-opacity before:duration-500",
                "shadow-sm hover:shadow-md",
                "border border-primary/10 hover:border-primary/20",
                "hover:scale-105 transition-all duration-200"
              )}
            >
              <Image
                src={imageSrc}
                alt={text}
                fill
                className="object-cover"
                aria-hidden="true"
                priority={true}
              />
            </div>
            <div>
              <H3 className="!mb-2 p-2 text-2xl font-semibold text-foreground">
                {text}
              </H3>
              <BodyText className="text-base text-foreground/80">
                {subtext}
              </BodyText>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            {benefits.map((benefit, i) => (
              <BenefitItem key={i} {...benefit} />
            ))}
          </div>
        </div>
      </div>
    </article>
  );
};
