"use client";

import { ArrowR<PERSON> } from "lucide-react";
import { H3, BodyText } from "@/modules/ui/typography";
import { Button } from "@/modules/ui/button";
import Link from "next/link";
import { cn } from "@/modules/ui";
import { features } from "./features-data";
import { FeatureCard } from "./feature-card";
import { ProfessionalToolsCard } from "./professional-tools-card";

export default function HeroCards() {
  return (
    <section
      id="features"
      aria-labelledby="features-heading"
      className={cn(
        " max-w-7xl mx-auto space-y-12 md:space-y-24",
        "opacity-0 translate-y-4",
        "animate-fade-in-up"
      )}
      style={{ "--delay": "0.4s" } as React.CSSProperties}
    >
      <div className="absolute inset-0 -z-10 bg-background" aria-hidden="true">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_top_right,rgba(var(--primary-rgb),0.05),transparent_40%)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_bottom_left,rgba(var(--secondary-rgb),0.05),transparent_40%)]" />
      </div>

      {features.map((feature, index) => {
        if (feature.text === "Professional Tools") {
          return (
            <div key={index} className="flex justify-center w-full px-4 sm:px-6 lg:px-8">
              <div className="w-full min-w-md mx-auto">
                <ProfessionalToolsCard {...feature} />
              </div>
            </div>
          );
        }

        return <FeatureCard key={index} index={index} {...feature} />;
      })}

      <div
        className={cn(
          "relative text-center max-w-2xl mx-auto px-4 sm:px-6 lg:px-8",
          "opacity-0 translate-y-4",
          "animate-fade-in-up"
        )}
        style={{ "--delay": "0.6s" } as React.CSSProperties}
      >
        <H3
          id="features-heading"
          className="text-2xl sm:text-3xl font-semibold bg-clip-text text-transparent bg-gradient-to-br from-foreground to-foreground/80 mb-4"
        >
          Ready to Transform Your Space?
        </H3>
        <BodyText className="text-base text-foreground/60 mb-8">
          Get started with our AI-powered tools today. No credit card required.
        </BodyText>
        <Link href="/dashboard" aria-label="Try all tools for free">
          <Button
            variant="gradient-primary"
            size="lg"
            shine
            glow
            className="text-[15px] sm:text-base px-6 sm:px-8 py-5 sm:py-6 group"
          >
            <span className="flex items-center gap-2 relative z-10">
              Try All Tools For Free
              <ArrowRight
                className="w-4 h-4 group-hover:translate-x-1 transition-transform"
                aria-hidden="true"
              />
            </span>
          </Button>
        </Link>
      </div>
    </section>
  );
}
