import {
  DollarSign,
  Rocket,
  <PERSON>,
  Pa<PERSON>,
  Layers,
  Clock,
  Download,
  <PERSON>,
  Gauge,
  Crown,
  Award,
  Sparkles,
} from "lucide-react";

export const features = [
  {
    imageSrc: "/icons/virtual-staging-icon.webp",
    text: "Virtual Staging",
    subtext: "From Empty to Extraordinary in Seconds",
    benefits: [
      {
        icon: DollarSign,
        text: "Save thousands on physical staging",
        color: "text-green-500",
      },
      {
        icon: Rocket,
        text: "Show properties immediately",
        color: "text-blue-500",
      },
      {
        icon: Star,
        text: "Win more listings instantly",
        color: "text-yellow-500",
      },
      {
        icon: Palette,
        text: "Multiple style variations",
        color: "text-purple-500",
      },
    ],
    delay: 0.1,
    gradient: "from-blue-600 to-blue-900",
    video: "/videos/hero-cards/virtual-staging.mp4",
  },
  {
    imageSrc: "/icons/interior-design-icon.webp",
    text: "Interior Design",
    subtext: "Skip the Render, See Your Vision Instantly",
    benefits: [
      {
        icon: Layers,
        text: "Instant concept visualization",
        color: "text-indigo-500",
      },
      {
        icon: Clock,
        text: "Real-time client meetings",
        color: "text-cyan-500",
      },
      {
        icon: Palette,
        text: "Multiple design directions",
        color: "text-pink-500",
      },
      {
        icon: Rocket,
        text: "Faster approval process",
        color: "text-orange-500",
      },
    ],
    delay: 0.2,
    gradient: "from-indigo-600 to-indigo-900",
    video: "/videos/hero-cards/interior-design.mp4",
  },
  {
    imageSrc: "/icons/exterior-design-icon.webp",
    text: "Exterior Transformations",
    subtext: "Stunning Curb Appeal in Clicks",
    benefits: [
      {
        icon: Crown,
        text: "Enhance property exteriors",
        color: "text-amber-500",
      },
      {
        icon: Palette,
        text: "Seasonal variations",
        color: "text-emerald-500",
      },
      {
        icon: Layers,
        text: "Landscape visualizations",
        color: "text-teal-500",
      },
      {
        icon: Star,
        text: "Perfect lighting & shadows",
        color: "text-violet-500",
      },
    ],
    delay: 0.3,
    gradient: "from-emerald-600 to-emerald-900",
    video: "/videos/hero-cards/exterior-design.mp4",
  },
  {
    imageSrc: "/icons/smart-image-editor-icon.webp",
    text: "Smart Image Editor",
    subtext: "Perfect Every Detail, Just Point and Transform",
    benefits: [
      {
        icon: Shield,
        text: "Remove unwanted items",
        color: "text-rose-500",
      },
      {
        icon: Layers,
        text: "Add missing elements",
        color: "text-sky-500",
      },
      {
        icon: Gauge,
        text: "Fix imperfections",
        color: "text-blue-500",
      },
      {
        icon: Award,
        text: "Natural transformations",
        color: "text-purple-500",
      },
    ],
    delay: 0.4,
    gradient: "from-purple-600 to-purple-900",
    video: "/videos/hero-cards/smart-editor.mp4",
  },
  {
    imageSrc: "/icons/professional-tools-icon.webp",
    text: "Professional Tools",
    subtext: "Complete Suite for Perfect Results",
    benefits: [
      {
        icon: Shield,
        text: "Background removal",
        color: "text-indigo-500",
      },
      {
        icon: Gauge,
        text: "Smart upscaling",
        color: "text-cyan-500",
      },
      {
        icon: Sparkles,
        text: "Crystal clear details",
        color: "text-emerald-500",
      },
    ],
    delay: 0.5,
    gradient: "from-teal-600 to-teal-900",
  },
];
