import { LucideIcon, Loader2 } from "lucide-react";
import Image from "next/image";
import { H3, BodyText } from "@/modules/ui/typography";
import { cn } from "@/modules/ui";
import { BenefitItem } from "./benefit-item";
import { useState, useEffect, useRef } from "react";

interface Benefit {
  icon: LucideIcon;
  text: string;
  color: string;
}

interface FeatureCardProps {
  index: number;
  imageSrc: string;
  text: string;
  subtext: string;
  benefits: Benefit[];
  delay: number;
  video?: string;
}

export const FeatureCard = ({
  index,
  imageSrc,
  text,
  subtext,
  benefits,
  delay,
  video,
}: FeatureCardProps) => {
  const [isVideoLoading, setIsVideoLoading] = useState(true);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Use Intersection Observer to detect when the video is in view
  useEffect(() => {
    if (!video) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        setIsInView(entry.isIntersecting);
      },
      { threshold: 0.1 }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => {
      if (containerRef.current) {
        observer.unobserve(containerRef.current);
      }
    };
  }, [video]);

  // Start loading the video when it comes into view
  useEffect(() => {
    if (!video || !isInView || !videoRef.current) return;

    // Change preload to auto when in view
    videoRef.current.preload = "auto";

    // Try to play the video when in view
    const playVideo = async () => {
      try {
        if (videoRef.current) {
          await videoRef.current.play();
        }
      } catch (error) {
        console.error("Error playing video:", error);
      }
    };

    playVideo();
  }, [video, isInView]);

  const handleVideoLoaded = () => {
    setIsVideoLoading(false);
    setIsVideoPlaying(true);
  };

  const handleVideoError = () => {
    setIsVideoLoading(false);
    console.error("Error loading video");
  };

  const renderContent = () => {
    if (video) {
      return (
        <div
          ref={containerRef}
          className="relative w-full aspect-video max-h-[30rem] rounded-2xl overflow-hidden flex-shrink-0"
        >
          <div className={cn(
            'absolute inset-0 z-10 transition-opacity duration-500',
            isVideoPlaying ? 'opacity-0' : 'opacity-100'
          )}>
            <Image
              src={imageSrc}
              alt={text}
              fill
              className="rounded-xl object-cover ring-1 ring-primary/20 shadow-md"
              priority
            />
            {isVideoLoading && isInView && (
              <div className="absolute inset-0 flex items-center justify-center bg-black/5 backdrop-blur-sm rounded-xl">
                <div className="flex flex-col items-center gap-2">
                  <Loader2 className="w-8 h-8 text-primary animate-spin" />
                  <span className="text-xs text-primary/90 font-medium animate-pulse">Loading video...</span>
                </div>
              </div>
            )}
          </div>

          <video
            ref={videoRef}
            className="absolute inset-0 w-full h-full object-cover"
            autoPlay
            playsInline
            loop
            muted
            preload="metadata"
            poster={imageSrc}
            onLoadedData={handleVideoLoaded}
            onError={handleVideoError}
          >
            <source src={video} type="video/mp4" />
          </video>
        </div>
      );
    }
    return null;
  };

  return (
    <article
      className={cn(
        "relative overflow-hidden rounded-2xl",
        "bg-card/90 dark:bg-card backdrop-blur-xl border border-primary/10 dark:border-primary/20",
        "hover:border-primary/30 transition-all duration-500",
        "shadow-md hover:shadow-lg",
        "hover:z-10 group",
        "opacity-0 translate-y-4",
        "animate-fade-in-up"
      )}
      style={{ "--delay": `${delay}s` } as React.CSSProperties}
    >
      <div className="absolute inset-0 bg-gradient-to-br from-primary/[0.08] via-transparent to-secondary/[0.08] dark:from-primary/[0.15] dark:to-secondary/[0.15] opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

      <div
        className={`flex flex-col ${index % 2 === 0 ? "md:flex-row" : "md:flex-row-reverse"} md:items-start`}
      >
        <div className="md:w-1/2 p-8 flex flex-col justify-center relative z-10">
          <div className="flex items-start gap-8 mb-8">
            <div
              className={cn(
                "relative flex-shrink-0 w-20 h-20 rounded-2xl overflow-hidden mb-6",
                "before:absolute before:inset-0 before:bg-gradient-to-br before:from-primary/15 before:to-secondary/15 before:opacity-0 before:hover:opacity-100 before:transition-opacity before:duration-500",
                "shadow-sm hover:shadow-md",
                "border border-primary/10 hover:border-primary/20",
                "hover:scale-105 transition-all duration-200"
              )}
            >
              <Image
                src={imageSrc}
                alt={text}
                fill
                className="object-cover"
                priority={true}
              />
            </div>
            <div>
              <H3 className="!mb-2 p-2 text-2xl font-semibold text-foreground">
                {text}
              </H3>
              <BodyText className="text-base text-foreground/80">
                {subtext}
              </BodyText>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {benefits.map((benefit, i) => (
              <BenefitItem key={i} {...benefit} />
            ))}
          </div>
        </div>

        <div className="md:w-1/2 min-w-0 p-4">
          {renderContent()}
        </div>
      </div>
    </article>
  );
};
