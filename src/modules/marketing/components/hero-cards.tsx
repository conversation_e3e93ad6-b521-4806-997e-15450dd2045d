"use client";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Palette,
  Lay<PERSON>,
  Clock,
  Download,
  Shield,
  Gauge,
  Crown,
  Award,
  ArrowRight,
} from "lucide-react";
import { useState } from "react";
import { H3, BodyText, Small } from "@/modules/ui/typography";
import Image from "next/image";
import { Button } from "@/modules/ui/button";
import Link from "next/link";
import { cn } from "@/modules/ui";
import { FeatureCard } from "./hero-cards/feature-card";
import { ProfessionalToolsCard } from "./hero-cards/professional-tools-card";

const features = [
  {
    imageSrc: "/icons/virtual-staging-icon.webp",
    text: "Virtual Staging",
    subtext: "From Empty to Extraordinary in Seconds",
    benefits: [
      {
        icon: DollarSign,
        text: "Save thousands on physical staging",
        color: "text-green-500 dark:text-green-400",
      },
      {
        icon: Rocket,
        text: "Show properties immediately",
        color: "text-blue-500 dark:text-blue-400",
      },
      {
        icon: Star,
        text: "Win more listings instantly",
        color: "text-yellow-500 dark:text-yellow-400",
      },
      {
        icon: Palette,
        text: "Multiple style variations",
        color: "text-purple-500 dark:text-purple-400",
      },
    ],
    delay: 0.1,
    gradient: "from-blue-600 to-blue-900",
    video: "/videos/hero-cards/virtual-staging.mp4",
  },
  {
    imageSrc: "/icons/interior-design-icon.webp",
    text: "Interior Design",
    subtext: "Skip the Render, See Your Vision Instantly",
    benefits: [
      {
        icon: Layers,
        text: "Instant concept visualization",
        color: "text-indigo-500 dark:text-indigo-400",
      },
      {
        icon: Clock,
        text: "Real-time client meetings",
        color: "text-cyan-500 dark:text-cyan-400",
      },
      {
        icon: Palette,
        text: "Multiple design directions",
        color: "text-pink-500 dark:text-pink-400",
      },
      {
        icon: Rocket,
        text: "Faster approval process",
        color: "text-orange-500 dark:text-orange-400",
      },
    ],
    delay: 0.2,
    gradient: "from-indigo-600 to-indigo-900",
    video: "/videos/hero-cards/interior-design.mp4",
  },
  {
    imageSrc: "/icons/exterior-design-icon.webp",
    text: "Exterior Transformations",
    subtext: "Stunning Curb Appeal in Clicks",
    benefits: [
      {
        icon: Crown,
        text: "Enhance property exteriors",
        color: "text-amber-500 dark:text-amber-400",
      },
      {
        icon: Palette,
        text: "Seasonal variations",
        color: "text-emerald-500 dark:text-emerald-400",
      },
      {
        icon: Layers,
        text: "Landscape visualizations",
        color: "text-teal-500 dark:text-teal-400",
      },
      {
        icon: Star,
        text: "Perfect lighting & shadows",
        color: "text-violet-500 dark:text-violet-400",
      },
    ],
    delay: 0.3,
    gradient: "from-emerald-600 to-emerald-900",
    video: "/videos/exterior-design.mp4",
  },
  {
    imageSrc: "/icons/smart-image-editor-icon.webp",
    text: "Smart Image Editor",
    subtext: "Perfect Every Detail, Just Point and Transform",
    benefits: [
      {
        icon: Shield,
        text: "Remove unwanted items",
        color: "text-rose-500 dark:text-rose-400",
      },
      {
        icon: Layers,
        text: "Add missing elements",
        color: "text-sky-500 dark:text-sky-400",
      },
      {
        icon: Gauge,
        text: "Fix imperfections",
        color: "text-blue-500 dark:text-blue-400",
      },
      {
        icon: Award,
        text: "Natural transformations",
        color: "text-purple-500 dark:text-purple-400",
      },
    ],
    delay: 0.4,
    gradient: "from-purple-600 to-purple-900",
    video: "/videos/smart-editor.mp4",
  },
  {
    imageSrc: "/icons/professional-tools-icon.webp",
    text: "Professional Tools",
    subtext: "Complete Suite for Perfect Results",
    benefits: [
      {
        icon: Shield,
        text: "Background removal",
        color: "text-indigo-500 dark:text-indigo-400",
      },
      {
        icon: Gauge,
        text: "Smart upscaling",
        color: "text-cyan-500 dark:text-cyan-400",
      },
      {
        icon: Crown,
        text: "Crystal clear details",
        color: "text-emerald-500 dark:text-emerald-400",
      },
    ],
    delay: 0.5,
    gradient: "from-teal-600 to-teal-900",
  },
];

export default function HeroCards() {
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);

  return (
    <section
      id="features"
      aria-labelledby="features-heading"
      className={cn(
        "relative max-w-7xl mx-auto space-y-12 md:space-y-24",
        "opacity-0 translate-y-4",
        "animate-fade-in-up"
      )}
      style={{ "--delay": "0.4s" } as React.CSSProperties}
    >
      <div className="absolute inset-0 -z-10" aria-hidden="true">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_top_right,rgba(var(--primary-rgb),0.08),transparent_40%)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_bottom_left,rgba(var(--secondary-rgb),0.08),transparent_40%)]" />
      </div>

      {features.map((feature, index) => {
        if (feature.text === "Professional Tools") {
          return (
            <div
              key={index}
              className="flex justify-center w-full px-4 sm:px-6 lg:px-8"
            >
              <div className="w-full max-w-2xl mx-auto">
                <ProfessionalToolsCard
                  imageSrc={feature.imageSrc}
                  text={feature.text}
                  subtext={feature.subtext}
                  benefits={feature.benefits}
                  delay={feature.delay}
                />
              </div>
            </div>
          );
        }
        return (
          <FeatureCard
            key={index}
            index={index}
            imageSrc={feature.imageSrc}
            text={feature.text}
            subtext={feature.subtext}
            benefits={feature.benefits}
            delay={feature.delay}
            video={feature.video}
          />
        );
      })}

      <div
        className={cn(
          "relative text-center max-w-2xl mx-auto",
          "opacity-0 translate-y-4",
          "animate-fade-in-up"
        )}
        style={{ "--delay": "0.6s" } as React.CSSProperties}
      >
        <H3
          id="features-heading"
          className="text-2xl sm:text-3xl font-semibold bg-clip-text text-transparent bg-gradient-to-br from-foreground to-foreground/80 mb-4"
        >
          Ready to Transform Your Space?
        </H3>
        <BodyText className="text-base text-foreground/70 mb-8">
          Get started with our AI-powered tools today. No credit card required.
        </BodyText>
        <Link href="/dashboard" aria-label="Try all tools for free">
          <Button
            variant="gradient-primary"
            size="lg"
            shine
            glow
            className="text-[15px] sm:text-base px-6 sm:px-8 py-5 sm:py-6 group shadow-md hover:shadow-lg transition-shadow"
          >
            <span className="flex items-center gap-2 relative z-10">
              Try All Tools For Free
              <ArrowRight
                className="w-4 h-4 group-hover:translate-x-1 transition-transform"
                aria-hidden="true"
              />
            </span>
          </Button>
        </Link>
      </div>
    </section>
  );
}

// The cardVariants and styles can be kept for potential future use or removed if not needed
