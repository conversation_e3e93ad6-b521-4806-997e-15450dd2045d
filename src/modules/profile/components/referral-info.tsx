"use client";
import { useState } from "react";
import { But<PERSON> } from "@/modules/ui/button";
import { toast } from "sonner";
import { User } from "@prisma/client";
import Image from "next/image";
import {
  TwitterShareButton,
  FacebookShareButton,
  LinkedinShareButton,
  WhatsappShareButton,
  EmailShareButton,
} from "react-share";
import {
  Twitter,
  Facebook,
  Linkedin,
  MessageCircle,
  Mail,
  Copy,
  Share2,
  Users2,
  Instagram,
  MessageSquare,
} from "lucide-react";
import { H3, Small, BodyText } from "@/modules/ui/typography";

interface ReferralInfoProps {
  user: User & {
    referralCode?: { code: string } | null;
    totalReferrals: number;
    referralCredits: number;
  };
}

export default function ReferralInfo({ user }: ReferralInfoProps) {
  const [isLoading, setIsLoading] = useState(false);
  const referralCode = user.referralCode?.code || "";
  const referralLink = referralCode
    ? `${window.location.origin}/login?ref=${referralCode}`
    : "";

  const shareMessages = {
    twitter: `🚀 Transform your real estate photos with AI on @Renovaitor! Get 5 FREE credits using my referral code. Join me in revolutionizing property marketing! 🏠✨`,
    facebook: `🏠 Want to enhance your real estate photos with AI? Join me on Renovaitor! Use my referral code and get 5 FREE credits to start transforming your property images. Click the link below! ✨`,
    linkedin: `🏢 Revolutionize your real estate marketing with AI-powered photo enhancement! I'm using Renovaitor to transform property photos, and you can too. Get 5 FREE credits using my referral link. Join the future of real estate marketing!`,
    whatsapp: `Hey! 👋 I've been using Renovaitor to transform my real estate photos with AI, and it's amazing! Join using my referral link and get 5 FREE credits. Check it out! 🏠✨`,
    email: `Hi there!

I wanted to share an amazing tool I've been using for real estate photo enhancement. Renovaitor uses AI to transform property photos, and I think you'll love it!

Join using my referral link and get 5 FREE credits to start:`,
    instagram: {
      caption: `🏠 Transform your real estate photos with AI!
✨ Get 5 FREE credits on Renovaitor
🎁 Use my referral code: ${referralCode}
🔗 ${referralLink}

#RealEstate #AI #PropertyMarketing #RealEstatePhotography #Innovation #InteriorDesign #RealEstateMarketing #PropertyStyling #RealEstateAgent #PropertyPhotography`,
      instructions:
        "1. Open Instagram\n2. Create a new post\n3. Paste this caption",
    },
    messages: `Hey! Check out Renovaitor - it's revolutionizing real estate photos with AI! Use my referral code ${referralCode} to get 5 FREE credits. Here's the link: ${referralLink} 🏠✨`,
  };

  const copyReferralCode = async () => {
    if (!referralCode) return;
    await navigator.clipboard.writeText(referralCode);
    toast.success("Referral code copied to clipboard!");
  };

  const copyReferralLink = async () => {
    if (!referralLink) return;
    await navigator.clipboard.writeText(referralLink);
    toast.success("Referral link copied to clipboard!");
  };

  const copyInstagramMessage = async () => {
    await navigator.clipboard.writeText(shareMessages.instagram.caption);
    toast.success("Instagram caption copied!", {
      description: shareMessages.instagram.instructions,
      duration: 5000,
    });
  };

  const copyMessagesText = async () => {
    await navigator.clipboard.writeText(shareMessages.messages);
    toast.success("Message text copied to clipboard!");
  };

  return (
    <div
      className="relative overflow-hidden rounded-xl
                bg-gradient-to-b from-background to-background/80
                border border-border/50
                hover:border-border
                transition-all duration-300
                shadow-sm"
    >
      <div className="p-4 sm:p-6 md:p-8">
        <div className="flex items-center gap-3 mb-6">
          <Share2 className="h-4 w-4 md:h-5 md:w-5 text-foreground/70" />
          <H3 className="!text-lg md:!text-xl">Refer and Earn</H3>
        </div>

        <div className="space-y-6">
          {/* Stats Grid */}
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-muted/30 p-4 rounded-lg hover:bg-muted/40 transition-colors">
              <div className="flex items-center gap-3 mb-2">
                <Users2 className="h-5 w-5 text-foreground/70" />
                <p className="text-sm font-medium text-foreground/70">
                  Total Referrals
                </p>
              </div>
              <p className="text-2xl font-semibold text-foreground">
                {user.totalReferrals}
              </p>
            </div>
            <div className="bg-muted/30 p-4 rounded-lg hover:bg-muted/40 transition-colors">
              <div className="flex items-center gap-3 mb-2">
                <Image
                  src="/images/coin.png"
                  alt="Credits"
                  width={20}
                  height={20}
                  className="object-contain"
                />
                <p className="text-sm font-medium text-foreground/70">
                  Referral Credits
                </p>
              </div>
              <p className="text-2xl font-semibold text-foreground">
                {user.referralCredits}
              </p>
            </div>
          </div>

          {/* Referral Code Section */}
          {referralCode && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Small className="text-foreground/70">Your Referral Code</Small>
                <div className="flex gap-2">
                  <input
                    type="text"
                    readOnly
                    value={referralCode}
                    className="flex-1 bg-muted/30 border border-border/50 rounded-lg px-4 py-2 text-sm font-mono"
                  />
                  <Button
                    onClick={copyReferralCode}
                    disabled={isLoading}
                    variant="outline"
                    size="icon"
                    className="hover:bg-muted/50 transition-colors"
                  >
                    <Copy className="h-4 w-4 text-foreground/70" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Small className="text-foreground/70">Your Referral Link</Small>
                <div className="flex gap-2">
                  <input
                    type="text"
                    readOnly
                    value={referralLink}
                    className="flex-1 bg-muted/30 border border-border/50 rounded-lg px-4 py-2 text-sm font-mono"
                  />
                  <Button
                    onClick={copyReferralLink}
                    disabled={isLoading}
                    variant="outline"
                    size="icon"
                    className="hover:bg-muted/50 transition-colors"
                  >
                    <Copy className="h-4 w-4 text-foreground/70" />
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Social Sharing Section */}
          <div className="space-y-3">
            <Small className="text-foreground/70">
              Share your referral link
            </Small>
            <div className="flex flex-wrap gap-2">
              <TwitterShareButton
                url={referralLink}
                title={shareMessages.twitter}
              >
                <Button
                  variant="outline"
                  size="icon"
                  className="hover:bg-muted/50 transition-colors"
                >
                  <Twitter className="h-4 w-4 text-foreground/70" />
                </Button>
              </TwitterShareButton>

              <FacebookShareButton url={referralLink} hashtag="#Renovaitor">
                <Button
                  variant="outline"
                  size="icon"
                  className="hover:bg-muted/50 transition-colors"
                >
                  <Facebook className="h-4 w-4 text-foreground/70" />
                </Button>
              </FacebookShareButton>

              <LinkedinShareButton
                url={referralLink}
                title={shareMessages.linkedin}
              >
                <Button
                  variant="outline"
                  size="icon"
                  className="hover:bg-muted/50 transition-colors"
                >
                  <Linkedin className="h-4 w-4 text-foreground/70" />
                </Button>
              </LinkedinShareButton>

              <WhatsappShareButton
                url={referralLink}
                title={shareMessages.whatsapp}
              >
                <Button
                  variant="outline"
                  size="icon"
                  className="hover:bg-muted/50 transition-colors"
                >
                  <MessageCircle className="h-4 w-4 text-foreground/70" />
                </Button>
              </WhatsappShareButton>

              <EmailShareButton
                url={referralLink}
                subject="Transform your real estate photos with Renovaitor!"
                body={shareMessages.email}
              >
                <Button
                  variant="outline"
                  size="icon"
                  className="hover:bg-muted/50 transition-colors"
                >
                  <Mail className="h-4 w-4 text-foreground/70" />
                </Button>
              </EmailShareButton>

              <Button
                variant="outline"
                size="icon"
                className="hover:bg-muted/50 transition-colors group relative"
                onClick={copyInstagramMessage}
                title="Copy Instagram caption"
              >
                <Instagram className="h-4 w-4 text-foreground/70" />
                <span
                  className="absolute -top-10 left-1/2 -translate-x-1/2 
                               bg-popover text-popover-foreground text-xs rounded px-2 py-1
                               opacity-0 group-hover:opacity-100 transition-opacity
                               whitespace-nowrap shadow-sm"
                >
                  Copy Instagram caption
                </span>
              </Button>

              <Button
                variant="outline"
                size="icon"
                className="hover:bg-muted/50 transition-colors"
                onClick={copyMessagesText}
              >
                <MessageSquare className="h-4 w-4 text-foreground/70" />
              </Button>
            </div>
          </div>

          {/* Info Card */}
          <div className="bg-muted/30 p-6 rounded-lg hover:bg-muted/40 transition-colors">
            <div className="flex items-start gap-4">
              <Share2 className="h-4 w-4 text-foreground/70 mt-1" />
              <div>
                <Small className="font-medium text-foreground/90">
                  How it works
                </Small>
                <Small className="block text-xs text-foreground/70 mt-1.5 leading-relaxed">
                  Share your referral link with friends. When they sign up,
                  you&apos;ll both receive 5 credits! The more friends you
                  invite, the more credits you earn.
                </Small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
