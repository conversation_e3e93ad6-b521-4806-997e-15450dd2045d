"use client";
import {
  Package,
  Calendar,
  CheckCircle2,
  <PERSON>Circle,
  CreditCard,
} from "lucide-react";
import { But<PERSON> } from "@/modules/ui/button";
import { H3, Small, BodyText } from "@/modules/ui/typography";
import { Subscription } from "@prisma/client";
import { getSubscriptionPortalUrl } from "@/modules/profile/actions/subscription";
import { formatDate } from "@/lib/utils";
import Link from "next/link";

interface SubscriptionInfoProps {
  subscription: Subscription | null;
}

export default function SubscriptionInfo({
  subscription,
}: SubscriptionInfoProps) {
  return (
    <div
      className="relative overflow-hidden rounded-xl
                bg-gradient-to-b from-background to-background/80
                border border-border/50
                hover:border-border
                transition-all duration-300
                shadow-sm"
    >
      <div className="p-4 sm:p-6 md:p-8">
        <div className="flex items-center gap-3 mb-6">
          <CreditCard className="h-4 w-4 md:h-5 md:w-5 text-foreground/70" />
          <H3 className="!text-lg md:!text-xl">Subscription Details</H3>
        </div>

        {subscription ? (
          <div className="space-y-4">
            <div className="bg-muted/30 p-4 rounded-lg hover:bg-muted/40 transition-colors">
              <div className="flex items-center gap-3 mb-1.5">
                <Package className="h-4 w-4 text-foreground/70" />
                <Small className="text-foreground/70">Current Plan</Small>
              </div>
              <BodyText className="font-medium">
                {subscription.packageName}
              </BodyText>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="bg-muted/30 p-3 rounded-lg hover:bg-muted/40 transition-colors">
                <div className="flex items-center gap-3 mb-1.5">
                  {subscription.isActive ? (
                    <CheckCircle2 className="h-4 w-4 text-emerald-500" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-500" />
                  )}
                  <Small className="text-foreground/70">Status</Small>
                </div>
                <span
                  className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                    subscription.isActive
                      ? "bg-emerald-100/50 text-emerald-700 dark:bg-emerald-500/10 dark:text-emerald-400"
                      : "bg-red-100/50 text-red-700 dark:bg-red-500/10 dark:text-red-400"
                  }`}
                >
                  {subscription.paymentStatus}
                </span>
              </div>

              {subscription.nextBillingDate && (
                <div className="bg-muted/30 p-3 rounded-lg hover:bg-muted/40 transition-colors">
                  <div className="flex items-center gap-3 mb-1.5">
                    <Calendar className="h-4 w-4 text-foreground/70" />
                    <Small className="text-foreground/70">Next Billing</Small>
                  </div>
                  <Small className="font-medium">
                    {formatDate(subscription.nextBillingDate)}
                  </Small>
                </div>
              )}
            </div>

            <div className="pt-4">
              <ManageSubscriptionButton
                subscriptionId={subscription.subscriptionId}
              />
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="bg-muted/30 p-6 rounded-lg hover:bg-muted/40 transition-colors text-center">
              <XCircle className="h-5 w-5 md:h-6 md:w-6 text-foreground/70 mx-auto mb-3" />
              <BodyText className="text-foreground/70 mb-4">
                No active subscription
              </BodyText>
              <Link href="/pricing" className="w-full">
                <Button className="w-full" size="sm">
                  View Plans
                </Button>
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

function ManageSubscriptionButton({
  subscriptionId,
}: {
  subscriptionId: string | null;
}) {
  return (
    <Button
      className="w-full"
      onClick={async () => {
        const url = await getSubscriptionPortalUrl(subscriptionId);
        if (url) window.location.href = url;
      }}
    >
      Manage Subscription
    </Button>
  );
}
