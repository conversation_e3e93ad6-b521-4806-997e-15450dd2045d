"use client";
import { User } from "next-auth";
import Image from "next/image";
import { useState } from "react";
import { But<PERSON> } from "@/modules/ui/button";
import { Input } from "@/modules/ui/input";
import { uploadUserImageToCloudflare } from "@/lib/cloudflare";
import { toast } from "sonner";
import { updateUserProfile } from "@/lib/user";
import { UserCircle2, Mail, Camera, Pencil } from "lucide-react";
import { H3, Small, BodyText } from "@/modules/ui/typography";

interface ProfileInfoProps {
  user: User & { id: string };
}

export default function ProfileInfo({ user }: ProfileInfoProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [name, setName] = useState(user.name || "");
  const [isLoading, setIsLoading] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | null>(null);

  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    console.log("Selected file:", {
      name: file.name,
      type: file.type,
      size: file.size,
    });

    // Basic validation
    if (!file.type.startsWith("image/")) {
      toast.error("Please upload an image file");
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      toast.error("Image must be less than 5MB");
      return;
    }

    // Show preview immediately
    const reader = new FileReader();
    reader.onloadend = () => {
      setPreviewImage(reader.result as string);
    };
    reader.readAsDataURL(file);

    try {
      setIsUploading(true);

      const formData = new FormData();
      formData.append("file", file);
      console.log("FormData created with file");

      const imageUrl = await uploadUserImageToCloudflare(formData);
      console.log("Received image URL:", imageUrl);

      if (!imageUrl) {
        throw new Error("No image URL returned");
      }

      setPreviewImage(imageUrl);
      toast.success("Image uploaded successfully");
    } catch (error) {
      console.error("Detailed upload error:", {
        error,
        message: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : undefined,
      });
      toast.error("Failed to upload image. Please try again.");
      setPreviewImage(user.image || null);
    } finally {
      setIsUploading(false);
    }
  };

  const handleSubmit = async (e: React.MouseEvent) => {
    e.preventDefault();
    if (!user.id) {
      toast.error("User ID is required");
      return;
    }

    setIsLoading(true);
    try {
      await updateUserProfile(user.id, {
        name: name || undefined,
        image: previewImage || undefined,
      });

      toast.success("Profile updated successfully");
      setIsEditing(false);
    } catch (error) {
      toast.error("Failed to update profile");
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      className="relative overflow-hidden rounded-xl
                bg-gradient-to-b from-background to-background/80
                border border-border/50
                hover:border-border
                transition-all duration-300
                shadow-sm"
    >
      <div className="p-4 sm:p-6 md:p-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <UserCircle2 className="h-4 w-4 md:h-5 md:w-5 text-muted-foreground" />
            <H3 className="!text-lg md:!text-xl">Personal Information</H3>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditing(!isEditing)}
            className="flex items-center gap-2"
          >
            {isEditing ? (
              "Cancel"
            ) : (
              <>
                <Pencil className="h-3.5 w-3.5" />
                <span>Edit</span>
              </>
            )}
          </Button>
        </div>

        <div className="flex flex-col sm:flex-row items-center sm:items-start gap-6">
          <div className="flex-shrink-0 relative group">
            <div
              className="relative h-16 w-16 md:h-20 md:w-20 rounded-full overflow-hidden 
                            ring-2 ring-border/50 hover:ring-border transition-colors"
            >
              <Image
                src={previewImage || user.image || "/default-avatar.png"}
                alt={user.name || "Profile"}
                fill
                className="object-cover"
              />
            </div>
            {isEditing && (
              <label className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-full cursor-pointer opacity-0 group-hover:opacity-100 transition-opacity">
                <div className="flex items-center gap-2 text-white text-xs">
                  <Camera className="h-3.5 w-3.5" />
                  <span>{isUploading ? "Uploading..." : "Change"}</span>
                </div>
                <input
                  type="file"
                  className="hidden"
                  accept="image/*"
                  onChange={handleImageChange}
                  disabled={isUploading || isLoading}
                />
              </label>
            )}
          </div>

          <div className="flex-grow w-full space-y-4">
            <div className="bg-muted/30 p-3 rounded-lg hover:bg-muted/40 transition-colors">
              <div className="flex items-center gap-3 mb-1.5">
                <UserCircle2 className="h-3.5 w-3.5 md:h-4 md:w-4 text-muted-foreground" />
                <Small className="!text-xs md:!text-sm !text-muted-foreground">
                  Name
                </Small>
              </div>
              {isEditing ? (
                <Input
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="mt-1"
                  disabled={isLoading}
                />
              ) : (
                <BodyText className="!text-sm md:!text-base">
                  {user.name}
                </BodyText>
              )}
            </div>

            <div className="bg-muted/30 p-3 rounded-lg hover:bg-muted/40 transition-colors">
              <div className="flex items-center gap-3 mb-1.5">
                <Mail className="h-3.5 w-3.5 md:h-4 md:w-4 text-muted-foreground" />
                <Small className="!text-xs md:!text-sm !text-muted-foreground">
                  Email
                </Small>
              </div>
              <BodyText className="!text-sm md:!text-base">
                {user.email}
              </BodyText>
            </div>
          </div>
        </div>

        {isEditing && (
          <div className="mt-6 flex justify-end">
            <Button
              variant="default"
              size="sm"
              onClick={handleSubmit}
              disabled={isLoading}
            >
              {isLoading ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
