"use server";

import prisma from "@/lib/prisma/prisma";
import { currentUser } from "@/modules/auth/actions/user-actions";

export async function getSubscriptionPortalUrl(
  subscriptionId: string | null
): Promise<string | null> {
  const user = await currentUser();

  if (!user?.id) return null;

  const subscription = await prisma.subscription.findUnique({
    where: { userId: user.id },
    select: {
      subscriptionId: true,
      customerPortalUrl: true,
      updatePaymentMethodUrl: true,
      cancelUrl: true,
    },
  });

  if (!subscription) return null;

  return subscription.customerPortalUrl || null;
}
