"use client";
import { useState } from "react";
import { use<PERSON><PERSON>, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/modules/ui/button";
import { useToast } from "@/modules/ui/use-toast";
import { Input } from "@/modules/ui/input";
import * as Icons from "@/modules/ui/icons";
import { SignIn } from "@/modules/auth/components/login/sign-in-google";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/modules/ui/form";
import { signIn } from "next-auth/react";
import { useSearchParams, useRouter } from "next/navigation";
import { EmailSentDialog, EmailSentDialogProps } from "./email-sent-dialog";

const schema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().optional(),
});

type FormData = z.infer<typeof schema>;

interface AdminCheckResponse {
  isAdmin: boolean;
}

const LoginForm = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get("callbackUrl") || "/dashboard";
  const [isLoading, setIsLoading] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [showEmailSentDialog, setShowEmailSentDialog] = useState(false);
  const { toast } = useToast();

  const form = useForm<FormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const checkAdmin = async (email: string): Promise<boolean> => {
    try {
      const res = await fetch("/api/check-admin", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });
      const data = (await res.json()) as AdminCheckResponse;
      return data.isAdmin;
    } catch (error) {
      console.error("Error checking admin status:", error);
      return false;
    }
  };

  const onSubmit: SubmitHandler<FormData> = async (formData: FormData) => {
    try {
      setIsLoading(true);
      const admin = await checkAdmin(formData.email);
      setIsAdmin(admin);

      if (admin && !formData.password) {
        toast({
          title: "Admin Login",
          description: "Please enter your password.",
          variant: "default",
        });
        return;
      }

      if (admin) {
        const result = await signIn("credentials", {
          username: formData.email,
          password: formData.password,
          redirect: false,
        });

        if (result?.error) {
          toast({
            title: "Error",
            description: result.error,
            variant: "destructive",
          });
          return;
        }

        router.push(callbackUrl);
      } else {
        await signIn("resend", {
          email: formData.email,
          redirect: false,
        });
        setShowEmailSentDialog(true);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: (error as Error).message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDialogClose = (): void => {
    setShowEmailSentDialog(false);
  };

  const handleEmailLinkClick = (): void => {
    window.location.href = "mailto:";
    setTimeout(() => {
      setShowEmailSentDialog(false);
    }, 500);
  };

  const handleTryAnotherEmail = (): void => {
    setShowEmailSentDialog(false);
    form.reset();
  };

  const handleResendEmail = async (): Promise<void> => {
    try {
      await signIn("resend", {
        email: form.getValues("email"),
        redirect: false,
      });

      toast({
        title: "Email Resent",
        description: "We've sent you another magic link.",
        variant: "default",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to resend email. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="grid gap-4 w-full mt-6 sm:mt-8">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="space-y-3 sm:space-y-4"
        >
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-foreground/90 font-medium flex flex-col sm:flex-row sm:items-center sm:justify-between">
                  <span className="mb-1 sm:mb-0">
                    Email Address <span className="text-primary">*</span>
                  </span>
                  <span className="text-[0.7rem] sm:text-xs text-muted-foreground">
                    We&apos;ll send you a secure login link
                  </span>
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder="<EMAIL>"
                    {...field}
                    className="h-10 sm:h-11 bg-background/50 border-primary/10 
                             text-foreground placeholder:text-muted-foreground/60
                             focus:border-primary/30 focus:ring-primary/20
                             backdrop-blur-sm transition-all duration-200
                             rounded-lg"
                    onBlur={async () => {
                      const isAdminUser = await checkAdmin(field.value);
                      setIsAdmin(isAdminUser);
                    }}
                  />
                </FormControl>
                <FormMessage className="text-xs sm:text-sm" />
              </FormItem>
            )}
          />

          {isAdmin && (
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-foreground/90 font-medium flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <span className="mb-1 sm:mb-0">
                      Admin Password <span className="text-primary">*</span>
                    </span>
                    <span className="text-[0.7rem] sm:text-xs text-muted-foreground">
                      Required for admin access
                    </span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder="Enter your admin password"
                      {...field}
                      className="h-10 sm:h-11 bg-background/50 border-primary/10 
                               text-foreground placeholder:text-muted-foreground/60
                               focus:border-primary/30 focus:ring-primary/20
                               backdrop-blur-sm transition-all duration-200
                               rounded-lg"
                    />
                  </FormControl>
                  <FormMessage className="text-xs sm:text-sm" />
                </FormItem>
              )}
            />
          )}

          <Button
            type="submit"
            variant="gradient-primary"
            size="lg"
            shine
            glow
            className="w-full text-[15px] sm:text-base px-6 sm:px-8 py-5 sm:py-6 group"
          >
            <span className="flex items-center gap-2 relative z-10">
              {isLoading && <Icons.Spinner className="h-4 w-4 animate-spin" />}
              {isLoading ? "Signing In..." : "Continue with Email"}
            </span>
          </Button>

          {!isAdmin && (
            <>
              <div className="relative my-6">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t border-primary/10" />
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-background/80 px-2 text-muted-foreground backdrop-blur-sm">
                    Or continue with
                  </span>
                </div>
              </div>
              <SignIn />
              <p className="text-xs text-center text-muted-foreground mt-4">
                Fast, secure sign-in with your Google account
              </p>
            </>
          )}
        </form>
      </Form>
      <EmailSentDialog
        isOpen={showEmailSentDialog}
        onClose={handleDialogClose}
        onOpenEmail={handleEmailLinkClick}
        email={form.getValues("email")}
        onTryAnotherEmail={handleTryAnotherEmail}
        onResend={handleResendEmail}
      />
    </div>
  );
};

export default LoginForm;
