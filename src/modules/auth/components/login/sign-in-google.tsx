"use client";
import { useState } from "react";
import { signIn } from "next-auth/react";
import { But<PERSON> } from "@/modules/ui/button";
import { useToast } from "@/modules/ui/use-toast";
import { Loader2, Google } from "@/modules/ui/icons";
import { useSearchParams } from "next/navigation";

export function SignIn() {
  const { toast } = useToast();
  const [isGoogleLoading, setIsGoogleLoading] = useState<boolean>(false);
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get("callbackUrl") || "/dashboard";

  const handleSignIn = async () => {
    setIsGoogleLoading(true);
    try {
      const result = await signIn("google", {
        callbackUrl,
        redirect: true,
      });
    } catch (error) {
      console.error("Sign in error:", error);
      toast({
        title: "Sign in failed",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
      setIsGoogleLoading(false);
    }
  };

  return (
    <Button
      onClick={handleSignIn}
      variant="gradient-secondary"
      size="lg"
      shine
      className="w-full text-[15px] sm:text-base px-6 sm:px-8 py-5 sm:py-6 group"
      disabled={isGoogleLoading}
    >
      <span className="flex items-center gap-2 relative z-10">
        {isGoogleLoading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Google className="h-4 w-4" />
        )}
        {isGoogleLoading ? "Connecting..." : "Continue with Google"}
      </span>
    </Button>
  );
}
