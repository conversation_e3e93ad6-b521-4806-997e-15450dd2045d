"use client";

import { Dialog, DialogContent } from "@/modules/ui/dialog";
import { <PERSON><PERSON> } from "@/modules/ui/button";
import {
  MailIcon,
  ArrowR<PERSON>,
  Loader2,
  Clock,
  Inbox,
  RotateCw,
} from "lucide-react";
import { useState } from "react";
import { cn } from "@/lib/utils";

export interface EmailSentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onOpenEmail: () => void;
  email: string;
  onTryAnotherEmail: () => void;
  onResend: () => Promise<void>;
}

const emailClients = [
  {
    name: "Default email app",
    url: "mailto:",
    icon: <MailIcon className="h-4 w-4" />,
    primary: true,
  },
  {
    name: "Gmail",
    url: "https://mail.google.com/mail",
    icon: (
      <svg
        className="h-4 w-4"
        viewBox="0 0 24 24"
        fill="currentColor"
        aria-hidden="true"
      >
        <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm-.4 4.25l-7.07 4.42c-.32.2-.74.2-1.06 0L4.4 8.25c-.25-.16-.4-.43-.4-.72 0-.67.73-1.07 1.3-.72L12 11l6.7-4.19c.57-.35 1.3.05 1.3.72 0 .29-.15.56-.4.72z" />
      </svg>
    ),
  },
];

export const EmailSentDialog = ({
  isOpen,
  onClose,
  onOpenEmail,
  email,
  onTryAnotherEmail,
  onResend,
}: EmailSentDialogProps) => {
  const [isResending, setIsResending] = useState(false);

  const handleResend = async () => {
    setIsResending(true);
    try {
      await onResend();
    } catch (error) {
      console.error("Failed to resend email:", error);
    } finally {
      setIsResending(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        size="sm"
        className="
          max-h-[85vh]
          overflow-y-auto
          border
          shadow-md
          rounded-2xl
          p-0
          bg-gradient-to-b from-background to-background/95
          backdrop-blur-xl
        "
      >
        <div className="flex flex-col items-center justify-center space-y-5 p-6">
          {/* Animated Email Icon */}
          <div className="relative w-16 h-16 mx-auto mb-2">
            <div className="absolute inset-0 bg-primary/20 rounded-full blur-2xl animate-pulse" />
            <div className="relative rounded-full bg-gradient-to-b from-primary/20 via-primary/10 to-transparent p-4 backdrop-blur-sm ring-1 ring-primary/20">
              <MailIcon className="h-8 w-8 text-primary animate-bounce" />
            </div>
          </div>

          {/* Title and Email */}
          <div className="space-y-2 text-center">
            <h2 className="text-xl font-semibold tracking-tight bg-gradient-to-br from-foreground to-foreground/70 bg-clip-text text-transparent">
              Check your inbox
            </h2>
            <p className="text-sm text-muted-foreground/80">
              We&apos;ve sent a magic link to
            </p>
            <p className="text-sm font-medium text-foreground px-4 py-2 bg-muted/50 rounded-lg max-w-[250px] truncate">
              {email}
            </p>
          </div>

          {/* Instructions */}
          <div className="w-full space-y-3">
            {/* Main instruction */}
            <div className="rounded-xl border border-primary/10 bg-primary/5 p-4">
              <div className="flex items-center gap-3">
                <div className="flex-shrink-0 rounded-full bg-primary/10 p-1.5">
                  <ArrowRight className="h-4 w-4 text-primary" />
                </div>
                <p className="text-sm text-muted-foreground">
                  Click the link in the email to sign in
                </p>
              </div>
            </div>

            {/* Additional Info */}
            <div className="space-y-2">
              {/* Delivery Time */}
              <div className="flex items-start gap-2">
                <div className="flex-shrink-0 mt-0.5">
                  <Clock className="h-4 w-4 text-muted-foreground/70" />
                </div>
                <span className="text-xs text-muted-foreground/80">
                  The email should arrive within 1 minute
                </span>
              </div>

              {/* Spam Check */}
              <div className="flex items-start gap-2">
                <div className="flex-shrink-0 mt-0.5">
                  <Inbox className="h-4 w-4 text-muted-foreground/70" />
                </div>
                <span className="text-xs text-muted-foreground/80">
                  Can&apos;t find the email? Check your spam folder or make sure
                  you entered the correct email address
                </span>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col items-center gap-3 w-full pt-2">
            {/* Email Clients */}
            <div className="w-full flex gap-2">
              {emailClients.map((client) => (
                <Button
                  key={client.name}
                  variant={client.primary ? "gradient-primary" : "outline"}
                  size="sm"
                  onClick={() => {
                    window.open(client.url, "_blank");
                    setTimeout(() => onClose(), 500);
                  }}
                  className={cn(
                    "text-sm font-medium flex items-center justify-center gap-2 transition-all duration-200",
                    client.primary ? "w-4/5" : "w-1/5",
                    client.primary && "py-5 group shine glow",
                    !client.primary &&
                      cn(
                        "text-muted-foreground hover:text-foreground",
                        "border-primary/10 hover:border-primary/20",
                        "hover:bg-primary/5 hover:scale-105",
                        "backdrop-blur-sm"
                      )
                  )}
                  title={!client.primary ? `Open in ${client.name}` : undefined}
                >
                  <span
                    className={cn(
                      "relative z-10 flex items-center gap-2",
                      !client.primary &&
                        "hover:scale-110 transition-transform duration-200"
                    )}
                  >
                    {client.icon}
                    {client.primary && "Open email app"}
                  </span>
                </Button>
              ))}
            </div>

            <div className="w-full pt-2 space-y-2 border-t border-border/50">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleResend}
                disabled={isResending}
                className="w-full text-sm font-medium text-muted-foreground hover:text-primary transition-colors group relative overflow-hidden py-5"
              >
                <span className="relative z-10 flex items-center justify-center gap-2">
                  {isResending ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Resending...
                    </>
                  ) : (
                    <>
                      <RotateCw className="h-4 w-4" />
                      Resend email
                    </>
                  )}
                </span>
              </Button>
              <button
                onClick={(e) => {
                  e.preventDefault();
                  onTryAnotherEmail();
                }}
                className="w-full text-xs text-muted-foreground/60 hover:text-muted-foreground transition-colors py-2"
              >
                Try another email address
              </button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
