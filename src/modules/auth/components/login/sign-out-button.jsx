"use client";
import React from "react";
import { useRouter } from "next/navigation";
import { DropdownMenuItem } from "@/modules/ui/dropdown-menu";
import { Check, LogOut } from "@/modules/ui/icons";
import { useToast } from "@/modules/ui/use-toast";
import { signOut } from "next-auth/react";

const SignOutButton = () => {
  const router = useRouter();
  const { toast } = useToast();

  const handleSignOut = async () => {
    await signOut({ redirect: false });
    toast({
      title: (
        <div className="flex items-center">
          <Check className="mr-2 h-5 w-5 text-green-500" />
          <span>Logout successfully!</span>
        </div>
      ),
      description: "You have successfully loggedout from the app",
    });
    router.push("/");
  };

  return (
    <DropdownMenuItem onSelect={handleSignOut}>
      <LogOut className="mr-2 h-5 w-5" />
      Logout
    </DropdownMenuItem>
  );
};

export default SignOutButton;
