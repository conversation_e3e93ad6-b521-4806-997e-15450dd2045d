import { cn } from "@/lib/utils";

// Define base spacing and alignment types
const spacing = {
  tight: "mb-2",
  normal: "mb-4",
  loose: "mb-6",
} as const;

const alignment = {
  left: "text-left",
  center: "text-center",
  right: "text-right",
} as const;

// Define variants for flexible styling
const variants = {
  default: "",
  gradient: "bg-clip-text text-transparent",
  muted: "text-muted-foreground",
  primary: "text-primary",
  secondary: "text-secondary",
} as const;

// Type definitions
type SpacingType = keyof typeof spacing;
type AlignmentType = keyof typeof alignment;
type VariantType = keyof typeof variants;

// Base interface for typography props
interface BaseTypographyProps {
  className?: string;
  spacing?: SpacingType;
  align?: AlignmentType;
}

// Create a type helper for combining props
type CombineProps<T> = BaseTypographyProps & T;

// Specific interfaces for different component types
interface TypographyProps
  extends CombineProps<React.HTMLAttributes<HTMLElement>> {}

interface HeadingProps
  extends CombineProps<React.HTMLAttributes<HTMLHeadingElement>> {
  variant?: VariantType;
}

interface BodyTextProps
  extends CombineProps<React.HTMLAttributes<HTMLParagraphElement>> {
  variant?: keyof typeof typography.body;
}

interface GradientHeadingProps
  extends CombineProps<React.HTMLAttributes<HTMLHeadingElement>> {
  variant?: keyof typeof typography.gradient;
}

// Update the interfaces to use correct HTML attributes
interface BlockquoteProps
  extends CombineProps<React.BlockquoteHTMLAttributes<HTMLQuoteElement>> {}

interface SmallProps extends CombineProps<React.HTMLAttributes<HTMLElement>> {}

interface SubtleProps
  extends CombineProps<React.HTMLAttributes<HTMLParagraphElement>> {}

// Enhanced typography system with improved mobile scaling
export const typography = {
  h1: "scroll-m-20 text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold tracking-tight leading-[1.1] mb-4",
  h2: "scroll-m-20 text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-semibold tracking-tight leading-[1.15] mb-4",
  h3: "scroll-m-20 text-base sm:text-lg md:text-xl lg:text-2xl xl:text-3xl font-semibold tracking-tight mb-3",
  h4: "scroll-m-20 text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl font-semibold tracking-tight mb-3",
  h5: "scroll-m-20 text-sm sm:text-base md:text-lg lg:text-xl font-semibold tracking-tight mb-2",
  h6: "scroll-m-20 text-xs sm:text-sm md:text-base lg:text-lg font-semibold tracking-tight mb-2",

  body: {
    xl: "text-base sm:text-lg md:text-xl lg:text-2xl leading-relaxed tracking-tight",
    large:
      "text-sm sm:text-base md:text-lg lg:text-xl leading-relaxed tracking-tight",
    default: "text-[13px] sm:text-sm md:text-base lg:text-lg leading-relaxed",
    small: "text-xs sm:text-[13px] md:text-sm lg:text-base leading-relaxed",
    xs: "text-[11px] sm:text-xs md:text-sm leading-normal",
  },

  gradient: {
    primary: "bg-gradient-to-br from-foreground via-foreground/90 to-primary",
    secondary: "bg-gradient-to-br from-primary to-secondary",
    accent: "bg-gradient-to-br from-secondary via-primary to-foreground",
    rainbow: "bg-gradient-to-r from-purple-500 via-primary to-pink-500",
  },

  colors: {
    default: "text-foreground",
    muted: "text-muted-foreground",
    primary: "text-primary",
    secondary: "text-secondary",
    accent: "text-accent",
    white: "text-white",
    "white/80": "text-white/80",
  },

  styles: {
    lead: "text-sm sm:text-base md:text-lg lg:text-xl text-foreground/80 leading-relaxed tracking-tight",
    large: "text-sm sm:text-base md:text-lg font-semibold leading-relaxed",
    small: "text-[11px] sm:text-xs md:text-sm font-medium leading-tight",
    muted:
      "text-[11px] sm:text-xs md:text-sm text-muted-foreground leading-tight",
    subtle:
      "text-[11px] sm:text-xs md:text-sm text-foreground/70 leading-tight",
  },

  heroHeading:
    "text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold leading-[1.1] tracking-tight text-foreground",
  sectionHeading:
    "text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold leading-[1.2] tracking-tight text-foreground",
  cardTitle:
    "text-base sm:text-lg md:text-xl lg:text-2xl font-semibold leading-none tracking-tight",
  blockquote:
    "mt-6 border-l-2 border-primary pl-6 italic text-sm sm:text-base md:text-lg leading-relaxed",
} as const;

// Helper function to get spacing class
const getSpacingClass = (spacingValue: SpacingType | undefined) => {
  if (!spacingValue) return "";
  return spacing[spacingValue];
};

// Helper function to get alignment class
const getAlignmentClass = (alignValue: AlignmentType | undefined) => {
  if (!alignValue) return "";
  return alignment[alignValue];
};

// Component implementations
export const H1 = ({ className, spacing, align, ...props }: HeadingProps) => (
  <h1
    className={cn(
      typography.h1,
      getSpacingClass(spacing),
      getAlignmentClass(align),
      className
    )}
    {...props}
  />
);

export const H2 = ({ className, spacing, align, ...props }: HeadingProps) => (
  <h2
    className={cn(
      typography.h2,
      getSpacingClass(spacing),
      getAlignmentClass(align),
      className
    )}
    {...props}
  />
);

export const H3 = ({ className, spacing, align, ...props }: HeadingProps) => (
  <h3
    className={cn(
      typography.h3,
      getSpacingClass(spacing),
      getAlignmentClass(align),
      className
    )}
    {...props}
  />
);

export const GradientHeading = ({
  children,
  className,
  variant = "primary",
  spacing,
  align,
  ...props
}: GradientHeadingProps) => {
  return (
    <h2
      className={cn(
        "scroll-m-20 text-xl sm:text-2xl md:text-3xl lg:text-4xl font-semibold",
        "bg-clip-text text-transparent",
        typography.gradient[variant],
        getSpacingClass(spacing),
        align && alignment[align],
        className
      )}
      {...props}
    >
      {children}
    </h2>
  );
};

export const Lead = ({
  className,
  spacing,
  align,
  ...props
}: TypographyProps) => (
  <p
    className={cn(
      typography.styles.lead,
      getSpacingClass(spacing),
      getAlignmentClass(align),
      className
    )}
    {...props}
  />
);

export const BodyText = ({
  className,
  variant = "default",
  spacing,
  align,
  ...props
}: BodyTextProps) => {
  return (
    <p
      className={cn(
        typography.body[variant],
        getSpacingClass(spacing),
        align && alignment[align],
        className
      )}
      {...props}
    />
  );
};

export const Blockquote = ({
  children,
  className,
  spacing = "normal",
  align,
  ...props
}: BlockquoteProps) => {
  return (
    <blockquote
      className={cn(
        typography.blockquote,
        getSpacingClass(spacing),
        getAlignmentClass(align),
        className
      )}
      {...props}
    >
      {children}
    </blockquote>
  );
};

export const Small = ({
  className,
  spacing,
  align,
  ...props
}: TypographyProps) => (
  <p
    className={cn(
      typography.styles.small,
      "tracking-tight",
      getSpacingClass(spacing),
      getAlignmentClass(align),
      className
    )}
    {...props}
  />
);

export const Subtle = ({
  children,
  className,
  spacing = "normal",
  align,
  ...props
}: SubtleProps) => {
  return (
    <p
      className={cn(
        typography.styles.subtle,
        "tracking-tight",
        getSpacingClass(spacing),
        getAlignmentClass(align),
        className
      )}
      {...props}
    >
      {children}
    </p>
  );
};
