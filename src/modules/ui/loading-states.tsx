import { Loader2 } from "lucide-react";
import { cn } from "@/modules/ui/utils/cn";

interface SpinnerProps {
  size?: "sm" | "md" | "lg";
  className?: string;
}

export function Spinner({ size = "md", className }: SpinnerProps) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-8 w-8",
    lg: "h-12 w-12",
  };

  return (
    <Loader2
      className={cn("animate-spin text-primary", sizeClasses[size], className)}
    />
  );
}

export function PulseBars() {
  return (
    <div className="flex items-center space-x-2">
      {Array.from({ length: 3 }).map((_, i) => (
        <div
          key={i}
          className="w-2 h-8 bg-primary rounded-full animate-pulse"
          style={{
            animationDelay: `${i * 0.15}s`,
          }}
        />
      ))}
    </div>
  );
}

export function LoadingDots() {
  return (
    <div className="flex space-x-2 items-center">
      {Array.from({ length: 3 }).map((_, i) => (
        <div
          key={i}
          className="w-3 h-3 rounded-full bg-primary animate-bounce"
          style={{
            animationDelay: `${i * 0.15}s`,
          }}
        />
      ))}
    </div>
  );
}

export function LoadingSpinner() {
  return (
    <div className="flex flex-col items-center justify-center gap-4">
      <Spinner size="lg" />
      <p className="text-sm text-muted-foreground animate-pulse">Loading...</p>
    </div>
  );
}

export function LoadingCircles() {
  return (
    <div className="flex items-center justify-center">
      <div className="relative">
        {Array.from({ length: 3 }).map((_, i) => (
          <div
            key={i}
            className="absolute inset-0 border-2 border-primary rounded-full animate-ping"
            style={{
              animationDelay: `${i * 0.3}s`,
              opacity: 1 - i * 0.2,
            }}
          />
        ))}
        <div className="relative w-12 h-12 border-2 border-primary rounded-full" />
      </div>
    </div>
  );
}
