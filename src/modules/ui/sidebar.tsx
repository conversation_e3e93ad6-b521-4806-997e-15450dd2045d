"use client";
import { cn } from "@/modules/ui";
import Link, { LinkProps } from "next/link";
import React, { createContext, useContext } from "react";
import { usePathname } from "next/navigation";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/modules/ui/tooltip";

interface Links {
  label: string;
  href: string;
  icon: React.JSX.Element | React.ReactNode;
}

interface SidebarContextProps {
  className?: string;
}

const SidebarContext = createContext<SidebarContextProps>({});

export const useSidebar = () => {
  const context = useContext(SidebarContext);
  if (!context) {
    throw new Error("useSidebar must be used within a SidebarProvider");
  }
  return context;
};

export const SidebarProvider = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <SidebarContext.Provider value={{ className }}>
      {children}
    </SidebarContext.Provider>
  );
};

export const Sidebar = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return <SidebarProvider className={className}>{children}</SidebarProvider>;
};

export const SidebarBody = ({
  className,
  children,
  ...props
}: React.ComponentProps<"div">) => {
  return (
    <>
      <DesktopSidebar className={className} {...props}>
        {children}
      </DesktopSidebar>
      <MobileSidebar className={className} {...props}>
        {children}
      </MobileSidebar>
    </>
  );
};

export const DesktopSidebar = ({
  className,
  children,
  ...props
}: React.ComponentProps<"div">) => {
  const { className: contextClassName } = useSidebar();

  return (
    <div
      className={cn(
        "h-full p-1 py-2 hidden md:flex md:flex-col bg-white/50 dark:bg-black/50 border-r border-border",
        contextClassName,
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

export const MobileSidebar = ({
  className,
  children,
  ...props
}: React.ComponentProps<"div">) => {
  return (
    <div
      className={cn(
        "md:hidden fixed bottom-0 left-0 right-0 bg-background border-t border-border z-50",
        className
      )}
      {...props}
    >
      <div className="flex justify-around items-center h-16 px-2">
        {children}
      </div>
    </div>
  );
};

export const SidebarLink = ({
  link,
  className,
  ...props
}: {
  link: Links;
  className?: string;
  props?: LinkProps;
}) => {
  const pathname = usePathname();
  const isActive = pathname === link.href;
  const isMobile = typeof window !== "undefined" && window.innerWidth < 768;

  const linkContent = (
    <div className={cn("flex-shrink-0", isMobile && "mb-1")}>{link.icon}</div>
  );

  const mobileLabel = isMobile && (
    <span
      className={cn(
        "font-medium",
        "text-xs",
        isActive && "text-primary",
        !isActive && "text-muted-foreground hover:text-foreground"
      )}
    >
      {link.label}
    </span>
  );

  const linkElement = (
    <Link
      href={link.href}
      className={cn(
        "flex items-center rounded-sm  relative",
        !isMobile && [
          "h-12 px-3",
          "justify-center",
          isActive
            ? "text-primary font-medium"
            : "text-muted-foreground hover:text-foreground hover:bg-muted",
        ],
        className
      )}
      {...props}
    >
      {linkContent}
      {mobileLabel}
    </Link>
  );

  if (isMobile) {
    return linkElement;
  }

  console.log("Tooltip label:", link.label);

  return (
    <Tooltip>
      <TooltipTrigger asChild>{linkElement}</TooltipTrigger>
      <TooltipContent
        side="right"
        sideOffset={12}
        className="bg-popover text-popover-foreground"
      >
        <p>{link.label}</p>
      </TooltipContent>
    </Tooltip>
  );
};
