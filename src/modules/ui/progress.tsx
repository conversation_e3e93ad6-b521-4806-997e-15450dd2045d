"use client";

import * as React from "react";
import * as ProgressPrimitive from "@radix-ui/react-progress";
import { cn } from "@/modules/ui/utils/cn";

interface ProgressProps
  extends React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root> {
  indicatorClassName?: string;
  showSteps?: boolean;
  currentStep?: number;
  totalSteps?: number;
}

const Progress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  ProgressProps
>(
  (
    {
      className,
      value,
      indicatorClassName,
      showSteps = false,
      currentStep = 1,
      totalSteps = 4,
      ...props
    },
    ref
  ) => (
    <ProgressPrimitive.Root
      ref={ref}
      className={cn(
        "relative h-1 w-full overflow-hidden rounded-full bg-gray-100 dark:bg-gray-800",
        className
      )}
      {...props}
    >
      <ProgressPrimitive.Indicator
        className={cn(
          "h-full flex-1 transition-all duration-500 ease-in-out",
          showSteps ? "bg-transparent" : "bg-primary",
          indicatorClassName
        )}
        style={{
          transform: `translateX(-${100 - (value || 0)}%)`,
        }}
      >
        {showSteps && (
          <div className="absolute inset-0 flex items-center justify-between">
            {Array.from({ length: totalSteps }).map((_, index) => {
              const stepValue = ((index + 1) / totalSteps) * 100;
              const isCompleted = (value || 0) >= stepValue;
              const isCurrent = index + 1 === currentStep;

              return (
                <div
                  key={index}
                  className={cn(
                    "w-1 h-1 rounded-full transition-all duration-300",
                    isCompleted ? "bg-primary" : "bg-gray-200 dark:bg-gray-700",
                    isCurrent && !isCompleted && "bg-primary/50"
                  )}
                />
              );
            })}
          </div>
        )}
      </ProgressPrimitive.Indicator>
    </ProgressPrimitive.Root>
  )
);

Progress.displayName = ProgressPrimitive.Root.displayName;

export { Progress };
