"use client";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";

interface GradientBackgroundProps {
  className?: string;
  children?: React.ReactNode;
  variant?: "default" | "subtle" | "intense";
}

export const GradientBackground = ({
  className,
  children,
  variant = "default",
}: GradientBackgroundProps) => {
  return (
    <div className={cn("relative overflow-hidden", className)}>
      {/* Base background */}
      <div className="absolute inset-0 bg-card" />

      {/* Animated gradient blobs */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Primary blob */}
        <motion.div
          animate={{
            scale: [1, 1.1, 1],
            x: [0, 20, 0],
            y: [0, -30, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
          }}
          className={cn(
            "absolute -top-40 -right-40 w-80 h-80",
            "rounded-full mix-blend-multiply filter blur-3xl",
            variant === "intense"
              ? "bg-primary/30"
              : variant === "subtle"
              ? "bg-primary/10"
              : "bg-primary/15"
          )}
        />

        {/* Secondary blob */}
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            x: [0, -20, 0],
            y: [0, 30, 0],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1,
          }}
          className={cn(
            "absolute -bottom-40 -left-40 w-80 h-80",
            "rounded-full mix-blend-multiply filter blur-3xl",
            variant === "intense"
              ? "bg-secondary/30"
              : variant === "subtle"
              ? "bg-secondary/10"
              : "bg-secondary/15"
          )}
        />

        {/* Accent blob */}
        <motion.div
          animate={{
            scale: [1, 0.9, 1],
            x: [0, 30, 0],
            y: [0, 20, 0],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2,
          }}
          className={cn(
            "absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2",
            "w-80 h-80 rounded-full mix-blend-multiply filter blur-3xl",
            variant === "intense"
              ? "bg-accent/30"
              : variant === "subtle"
              ? "bg-accent/10"
              : "bg-accent/15"
          )}
        />
      </div>

      {/* Radial gradients */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_top_right,rgba(var(--primary-rgb),0.08),transparent_40%)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_bottom_left,rgba(var(--secondary-rgb),0.08),transparent_40%)]" />
      </div>

      {/* Grid overlay */}
      <div className="absolute inset-0 bg-grid-white/[0.03] bg-grid-black/[0.03]" />

      {/* Content */}
      {children && <div className="relative z-10">{children}</div>}
    </div>
  );
};
