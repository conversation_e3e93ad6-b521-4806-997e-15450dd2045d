// src/components/ui/loading.tsx
import React from "react";
import { Skeleton } from "@/modules/ui/skeleton";

interface LoadingProps {
  variant?: "skeleton" | "spinner";
  width?: string;
  height?: string;
}

export const Loading: React.FC<LoadingProps> = ({
  variant = "skeleton",
  width = "100%",
  height = "20px",
}) => {
  if (variant === "spinner") {
    return <div className="spinner" />; // Implement your spinner CSS
  }

  return <Skeleton className={`w-${width} h-${height}`} />;
};
