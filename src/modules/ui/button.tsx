import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/modules/ui/utils/cn";

const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none relative overflow-hidden",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline:
          "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        "gradient-primary":
          "gradient-primary text-primary-foreground hover:opacity-90 before:absolute before:inset-0 before:rounded-md before:border before:border-white/10",
        "gradient-secondary":
          "gradient-secondary text-secondary-foreground hover:opacity-90 before:absolute before:inset-0 before:rounded-md before:border before:border-white/5",
        "gradient-subtle":
          "bg-gradient-to-r from-background/80 to-background/40 hover:from-primary/20 hover:to-accent/20 text-foreground border border-white/10 backdrop-blur-sm",
        "gradient-glass":
          "glass-effect hover:bg-accent/10 text-foreground transition-all",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
      glow: {
        true: "shadow-[0_0_20px_rgba(var(--primary),0.3)] hover:shadow-[0_0_25px_rgba(var(--primary),0.4)]",
        false: "",
      },
      shine: {
        true: "group",
        false: "",
      },
    },
    compoundVariants: [
      {
        shine: true,
        className: "relative overflow-hidden",
      },
    ],
    defaultVariants: {
      variant: "default",
      size: "default",
      glow: false,
      shine: false,
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, glow, shine, children, ...props }, ref) => {
    return (
      <button
        className={cn(
          buttonVariants({ variant, size, glow, shine, className })
        )}
        ref={ref}
        {...props}
      >
        {children}
        {shine && (
          <div
            className="absolute inset-0 bg-[linear-gradient(110deg,rgba(255,255,255,0.3),rgba(255,255,255,0))]
                      opacity-0 group-hover:opacity-100 transition-opacity duration-500"
          />
        )}
      </button>
    );
  }
);
Button.displayName = "Button";

export { Button, buttonVariants };
