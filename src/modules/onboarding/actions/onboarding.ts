"use server";

import { processReferral } from "@/lib/services/referral-service";
import prisma from "@/lib/prisma/prisma";
import { Prisma } from "@prisma/client";

interface OnboardingData {
  userId: string;
  name: string;
  industry: string;
  source: string;
  referralCode?: string;
}

export async function completeOnboarding(data: OnboardingData) {
  try {
    await prisma.$transaction(async (tx) => {
      // Update user profile
      await tx.user.update({
        where: { id: data.userId },
        data: {
          name: data.name,
          industry: data.industry,
          source: data.source,
        },
      });

      // Process referral if code exists
      if (data.referralCode) {
        await processReferral(tx, data.userId, data.referralCode);
      }

      // Generate referral code for the new user
      await tx.referralCode.create({
        data: {
          code: generateReferralCode(),
          userId: data.userId,
        },
      });
    });

    return { success: true };
  } catch (error) {
    console.error("Onboarding error:", error);
    throw new Error("Failed to complete onboarding");
  }
}

function generateReferralCode(): string {
  return `${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
}
