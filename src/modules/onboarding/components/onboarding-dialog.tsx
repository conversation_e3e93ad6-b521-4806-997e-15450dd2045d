"use client";
import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
  DialogDescription,
} from "@/modules/ui/dialog";
import { Button } from "@/modules/ui/button";
import { Input } from "@/modules/ui/input";
import { useRouter } from "next/navigation";
import { completeOnboarding } from "../actions/onboarding";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/modules/ui/select";
import { Card, CardContent } from "@/modules/ui/card";
import { toast } from "sonner";
import { Mail, Loader2 } from "lucide-react";
import { ScrollArea } from "@/modules/ui/scroll-area";

interface OnboardingDialogProps {
  userId: string;
  email: string;
  name?: string | null;
}

interface IndustryOption {
  value: string;
  label: string;
  additionalFields?: {
    role?: boolean;
    companySize?: boolean;
    projectType?: boolean;
  };
}

const industries: IndustryOption[] = [
  {
    value: "real_estate",
    label: "Real Estate",
    additionalFields: {
      role: true,
      companySize: true,
    },
  },
  {
    value: "interior_design",
    label: "Interior Design",
    additionalFields: {
      role: true,
      projectType: true,
    },
  },
  {
    value: "architecture",
    label: "Architecture",
    additionalFields: {
      role: true,
      companySize: true,
    },
  },
  {
    value: "individual",
    label: "Individual",
    additionalFields: {
      projectType: true,
    },
  },
  { value: "other", label: "Other" },
];

export default function OnboardingDialog({
  userId,
  email,
  name,
}: OnboardingDialogProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: name || "",
    industry: "",
    industryOther: "",
    role: "",
    companySize: "",
    projectType: "",
    referralCode: "",
    source: "",
    sourceOther: "",
  });

  // Get additional fields based on selected industry
  const selectedIndustry = industries.find(
    (i) => i.value === formData.industry
  );
  const showAdditionalFields = selectedIndustry?.additionalFields;

  // Validation including additional fields
  const isFormValid = () => {
    const baseValidation =
      formData.name.length >= 2 &&
      formData.industry &&
      formData.source &&
      (formData.industry !== "other" || formData.industryOther.length >= 2) &&
      (formData.source !== "other" || formData.sourceOther.length >= 2);

    if (!showAdditionalFields) return baseValidation;

    const additionalValidation = Object.entries(showAdditionalFields).every(
      ([field, required]) => {
        if (!required) return true;
        return Boolean(formData[field as keyof typeof formData]);
      }
    );

    return baseValidation && additionalValidation;
  };

  const sources = [
    { value: "search_engine", label: "Search Engine" },
    { value: "social_media", label: "Social Media" },
    { value: "friend_referral", label: "Friend/Referral" },
    { value: "advertisement", label: "Advertisement" },
    { value: "other", label: "Other" },
  ];

  const handleSubmit = async () => {
    if (!formData.name || !formData.industry || !formData.source) {
      toast.error("Please fill in all required fields");
      return;
    }

    setIsSubmitting(true);
    try {
      await completeOnboarding({
        userId,
        ...formData,
      });
      toast.success("Profile setup completed!");
      router.refresh();
    } catch (error) {
      console.error("Onboarding error:", error);
      toast.error("Failed to complete setup. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={true} onOpenChange={() => {}} modal>
      <DialogContent
        size="lg"
        className="sm:max-w-[600px] gap-6 h-[90dvh] sm:h-auto max-h-[90dvh] p-0 flex flex-col"
      >
        <ScrollArea className="flex-1 overflow-auto w-full">
          <div className="p-6">
            <DialogHeader className="space-y-3">
              <DialogTitle className="text-3xl text-center font-bold">
                Welcome to Renovaitor 👋
              </DialogTitle>
              <DialogDescription className="text-base text-start text-muted-foreground leading-relaxed">
                Help us personalize your experience by telling us a bit about
                yourself.
                <br /> This will help us provide better recommendations.
              </DialogDescription>
            </DialogHeader>

            <Card className="border-muted shadow-none">
              <CardContent className="pt-6 space-y-8">
                {/* Name and Email Section */}
                <div className="space-y-4">
                  <h3 className="font-medium">Personal Information</h3>
                  <div className="space-y-2">
                    <label className="text-sm font-medium flex items-center gap-2">
                      Name <span className="text-red-500">*</span>
                    </label>
                    <Input
                      placeholder="Enter your name"
                      value={formData.name}
                      onChange={(e) =>
                        setFormData({ ...formData, name: e.target.value })
                      }
                    />
                    {email && (
                      <div className="flex items-center gap-2 px-3 py-2 bg-muted rounded-md">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm text-muted-foreground">
                          {email}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Industry Section */}
                <div className="space-y-4">
                  <h3 className="font-medium">Professional Details</h3>
                  <div className="space-y-2">
                    <label className="text-sm font-medium flex items-center gap-2">
                      Industry <span className="text-red-500">*</span>
                      <span className="text-xs text-muted-foreground font-normal">
                        (Select the option that best describes you)
                      </span>
                    </label>
                    <Select
                      value={formData.industry}
                      onValueChange={(value) =>
                        setFormData({
                          ...formData,
                          industry: value,
                          role: "",
                          companySize: "",
                          projectType: "",
                        })
                      }
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select your industry" />
                      </SelectTrigger>
                      <SelectContent>
                        {industries.map((industry) => (
                          <SelectItem
                            key={industry.value}
                            value={industry.value}
                            className="cursor-pointer"
                          >
                            {industry.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {formData.industry === "other" && (
                      <Input
                        placeholder="Please specify your industry"
                        value={formData.industryOther}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            industryOther: e.target.value,
                          })
                        }
                      />
                    )}
                  </div>

                  {/* Additional Fields in Industry Section */}
                  {showAdditionalFields && (
                    <div className="pl-4 border-l-2 border-muted space-y-4">
                      {showAdditionalFields.role && (
                        <div className="space-y-2">
                          <label className="text-sm font-medium flex items-center gap-2">
                            Your Role <span className="text-red-500">*</span>
                          </label>
                          <Select
                            value={formData.role}
                            onValueChange={(value) =>
                              setFormData({ ...formData, role: value })
                            }
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select your role" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="owner">
                                Owner/Principal
                              </SelectItem>
                              <SelectItem value="manager">Manager</SelectItem>
                              <SelectItem value="agent">
                                Agent/Associate
                              </SelectItem>
                              <SelectItem value="employee">Employee</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      )}

                      {showAdditionalFields.companySize && (
                        <div className="space-y-2">
                          <label className="text-sm font-medium flex items-center gap-2">
                            Company Size <span className="text-red-500">*</span>
                          </label>
                          <Select
                            value={formData.companySize}
                            onValueChange={(value) =>
                              setFormData({ ...formData, companySize: value })
                            }
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select company size" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="solo">
                                Solo/Freelancer
                              </SelectItem>
                              <SelectItem value="small">
                                2-10 employees
                              </SelectItem>
                              <SelectItem value="medium">
                                11-50 employees
                              </SelectItem>
                              <SelectItem value="large">
                                50+ employees
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      )}

                      {showAdditionalFields.projectType && (
                        <div className="space-y-2">
                          <label className="text-sm font-medium flex items-center gap-2">
                            Project Type <span className="text-red-500">*</span>
                          </label>
                          <Select
                            value={formData.projectType}
                            onValueChange={(value) =>
                              setFormData({ ...formData, projectType: value })
                            }
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select project type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="residential">
                                Residential
                              </SelectItem>
                              <SelectItem value="commercial">
                                Commercial
                              </SelectItem>
                              <SelectItem value="both">Both</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* Source and Referral Section */}
                <div className="space-y-4">
                  <h3 className="font-medium">Additional Information</h3>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium flex items-center gap-2">
                        How did you hear about us?{" "}
                        <span className="text-red-500">*</span>
                      </label>
                      <Select
                        value={formData.source}
                        onValueChange={(value) =>
                          setFormData({ ...formData, source: value })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select source" />
                        </SelectTrigger>
                        <SelectContent>
                          {sources.map((source) => (
                            <SelectItem key={source.value} value={source.value}>
                              {source.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {formData.source === "other" && (
                        <Input
                          placeholder="Please specify how you heard about us"
                          value={formData.sourceOther}
                          onChange={(e) =>
                            setFormData({
                              ...formData,
                              sourceOther: e.target.value,
                            })
                          }
                        />
                      )}
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium flex items-center gap-2">
                        Referral Code
                        <span className="text-xs text-muted-foreground">
                          (Optional - Enter if someone referred you)
                        </span>
                      </label>
                      <Input
                        placeholder="Enter referral code"
                        value={formData.referralCode}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            referralCode: e.target.value,
                          })
                        }
                      />
                    </div>
                  </div>
                </div>

                <Button
                  className="w-full"
                  size="lg"
                  onClick={handleSubmit}
                  disabled={!isFormValid() || isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Setting up your account...
                    </>
                  ) : (
                    "Complete Setup"
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
