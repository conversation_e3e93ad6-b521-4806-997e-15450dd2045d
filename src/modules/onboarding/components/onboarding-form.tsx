"use client";
import { useState } from "react";
import { But<PERSON> } from "@/modules/ui/button";
import { Input } from "@/modules/ui/input";
import { Select } from "@/modules/ui/select";
import { completeOnboarding } from "../actions/onboarding";
import { useRouter } from "next/navigation";

interface OnboardingFormProps {
  userId: string;
  email: string;
}

export default function OnboardingForm({ userId, email }: OnboardingFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    industry: "",
    referralCode: "",
    source: "",
  });

  const industries = [
    "Real Estate",
    "Interior Design",
    "Architecture",
    "Individual",
    "Other",
  ];

  const sources = [
    { value: "search_engine", label: "Search Engine" },
    { value: "social_media", label: "Social Media" },
    { value: "friend_referral", label: "Friend/Referral" },
    { value: "advertisement", label: "Advertisement" },
    { value: "other", label: "Other" },
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      await completeOnboarding({
        userId,
        ...formData,
      });
      // Refresh the current route to remove the dialog
      router.refresh();
    } catch (error) {
      console.error("Onboarding error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSelectChange = (
    e: React.ChangeEvent<HTMLSelectElement>,
    field: "industry" | "source"
  ) => {
    setFormData({ ...formData, [field]: e.target.value });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6 max-w-md mx-auto">
      <div>
        <label className="block text-sm font-medium mb-2">Name</label>
        <Input
          required
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">Industry</label>
        <select
          className="w-full rounded-md border border-gray-300 bg-white px-3 py-2"
          value={formData.industry}
          onChange={(e) => handleSelectChange(e, "industry")}
          required
        >
          <option value="">Select Industry</option>
          {industries.map((industry) => (
            <option key={industry} value={industry}>
              {industry}
            </option>
          ))}
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">
          How did you hear about us?
        </label>
        <select
          className="w-full rounded-md border border-gray-300 bg-white px-3 py-2"
          value={formData.source}
          onChange={(e) => handleSelectChange(e, "source")}
          required
        >
          <option value="">Select Source</option>
          {sources.map((source) => (
            <option key={source.value} value={source.value}>
              {source.label}
            </option>
          ))}
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">
          Referral Code (Optional)
        </label>
        <Input
          value={formData.referralCode}
          onChange={(e) =>
            setFormData({ ...formData, referralCode: e.target.value })
          }
        />
      </div>

      <Button type="submit" className="w-full" disabled={isSubmitting}>
        {isSubmitting ? "Saving..." : "Complete Setup"}
      </Button>
    </form>
  );
}
