"use client";

import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import type { Subscription } from "@prisma/client";

export type SubscriptionStatus = {
  isSubscribed: boolean;
  subscription: Subscription | null;
  isLoading: boolean;
  error: Error | null;
  remainingCredits?: number;
  daysUntilRenewal?: number;
};

export function useSubscription(): SubscriptionStatus {
  const { data: session, status: authStatus } = useSession();
  const [subscriptionStatus, setSubscriptionStatus] =
    useState<SubscriptionStatus>({
      isSubscribed: false,
      subscription: null,
      isLoading: true,
      error: null,
    });

  useEffect(() => {
    const checkSubscription = async () => {
      if (authStatus === "loading" || !session?.user?.id) {
        setSubscriptionStatus((prev) => ({
          ...prev,
          isLoading: authStatus === "loading",
        }));
        return;
      }

      try {
        // Check if subscription exists and is active
        const isSubscribed = session.user?.isSubscribed || false;
        const subscription = session.user?.subscription || null;

        // Calculate remaining credits and days until renewal if subscription exists
        let remainingCredits, daysUntilRenewal;
        if (subscription) {
          remainingCredits = subscription.monthlyCredits || 0;
          if (subscription.nextBillingDate) {
            const now = new Date();
            const nextBilling = new Date(subscription.nextBillingDate);
            daysUntilRenewal = Math.ceil(
              (nextBilling.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
            );
          }
        }

        setSubscriptionStatus({
          isSubscribed,
          subscription,
          isLoading: false,
          error: null,
          remainingCredits,
          daysUntilRenewal,
        });
      } catch (error) {
        setSubscriptionStatus((prev) => ({
          ...prev,
          isLoading: false,
          error:
            error instanceof Error
              ? error
              : new Error("Failed to check subscription status"),
        }));
      }
    };

    checkSubscription();
  }, [session, authStatus]);

  return subscriptionStatus;
}
