"use server";

import { revalidatePath } from "next/cache";

export async function createCheckout(productId: string, variantId: string) {
  try {
    const response = await fetch("https://api.lemonsqueezy.com/v1/checkouts", {
      method: "POST",
      headers: {
        Accept: "application/vnd.api+json",
        "Content-Type": "application/vnd.api+json",
        Authorization: `Bearer ${process.env.LEMON_SQUEEZY_API_KEY}`,
      },
      body: JSON.stringify({
        data: {
          type: "checkouts",
          attributes: {
            checkout_data: {
              custom: {
                user_id: "cm17vg88w0000gnlb0n06n19j", // You can pass custom data here
              },
            },
            // Add other required attributes here
          },
          relationships: {
            store: {
              data: {
                type: "stores",
                id: process.env.LEMON_SQUEEZY_STORE_ID,
              },
            },
            variant: {
              data: {
                type: "variants",
                id: variantId,
              },
            },
          },
        },
      }),
    });

    if (!response.ok) {
      const errorBody = await response.text();
      console.error("Checkout creation failed. Status:", response.status);
      console.error("Response body:", errorBody);
      throw new Error(
        `Failed to create checkout: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();
    revalidatePath("/pricing");
    return data.data.attributes.url;
  } catch (error) {
    console.error("Error creating checkout:", error);
    if (error instanceof Error) {
      throw new Error(`Error creating checkout: ${error.message}`);
    } else {
      throw new Error("Error creating checkout: Unknown error");
    }
  }
}
