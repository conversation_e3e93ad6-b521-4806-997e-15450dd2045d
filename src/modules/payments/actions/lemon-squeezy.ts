// app/actions/createCheckout.ts
"use server";

import { lemonSqueezySetup } from "@lemonsqueezy/lemonsqueezy.js";
import { createOrUpdateSubscription } from "./subscription";
import type {
  CreateCheckoutResult,
  CheckoutResponse,
} from "../types/lemon-squeezy";

interface VariantDetails {
  isSubscription: boolean;
  subscriptionMonths: number;
  packageName: string;
}

export async function createLemonSqueezyCheckout(
  variantId: number,
  userId: string
): Promise<CreateCheckoutResult> {
  try {
    lemonSqueezySetup({ apiKey: process.env.LEMON_SQUEEZY_API_KEY as string });

    const response = await fetch("https://api.lemonsqueezy.com/v1/checkouts", {
      method: "POST",
      headers: {
        Accept: "application/vnd.api+json",
        "Content-Type": "application/vnd.api+json",
        Authorization: `Bearer ${process.env.LEMON_SQUEEZY_API_KEY}`,
      },
      body: JSON.stringify({
        data: {
          type: "checkouts",
          attributes: {
            custom_price: null,
            product_options: {
              enabled_variants: [variantId],
            },
            checkout_options: {
              embed: false,
              media: true,
              logo: true,
              desc: true,
              discount: true,
              subscription_preview: true,
              button_color: "#7047EB",
            },
            checkout_data: {
              custom: {
                user_id: userId,
              },
            },
            expires_at: null,
            preview: false,
          },
          relationships: {
            store: {
              data: {
                type: "stores",
                id: process.env.LEMON_SQUEEZY_STORE_ID,
              },
            },
            variant: {
              data: {
                type: "variants",
                id: variantId.toString(),
              },
            },
          },
        },
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const checkoutData = (await response.json()) as CheckoutResponse;
    return { checkoutUrl: checkoutData.data.attributes.url };
  } catch (error) {
    console.error("Error creating checkout:", error);
    throw new Error("Failed to create checkout");
  }
}

async function getVariantDetails(
  variantId: number
): Promise<VariantDetails | null> {
  try {
    const response = await fetch(
      `https://api.lemonsqueezy.com/v1/variants/${variantId}`,
      {
        headers: {
          Authorization: `Bearer ${process.env.LEMON_SQUEEZY_API_KEY}`,
          Accept: "application/vnd.api+json",
        },
      }
    );

    if (!response.ok) {
      throw new Error(
        `Failed to fetch variant details: ${response.statusText}`
      );
    }

    const data = await response.json();

    return {
      isSubscription: data.data.attributes.is_subscription || false,
      subscriptionMonths: data.data.attributes.subscription_interval_count || 1,
      packageName: data.data.attributes.name || "default",
    };
  } catch (error) {
    console.error("Error fetching variant details:", error);
    return null;
  }
}
