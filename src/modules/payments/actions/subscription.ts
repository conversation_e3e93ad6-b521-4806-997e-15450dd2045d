"use server";

import prisma from "@/lib/prisma/prisma";
import { revalidatePath } from "next/cache";
import { getUserSubscription } from "@/lib/subscription";
import type { Subscription, SubscriptionStatus } from "@prisma/client";

export async function createOrUpdateSubscription(
  userId: string,
  packageName: string,
  startDate: Date,
  endDate: Date
): Promise<Subscription> {
  const subscription = await prisma.subscription.upsert({
    where: { userId },
    update: {
      packageName,
      startDate,
      endDate,
      isActive: true,
      allowPrivateImages: true,
      paymentStatus: "ACTIVE" as SubscriptionStatus,
    },
    create: {
      userId,
      packageName,
      startDate,
      endDate,
      isActive: true,
      allowPrivateImages: true,
      paymentStatus: "ACTIVE" as SubscriptionStatus,
    },
  });

  console.log("Created/Updated subscription:", subscription);
  return subscription;
}

export async function cancelSubscription(userId: string): Promise<void> {
  try {
    await prisma.subscription.update({
      where: { userId },
      data: {
        isActive: false,
        allowPrivateImages: false,
        paymentStatus: "CANCELED" as SubscriptionStatus,
      },
    });

    revalidatePath("/dashboard");
  } catch (error) {
    console.error("Error cancelling subscription:", error);
    throw error;
  }
}

export async function getSubscriptionStatus(userId: string): Promise<{
  isActive: boolean;
  allowPrivateImages: boolean;
  packageName: string | null;
  paymentStatus?: SubscriptionStatus;
}> {
  const subscription = await getUserSubscription(userId);
  return {
    isActive: subscription?.isActive ?? false,
    allowPrivateImages: subscription?.allowPrivateImages ?? false,
    packageName: subscription?.packageName ?? null,
    paymentStatus:
      subscription?.paymentStatus ?? ("ACTIVE" as SubscriptionStatus),
  };
}

export async function checkPrivateImagesAccess(userId: string) {
  try {
    const subscription = await getUserSubscription(userId);
    return subscription?.isActive && subscription?.allowPrivateImages;
  } catch (error) {
    console.error("Error checking private images access:", error);
    return false;
  }
}

export async function updateUserPrivacySettings(
  userId: string,
  isPublic: boolean
) {
  try {
    const subscription = await getUserSubscription(userId);

    if (!subscription?.isActive || !subscription?.allowPrivateImages) {
      throw new Error("Subscription required for private images");
    }

    // Update all user's content to the new privacy setting
    await prisma.$transaction([
      prisma.interiorDesign.updateMany({
        where: { userId },
        data: { isPublic },
      }),
      prisma.exteriorDesign.updateMany({
        where: { userId },
        data: { isPublic },
      }),
      prisma.backgroundRemoval.updateMany({
        where: { userId },
        data: { isPublic },
      }),
      prisma.upscaleImage.updateMany({
        where: { userId },
        data: { isPublic },
      }),
      prisma.virtualStaging.updateMany({
        where: { userId },
        data: { isPublic },
      }),
      prisma.editImage.updateMany({
        where: { userId },
        data: { isPublic },
      }),
    ]);

    return true;
  } catch (error) {
    console.error("Error updating privacy settings:", error);
    throw error;
  }
}
