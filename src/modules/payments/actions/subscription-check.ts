"use server";

import prisma from "@/lib/prisma/prisma";

export async function checkSubscriptionStatus(userId: string) {
  try {
    const subscription = await prisma.subscription.findUnique({
      where: { userId },
      select: {
        isActive: true,
        allowPrivateImages: true,
        packageName: true,
      },
    });

    return {
      hasPrivateAccess:
        subscription?.isActive && subscription?.allowPrivateImages,
      packageName: subscription?.packageName,
    };
  } catch (error) {
    console.error("Error checking subscription status:", error);
    return {
      hasPrivateAccess: false,
      packageName: null,
    };
  }
}
