export type PlanInterval = "monthly" | "yearly";

export type PlanVariant = {
  price: number;
  variantId: string;
  credits: number;
};

export type Plan = {
  name: string;
  description: string;
  features: string[];
  variants: {
    [key in PlanInterval]: PlanVariant;
  };
  lemonSqueezyId: string;
};

export type TopUpProduct = {
  name: string;
  description: string;
  price: number;
  credits: number;
  lemonSqueezyId: string;
  variantId: string;
};

export type CreditProduct = {
  credits: number;
  price: number;
  variantId: string;
  pricePerCredit: number;
  isBestValue?: boolean;
};
