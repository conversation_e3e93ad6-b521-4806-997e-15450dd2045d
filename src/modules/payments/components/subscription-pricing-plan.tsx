"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { toast } from "sonner";
import { GradientHeading, Lead } from "@/modules/ui/typography";
import { cn } from "@/modules/ui";

// Types
import { Plan, PlanInterval, TopUpProduct } from "../types";

// Data
import { plans, creditProducts, topUpProduct, freePlan } from "./pricing-data";

// Utils
import { createLemonSqueezyCheckout } from "@/modules/payments/actions/lemon-squeezy";
import {
  calculateYearlyDiscount,
  calculateYearlySavings,
} from "./pricing-utils";

// Components
import { ViewToggle } from "./pricing-components/view-toggle";
import { IntervalToggle } from "./pricing-components/interval-toggle";
import { FreePlanCard } from "./pricing-components/free-plan-card";
import { SubscriptionPlanCard } from "./pricing-components/subscription-plan-card";
import { TopUpCard } from "./pricing-components/top-up-card";
import { PricingFooter } from "./pricing-components/pricing-footer";

interface PricingPlansProps {
  onSubscriptionComplete?: () => void;
  compact?: boolean;
}

export default function PricingPlans({ compact = false }: PricingPlansProps) {
  const [selectedIntervals, setSelectedIntervals] = useState<
    Record<string, PlanInterval>
  >({
    "Renovaitor Pro": "monthly",
    "Renovaitor Premium": "monthly",
  });
  const [isLoading, setIsLoading] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [selectedCreditPackage, setSelectedCreditPackage] = useState(
    creditProducts[1]
  );
  const [viewType, setViewType] = useState<"subscription" | "credits">(
    "subscription"
  );

  const router = useRouter();
  const { data: session } = useSession();

  const handleBulkIntervalChange = (newInterval: PlanInterval) => {
    setSelectedIntervals(
      plans.reduce((acc, plan) => ({ ...acc, [plan.name]: newInterval }), {})
    );
  };

  const handleCheckout = async (plan: Plan | TopUpProduct) => {
    if (!session?.user?.id) {
      toast.error("Please sign in to continue");
      router.push("/login");
      return;
    }

    try {
      setIsLoading(true);
      setSelectedPlan("variants" in plan ? plan.name : "top-up");

      const variantId =
        "variants" in plan
          ? plan.variants[selectedIntervals[plan.name]].variantId
          : selectedCreditPackage.variantId;

      const numericVariantId = parseInt(variantId);
      if (isNaN(numericVariantId)) {
        throw new Error(`Invalid variant ID: ${variantId}`);
      }

      const result = await createLemonSqueezyCheckout(
        numericVariantId,
        session.user.id
      );
      if (!result?.checkoutUrl) throw new Error("No checkout URL received");

      window.location.replace(result.checkoutUrl);
    } catch (error) {
      console.error("Error creating checkout:", error);
      toast.error(
        error instanceof Error
          ? `Checkout failed: ${error.message}`
          : "Failed to create checkout. Please try again."
      );
      setIsLoading(false);
      setSelectedPlan(null);
    }
  };

  return (
    <section className="bg-gradient-to-br from-background via-background/98 to-background relative py-12 md:20">
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(var(--primary),0.03)_0%,transparent_65%)]" />
      <div
        className={cn(
          "mx-auto max-w-[1400px] relative",
          compact ? "px-4" : "px-6"
        )}
      >
        {!compact && (
          <div className="text-center mb-8">
            <GradientHeading className="text-4xl font-bold mb-4">
              Choose Your Plan
            </GradientHeading>
            <Lead className="max-w-2xl mx-auto">
              Unlock the full potential of your interior design projects with
              our flexible pricing options.
            </Lead>
          </div>
        )}

        <ViewToggle
          viewType={viewType}
          setViewType={setViewType}
          compact={compact}
        />

        {viewType === "subscription" && (
          <IntervalToggle
            selectedIntervals={selectedIntervals}
            onIntervalChange={handleBulkIntervalChange}
            yearlyDiscount={calculateYearlyDiscount(plans[0])}
          />
        )}

        <div
          className={cn(
            "grid gap-8",
            viewType === "subscription" ? "md:grid-cols-3" : "justify-center"
          )}
        >
          {viewType === "subscription" ? (
            <>
              <FreePlanCard plan={freePlan} />
              {plans.map((plan) => (
                <SubscriptionPlanCard
                  key={plan.name}
                  plan={plan}
                  interval={selectedIntervals[plan.name]}
                  isLoading={isLoading && selectedPlan === plan.name}
                  onCheckout={() => handleCheckout(plan)}
                  yearlyDiscount={calculateYearlyDiscount(plan)}
                  yearlySavings={calculateYearlySavings(plan)}
                />
              ))}
            </>
          ) : (
            <TopUpCard
              product={topUpProduct}
              selectedPackage={selectedCreditPackage}
              onPackageChange={setSelectedCreditPackage}
              isLoading={isLoading && selectedPlan === "top-up"}
              onCheckout={() => handleCheckout(topUpProduct)}
              creditProducts={creditProducts}
            />
          )}
        </div>

        <PricingFooter />
      </div>
    </section>
  );
}
