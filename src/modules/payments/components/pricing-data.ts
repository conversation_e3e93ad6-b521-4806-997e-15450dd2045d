import { Plan, TopUpProduct, CreditProduct } from "../types";

// Base features that come with the free plan
const baseFeatures = {
  designs: ["10 AI-generated designs to try", "Basic styles only"],
  gallery: ["Public gallery access"],
  limits: ["Only 1 design at a time", "Slower queue times"],
};

// Additional features for Pro plan
const proFeatures = {
  designs: [
    "Up to 1,000 AI-generated designs/month",
    "Access a wide range of premium styles",
    "Photorealistic, client-ready renders",
  ],
  gallery: ["Private Gallery for secure presentations"],
  commercial: ["Commercial License for monetizing your work"],
  limits: ["Generate up to 8 designs in parallel"],
};

// Additional features for Premium plan
const premiumFeatures = {
  designs: [
    "Up to 5,000 AI-generated designs/month",
    "Full access to all style packs",
    "Commercial exclusives",
  ],
  gallery: ["Private Gallery for confidential projects"],
  commercial: ["Commercial License for unlimited business use"],
  support: [
    "Priority Support with lightning-fast responses",
    "Early Access to new features & beta tools",
  ],
  limits: ["Generate up to 16 designs simultaneously"],
};

export const freePlan = {
  name: "Free",
  description: "Get started at no cost",
  features: Object.values(baseFeatures).flat(),
};

export const plans: Plan[] = [
  {
    name: "Renovaitor Pro",
    description: "Perfect for individuals and small teams",
    features: [
      "Everything in Free, plus:",
      ...Object.values(proFeatures).flat(),
    ],
    variants: {
      monthly: {
        price: 39.0,
        variantId: "526739",
        credits: 1000,
      },
      yearly: {
        price: 349.0,
        variantId: "526719",
        credits: 12000,
      },
    },
    lemonSqueezyId: "122393",
  },
  {
    name: "Renovaitor Premium",
    description: "Ideal for professionals and larger teams",
    features: [
      "Everything in Pro, plus:",
      ...Object.values(premiumFeatures).flat(),
    ],
    variants: {
      monthly: {
        price: 99.0,
        variantId: "526745",
        credits: 5000,
      },
      yearly: {
        price: 899.0,
        variantId: "526746",
        credits: 60000,
      },
    },
    lemonSqueezyId: "355424",
  },
];

export const creditProducts: CreditProduct[] = [
  {
    credits: 50,
    price: 9.0,
    variantId: "619715",
    pricePerCredit: 0.18,
  },
  {
    credits: 200,
    price: 19.0,
    variantId: "135060",
    pricePerCredit: 0.095,
  },
  {
    credits: 500,
    price: 39.0,
    variantId: "619716",
    pricePerCredit: 0.078,
  },
  {
    credits: 1000,
    price: 69.0,
    variantId: "619721",
    pricePerCredit: 0.069,
  },
  {
    credits: 1500,
    price: 99.0,
    variantId: "619722",
    pricePerCredit: 0.066,
    isBestValue: true,
  },
];

export const topUpProduct: TopUpProduct = {
  name: "Credit Top-Up",
  description: "Pay-as-you-go option for flexible usage",
  lemonSqueezyId: "117788",
  price: 10,
  credits: 100,
  variantId: "117788-1",
};
