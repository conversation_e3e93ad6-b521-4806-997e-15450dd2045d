import { Plan } from "../types";

export const calculateYearlyDiscount = (plan: Plan): number => {
  const monthlyYearTotal = plan.variants.monthly.price * 12;
  const yearlyTotal = plan.variants.yearly.price;
  const savingsPercentage =
    ((monthlyYearTotal - yearlyTotal) / monthlyYearTotal) * 100;
  return Math.round(savingsPercentage);
};

export const calculateYearlySavings = (plan: Plan): string => {
  const monthlyYearTotal = plan.variants.monthly.price * 12;
  const yearlyTotal = plan.variants.yearly.price;
  return (monthlyYearTotal - yearlyTotal).toFixed(2);
};
