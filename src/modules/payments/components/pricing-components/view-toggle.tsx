import { motion } from "framer-motion";
import { cn } from "@/modules/ui";

interface ViewToggleProps {
  viewType: "subscription" | "credits";
  setViewType: (type: "subscription" | "credits") => void;
  compact?: boolean;
}

export function ViewToggle({
  viewType,
  setViewType,
  compact,
}: ViewToggleProps) {
  return (
    <motion.div
      className="flex justify-center mb-8"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: 0.3 }}
    >
      <div className="flex gap-1 p-0.5 bg-background/50 backdrop-blur-sm border border-primary/10 rounded-md">
        <button
          onClick={() => setViewType("subscription")}
          className={cn(
            "px-4 py-1.5 rounded-md transition-all duration-300",
            viewType === "subscription"
              ? "bg-primary text-primary-foreground shadow-lg"
              : "text-muted-foreground hover:text-foreground",
            compact ? "text-sm" : "text-base md:text-lg"
          )}
        >
          Subscriptions
        </button>
        <button
          onClick={() => setViewType("credits")}
          className={cn(
            "px-4 py-1.5 rounded-md transition-all duration-300",
            viewType === "credits"
              ? "bg-primary text-primary-foreground shadow-lg"
              : "text-muted-foreground hover:text-foreground",
            compact ? "text-sm" : "text-base md:text-lg"
          )}
        >
          Buy Credits
        </button>
      </div>
    </motion.div>
  );
}
