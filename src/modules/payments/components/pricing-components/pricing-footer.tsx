import { motion } from "framer-motion";
import { But<PERSON> } from "@/modules/ui/button";
import Link from "next/link";
import { cn } from "@/modules/ui";

const cardVariants = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  hover: {
    scale: 1.02,
    backgroundColor: "rgba(var(--primary), 0.12)",
  },
};

interface FooterCardProps {
  icon: string;
  label: string;
  text?: string;
  highlight?: string;
  delay: number;
}

const FooterCard = ({
  icon,
  label,
  text,
  highlight,
  delay,
}: FooterCardProps) => (
  <motion.div
    variants={cardVariants}
    initial="initial"
    animate="animate"
    whileHover="hover"
    transition={{ duration: 0.3, delay }}
    className={cn(
      "flex items-center whitespace-nowrap justify-center gap-3",
      "bg-card/90 dark:bg-card/90 backdrop-blur-sm",
      "border border-primary/10 dark:border-primary/20 hover:border-primary/20 dark:hover:border-primary/30",
      "px-6 py-3.5 rounded-lg",
      "shadow-sm hover:shadow-md hover:shadow-primary/5",
      "transition-all duration-300"
    )}
  >
    <span role="img" aria-label={label} className="text-xl opacity-90 shrink-0">
      {icon}
    </span>
    <span className="flex items-center gap-1.5 text-[15px]">
      {text && <span className="text-muted-foreground shrink-0">{text}</span>}
      {highlight && (
        <span className="font-medium text-primary flex items-center gap-1 shrink-0">
          {highlight}
        </span>
      )}
      {!text && !highlight && (
        <span className="text-foreground/80 dark:text-foreground/70 shrink-0">{label}</span>
      )}
    </span>
  </motion.div>
);

export function PricingFooter() {
  return (
    <motion.div
      className="mt-16 text-center"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.8 }}
    >
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 max-w-7xl mx-auto px-6 mb-8">
        <FooterCard
          icon="🔒"
          label="secure"
          text="Powered by"
          highlight="Lemon Squeezy 🍋"
          delay={0.1}
        />
        <FooterCard
          icon="🎭"
          label="cancel"
          text="Cancel anytime, hassle-free"
          delay={0.2}
        />
        <FooterCard icon="🛡️" label="ssl" text="SSL Encrypted" delay={0.3} />
        <FooterCard
          icon="💳"
          label="payment"
          text="Secure Payment"
          delay={0.4}
        />
      </div>

      <motion.div
        className="mt-12"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.6 }}
      >
        <Link href="mailto:<EMAIL>" passHref>
          <Button
            variant="outline"
            className={cn(
              "group bg-background/50 hover:bg-background/80",
              "border-primary/10 hover:border-primary/20",
              "backdrop-blur-sm transition-all duration-300",
              "text-foreground font-medium",
              "px-6 py-2.5"
            )}
          >
            <span className="text-foreground/90 dark:text-foreground/80 font-medium group-hover:text-foreground transition-colors duration-200">Contact Sales</span>
            <motion.span
              className="ml-2 inline-block text-amber-300"
              role="img"
              aria-label="star"
              initial={{ rotate: 0 }}
              whileHover={{ rotate: 360, scale: 1.2 }}
              transition={{ duration: 0.6, ease: "easeOut" }}
            >
              ⭐
            </motion.span>
          </Button>
        </Link>
      </motion.div>
    </motion.div>
  );
}
