import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader } from "@/modules/ui/card";
import { But<PERSON> } from "@/modules/ui/button";
import { Check } from "lucide-react";
import { H3, BodyText, Small, Subtle } from "@/modules/ui/typography";
import { cn } from "@/modules/ui";
import Image from "next/image";

interface FreePlanCardProps {
  plan: {
    name: string;
    description: string;
    features: string[];
  };
}

export function FreePlanCard({ plan }: FreePlanCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      <Card
        className="group flex flex-col h-full
        bg-card dark:bg-card/90
        backdrop-blur-md border border-muted/50 dark:border-muted/30
        shadow-md
        hover:scale-[1.01] hover:-translate-y-1
        hover:shadow-lg
        hover:border-primary/30
        transition-all duration-300 p-6"
      >
        <CardHeader className="text-center pb-2">
          <H3 className="text-2xl font-bold text-foreground">{plan.name}</H3>
          <BodyText className="text-base text-muted-foreground/90">
            {plan.description}
            <br />
            <span className="text-foreground font-medium mt-1 block">
              Basic features to get you started.
            </span>
          </BodyText>
        </CardHeader>

        <CardContent className="flex-grow space-y-6">
          <div className="flex flex-col items-center">
            <div className="flex items-baseline gap-2">
              <span className="text-5xl font-bold text-foreground">$0</span>
              <Small className="text-muted-foreground/90">forever</Small>
            </div>
          </div>

          <div className="flex justify-center">
            <div
              className="flex items-center gap-2 bg-primary/5 dark:bg-primary/10
              hover:bg-primary/10 dark:hover:bg-primary/15 hover:scale-105
              active:scale-95
              transition-all duration-300 py-3 px-5 rounded-lg
              group-hover:translate-y-0.5 shadow-sm"
            >
              <Image
                src="/images/coin.png"
                alt="Credits"
                width={20}
                height={20}
                className="w-5 h-5 transition-transform group-hover:rotate-12"
              />
              <span className="font-medium text-primary">30 designs/month</span>
            </div>
          </div>

          <Button
            className="w-full bg-muted/70 hover:bg-muted/80 active:bg-muted
              text-muted-foreground font-medium relative overflow-hidden
              transition-all duration-300 group/button border border-muted/30
              shadow-sm"
            disabled
          >
            Current Plan
            <span className="absolute right-4 opacity-50 transition-transform group-hover/button:translate-x-1">
              →
            </span>
          </Button>

          <ul className="space-y-4">
            {plan.features.map((feature, idx) => (
              <motion.li
                key={idx}
                className="flex items-start gap-3 group/item"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: idx * 0.1 }}
                whileHover={{ x: 4 }}
              >
                <div
                  className="shrink-0 rounded-full p-1 bg-primary/10 dark:bg-primary/20
                  group-hover/item:bg-primary/20 dark:group-hover/item:bg-primary/30 group-hover/item:scale-110
                  transition-all duration-300"
                >
                  <Check className="w-4 h-4 text-primary transition-transform group-hover/item:scale-110" />
                </div>
                <BodyText className="text-sm text-foreground/80 dark:text-foreground/70 group-hover/item:text-foreground transition-colors duration-300">
                  {feature}
                </BodyText>
              </motion.li>
            ))}
          </ul>

          <Subtle className="text-sm text-center text-muted-foreground/70 dark:text-muted-foreground/60 hover:text-muted-foreground/90 transition-colors duration-200">
            Upgrade anytime for more features, faster rendering, and more
            designs.
          </Subtle>
        </CardContent>
      </Card>
    </motion.div>
  );
}
