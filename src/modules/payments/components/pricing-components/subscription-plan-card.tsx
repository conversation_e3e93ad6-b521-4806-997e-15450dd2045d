import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er } from "@/modules/ui/card";
import { But<PERSON> } from "@/modules/ui/button";
import { Check, Loader2 } from "lucide-react";
import { H3, BodyText, Small, Subtle } from "@/modules/ui/typography";
import { cn } from "@/modules/ui";
import Image from "next/image";
import { Plan, PlanInterval } from "../../types";

interface SubscriptionPlanCardProps {
  plan: Plan;
  interval: PlanInterval;
  isLoading: boolean;
  onCheckout: () => void;
  yearlyDiscount: number;
  yearlySavings: string;
}

const FeatureItem = ({ feature }: { feature: string }) => (
  <motion.li
    className="flex items-start gap-3 group"
    initial={{ opacity: 0, x: -10 }}
    animate={{ opacity: 1, x: 0 }}
    transition={{ duration: 0.4 }}
    whileHover={{ x: 2 }}
  >
    <div className="shrink-0 rounded-full p-1 bg-primary/10 group-hover:bg-primary/20 transition-colors duration-200">
      <Check className="w-4 h-4 text-primary" />
    </div>
    <BodyText className="text-sm text-foreground/80 dark:text-foreground/70 group-hover:text-foreground transition-colors duration-200">{feature}</BodyText>
  </motion.li>
);

export function SubscriptionPlanCard({
  plan,
  interval,
  isLoading,
  onCheckout,
  yearlySavings,
}: SubscriptionPlanCardProps) {
  const isPremium = plan.name === "Renovaitor Premium";
  const price =
    interval === "yearly"
      ? (plan.variants[interval].price / 12).toFixed(2)
      : plan.variants[interval].price;
  const credits =
    interval === "yearly"
      ? (plan.variants[interval].credits / 12).toLocaleString()
      : plan.variants[interval].credits.toLocaleString();

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      <Card
        className={cn(
          "group flex flex-col h-full",
          "bg-card dark:bg-card/90",
          "backdrop-blur-md relative overflow-hidden border transition-all duration-300",
          "hover:scale-[1.01] hover:-translate-y-1",
          "shadow-md hover:shadow-lg",
          "hover:border-primary/30",
          "p-6 pb-8",
          isPremium && [
            "border-primary/30 dark:border-primary/40",
            "shadow-[0_8px_20px_rgba(var(--primary),0.08)]",
            "hover:shadow-[0_8px_30px_rgba(var(--primary),0.15)]",
            "bg-gradient-to-b from-card to-primary/[0.03] dark:from-card/90 dark:to-primary/[0.05]",
            "scale-[1.01]",
            "before:absolute before:inset-0 before:bg-gradient-to-b before:from-transparent before:to-primary/[0.02] before:opacity-0 before:transition-opacity before:duration-300",
            "hover:before:opacity-100",
          ]
        )}
      >
        {isPremium && (
          <div
            className="absolute -right-12 top-8 bg-primary text-primary-foreground text-xs font-medium py-1.5 px-12 transform rotate-45 shadow-md
            after:absolute after:inset-0 after:bg-white/10 after:opacity-0 after:transition-opacity after:duration-300
            hover:after:opacity-100 z-10"
          >
            <span className="font-semibold">Best Value</span>
          </div>
        )}

        <CardHeader className="text-center relative">
          <H3
            className={cn(
              "text-2xl font-bold",
              isPremium
                ? "bg-clip-text text-transparent bg-gradient-to-br from-foreground to-primary"
                : "text-foreground"
            )}
          >
            {plan.name}
          </H3>
          <BodyText className="text-base text-muted-foreground/90">
            {plan.description}
            <br />
            <span className="text-foreground font-medium mt-1 block">
              {isPremium
                ? "Get maximum speed, exclusive styles, and top-tier support."
                : "More speed, advanced styles, and no waiting."}
            </span>
          </BodyText>
        </CardHeader>

        <CardContent className="flex-grow space-y-6">
          <div className="flex flex-col items-center">
            <div className="flex items-baseline gap-2">
              <span className="text-5xl font-bold text-foreground">
                ${price}
              </span>
              <Small className="text-muted-foreground/90 dark:text-muted-foreground/80">/month</Small>
            </div>
            {interval === "yearly" && (
              <Small className="text-muted-foreground/80 dark:text-muted-foreground/70 mt-1">
                billed annually (${plan.variants[interval].price}/year)
              </Small>
            )}
          </div>

          {interval === "yearly" && (
            <div className="bg-primary/10 dark:bg-primary/20 rounded-full py-1.5 px-6 text-primary font-medium text-sm text-center transform hover:scale-[1.02] transition-transform shadow-sm">
              Save ${yearlySavings} yearly!
            </div>
          )}

          <div className="flex justify-center my-6">
            <div className="flex items-center gap-2 bg-primary/5 dark:bg-primary/10 py-3 px-5 rounded-lg hover:bg-primary/10 dark:hover:bg-primary/15 transition-colors duration-200 group/credits">
              <Image
                src="/images/coin.png"
                alt="Credits"
                width={20}
                height={20}
                className="w-5 h-5 group-hover/credits:scale-110 transition-transform duration-200"
              />
              <span className="font-semibold text-primary">
                {credits} credits/month
              </span>
            </div>
          </div>

          <Button
            onClick={onCheckout}
            disabled={isLoading}
            className={cn(
              "w-full mb-4 relative overflow-hidden group/button",
              isPremium
                ? "bg-primary hover:bg-primary/90 text-primary-foreground shadow-md hover:shadow-lg"
                : "bg-primary/10 hover:bg-primary/20 text-foreground hover:text-foreground/90 border border-primary/10 hover:border-primary/20"
            )}
          >
            {isLoading ? (
              <div className="flex items-center justify-center gap-2">
                <Loader2 className="w-5 h-5 animate-spin" />
                Processing...
              </div>
            ) : (
              <>
                Subscribe Now
                <span className="absolute right-4 transition-transform duration-300 group-hover/button:translate-x-1">→</span>
              </>
            )}
          </Button>

          <ul className="space-y-4">
            {plan.features.map((feature, idx) => {
              if (feature.startsWith("Everything in")) {
                return (
                  <motion.li
                    key={idx}
                    className="text-sm font-medium text-primary border-b border-primary/20 pb-2 mb-2"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.5 }}
                  >
                    {feature}
                  </motion.li>
                );
              }
              return <FeatureItem key={idx} feature={feature} />;
            })}
          </ul>

          <Subtle className="text-sm text-center mt-6 text-muted-foreground/70 dark:text-muted-foreground/60 hover:text-muted-foreground/90 transition-colors duration-200">
            Earn referral designs as friends join.
          </Subtle>
        </CardContent>
      </Card>
    </motion.div>
  );
}
