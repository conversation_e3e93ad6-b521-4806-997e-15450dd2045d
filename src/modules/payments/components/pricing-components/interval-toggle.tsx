import { PlanInterval } from "../../types";
import { cn } from "@/modules/ui";

interface IntervalToggleProps {
  selectedIntervals: { [key: string]: PlanInterval };
  onIntervalChange: (interval: PlanInterval) => void;
  yearlyDiscount: number;
}

export function IntervalToggle({
  selectedIntervals,
  onIntervalChange,
  yearlyDiscount,
}: IntervalToggleProps) {
  const isMonthly = Object.values(selectedIntervals).every(
    (interval) => interval === "monthly"
  );
  const isYearly = Object.values(selectedIntervals).every(
    (interval) => interval === "yearly"
  );

  return (
    <div className="flex justify-center mb-8">
      <div className="flex bg-background/50 dark:bg-background/30 backdrop-blur-sm border border-primary/10 dark:border-primary/20 rounded-md p-0.5 shadow-sm">
        <button
          onClick={() => onIntervalChange("monthly")}
          className={cn(
            "px-3 py-1 rounded-md text-sm font-medium transition-all duration-200",
            isMonthly
              ? "bg-primary text-primary-foreground shadow-md"
              : "text-muted-foreground hover:text-foreground/90"
          )}
        >
          Monthly
        </button>
        <button
          onClick={() => onIntervalChange("yearly")}
          className={cn(
            "px-3 py-1 rounded-md text-sm font-medium transition-all duration-200",
            isYearly
              ? "bg-primary text-primary-foreground shadow-md"
              : "text-muted-foreground hover:text-foreground/90"
          )}
        >
          Yearly
          <span className="ml-1 text-xs font-semibold px-2 py-0.5 rounded-md bg-green-500/90 text-white">
            Save {yearlyDiscount}%
          </span>
        </button>
      </div>
    </div>
  );
}
