import { motion } from "framer-motion";
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/modules/ui/card";
import { <PERSON><PERSON> } from "@/modules/ui/button";
import { Check, Loader2 } from "lucide-react";
import { H3, BodyText, Small } from "@/modules/ui/typography";
import { Slider } from "@/modules/ui/slider";
import Image from "next/image";
import { TopUpProduct, CreditProduct } from "../../types";

interface TopUpCardProps {
  product: TopUpProduct;
  selectedPackage: CreditProduct;
  onPackageChange: (pkg: CreditProduct) => void;
  isLoading: boolean;
  onCheckout: () => void;
  creditProducts: CreditProduct[];
}

export function TopUpCard({
  product,
  selectedPackage,
  onPackageChange,
  isLoading,
  onCheckout,
  creditProducts,
}: TopUpCardProps) {
  const handleSliderChange = (value: number[]) => {
    const index = Math.floor((value[0] / 100) * (creditProducts.length - 1));
    onPackageChange(creditProducts[index]);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="w-full max-w-md"
    >
      <Card
        className="group flex flex-col h-full
        bg-card dark:bg-card/90
        backdrop-blur-md border border-muted/50 dark:border-muted/30
        shadow-md
        hover:scale-[1.01] hover:-translate-y-1
        hover:shadow-lg
        hover:border-primary/30
        transition-all duration-300 relative overflow-hidden"
      >
        <div
          className="bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 dark:from-primary/10 dark:via-primary/20 dark:to-primary/10
          text-primary font-medium text-sm py-2 px-4 text-center
          border-b border-primary/10 dark:border-primary/20 backdrop-blur-sm"
        >
          Pay As You Go
        </div>

        <CardHeader className="text-center pt-8">
          <H3 className="text-2xl font-bold text-foreground">{product.name}</H3>
          <BodyText className="text-base text-muted-foreground/90 mt-1">
            {product.description}
          </BodyText>
        </CardHeader>

        <CardContent className="flex-grow">
          <div className="space-y-8 mb-8">
            <div className="flex items-baseline justify-center gap-2">
              <span className="text-5xl font-bold text-foreground">
                ${selectedPackage.price}
              </span>
              <Small className="text-muted-foreground/90 dark:text-muted-foreground/80">one-time</Small>
            </div>

            <div className="relative">
              {selectedPackage.isBestValue && (
                <div className="absolute -top-4 left-1/2 -translate-x-1/2 z-10">
                  <span
                    className="px-4 py-1 text-xs font-medium rounded-full
                    bg-primary text-primary-foreground shadow-md
                    animate-pulse"
                  >
                    Best Value
                  </span>
                </div>
              )}
              <div
                className="flex items-center justify-center gap-2
                bg-primary/5 dark:bg-primary/10 hover:bg-primary/10 dark:hover:bg-primary/15
                transition-all duration-300 py-3.5 px-5 rounded-lg
                group-hover:scale-[1.02] shadow-sm"
              >
                <Image
                  src="/images/coin.png"
                  alt="Credits"
                  width={20}
                  height={20}
                  className="w-5 h-5 group-hover:scale-110 transition-transform duration-200"
                />
                <span className="font-medium text-primary">
                  {selectedPackage.credits.toLocaleString()} AI-generated
                  designs
                </span>
              </div>
            </div>

            <div className="space-y-3 px-4">
              <Slider
                defaultValue={[40]}
                max={100}
                step={1}
                onValueChange={handleSliderChange}
                className="relative [&>span]:bg-primary/10 dark:[&>span]:bg-primary/20 [&>span]:h-2.5 [&>span]:rounded-full
                  [&>span>span]:bg-primary/80 [&>span>span]:h-full [&>span>span]:rounded-full
                  [&>span>span>span]:h-5 [&>span>span>span]:w-5 [&>span>span>span]:-mt-1.5
                  [&>span>span>span]:bg-primary [&>span>span>span]:ring-2 [&>span>span>span]:ring-background
                  [&>span>span>span]:transition-shadow [&>span>span>span]:duration-300
                  [&>span>span>span]:hover:ring-4 [&>span>span>span]:active:ring-8 cursor-pointer
                  group/slider"
              />
              <div className="flex justify-between items-center text-sm">
                <span className="text-muted-foreground/80">
                  ${creditProducts[0].pricePerCredit}/design
                </span>
                <span className="text-primary font-medium">
                  ${selectedPackage.pricePerCredit}/design
                </span>
              </div>
              <div className="text-center text-xs text-muted-foreground/70 mt-1">
                Drag to adjust package size
              </div>
            </div>

            <Button
              onClick={onCheckout}
              disabled={isLoading}
              className="w-full bg-primary hover:bg-primary/90 text-primary-foreground font-medium
                transition-all duration-300 relative overflow-hidden group/button shadow-md hover:shadow-lg
                before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent
                before:via-white/10 before:to-transparent before:-translate-x-full
                hover:before:translate-x-full before:transition-transform before:duration-700"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="w-5 h-5 animate-spin" />
                  Processing...
                </div>
              ) : (
                <>
                  Buy {selectedPackage.credits} Designs
                  <span className="absolute right-4 transition-transform group-hover/button:translate-x-1">
                    →
                  </span>
                </>
              )}
            </Button>

            <ul className="space-y-4">
              {[
                "Instant access to more designs",
                "No subscription required",
                "Designs never expire",
                "Ideal for occasional use",
              ].map((feature, index) => (
                <motion.li
                  key={index}
                  className="flex items-start gap-3"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <div className="rounded-full p-1 bg-primary/10 dark:bg-primary/20 group-hover:bg-primary/20 dark:group-hover:bg-primary/30 transition-colors duration-200">
                    <Check className="w-4 h-4 text-primary" />
                  </div>
                  <BodyText className="text-sm text-foreground/80 dark:text-foreground/70 group-hover:text-foreground transition-colors duration-200">{feature}</BodyText>
                </motion.li>
              ))}
            </ul>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
