"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from "@/modules/ui/dialog";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useSubscription } from "@/modules/payments/hooks/use-subscription";
import { motion } from "framer-motion";
import { Check, Loader2 } from "lucide-react";
import { Button } from "@/modules/ui/button";
import Image from "next/image";
import { cn } from "@/modules/ui";
import { createLemonSqueezyCheckout } from "@/modules/payments/actions/lemon-squeezy";
import { toast } from "sonner";
import { Switch } from "@/modules/ui/switch";
import Link from "next/link";
import { ScrollArea } from "@/modules/ui/scroll-area";

// Types
interface PaywallDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  description?: string;
  trigger?: React.ReactNode;
  persistDismissal?: boolean;
  feature?:
    | "pro-styles"
    | "premium-styles"
    | "credits"
    | "private-images"
    | string;
  credits?: number;
  requiredCredits?: number;
  title?: string;
  defaultPlan?: "pro" | "premium";
}

type PlanInterval = "monthly" | "yearly";

type PlanVariant = {
  price: number;
  variantId: string;
  credits: number;
};

type Plan = {
  name: string;
  description: string;
  features: string[];
  variants: {
    [key in PlanInterval]: PlanVariant;
  };
  lemonSqueezyId: string;
};

// Use the same plans as subscription-pricing-plan.tsx
const plans: Plan[] = [
  {
    name: "Renovaitor Pro",
    description: "Perfect for individuals and small teams",
    features: [
      "Up to 1,000 AI-generated designs/month",
      "Generate up to 8 designs in parallel",
      "Access a wide range of premium styles",
      "Photorealistic, client-ready renders",
      "Private Gallery for secure presentations",
      "Commercial License for monetizing your work",
    ],
    variants: {
      monthly: {
        price: 39.0,
        variantId: "526739",
        credits: 1000,
      },
      yearly: {
        price: 349.0,
        variantId: "526719",
        credits: 12000,
      },
    },
    lemonSqueezyId: "122393",
  },
  {
    name: "Renovaitor Premium",
    description: "Ideal for professionals and larger teams",
    features: [
      "Up to 5,000 AI-generated designs/month",
      "Generate up to 16 designs simultaneously",
      "Full access to all style packs, including commercial exclusives",
      "Photorealistic renders that impress and convert",
      "Private Gallery for confidential projects",
      "Commercial License for unlimited business use",
      "Priority Support with lightning-fast responses",
      "Early Access to new features & beta tools",
    ],
    variants: {
      monthly: {
        price: 99.0,
        variantId: "526745",
        credits: 5000,
      },
      yearly: {
        price: 899.0,
        variantId: "526746",
        credits: 60000,
      },
    },
    lemonSqueezyId: "355424",
  },
];

const Tooltip = ({ content }: { content: string }) => (
  <div className="group relative inline-block ml-1.5">
    <button
      className="flex items-center justify-center w-4 h-4 rounded-full 
                 bg-white/10 hover:bg-white/20 transition-colors duration-200
                 text-[10px] text-white/70 hover:text-white/90"
      aria-label="More information"
    >
      <span>i</span>
    </button>
    <div
      className="absolute left-1/2 top-full mt-2 -translate-x-1/2 z-50
                 w-64 p-3 rounded-lg shadow-xl
                 bg-zinc-900/95 backdrop-blur-sm border border-white/10
                 text-xs text-white/90 leading-relaxed
                 opacity-0 invisible group-hover:opacity-100 group-hover:visible
                 transform -translate-y-1 group-hover:translate-y-0
                 transition-all duration-200 ease-out"
    >
      <div
        className="absolute -top-1 left-1/2 -translate-x-1/2 
                    w-2 h-2 rotate-45 bg-zinc-900/95 border-t border-l border-white/10"
      />
      {content}
    </div>
  </div>
);

// Update the feature display to handle simple string features
const FeatureDisplay = ({ feature }: { feature: string }) => {
  // Extract tooltip content if feature contains a separator
  const [featureText, tooltipContent] = feature.includes("|")
    ? feature.split("|")
    : [feature, ""];

  return (
    <motion.div
      variants={{
        hidden: { opacity: 0, x: -20 },
        visible: { opacity: 1, x: 0 },
      }}
      className="flex items-start gap-3"
    >
      <div className="mt-1 rounded-full border border-primary/30 p-1">
        <Check className="w-3 h-3 text-foreground" />
      </div>
      <div className="flex items-center">
        <span className="text-[15px] text-foreground/90">{featureText}</span>
        {tooltipContent && <Tooltip content={tooltipContent} />}
      </div>
    </motion.div>
  );
};

export function PaywallDialog({
  open = false,
  onOpenChange,
  description,
  trigger,
  persistDismissal = false,
  feature,
  credits,
  requiredCredits,
  title,
  defaultPlan,
}: PaywallDialogProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { isLoading, subscription } = useSubscription();
  const [isCheckoutLoading, setIsCheckoutLoading] = useState(false);
  const [selectedInterval, setSelectedInterval] =
    useState<PlanInterval>("yearly");
  const [isImageLoading, setIsImageLoading] = useState(true);

  // Initialize selectedPlan with useMemo to prevent unnecessary rerenders
  const [selectedPlan, setSelectedPlan] = useState<Plan>(() => {
    if (defaultPlan === "premium") return plans[1];
    if (defaultPlan === "pro") return plans[0];
    if (feature === "premium-styles") return plans[1];
    return plans[0];
  });

  // Update selectedPlan when defaultPlan or feature changes
  useEffect(() => {
    let newPlan: Plan;
    if (defaultPlan === "premium") newPlan = plans[1];
    else if (defaultPlan === "pro") newPlan = plans[0];
    else if (feature === "premium-styles") newPlan = plans[1];
    else newPlan = plans[0];

    setSelectedPlan(newPlan);
  }, [defaultPlan, feature]);

  if (isLoading) return null;

  const handleOpenChange = (newOpen: boolean) => {
    if (newOpen && status !== "authenticated") {
      router.push("/login");
      return;
    }
    onOpenChange?.(newOpen);
  };

  const calculateYearlyDiscount = (plan: Plan) => {
    const monthlyYearTotal = plan.variants.monthly.price * 12;
    const yearlyTotal = plan.variants.yearly.price;
    const savingsPercentage =
      ((monthlyYearTotal - yearlyTotal) / monthlyYearTotal) * 100;
    return Math.round(savingsPercentage);
  };

  const handleCheckoutSubscription = async (plan: Plan) => {
    if (!session?.user?.id) {
      toast.error("Please sign in to continue");
      router.push("/login");
      return;
    }

    try {
      setIsCheckoutLoading(true);

      const variantId = plan.variants[selectedInterval].variantId;
      const numericVariantId = parseInt(variantId, 10);
      if (isNaN(numericVariantId)) {
        throw new Error(`Invalid variant ID: ${variantId}`);
      }

      const result = await createLemonSqueezyCheckout(
        numericVariantId,
        session.user.id
      );

      if (!result?.checkoutUrl) {
        throw new Error("No checkout URL received");
      }
      window.location.replace(result.checkoutUrl);
    } catch (error) {
      console.error("Error creating checkout:", error);
      toast.error(
        error instanceof Error
          ? `Checkout failed: ${error.message}`
          : "Failed to create checkout. Please try again."
      );
      setIsCheckoutLoading(false);
    }
  };

  const dialogContent = (
    <DialogContent className="max-w-[900px] h-[95dvh] sm:h-auto p-0 border-none rounded-3xl overflow-hidden shadow-2xl bg-gradient-to-b from-background/95 via-background to-background/95">
      <div className="flex flex-col md:flex-row min-h-[650px]">
        {/* Left side - Gradient Image */}
        <div className="relative w-full md:w-2/5">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/30 via-primary/30 to-blue-500/30">
            <div className="absolute inset-0 backdrop-blur-[2px]">
              {isImageLoading && (
                <div className="absolute inset-0 animate-pulse bg-gray-200/20" />
              )}
              <Image
                src="/paywall-image.jpg"
                alt="Background texture"
                className={cn(
                  "object-cover transition-all duration-700 ease-in-out",
                  isImageLoading ? "opacity-0" : "opacity-80"
                )}
                fill
                sizes="(max-width: 768px) 100vw, 33vw"
                priority
                quality={100}
                onLoadingComplete={() => setIsImageLoading(false)}
              />

              {/* Overlay gradient */}
              <div className="absolute inset-0 bg-gradient-to-t from-background/80 via-background/20 to-transparent" />
            </div>
          </div>
        </div>

        {/* Right side - Updated structure */}
        <div className="flex-1 flex flex-col h-full">
          {/* Scrollable content area */}
          <ScrollArea className="flex-1">
            <div className="flex flex-col space-y-8 p-8 md:p-10">
              {/* Header */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <h2 className="text-3xl font-bold tracking-tight">
                  {title || "Unlock Pro Features"}
                </h2>
                <p className="mt-2 text-lg text-muted-foreground">
                  {description ||
                    "Transform your design workflow with professional tools and resources"}
                </p>
              </motion.div>

              {/* Only show recommended plan badge for premium features */}
              {(feature === "premium-styles" || defaultPlan === "premium") && (
                <div className="bg-primary/10 text-primary rounded-lg p-4">
                  <h4 className="font-medium mb-2">Recommended Plan</h4>
                  <p className="text-sm">
                    Get access to all premium styles with our Premium plan
                  </p>
                </div>
              )}

              {/* Features section */}
              <motion.div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {selectedPlan.features.map((feature, idx) => (
                  <FeatureDisplay key={idx} feature={feature} />
                ))}
              </motion.div>

              {/* Usage Examples */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="p-4 space-y-3 rounded-xl bg-primary/5 border border-primary/10"
              >
                <h3 className="font-medium text-foreground/90">Perfect for:</h3>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  {[
                    "Real estate agents showcasing property potential",
                    "Interior designers creating client presentations",
                    "Property developers visualizing renovations",
                    "Home stagers creating virtual staging portfolios",
                  ].map((text, i) => (
                    <li key={i} className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 rounded-full bg-primary/80" />
                      {text}
                    </li>
                  ))}
                </ul>
              </motion.div>
            </div>
          </ScrollArea>

          {/* Sticky bottom section with pricing and buttons */}
          <div className="sticky bottom-0 border-t border-border/10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="p-6">
              {/* Price and Billing Section */}
              <div className="flex items-center justify-between mb-4">
                {/* Price Display */}
                <div>
                  <div className="flex items-baseline">
                    <span className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-br from-foreground to-primary">
                      $
                      {selectedInterval === "yearly"
                        ? (selectedPlan.variants.yearly.price / 12).toFixed(0)
                        : selectedPlan.variants.monthly.price}
                    </span>
                    <span className="text-sm text-muted-foreground ml-1">
                      /mo
                    </span>
                  </div>
                  {selectedInterval === "yearly" && (
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-xs font-medium text-red-400">
                        Save {calculateYearlyDiscount(selectedPlan)}%
                      </span>
                      <span className="text-xs text-muted-foreground">
                        ${selectedPlan.variants.yearly.price}/year
                      </span>
                    </div>
                  )}
                </div>

                {/* Billing Toggle */}
                <div className="flex items-center gap-2 p-1 rounded-lg bg-primary/5">
                  <button
                    onClick={() => setSelectedInterval("monthly")}
                    className={cn(
                      "px-3 py-1.5 text-xs rounded-md transition-colors",
                      selectedInterval === "monthly"
                        ? "bg-primary text-primary-foreground"
                        : "text-muted-foreground hover:text-foreground"
                    )}
                  >
                    Monthly
                  </button>
                  <button
                    onClick={() => setSelectedInterval("yearly")}
                    className={cn(
                      "px-3 py-1.5 text-xs rounded-md transition-colors",
                      selectedInterval === "yearly"
                        ? "bg-primary text-primary-foreground"
                        : "text-muted-foreground hover:text-foreground"
                    )}
                  >
                    Yearly
                  </button>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                <Button
                  onClick={() => handleCheckoutSubscription(selectedPlan)}
                  disabled={isCheckoutLoading}
                  className="relative w-full h-11 text-sm font-medium bg-gradient-to-r from-primary/90 to-primary hover:from-primary hover:to-primary/90 text-primary-foreground rounded-lg transition-all hover:scale-[1.02] disabled:hover:scale-100 shadow-lg shadow-primary/25"
                >
                  {isCheckoutLoading ? (
                    <div className="flex items-center gap-2">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      Processing...
                    </div>
                  ) : (
                    <div className="flex items-center justify-center gap-2">
                      <span>Start {selectedPlan.name}</span>
                      <div className="flex items-center gap-1 text-xs opacity-90">
                        <Image
                          src="/images/coin.png"
                          alt="Credits"
                          width={12}
                          height={12}
                          className="object-contain"
                          priority
                        />
                        <span>
                          {(selectedInterval === "yearly"
                            ? selectedPlan.variants.monthly.credits
                            : selectedPlan.variants.monthly.credits
                          ).toLocaleString()}
                          /mo
                        </span>
                      </div>
                    </div>
                  )}
                </Button>

                <div className="flex items-center justify-between text-xs px-1">
                  <Link
                    href="/pricing"
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Compare plans
                  </Link>
                  <div className="flex items-center gap-1 text-muted-foreground">
                    <span>🍋</span>
                    Secure checkout
                  </div>
                  <button
                    onClick={() => onOpenChange?.(false)}
                    className="text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Continue free
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DialogContent>
  );

  if (trigger) {
    return (
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogTrigger asChild>{trigger}</DialogTrigger>
        {dialogContent}
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      {dialogContent}
    </Dialog>
  );
}
