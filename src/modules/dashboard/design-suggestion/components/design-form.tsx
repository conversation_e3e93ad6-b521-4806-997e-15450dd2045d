"use client";

import { useDropzone } from "react-dropzone";
import { Input } from "@/modules/ui/input";
import { Label } from "@/modules/ui/label";
import Image from "next/image";
import { cn } from "@/modules/ui";
import { useState, useEffect } from "react";
import { Ruler } from "lucide-react";
import { Button } from "@/modules/ui/button";
import { Camera } from "lucide-react";

interface DesignFormProps {
  state: {
    room: string;
    style: string;
    dimensions: string;
    imagePreview: string | null;
  };
  setRoom: (value: string) => void;
  setStyle: (value: string) => void;
  setDimensions: (value: string) => void;
  handleImageUpload: (file: File) => void;
  setImagePreview: (value: string | null) => void;
}

interface Dimensions {
  area: string;
  unit: "imperial" | "metric";
}

export function DesignForm({
  state,
  setRoom,
  setStyle,
  setDimensions,
  handleImageUpload,
  setImagePreview,
}: DesignFormProps) {
  const [localDimensions, setLocalDimensions] = useState<Dimensions>({
    area: "",
    unit: "metric",
  });

  // Convert dimensions string to local state on mount and when state.dimensions changes
  useEffect(() => {
    if (state.dimensions) {
      const match = state.dimensions.match(/(\d+(?:\.\d+)?)\s*(ft²|m²)/);
      if (match) {
        setLocalDimensions({
          area: match[1],
          unit: match[2] === "ft²" ? "imperial" : "metric",
        });
      }
    }
  }, [state.dimensions]);

  // Update parent dimensions when local dimensions change
  useEffect(() => {
    const area = parseFloat(localDimensions.area);
    if (!isNaN(area)) {
      const unit = localDimensions.unit === "imperial" ? "ft²" : "m²";
      const dimensionString = `${area} ${unit}`;
      setDimensions(dimensionString);
    }
  }, [localDimensions, setDimensions]);

  const toggleUnit = () => {
    const area = parseFloat(localDimensions.area);
    if (!isNaN(area)) {
      const newUnit =
        localDimensions.unit === "imperial" ? "metric" : "imperial";
      const convertedArea =
        localDimensions.unit === "imperial"
          ? (area / 10.764).toFixed(1)
          : (area * 10.764).toFixed(1);

      setLocalDimensions({
        area: convertedArea,
        unit: newUnit,
      });
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      "image/*": [".jpeg", ".jpg", ".png", ".webp"],
    },
    maxFiles: 1,
    onDrop: (acceptedFiles) => {
      if (acceptedFiles?.[0]) {
        handleImageUpload(acceptedFiles[0]);
      }
    },
  });

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label className="text-base font-semibold">Room Image (Required)</Label>
        <div
          {...getRootProps()}
          className={cn(
            "border-2 border-dashed rounded-lg p-8 cursor-pointer transition-colors",
            "hover:border-primary/50 hover:bg-secondary/50",
            isDragActive && "border-primary bg-secondary",
            state.imagePreview ? "border-primary" : "border-destructive",
            !state.imagePreview && "animate-pulse"
          )}
        >
          <input {...getInputProps()} required />
          {state.imagePreview ? (
            <div className="relative h-64 w-full">
              <Image
                src={state.imagePreview}
                alt="Room preview"
                fill
                className="object-cover rounded-lg"
              />
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setImagePreview(null);
                }}
                className="absolute top-2 right-2 bg-destructive text-destructive-foreground rounded-full p-1 hover:bg-destructive/90 transition-colors"
              >
                ✕
              </button>
            </div>
          ) : (
            <div className="text-center space-y-4">
              <div className="mx-auto w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center">
                <Camera className="w-8 h-8 text-primary" />
              </div>
              <div>
                <p className="text-base font-medium text-foreground">
                  {isDragActive
                    ? "Drop the image here"
                    : "Upload a photo of your room"}
                </p>
                <p className="text-sm text-muted-foreground mt-2">
                  Drag & drop or click to select
                </p>
              </div>
            </div>
          )}
        </div>
        {!state.imagePreview && (
          <p className="text-sm text-destructive mt-2">
            Please upload a room image to continue
          </p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="room">Room Type</Label>
        <Input
          id="room"
          value={state.room}
          onChange={(e) => setRoom(e.target.value)}
          placeholder="e.g., Living Room, Bedroom"
          className="focus-visible:ring-primary"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="style">Design Style</Label>
        <Input
          id="style"
          value={state.style}
          onChange={(e) => setStyle(e.target.value)}
          placeholder="e.g., Modern, Minimalist"
          className="focus-visible:ring-primary"
          required
        />
      </div>

      <div className="space-y-2">
        <Label className="flex items-center gap-2">
          <Ruler className="h-4 w-4" />
          Room Area
        </Label>
        <div className="flex gap-4">
          <div className="flex-1">
            <Input
              type="number"
              value={localDimensions.area}
              onChange={(e) =>
                setLocalDimensions((prev) => ({
                  ...prev,
                  area: e.target.value,
                }))
              }
              placeholder={`Area (${
                localDimensions.unit === "imperial" ? "ft²" : "m²"
              })`}
              className="focus-visible:ring-primary"
              required
            />
          </div>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={toggleUnit}
            className="shrink-0 w-24"
          >
            {localDimensions.unit === "imperial" ? "ft²" : "m²"}
          </Button>
        </div>
        {localDimensions.area && (
          <p className="text-sm text-muted-foreground mt-2">
            ≈{" "}
            {(
              parseFloat(localDimensions.area) *
              (localDimensions.unit === "imperial" ? 0.0929 : 10.764)
            ).toFixed(1)}{" "}
            {localDimensions.unit === "imperial" ? "m²" : "ft²"}
          </p>
        )}
      </div>
    </div>
  );
}
