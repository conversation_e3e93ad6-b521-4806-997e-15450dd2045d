"use client";
import React from "react";
import Link from "next/link";
import Image from "next/image";

const PLACEHOLDER_IMAGE = `data:image/svg+xml,${encodeURIComponent(`
<svg width="800" height="600" viewBox="0 0 800 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="800" height="600" fill="#F3F4F6"/>
  
  <!-- Floor -->
  <path d="M100 450 L700 450 L600 600 L200 600 Z" fill="#E5E7EB"/>
  
  <!-- Back wall -->
  <path d="M150 100 L650 100 L700 450 L100 450 Z" fill="#D1D5DB"/>
  
  <!-- Window -->
  <rect x="300" y="150" width="200" height="200" fill="#E5E7EB"/>
  <line x1="400" y1="150" x2="400" y2="350" stroke="#D1D5DB" stroke-width="2"/>
  <line x1="300" y1="250" x2="500" y2="250" stroke="#D1D5DB" stroke-width="2"/>
  
  <!-- Room details -->
  <rect x="150" y="400" width="100" height="50" fill="#9CA3AF"/>
  <rect x="550" y="380" width="80" height="70" fill="#9CA3AF"/>
  
  <!-- Text -->
  <text x="400" y="520" text-anchor="middle" font-family="system-ui" font-size="14" fill="#6B7280">
    Room Preview
  </text>
</svg>
`)}`;

interface Design {
  id: string;
  slug: string;
  room: string;
  style: string;
  dimensions: string;
  outputImage: string | null;
}

interface RelatedDesignsListProps {
  designs: Design[];
}

const RelatedDesignsList: React.FC<RelatedDesignsListProps> = ({ designs }) => {
  const [imageFallback, setImageFallback] = React.useState<{
    [key: string]: boolean;
  }>({});

  const handleImageError = (id: string) => {
    setImageFallback((prev) => ({
      ...prev,
      [id]: true,
    }));
  };

  if (designs.length === 0) {
    return (
      <p className="text-sm text-muted-foreground">No related designs found.</p>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
      {designs.map((design) => (
        <Link
          href={`/design-suggestions/${design.slug}`}
          key={design.id}
          className="group focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-primary rounded-lg"
        >
          <div className="block hover:opacity-80 transition-opacity">
            <div className="relative w-full pt-[66.67%] sm:pt-[75%] bg-muted/10 overflow-hidden rounded-lg">
              <Image
                src={
                  imageFallback[design.id]
                    ? PLACEHOLDER_IMAGE
                    : design.outputImage || PLACEHOLDER_IMAGE
                }
                alt={`${design.room} in ${design.style} style`}
                fill
                sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
                className="object-cover transition-all duration-300 group-hover:scale-105"
                onError={() => handleImageError(design.id)}
              />
            </div>
            <div className="mt-2">
              <p className="text-sm font-medium group-hover:text-primary transition-colors">
                {design.room} in {design.style} style
              </p>
              <p className="text-xs text-muted-foreground">
                {design.dimensions} sq m
              </p>
            </div>
          </div>
        </Link>
      ))}
    </div>
  );
};

export default RelatedDesignsList;
