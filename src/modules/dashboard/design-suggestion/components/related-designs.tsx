import React from "react";
import { getRelatedDesigns } from "@/lib/design-suggestions";
import RelatedDesignsList from "./related-designs-list";

interface RelatedDesignsProps {
  room: string;
  style: string;
  dimensions: string;
  currentSlug: string;
}

const RelatedDesigns: React.FC<RelatedDesignsProps> = async ({
  room,
  style,
  dimensions,
  currentSlug,
}) => {
  const relatedDesigns = await getRelatedDesigns(
    room,
    style,
    dimensions,
    currentSlug
  );

  return (
    <div>
      <h3 className="text-xl font-semibold mb-4">Related Designs</h3>
      <RelatedDesignsList designs={relatedDesigns} />
    </div>
  );
};

export default RelatedDesigns;
