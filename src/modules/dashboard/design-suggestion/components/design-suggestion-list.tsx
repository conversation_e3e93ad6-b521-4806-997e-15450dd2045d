"use client";
import React from "react";
import { useRouter, useSearchParams, usePathname } from "next/navigation";
import { ScrollArea } from "@/modules/ui/scroll-area";
import DesignSuggestionGrid, {
  DesignSuggestionGridSkeleton,
} from "./design-suggestion-grid";
import DesignSuggestionPagination from "./design-suggestion-pagination";
import { useCallback, useTransition } from "react";

interface DesignSuggestionListProps {
  suggestions: any[];
  currentPage: number;
  totalPages: number;
  totalCount: number;
  filters: React.ReactNode;
}

const DesignSuggestionList = ({
  suggestions,
  currentPage,
  totalPages,
  totalCount,
  filters,
}: DesignSuggestionListProps) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();

  const createQueryString = useCallback(
    (params: { [key: string]: string | number | null }) => {
      const current = new URLSearchParams(searchParams.toString());

      Object.entries(params).forEach(([key, value]) => {
        if (value === null) {
          current.delete(key);
        } else {
          current.set(key, String(value));
        }
      });

      return current.toString();
    },
    [searchParams]
  );

  const handlePageChange = useCallback(
    (page: number) => {
      startTransition(() => {
        const queryString = createQueryString({ page });
        router.push(`${pathname}?${queryString}`);
      });
    },
    [router, pathname, createQueryString]
  );

  const renderContent = () => {
    if (suggestions.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center py-12 px-4">
          <div className="bg-muted/50 rounded-full p-4 mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="w-8 h-8 text-muted-foreground"
            >
              <path d="M21 12a9 9 0 1 1-9-9c2.52 0 4.85.83 6.72 2.24" />
              <path d="M21 3v4h-4" />
            </svg>
          </div>
          <h3 className="text-lg font-medium mb-2">
            No design suggestions yet
          </h3>
          <p className="text-sm text-muted-foreground text-center max-w-md">
            Create your first design suggestion to get started with AI-powered
            interior design recommendations.
          </p>
        </div>
      );
    }

    return <DesignSuggestionGrid suggestions={suggestions} />;
  };

  return (
    <div className="flex flex-col h-full pb-16 sm:pb-0">
      <div className="backdrop-blur ">
        <div className="p-4">{filters}</div>
      </div>

      <div className="flex-1 overflow-auto">
        <ScrollArea className="h-full">
          <div className="relative">
            {isPending ? <DesignSuggestionGridSkeleton /> : renderContent()}
          </div>
        </ScrollArea>
      </div>

      {totalPages > 1 && (
        <div className="bg-background/95 mb-3 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <DesignSuggestionPagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalCount={totalCount}
            itemsPerPage={suggestions.length}
            onPageChange={handlePageChange}
            isLoading={isPending}
          />
        </div>
      )}
    </div>
  );
};

export default DesignSuggestionList;
