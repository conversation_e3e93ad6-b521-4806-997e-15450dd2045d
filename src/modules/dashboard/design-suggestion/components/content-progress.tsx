"use client";

import React from "react";
import { motion } from "framer-motion";
import { cn } from "@/modules/ui";

interface ContentProgressProps {
  sections: string[];
  currentSection: number;
  onSectionClick: (index: number) => void;
}

export function ContentProgress({
  sections,
  currentSection,
  onSectionClick,
}: ContentProgressProps) {
  return (
    <div className="flex items-center gap-2 p-4 overflow-x-auto scrollbar-hide">
      {sections.map((section, index) => (
        <button
          key={section}
          onClick={() => onSectionClick(index)}
          className={cn(
            "flex items-center gap-2 px-4 py-2 rounded-full text-sm font-medium transition-colors whitespace-nowrap",
            currentSection === index
              ? "bg-primary text-primary-foreground"
              : "bg-muted hover:bg-muted/80 text-muted-foreground"
          )}
        >
          <motion.div
            initial={false}
            animate={{
              scale: currentSection === index ? [1, 1.2, 1] : 1,
            }}
            transition={{ duration: 0.3 }}
            className="w-2 h-2 rounded-full bg-current"
          />
          {section}
        </button>
      ))}
    </div>
  );
}
