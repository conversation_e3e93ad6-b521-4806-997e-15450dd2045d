"use client";

import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Paintbrush, Sofa, Lightbulb, Palette, Leaf } from "lucide-react";
import { cn } from "@/modules/ui";
import { ColorPalettePreview } from "./color-palette-preview";

interface ColorPalette {
  name: string;
  colors: string[];
  description: string;
}

interface DesignElement {
  category: string;
  items: string[];
}

interface DesignVisualizationProps {
  colorPalettes: ColorPalette[];
  designElements: DesignElement[];
  className?: string;
  isLoading?: boolean;
}

const ElementIcons = {
  Furniture: Sofa,
  Lighting: Lightbulb,
  "Color Scheme": Palette,
  Materials: Paintbrush,
  Plants: Leaf,
} as const;

const LoadingPalette = () => (
  <div className="bg-card rounded-lg overflow-hidden border shadow-sm animate-pulse">
    <div className="h-32 bg-muted" />
    <div className="p-4">
      <div className="h-6 w-24 bg-muted rounded mb-2" />
      <div className="h-4 w-3/4 bg-muted rounded" />
      <div className="mt-3 flex gap-2">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="h-6 w-16 bg-muted rounded-full" />
        ))}
      </div>
    </div>
  </div>
);

const LoadingElement = () => (
  <div className="bg-card rounded-lg p-4 border shadow-sm animate-pulse">
    <div className="flex items-center gap-2 mb-3">
      <div className="w-4 h-4 rounded bg-muted" />
      <div className="h-6 w-32 bg-muted rounded" />
    </div>
    <div className="space-y-2">
      {[...Array(3)].map((_, i) => (
        <div key={i} className="flex items-center gap-2">
          <div className="w-1.5 h-1.5 rounded-full bg-muted" />
          <div className="h-4 w-full bg-muted rounded" />
        </div>
      ))}
    </div>
  </div>
);

export function DesignVisualization({
  colorPalettes,
  designElements,
  className,
  isLoading = false,
}: DesignVisualizationProps) {
  return (
    <div className={cn("space-y-8", className)}>
      {/* Color Palettes */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <Palette className="w-5 h-5 text-primary" />
          Color Palettes
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <AnimatePresence mode="wait">
            {isLoading ? (
              <>
                <LoadingPalette />
                <LoadingPalette />
              </>
            ) : (
              colorPalettes.map((palette, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <ColorPalettePreview
                    colors={palette.colors}
                    name={palette.name}
                    description={palette.description}
                  />
                </motion.div>
              ))
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Design Elements */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Design Elements</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <AnimatePresence mode="wait">
            {isLoading ? (
              <>
                <LoadingElement />
                <LoadingElement />
                <LoadingElement />
              </>
            ) : (
              designElements.map((element, index) => {
                const Icon =
                  ElementIcons[element.category as keyof typeof ElementIcons] ||
                  Paintbrush;
                return (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 + 0.3 }}
                    className="bg-card rounded-lg p-4 border shadow-sm hover:shadow-md transition-shadow"
                  >
                    <h4 className="font-medium flex items-center gap-2 mb-3">
                      <Icon className="w-4 h-4 text-primary" />
                      {element.category}
                    </h4>
                    <ul className="space-y-2">
                      {element.items.map((item, itemIndex) => (
                        <motion.li
                          key={itemIndex}
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{
                            delay: itemIndex * 0.05 + index * 0.1 + 0.3,
                          }}
                          className="text-sm text-muted-foreground flex items-center gap-2 group"
                        >
                          <span className="w-1.5 h-1.5 rounded-full bg-primary/50 group-hover:bg-primary transition-colors" />
                          {item}
                        </motion.li>
                      ))}
                    </ul>
                  </motion.div>
                );
              })
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}
