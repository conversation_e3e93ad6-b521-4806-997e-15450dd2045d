"use client";
import React from "react";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/modules/ui/pagination";

interface DesignSuggestionPaginationProps {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
  isLoading?: boolean;
}

const DesignSuggestionPagination: React.FC<DesignSuggestionPaginationProps> = ({
  currentPage,
  totalPages,
  totalCount,
  itemsPerPage,
  onPageChange,
  isLoading = false,
}) => {
  const startIndex = (currentPage - 1) * itemsPerPage + 1;
  const endIndex = Math.min(startIndex + itemsPerPage - 1, totalCount);

  if (totalPages <= 1 || totalCount === 0) {
    return null;
  }

  return (
    <div className="flex flex-col sm:flex-row items-center justify-between gap-2 px-2 py-1 sm:px-4 sm:py-2">
      <Pagination className="pb-0">
        <PaginationContent className="gap-1 sm:gap-2">
          {currentPage > 1 && (
            <PaginationItem>
              <PaginationPrevious
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  onPageChange(currentPage - 1);
                }}
                className="h-8 sm:h-9"
              />
            </PaginationItem>
          )}

          {[...Array(totalPages)].map((_, index) => {
            const pageNumber = index + 1;
            const shouldShowOnMobile =
              pageNumber === 1 ||
              pageNumber === totalPages ||
              pageNumber === currentPage;
            const shouldShowOnDesktop =
              pageNumber === 1 ||
              pageNumber === totalPages ||
              (pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1);

            if (shouldShowOnDesktop) {
              return (
                <PaginationItem key={index} className="hidden sm:block">
                  <PaginationLink
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      onPageChange(pageNumber);
                    }}
                    isActive={currentPage === pageNumber}
                    className="h-8 sm:h-9 min-w-[32px] sm:min-w-[36px]"
                  >
                    {pageNumber}
                  </PaginationLink>
                </PaginationItem>
              );
            } else if (shouldShowOnMobile) {
              return (
                <PaginationItem key={index} className="block sm:hidden">
                  <PaginationLink
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      onPageChange(pageNumber);
                    }}
                    isActive={currentPage === pageNumber}
                    className="h-8 min-w-[32px] px-2"
                  >
                    {pageNumber}
                  </PaginationLink>
                </PaginationItem>
              );
            } else if (
              (pageNumber === currentPage - 2 ||
                pageNumber === currentPage + 2) &&
              window.innerWidth > 640
            ) {
              return (
                <PaginationItem key={index} className="hidden sm:block">
                  <PaginationEllipsis className="h-8 sm:h-9" />
                </PaginationItem>
              );
            }
            return null;
          })}

          {currentPage < totalPages && (
            <PaginationItem>
              <PaginationNext
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  onPageChange(currentPage + 1);
                }}
                className="h-8 sm:h-9"
              />
            </PaginationItem>
          )}
        </PaginationContent>
      </Pagination>
    </div>
  );
};

export default DesignSuggestionPagination;
