"use client";
import React from "react";
import Image from "next/image";
import Link from "next/link";
import { Card, CardContent } from "@/modules/ui/card";
import { Badge } from "@/modules/ui/badge";
import { Skeleton } from "@/modules/ui/skeleton";
import { Eye } from "lucide-react";

interface DesignSuggestion {
  id: string;
  slug: string;
  room: string;
  style: string;
  dimensions: string;
  outputImage: string | null;
  inputImage: string | null;
}

interface DesignSuggestionGridProps {
  suggestions: DesignSuggestion[];
}

const PLACEHOLDER_IMAGE = `data:image/svg+xml,${encodeURIComponent(`
<svg width="800" height="600" viewBox="0 0 800 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="800" height="600" fill="#F3F4F6"/>
  <rect x="200" y="150" width="400" height="300" rx="8" fill="#E5E7EB"/>
  <path d="M350 250 L450 250 L400 200 Z" fill="#D1D5DB"/>
  <rect x="350" y="350" width="100" height="50" fill="#D1D5DB"/>
  <text x="400" y="320" text-anchor="middle" font-family="system-ui" font-size="14" fill="#6B7280">
    Room Preview
  </text>
</svg>
`)}`;

const DesignSuggestionGrid: React.FC<DesignSuggestionGridProps> = ({
  suggestions,
}) => {
  const [imageFallback, setImageFallback] = React.useState<{
    [key: string]: boolean;
  }>({});

  const handleImageError = (id: string) => {
    setImageFallback((prev) => ({
      ...prev,
      [id]: true,
    }));
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 p-6">
      {suggestions.map((suggestion) => (
        <Link
          href={`/dashboard/design-suggestions/${suggestion.slug}`}
          key={suggestion.id}
          className="group relative rounded-lg overflow-hidden transition-all duration-200 hover:ring-2 hover:ring-primary/20 hover:shadow-lg focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary"
        >
          <Card className="h-full w-full border-0">
            <div className="relative aspect-[16/9] bg-muted/10 overflow-hidden">
              <Image
                src={
                  imageFallback[suggestion.id]
                    ? PLACEHOLDER_IMAGE
                    : suggestion.outputImage ||
                      suggestion.inputImage ||
                      PLACEHOLDER_IMAGE
                }
                alt={`${suggestion.room} in ${suggestion.style} style`}
                fill
                sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                className="object-cover transition-transform duration-300 group-hover:scale-105"
                priority={false}
                loading="lazy"
                onError={() => handleImageError(suggestion.id)}
              />
              {/* Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300" />
              {/* View Icon */}
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                <div className="w-10 h-10 rounded-full bg-background/80 backdrop-blur-sm flex items-center justify-center text-primary transform translate-y-4 group-hover:translate-y-0 transition-all duration-300">
                  <Eye className="w-5 h-5" />
                </div>
              </div>
            </div>

            <CardContent className="p-4">
              <div className="space-y-3">
                <div className="flex items-start justify-between">
                  <h3 className="font-medium text-base sm:text-lg leading-tight group-hover:text-primary transition-colors duration-200">
                    {suggestion.room}
                  </h3>
                  <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded-full">
                    New
                  </span>
                </div>
                <div className="flex flex-wrap gap-2">
                  <Badge
                    variant="secondary"
                    className="text-xs bg-secondary/80 hover:bg-secondary transition-colors"
                  >
                    {suggestion.style}
                  </Badge>
                  <Badge
                    variant="outline"
                    className="text-xs border-border/50 bg-background/80"
                  >
                    {suggestion.dimensions} sq m
                  </Badge>
                </div>
                <div className="flex items-center justify-between text-sm text-muted-foreground pt-3 border-t border-border/50">
                  <span className="flex items-center gap-1">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                      className="w-4 h-4"
                    >
                      <path
                        fillRule="evenodd"
                        d="M9.69 18.933l.003.001C9.89 19.02 10 19 10 19s.11.02.308-.066l.002-.001.006-.003.018-.008a5.741 5.741 0 00.281-.14c.186-.096.446-.24.757-.433.62-.384 1.445-.966 2.274-1.765C15.302 14.988 17 12.493 17 9A7 7 0 103 9c0 3.492 1.698 5.988 3.355 7.584a13.731 13.731 0 002.273 1.765 11.842 11.842 0 00.976.544l.***************.006.003zM10 11.25a2.25 2.25 0 100-4.5 2.25 2.25 0 000 4.5z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Room Details
                  </span>
                  <span className="group-hover:text-primary transition-colors duration-200">
                    View →
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </Link>
      ))}
    </div>
  );
};

// Loading skeleton for the grid
export const DesignSuggestionGridSkeleton = () => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 p-6">
      {[...Array(6)].map((_, i) => (
        <Card key={i} className="overflow-hidden border-0">
          <div className="relative aspect-[16/9] bg-muted/10">
            <Skeleton className="h-full w-full" />
          </div>
          <CardContent className="p-4">
            <Skeleton className="h-6 w-3/4 mb-4" />
            <div className="flex gap-2">
              <Skeleton className="h-5 w-20" />
              <Skeleton className="h-5 w-24" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default DesignSuggestionGrid;
