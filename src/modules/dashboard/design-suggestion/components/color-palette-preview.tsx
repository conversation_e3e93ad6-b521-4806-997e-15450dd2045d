"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/modules/ui";
import { Card } from "@/modules/ui/card";
import { Copy, Check, Eye, EyeOff } from "lucide-react";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/modules/ui/tooltip";

interface ColorPalettePreviewProps {
  colors: string[];
  name: string;
  description: string;
}

export function ColorPalettePreview({
  colors,
  name,
  description,
}: ColorPalettePreviewProps) {
  const [copiedColor, setCopiedColor] = useState<string | null>(null);
  const [isHovered, setIsHovered] = useState(false);
  const [showColorValues, setShowColorValues] = useState(false);

  const copyToClipboard = async (color: string) => {
    await navigator.clipboard.writeText(color);
    setCopiedColor(color);
    setTimeout(() => setCopiedColor(null), 2000);
  };

  // Convert hex to RGB
  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? {
          r: parseInt(result[1], 16),
          g: parseInt(result[2], 16),
          b: parseInt(result[3], 16),
        }
      : null;
  };

  // Get contrasting text color
  const getContrastColor = (hexColor: string) => {
    const rgb = hexToRgb(hexColor);
    if (!rgb) return "white";
    const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
    return brightness > 128 ? "black" : "white";
  };

  return (
    <Card className="relative overflow-hidden group">
      {/* Color Preview */}
      <div className="h-40 relative">
        <div className="absolute inset-0 flex">
          {colors.map((color, index) => (
            <motion.div
              key={color}
              className="h-full relative cursor-pointer group/color flex-1"
              style={{ backgroundColor: color }}
              whileHover={{ flex: 1.5 }}
              transition={{ duration: 0.3 }}
              onClick={() => copyToClipboard(color)}
            >
              <AnimatePresence>
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className={cn(
                    "absolute inset-0 flex items-center justify-center",
                    "opacity-0 group-hover/color:opacity-100 transition-opacity",
                    "bg-black/20 "
                  )}
                >
                  {copiedColor === color ? (
                    <Check className="w-6 h-6 text-white drop-shadow-lg" />
                  ) : (
                    <Copy className="w-6 h-6 text-white drop-shadow-lg" />
                  )}
                </motion.div>
                {showColorValues && (
                  <motion.div
                    initial={{ opacity: 0, y: 5 }}
                    animate={{ opacity: 1, y: 0 }}
                    className={cn(
                      "absolute bottom-2 left-2 right-2 text-center px-2 py-1 text-xs rounded",
                      "bg-black/40 backdrop-blur-sm"
                    )}
                    style={{ color: getContrastColor(color) }}
                  >
                    {color}
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>

        {/* Toggle Color Values Button */}
        <motion.button
          className={cn(
            "absolute top-2 right-2 p-1.5 rounded-full",
            "bg-black/20 backdrop-blur-sm opacity-0 group-hover:opacity-100",
            "transition-opacity hover:bg-black/40"
          )}
          onClick={() => setShowColorValues(!showColorValues)}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
        >
          {showColorValues ? (
            <EyeOff className="w-4 h-4 text-white" />
          ) : (
            <Eye className="w-4 h-4 text-white" />
          )}
        </motion.button>
      </div>

      {/* Info */}
      <div className="p-4">
        <h4 className="font-medium mb-2 flex items-center justify-between">
          {name}
          <span className="text-xs text-muted-foreground">
            {colors.length} colors
          </span>
        </h4>
        <p className="text-sm text-muted-foreground">{description}</p>

        {/* Color Chips */}
        <div className="mt-4 flex flex-wrap gap-2">
          {colors.map((color) => (
            <Tooltip key={color}>
              <TooltipTrigger asChild>
                <motion.button
                  className={cn(
                    "h-8 px-3 rounded-full text-xs font-medium",
                    "flex items-center gap-2 group/chip"
                  )}
                  style={{
                    backgroundColor: color,
                    color: getContrastColor(color),
                  }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => copyToClipboard(color)}
                >
                  <span
                    className="w-2 h-2 rounded-full"
                    style={{
                      backgroundColor: getContrastColor(color),
                      opacity: 0.5,
                    }}
                  />
                  {color}
                  <Copy
                    className={cn(
                      "w-3 h-3 opacity-0 group-hover/chip:opacity-100 transition-opacity",
                      copiedColor === color ? "text-green-500" : ""
                    )}
                    style={{
                      color:
                        copiedColor === color
                          ? "currentColor"
                          : getContrastColor(color),
                    }}
                  />
                </motion.button>
              </TooltipTrigger>
              <TooltipContent>Click to copy</TooltipContent>
            </Tooltip>
          ))}
        </div>
      </div>
    </Card>
  );
}
