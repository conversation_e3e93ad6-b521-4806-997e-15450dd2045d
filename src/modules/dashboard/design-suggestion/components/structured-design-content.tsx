"use client";

import React from "react";

interface ColorPalette {
  name: string;
  colors: string[];
  description: string;
}

interface DesignElement {
  category: string;
  items: string[];
}

interface StructuredDesignContentProps {
  colorPalettes: ColorPalette[];
  designElements: DesignElement[];
  designDescription: {
    overallConcept: string;
    sections: {
      title: string;
      content: string[];
    }[];
  };
}

export function StructuredDesignContent({
  colorPalettes,
  designElements,
  designDescription,
}: StructuredDesignContentProps) {
  // Function to clean markdown formatting from text
  const cleanMarkdown = (text: string) => {
    return text.replace(/\*\*/g, "").replace(/^[-*]\s/, "");
  };

  return (
    <div className="space-y-8">
      {/* Color Palettes Section */}
      {colorPalettes.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-xl font-semibold">Color Palettes</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {colorPalettes.map((palette) => (
              <div
                key={palette.name}
                className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm"
              >
                <h4 className="font-medium mb-2">
                  {cleanMarkdown(palette.name)}
                </h4>
                <div className="flex gap-2 mb-2">
                  {palette.colors.map((color) => (
                    <div
                      key={color}
                      className="w-8 h-8 rounded-full border border-gray-200"
                      style={{ backgroundColor: color }}
                      title={color}
                    />
                  ))}
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  {cleanMarkdown(palette.description)}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Design Elements Section */}
      {designElements.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-xl font-semibold">Design Elements</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {designElements.map((element) => (
              <div
                key={element.category}
                className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm"
              >
                <h4 className="font-medium mb-2">
                  {cleanMarkdown(element.category)}
                </h4>
                <ul className="list-disc list-inside space-y-1 text-gray-600 dark:text-gray-300">
                  {element.items.map((item) => (
                    <li key={item}>{cleanMarkdown(item)}</li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Design Description Section */}
      <div className="space-y-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <h4 className="text-lg font-medium mb-4">Overall Design Concept</h4>
          <p className="text-gray-600 dark:text-gray-300 mb-8">
            {cleanMarkdown(designDescription.overallConcept)}
          </p>
          <div className="space-y-6">
            {designDescription.sections.map((section, index) => (
              <div
                key={section.title}
                className="border-t dark:border-gray-700 pt-4 first:border-t-0 first:pt-0"
              >
                <h4 className="text-lg font-medium mb-3">
                  {cleanMarkdown(section.title).replace(/^\d+\.\s*/, "")}
                </h4>
                <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                  {section.content.map((item, itemIndex) => (
                    <li key={itemIndex} className="pl-4">
                      {cleanMarkdown(item)}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
