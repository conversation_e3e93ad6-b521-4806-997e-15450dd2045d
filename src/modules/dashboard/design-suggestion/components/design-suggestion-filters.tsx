"use client";

import React, { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/modules/ui/select";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/modules/ui/button";
import { PlusCircle, Filter, X } from "lucide-react";
import { Badge } from "@/modules/ui/badge";
import { cn } from "@/lib/utils";

const roomOptions = ["Living Room", "Bedroom", "Kitchen", "Bathroom", "Office"];
const styleOptions = [
  "Modern",
  "Traditional",
  "Minimalist",
  "Rustic",
  "Industrial",
];

const DesignSuggestionFilters: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [showFilters, setShowFilters] = useState(false);

  const currentRoom = searchParams.get("room");
  const currentStyle = searchParams.get("style");
  const hasFilters = currentRoom || currentStyle;

  const handleFilterChange = (type: "room" | "style", value: string) => {
    const current = new URLSearchParams(searchParams.toString());
    if (value) {
      current.set(type, value);
    } else {
      current.delete(type);
    }
    current.set("page", "1");
    router.push(`/dashboard/design-suggestions?${current.toString()}`);
  };

  const clearFilters = () => {
    router.push("/dashboard/design-suggestions?page=1");
  };

  return (
    <div className="flex flex-col gap-2 w-full">
      <div className="flex items-center gap-2">
        <div className="hidden sm:flex items-center gap-2 flex-1">
          <Select
            value={currentRoom || ""}
            onValueChange={(value) => handleFilterChange("room", value)}
          >
            <SelectTrigger className="w-[180px] h-8">
              <SelectValue placeholder="Filter by Room" />
            </SelectTrigger>
            <SelectContent>
              {roomOptions.map((room) => (
                <SelectItem key={room} value={room.toLowerCase()}>
                  {room}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={currentStyle || ""}
            onValueChange={(value) => handleFilterChange("style", value)}
          >
            <SelectTrigger className="w-[180px] h-8">
              <SelectValue placeholder="Filter by Style" />
            </SelectTrigger>
            <SelectContent>
              {styleOptions.map((style) => (
                <SelectItem key={style} value={style.toLowerCase()}>
                  {style}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {hasFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="h-8"
            >
              <X className="h-4 w-4 mr-1" />
              Clear
            </Button>
          )}
        </div>

        <div className="flex items-center gap-1 sm:hidden">
          <Button
            variant="outline"
            size="sm"
            className={cn("h-8", showFilters && "bg-accent")}
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="h-4 w-4 mr-1" />
            Filters
            {hasFilters && (
              <Badge
                variant="secondary"
                className="ml-1 rounded-full h-5 min-w-[20px] px-1"
              >
                {[currentRoom, currentStyle].filter(Boolean).length}
              </Badge>
            )}
          </Button>

          {hasFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="h-8 w-8 px-0"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        <div className="flex-1 sm:flex-none flex justify-end">
          <Link
            href="/dashboard/design-suggestions/new"
            className="w-full sm:w-auto"
          >
            <Button
              variant="gradient-primary"
              size="sm"
              shine
              glow
              className="w-full sm:w-auto h-8 text-sm whitespace-nowrap"
            >
              <PlusCircle className="h-4 w-4 mr-1" />
              <span className="hidden sm:inline">New Design Suggestion</span>
              <span className="sm:hidden">New Design</span>
            </Button>
          </Link>
        </div>
      </div>

      <div
        className={cn(
          "sm:hidden overflow-hidden transition-all duration-200",
          showFilters ? "h-auto opacity-100" : "h-0 opacity-0"
        )}
      >
        <div className="space-y-2 pt-1">
          <div>
            <h4 className="text-xs font-medium text-muted-foreground mb-1">
              Room Type
            </h4>
            <Select
              value={currentRoom || ""}
              onValueChange={(value) => handleFilterChange("room", value)}
            >
              <SelectTrigger className="h-8">
                <SelectValue placeholder="Select room type" />
              </SelectTrigger>
              <SelectContent>
                {roomOptions.map((room) => (
                  <SelectItem key={room} value={room.toLowerCase()}>
                    {room}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <h4 className="text-xs font-medium text-muted-foreground mb-1">
              Style
            </h4>
            <Select
              value={currentStyle || ""}
              onValueChange={(value) => handleFilterChange("style", value)}
            >
              <SelectTrigger className="h-8">
                <SelectValue placeholder="Select style" />
              </SelectTrigger>
              <SelectContent>
                {styleOptions.map((style) => (
                  <SelectItem key={style} value={style.toLowerCase()}>
                    {style}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {hasFilters && (
          <div className="flex flex-wrap gap-1 pt-2">
            {currentRoom && (
              <Badge variant="secondary" className="h-6 text-xs">
                Room: {currentRoom}
                <button
                  onClick={() => handleFilterChange("room", "")}
                  className="ml-1 hover:text-destructive"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            )}
            {currentStyle && (
              <Badge variant="secondary" className="h-6 text-xs">
                Style: {currentStyle}
                <button
                  onClick={() => handleFilterChange("style", "")}
                  className="ml-1 hover:text-destructive"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default DesignSuggestionFilters;
