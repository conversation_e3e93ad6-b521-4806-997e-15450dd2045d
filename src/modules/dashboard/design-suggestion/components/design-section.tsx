"use client";

import React from "react";
import { motion } from "framer-motion";
import { cn } from "@/modules/ui";
import { Card } from "@/modules/ui/card";

interface DesignSectionProps {
  title: string;
  content: string[];
  index: number;
}

export function DesignSection({ title, content, index }: DesignSectionProps) {
  // Remove asterisks and clean up the text
  const cleanText = (text: string) => {
    return text.replace(/\*\*/g, "").trim();
  };

  // Check if a line is a sub-heading
  const isSubHeading = (text: string) => {
    return text.includes(":");
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
    >
      <Card className="overflow-hidden">
        <div className="border-l-4 border-primary p-4">
          <h3 className="text-lg font-semibold mb-4 text-foreground">
            {cleanText(title)}
          </h3>
          <div className="space-y-3">
            {content.map((line, lineIndex) => {
              const cleanedLine = cleanText(line);

              if (isSubHeading(cleanedLine)) {
                const [subHeading, ...rest] = cleanedLine.split(":");
                return (
                  <div key={lineIndex} className="space-y-2">
                    <h4 className="font-medium text-foreground">
                      {cleanText(subHeading)}
                    </h4>
                    <p className="text-sm text-muted-foreground pl-4">
                      {cleanText(rest.join(":"))}
                    </p>
                  </div>
                );
              }

              return (
                <motion.p
                  key={lineIndex}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 + lineIndex * 0.05 }}
                  className={cn(
                    "text-sm text-muted-foreground",
                    line.trim().startsWith("-") && "pl-4"
                  )}
                >
                  {cleanText(line)}
                </motion.p>
              );
            })}
          </div>
        </div>
      </Card>
    </motion.div>
  );
}
