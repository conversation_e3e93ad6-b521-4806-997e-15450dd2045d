"use client";

import React from "react";
import { motion } from "framer-motion";
import { Camera, Palette, Wand2, Image as ImageIcon } from "lucide-react";
import { cn } from "@/modules/ui";

const PROCESS_STEPS = [
  {
    icon: Camera,
    title: "Upload Room",
    description: "Upload a photo of your room",
  },
  {
    icon: Palette,
    title: "Choose Style",
    description: "Select your preferred design style",
  },
  {
    icon: Wand2,
    title: "Generate Design",
    description: "AI generates personalized suggestions",
  },
  {
    icon: ImageIcon,
    title: "Visualize",
    description: "See your room transformed",
  },
];

interface DesignProcessProps {
  currentStep: number;
}

export function DesignProcess({ currentStep }: DesignProcessProps) {
  return (
    <div className="py-8">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {PROCESS_STEPS.map((step, index) => {
          const Icon = step.icon;
          const isActive = currentStep === index;
          const isPast = currentStep > index;

          return (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="relative"
            >
              {/* Connector Line */}
              {index < PROCESS_STEPS.length - 1 && (
                <div className="hidden md:block absolute top-7 left-full w-full h-0.5 bg-muted">
                  <motion.div
                    className="h-full bg-primary"
                    initial={{ width: "0%" }}
                    animate={{ width: isPast ? "100%" : "0%" }}
                    transition={{ duration: 0.5 }}
                  />
                </div>
              )}

              <div className="flex flex-col items-center text-center">
                <div
                  className={cn(
                    "relative w-14 h-14 rounded-full border-2 flex items-center justify-center mb-3 transition-colors",
                    isActive
                      ? "border-primary bg-primary text-primary-foreground"
                      : isPast
                      ? "border-primary bg-primary/10 text-primary"
                      : "border-muted bg-background text-muted-foreground"
                  )}
                >
                  <Icon className="w-6 h-6" />
                  {isPast && (
                    <motion.div
                      className="absolute inset-0 rounded-full bg-primary/10"
                      initial={{ scale: 0 }}
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 0.5 }}
                    />
                  )}
                </div>
                <h3
                  className={cn(
                    "font-medium mb-1",
                    isActive ? "text-primary" : "text-foreground"
                  )}
                >
                  {step.title}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {step.description}
                </p>
              </div>
            </motion.div>
          );
        })}
      </div>
    </div>
  );
}
