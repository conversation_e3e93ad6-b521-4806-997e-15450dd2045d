"use client";

import React, { useCallback, useState } from "react";
import useDesignSuggestionStore from "../hooks/use-design-suggestion-store";
import { DesignForm } from "./design-form";
import { GeneratedContent } from "./generated-content";
import { ActionButtons } from "./action-buttons";
import { StyleGallery } from "./style-gallery";
import { ImageComparisonSlider } from "./image-comparison-slider";
import { DesignProcess } from "./design-process";
import { generateDesign } from "../actions/generate-design-suggestion";
import { saveDesignSuggestion } from "@/modules/dashboard/design-suggestion/actions/save-design-suggestion";
import { renderSuggestionInteriorImage } from "../actions/render-suggestion-interior-image";
import { useSession } from "next-auth/react";
import { useToast } from "@/modules/ui/use-toast";
import { readStreamableValue } from "ai/rsc";

interface ColorPalette {
  name: string;
  colors: string[];
  description: string;
}

interface DesignElement {
  category: string;
  items: string[];
}

export default function DesignSuggestionInterface() {
  const store = useDesignSuggestionStore();
  const { data: session } = useSession();
  const { toast } = useToast();
  const [currentStep, setCurrentStep] = useState(0);
  const [colorPalettes, setColorPalettes] = useState<ColorPalette[]>([]);
  const [designElements, setDesignElements] = useState<DesignElement[]>([]);

  const handleImageUpload = useCallback(
    (file: File) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        store.setImagePreview(reader.result as string);
        setCurrentStep(1); // Move to style selection step
      };
      reader.readAsDataURL(file);
    },
    [store]
  );

  const handleStyleSelect = (style: string) => {
    store.setStyle(style);
    setCurrentStep(2); // Move to generation step
  };

  const handleGenerate = async () => {
    if (store.isLoading) return;
    if (!store.imagePreview) {
      toast({
        title: "Error",
        description: "Please upload a room image to continue",
        variant: "destructive",
      });
      return;
    }

    store.setIsLoading(true);
    store.setSuggestion("");
    store.setCloudflareImageUrl(null);
    store.setSavedSlug(null);
    store.setCanGenerateImage(false);
    setCurrentStep(3); // Move to visualization step
    setColorPalettes([]);
    setDesignElements([]);

    const formData = new FormData();
    formData.append("room", store.room);
    formData.append("style", store.style);
    formData.append("dimensions", store.dimensions);

    if (store.imagePreview) {
      const response = await fetch(store.imagePreview);
      const blob = await response.blob();
      formData.append("image", blob, "image.jpg");
    }

    try {
      const response = await generateDesign(formData);
      let fullSuggestion = "";

      for await (const chunk of readStreamableValue(response.message)) {
        fullSuggestion += chunk;
        store.setSuggestion(fullSuggestion);
      }

      store.setCloudflareImageUrl(response.cloudflareImageUrl);
      setColorPalettes(response.colorPalettes);
      setDesignElements(response.designElements);

      const savedSuggestion = await saveDesignSuggestion({
        room: store.room,
        style: store.style,
        dimensions: store.dimensions,
        suggestion: fullSuggestion,
        inputImage: response.cloudflareImageUrl,
      });

      store.setSavedSlug(savedSuggestion.slug);
      store.setDesignSuggestionId(savedSuggestion.id);

      toast({
        title: "Success",
        description: "Design suggestion saved successfully!",
      });
    } catch (error) {
      console.error("Error generating design:", error);
      store.setSuggestion(
        "An error occurred while generating the design. Please try again."
      );
      store.setCanGenerateImage(false);
      setCurrentStep(2); // Go back to generation step on error
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "An error occurred",
        variant: "destructive",
      });
    } finally {
      store.setIsLoading(false);
    }
  };

  const handleGenerateImage = async () => {
    if (!session) {
      toast({
        title: "Error",
        description: "Please log in to generate images.",
        variant: "destructive",
      });
      return;
    }

    if ((session.user as any).imageCredits < 1) {
      toast({
        title: "Error",
        description: "Not enough credits to generate an image.",
        variant: "destructive",
      });
      return;
    }

    store.setIsGeneratingImage(true);

    try {
      if (!store.designSuggestionId) {
        throw new Error("Design suggestion ID is missing");
      }

      const result = await renderSuggestionInteriorImage({
        userId: session.user.id,
        designSuggestionId: store.designSuggestionId,
        prompt: store.suggestion,
        cloudflareImageUrl: store.cloudflareImageUrl || undefined,
      });

      if (result.success) {
        store.setGeneratedImageUrl(result.imageUrl);
        toast({
          title: "Success",
          description: "Image generated successfully!",
        });
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error("Error generating image:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to generate image. Please try again.",
        variant: "destructive",
      });
    } finally {
      store.setIsGeneratingImage(false);
    }
  };

  return (
    <div className="p-6 w-full max-w-7xl mx-auto space-y-8">
      <DesignProcess currentStep={currentStep} />

      <div className="flex flex-col lg:flex-row gap-8">
        <div className="w-full lg:w-1/3 space-y-6">
          <div className="bg-card rounded-lg p-6 shadow-md">
            <DesignForm
              state={{
                room: store.room,
                style: store.style,
                dimensions: store.dimensions,
                imagePreview: store.imagePreview,
              }}
              setRoom={store.setRoom}
              setStyle={store.setStyle}
              setDimensions={store.setDimensions}
              handleImageUpload={handleImageUpload}
              setImagePreview={store.setImagePreview}
            />
          </div>

          {currentStep >= 1 && (
            <div className="bg-card rounded-lg p-6 shadow-md">
              <h3 className="text-lg font-semibold mb-4">Choose Style</h3>
              <StyleGallery
                selectedStyle={store.style}
                onStyleSelect={handleStyleSelect}
              />
            </div>
          )}

          <ActionButtons
            onGenerate={handleGenerate}
            onGenerateImage={handleGenerateImage}
            isLoading={store.isLoading}
            isGeneratingImage={store.isGeneratingImage}
            hasSuggestion={!!store.suggestion}
            savedSlug={store.savedSlug}
            canGenerateImage={store.canGenerateImage}
          />
        </div>

        <div className="w-full lg:w-2/3">
          <GeneratedContent
            suggestion={store.suggestion}
            isLoading={store.isLoading}
            generatedImageUrl={store.generatedImageUrl}
            colorPalettes={colorPalettes}
            designElements={designElements}
          />

          {store.cloudflareImageUrl && store.generatedImageUrl && (
            <div className="mt-6">
              <h3 className="text-lg font-semibold mb-4">Before & After</h3>
              <ImageComparisonSlider
                beforeImage={store.cloudflareImageUrl}
                afterImage={store.generatedImageUrl}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
