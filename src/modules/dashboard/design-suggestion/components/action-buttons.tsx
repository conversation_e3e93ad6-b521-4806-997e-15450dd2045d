import React from "react";
import { But<PERSON> } from "@/modules/ui/button";
import { Loader2 } from "lucide-react";
import Link from "next/link";

interface ActionButtonsProps {
  onGenerate: () => void;
  onGenerateImage: () => void;
  isLoading: boolean;
  isGeneratingImage: boolean;
  hasSuggestion: boolean;
  savedSlug: string | null;
  canGenerateImage: boolean;
}

export function ActionButtons({
  onGenerate,
  onGenerateImage,
  isLoading,
  isGeneratingImage,
  hasSuggestion,
  savedSlug,
  canGenerateImage,
}: ActionButtonsProps) {
  return (
    <div className="flex flex-col gap-4">
      <Button
        variant="gradient-primary"
        size="default"
        shine
        glow
        className="text-[12px] sm:text-base px-4 sm:px-6 py-4 sm:py-4 group"
        onClick={onGenerate}
        disabled={isLoading}
      >
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Generating...
          </>
        ) : (
          "Generate Design Suggestion"
        )}
      </Button>
      {hasSuggestion && (
        <Button
          onClick={onGenerateImage}
          disabled={isGeneratingImage}
          variant={canGenerateImage ? "default" : "secondary"}
        >
          {isGeneratingImage ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Generating Image...
            </>
          ) : (
            `Generate Image (1 Credit)`
          )}
        </Button>
      )}
      {savedSlug && (
        <Link
          href={`/dashboard/design-suggestions/${savedSlug}`}
          className="w-full"
          passHref
        >
          <Button variant="outline" className="w-full">
            View Full Details
          </Button>
        </Link>
      )}
    </div>
  );
}
