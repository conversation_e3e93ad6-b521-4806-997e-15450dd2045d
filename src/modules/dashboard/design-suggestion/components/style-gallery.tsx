"use client";

import React from "react";
import Image from "next/image";
import { motion } from "framer-motion";
import { cn } from "@/modules/ui";

interface StyleOption {
  id: string;
  name: string;
  description: string;
  imageUrl: string;
  colors: string[];
}

const STYLE_OPTIONS: StyleOption[] = [
  {
    id: "modern",
    name: "Modern",
    description: "Clean lines, minimal decoration, and functional spaces",
    imageUrl: "/images/styles/modern.jpg",
    colors: ["#FFFFFF", "#000000", "#808080", "#D3D3D3"],
  },
  {
    id: "scandinavian",
    name: "Scandinavian",
    description: "Light, airy spaces with natural materials",
    imageUrl: "/images/styles/scandinavian.jpg",
    colors: ["#FFFFFF", "#F5F5F5", "#E8E8E8", "#DCDCDC"],
  },
  {
    id: "industrial",
    name: "Industrial",
    description: "Raw materials, exposed elements, and urban feel",
    imageUrl: "/images/styles/industrial.jpg",
    colors: ["#2B2B2B", "#4A4A4A", "#696969", "#808080"],
  },
  {
    id: "minimalist",
    name: "Minimalist",
    description: "Simplicity, functionality, and thoughtful design",
    imageUrl: "/images/styles/minimalist.jpg",
    colors: ["#FFFFFF", "#F8F8F8", "#F0F0F0", "#E8E8E8"],
  },
];

interface StyleGalleryProps {
  selectedStyle: string;
  onStyleSelect: (style: string) => void;
}

export function StyleGallery({
  selectedStyle,
  onStyleSelect,
}: StyleGalleryProps) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
      {STYLE_OPTIONS.map((style) => (
        <motion.div
          key={style.id}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => onStyleSelect(style.id)}
          className={cn(
            "relative rounded-lg overflow-hidden cursor-pointer border-2 transition-colors",
            selectedStyle === style.id
              ? "border-primary shadow-lg"
              : "border-transparent hover:border-primary/50"
          )}
        >
          <div className="relative aspect-video">
            <Image
              src={style.imageUrl}
              alt={style.name}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
          </div>
          <div className="absolute bottom-0 left-0 right-0 p-4">
            <h3 className="text-white font-semibold mb-1">{style.name}</h3>
            <p className="text-white/80 text-sm line-clamp-2">
              {style.description}
            </p>
            <div className="flex gap-1 mt-2">
              {style.colors.map((color, index) => (
                <div
                  key={index}
                  className="w-6 h-6 rounded-full border border-white/20"
                  style={{ backgroundColor: color }}
                />
              ))}
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
}
