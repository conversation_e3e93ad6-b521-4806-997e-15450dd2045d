"use client";

import React, { useState } from "react";
import Image from "next/image";
import { motion, useMotionValue } from "framer-motion";
import { cn } from "@/modules/ui";

interface ImageComparisonSliderProps {
  beforeImage: string;
  afterImage: string;
  className?: string;
}

export function ImageComparisonSlider({
  beforeImage,
  afterImage,
  className,
}: ImageComparisonSliderProps) {
  const [sliderPosition, setSliderPosition] = useState(50);
  const dragX = useMotionValue(0);

  const handleDrag = (event: any, info: any) => {
    const containerWidth = (event.target.parentElement as HTMLElement)
      .offsetWidth;
    const newPosition = (info.point.x / containerWidth) * 100;
    setSliderPosition(Math.min(Math.max(newPosition, 0), 100));
  };

  return (
    <div
      className={cn(
        "relative aspect-video rounded-lg overflow-hidden border shadow-sm",
        className
      )}
    >
      {/* Before Image */}
      <div className="absolute inset-0">
        <Image
          src={beforeImage}
          alt="Before"
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          priority
        />
      </div>

      {/* After Image (clipped) */}
      <div
        className="absolute inset-0"
        style={{
          clipPath: `inset(0 ${100 - sliderPosition}% 0 0)`,
        }}
      >
        <Image
          src={afterImage}
          alt="After"
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          priority
        />
      </div>

      {/* Slider */}
      <motion.div
        drag="x"
        dragConstraints={{ left: 0, right: 0 }}
        dragElastic={0}
        dragMomentum={false}
        onDrag={handleDrag}
        style={{
          x: dragX,
          left: `${sliderPosition}%`,
        }}
        className="absolute top-0 bottom-0"
      >
        <div className="absolute inset-y-0 -left-px w-0.5 bg-white shadow-[0_0_10px_rgba(0,0,0,0.3)]" />
        <div className="absolute top-1/2 -translate-y-1/2 -translate-x-1/2 w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center cursor-grab active:cursor-grabbing">
          <div className="w-6 h-0.5 bg-gray-400 rounded-full" />
        </div>
      </motion.div>

      {/* Labels */}
      <div className="absolute bottom-4 left-4 px-2 py-1 bg-black/50 text-white text-sm rounded">
        Before
      </div>
      <div className="absolute bottom-4 right-4 px-2 py-1 bg-black/50 text-white text-sm rounded">
        After
      </div>
    </div>
  );
}
