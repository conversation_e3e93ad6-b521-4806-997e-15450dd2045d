import React, { useMemo } from "react";
import { ScrollArea } from "@/modules/ui/scroll-area";
import { Loader2, Image as ImageIcon, Wand2 } from "lucide-react";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";
import { DesignVisualization } from "./design-visualization";
import { StructuredDesignContent } from "./structured-design-content";
import { parseDesignResponse } from "../utils/parse-design-response";

interface GeneratedContentProps {
  suggestion: string;
  isLoading: boolean;
  generatedImageUrl: string | null;
  colorPalettes?: {
    name: string;
    colors: string[];
    description: string;
  }[];
  designElements?: {
    category: string;
    items: string[];
  }[];
}

export function GeneratedContent({
  suggestion,
  isLoading,
  generatedImageUrl,
  colorPalettes = [],
  designElements = [],
}: GeneratedContentProps) {
  const parsedContent = useMemo(() => {
    if (!suggestion) return null;
    return parseDesignResponse(suggestion);
  }, [suggestion]);

  return (
    <div className="bg-card rounded-lg shadow-md overflow-hidden">
      <div className="p-6 border-b">
        <h2 className="text-xl font-semibold flex items-center gap-2">
          <Wand2 className="h-5 w-5 text-primary" />
          Generated Design
        </h2>
      </div>

      <div className="p-6">
        <ScrollArea className="h-[60vh] rounded-lg">
          <AnimatePresence mode="wait">
            {isLoading && !suggestion ? (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="flex flex-col items-center justify-center h-[50vh] gap-4"
              >
                <div className="relative">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  <div className="absolute inset-0 h-8 w-8 animate-ping opacity-20 rounded-full bg-primary" />
                </div>
                <p className="text-sm text-muted-foreground animate-pulse">
                  Crafting your perfect design...
                </p>
              </motion.div>
            ) : parsedContent ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="space-y-8"
              >
                {/* Visual Elements */}
                <DesignVisualization
                  colorPalettes={parsedContent.colorPalettes}
                  designElements={parsedContent.designElements}
                  isLoading={isLoading}
                />

                {/* Structured Content */}
                <StructuredDesignContent
                  colorPalettes={parsedContent.colorPalettes}
                  designElements={parsedContent.designElements}
                  designDescription={{
                    overallConcept: parsedContent.sections[0]?.content[0] || "",
                    sections: parsedContent.sections.slice(1),
                  }}
                />
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="text-muted-foreground h-[50vh] flex flex-col items-center justify-center gap-4"
              >
                <div className="relative">
                  <ImageIcon className="h-12 w-12 text-muted-foreground/30" />
                  <div className="absolute inset-0 animate-pulse opacity-20 rounded-full bg-primary" />
                </div>
                <p className="text-center max-w-sm">
                  Enter your room details and click generate to receive
                  personalized design suggestions
                </p>
              </motion.div>
            )}
          </AnimatePresence>
        </ScrollArea>

        <AnimatePresence>
          {generatedImageUrl && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              className="mt-6 space-y-4"
            >
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <ImageIcon className="h-4 w-4 text-primary" />
                Generated Visualization
              </h3>
              <div className="relative aspect-video w-full overflow-hidden rounded-lg border shadow-sm">
                <Image
                  src={generatedImageUrl}
                  alt="Generated interior design"
                  fill
                  className="object-cover transition-all duration-300 hover:scale-105"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  priority
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
