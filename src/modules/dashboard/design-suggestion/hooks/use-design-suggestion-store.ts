import { create } from "zustand";
import { PredictionStatus } from "@/modules/dashboard/main-features/types";

interface DesignSuggestionState {
  room: string;
  style: string;
  dimensions: string;
  instructionType: string;
  imagePreview: string | null;
  suggestion: string;
  isLoading: boolean;
  isGeneratingImage: boolean;
  cloudflareImageUrl: string | null;
  savedSlug: string | null;
  designSuggestionId: string | null;
  generatedImageUrl: string | null;
  canGenerateImage: boolean;

  setRoom: (room: string) => void;
  setStyle: (style: string) => void;
  setDimensions: (dimensions: string) => void;
  setInstructionType: (instructionType: string) => void;
  setImagePreview: (imagePreview: string | null) => void;
  setSuggestion: (suggestion: string) => void;
  setIsLoading: (isLoading: boolean) => void;
  setIsGeneratingImage: (isGeneratingImage: boolean) => void;
  setCloudflareImageUrl: (url: string | null) => void;
  setSavedSlug: (slug: string | null) => void;
  setDesignSuggestionId: (id: string | null) => void;
  setGeneratedImageUrl: (url: string | null) => void;
  setCanGenerateImage: (canGenerate: boolean) => void;
  reset: () => void;
}

const useDesignSuggestionStore = create<DesignSuggestionState>((set) => ({
  room: "Living room",
  style: "Tropical",
  dimensions: "15",
  instructionType: "flux",
  imagePreview: null,
  suggestion: "",
  isLoading: false,
  isGeneratingImage: false,
  cloudflareImageUrl: null,
  savedSlug: null,
  designSuggestionId: null,
  generatedImageUrl: null,
  canGenerateImage: false,

  setRoom: (room) => set({ room }),
  setStyle: (style) => set({ style }),
  setDimensions: (dimensions) => set({ dimensions }),
  setInstructionType: (instructionType) => set({ instructionType }),
  setImagePreview: (imagePreview) => set({ imagePreview }),
  setSuggestion: (suggestion) =>
    set((state) => ({
      suggestion,
      canGenerateImage: suggestion.trim().length > 0,
    })),
  setIsLoading: (isLoading) => set({ isLoading }),
  setIsGeneratingImage: (isGeneratingImage) => set({ isGeneratingImage }),
  setCloudflareImageUrl: (url) => set({ cloudflareImageUrl: url }),
  setSavedSlug: (slug) => set({ savedSlug: slug }),
  setDesignSuggestionId: (id) => set({ designSuggestionId: id }),
  setGeneratedImageUrl: (url) => set({ generatedImageUrl: url }),
  setCanGenerateImage: (canGenerate) => set({ canGenerateImage: canGenerate }),

  reset: () =>
    set({
      room: "Living room",
      style: "Tropical",
      dimensions: "15",
      instructionType: "flux",
      imagePreview: null,
      suggestion: "",
      isLoading: false,
      isGeneratingImage: false,
      cloudflareImageUrl: null,
      savedSlug: null,
      designSuggestionId: null,
      generatedImageUrl: null,
      canGenerateImage: false,
    }),
}));

export default useDesignSuggestionStore;
