"use server";

import { z } from "zod";
import Replicate from "replicate";
import prisma from "@/lib/prisma/prisma";
import { revalidatePath } from "next/cache";

const REPLICATE_API_TOKEN = process.env.REPLICATE_API_TOKEN;

const InputSchema = z.object({
  userId: z.string(),
  designSuggestionId: z.string(),
  prompt: z.string(),
  cloudflareImageUrl: z.string().optional(),
});

export async function renderSuggestionInteriorImage(
  input: z.infer<typeof InputSchema>
) {
  try {
    const validatedInput = InputSchema.parse(input);
    console.log("Validated input:", validatedInput);

    // Check if the DesignSuggestion exists
    const designSuggestion = await prisma.designSuggestion.findUnique({
      where: { id: validatedInput.designSuggestionId },
    });

    if (!designSuggestion) {
      throw new Error("Design suggestion not found");
    }

    // Decrease image credits by 1 before making the API call
    const updatedUser = await prisma.user.update({
      where: { id: validatedInput.userId },
      data: { imageCredits: { decrement: 1 } },
    });

    if (updatedUser.imageCredits < 0) {
      throw new Error("Insufficient image credits");
    }

    const replicate = new Replicate({
      auth: REPLICATE_API_TOKEN,
    });

    const prediction = await replicate.predictions.create({
      version:
        "dfad41707589d68ecdccd1dfa600d55a208f9310748e44bfe35b4a6291453d5e",
      input: {
        prompt: validatedInput.prompt,
        image: validatedInput.cloudflareImageUrl,
        scale_factor: 1,
        creativity: 0.8,
        resemblance: 0.6,
        seed: Math.floor(Math.random() * 1000000),
        dynamic: 6,
        handfix: "disabled",
        pattern: false,
        sharpen: 0,
        sd_model: "juggernaut_reborn.safetensors [338b85bc4f]",
        scheduler: "DPM++ 3M SDE Karras",
        tiling_width: 112,
        output_format: "png",
        tiling_height: 144,
        negative_prompt: "ugly, disfigured, low quality, blurry, nsfw",
        num_inference_steps: 18,
      },
    });
    console.log("Initial prediction:", prediction);

    // Wait for the prediction to complete
    let completedPrediction = prediction;
    while (
      completedPrediction.status !== "succeeded" &&
      completedPrediction.status !== "failed"
    ) {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      completedPrediction = await replicate.predictions.get(prediction.id);
      console.log("Prediction status:", completedPrediction.status);
    }
    console.log("Completed prediction:", completedPrediction);

    if (completedPrediction.status === "succeeded") {
      // Update the design suggestion with the generated image URL
      const updatedSuggestion = await prisma.designSuggestion.update({
        where: { id: validatedInput.designSuggestionId },
        data: { outputImage: completedPrediction.output[0] },
      });
      console.log("Updated design suggestion:", updatedSuggestion);

      revalidatePath("/design-suggestions/[slug]", "page");

      return {
        success: true,
        imageUrl: completedPrediction.output[0],
        updatedCredits: updatedUser.imageCredits,
      };
    } else {
      throw new Error("Image generation failed");
    }
  } catch (error) {
    console.error("Error in renderSuggestionInteriorImage:", error);

    // Refund credits in case of error
    if (
      error instanceof Error &&
      error.message !== "Insufficient image credits" &&
      error.message !== "Design suggestion not found"
    ) {
      await prisma.user.update({
        where: { id: input.userId },
        data: { imageCredits: { increment: 1 } },
      });
    }

    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: "Invalid input parameters",
        updatedCredits: 0,
      };
    }
    if (error instanceof Error) {
      return { success: false, error: error.message, updatedCredits: 0 };
    }
    return {
      success: false,
      error: "An unexpected error occurred",
      updatedCredits: 0,
    };
  }
}
