import prisma from "@/lib/prisma/prisma";

export async function getDesignSuggestions() {
  try {
    const suggestions = await prisma.designSuggestion.findMany({
      select: {
        id: true,
        slug: true,
        room: true,
        style: true,
        dimensions: true,
        inputImage: true,
        outputImage: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return suggestions;
  } catch (error) {
    console.error("Error fetching design suggestions:", error);
    return [];
  }
}
