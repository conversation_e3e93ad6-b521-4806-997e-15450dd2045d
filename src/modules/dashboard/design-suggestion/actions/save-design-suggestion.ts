"use server";
import prisma from "@/lib/prisma/prisma";
import { getAnonymousId } from "@/lib/anonymous-user";
import slugify from "slugify";
import { currentUser } from "@/modules/auth/actions/user-actions";

export async function saveDesignSuggestion({
  room,
  style,
  dimensions,
  suggestion,
  inputImage,
}: {
  room: string;
  style: string;
  dimensions: string;
  suggestion: string;
  inputImage: string | null;
}) {
  const session = await currentUser();
  const userId = session?.id;
  const anonymousId = getAnonymousId();

  const slug = slugify(`${room}-${style}-${Date.now()}`, { lower: true });

  // Generate a meta description
  const metaDescription = `Explore our AI-generated ${style} design for a ${dimensions}sqm ${room}. Get inspired by our interior design suggestions.`;

  const savedSuggestion = await prisma.designSuggestion.create({
    data: {
      userId,
      anonymousId: userId ? undefined : anonymousId,
      room,
      style,
      dimensions,
      suggestion,
      inputImage,
      slug,
      metaDescription,
    },
  });

  return { id: savedSuggestion.id, slug: savedSuggestion.slug };
}
