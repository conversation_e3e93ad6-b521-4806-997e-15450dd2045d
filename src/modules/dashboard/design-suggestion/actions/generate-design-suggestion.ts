"use server";

import { openai } from "@ai-sdk/openai";
import { streamText } from "ai";
import { createStreamableValue } from "ai/rsc";
import { uploadImageToCloudflare } from "@/lib/cloudflare";

const INTERIOR_ANALYSIS_INSTRUCTIONS = `You are an expert interior designer tasked with analyzing user-provided room images and suggesting improvements. Follow these steps and guidelines in your responses:

1. Image Analysis:
   * Carefully examine the provided room image.
   * Identify the room type (e.g., living room, bedroom, kitchen).
   * Note key structural elements (walls, windows, flooring).
   * Observe existing furniture, color schemes, and decor.

2. User Input Interpretation:
   * Understand the user's desired style for the redesign.
   * Consider the room dimensions and space constraints.

3. Design Conceptualization:
   * Based on the user's desired style, conceptualize improvements that transform the room while respecting its basic structure.
   * Create a cohesive color palette that aligns with the chosen style.
   * Suggest specific furniture pieces, materials, and decor elements.

4. Response Format:
   Your response should be structured in JSON format within a markdown code block, followed by a detailed design description:

   \`\`\`json
   {
     "colorPalettes": [
       {
         "name": "Primary Palette",
         "colors": ["#HEX1", "#HEX2", "#HEX3"],
         "description": "Description of how these colors work together"
       },
       {
         "name": "Accent Palette",
         "colors": ["#HEX1", "#HEX2"],
         "description": "How to use these accent colors"
       }
     ],
     "designElements": [
       {
         "category": "Furniture",
         "items": ["Item 1", "Item 2", "Item 3"]
       },
       {
         "category": "Lighting",
         "items": ["Item 1", "Item 2"]
       },
       {
         "category": "Materials",
         "items": ["Material 1", "Material 2"]
       }
     ]
   }
   \`\`\`

   Then provide a detailed design description in markdown format, including:
   * Overall design concept
   * Specific recommendations for furniture placement
   * Lighting suggestions
   * Material and texture recommendations
   * Color scheme implementation
   * Decor and accessory suggestions`;

interface GenerateDesignResponse {
  message: any;
  cloudflareImageUrl: string | null;
  colorPalettes: {
    name: string;
    colors: string[];
    description: string;
  }[];
  designElements: {
    category: string;
    items: string[];
  }[];
}

export async function generateDesign(
  formData: FormData
): Promise<GenerateDesignResponse> {
  const room = formData.get("room") as string;
  const style = formData.get("style") as string;
  const dimensions = formData.get("dimensions") as string;
  const image = formData.get("image") as File | null;

  const stream = createStreamableValue();
  let cloudflareImageUrl: string | null = null;
  let colorPalettes: GenerateDesignResponse["colorPalettes"] = [];
  let designElements: GenerateDesignResponse["designElements"] = [];

  if (!image) {
    throw new Error("Room image is required");
  }

  try {
    cloudflareImageUrl = await uploadImageToCloudflare(image);
  } catch (error) {
    console.error("Error uploading image to Cloudflare:", error);
    throw new Error("Failed to upload image");
  }

  (async () => {
    let content: any[] = [
      {
        type: "text",
        text: `Generate a design for a ${room} in ${style} style with dimensions of approximately ${dimensions} square meters.`,
      },
    ];

    if (cloudflareImageUrl) {
      content.push({
        type: "image",
        image: cloudflareImageUrl,
      });
    }

    const { textStream } = await streamText({
      model: openai("gpt-4o", {
        structuredOutputs: true,
      }),
      system: INTERIOR_ANALYSIS_INSTRUCTIONS,
      messages: [{ role: "user", content }],
    });

    let fullResponse = "";
    for await (const text of textStream) {
      fullResponse += text;
      stream.update(text);

      // Try to extract JSON data from the response
      const jsonMatch = fullResponse.match(/```json\n([\s\S]*?)\n```/);
      if (jsonMatch) {
        try {
          const jsonData = JSON.parse(jsonMatch[1]);
          colorPalettes = jsonData.colorPalettes || [];
          designElements = jsonData.designElements || [];
        } catch (error) {
          console.error("Error parsing JSON data:", error);
        }
      }
    }

    stream.done();
  })();

  return {
    message: stream.value,
    cloudflareImageUrl,
    colorPalettes,
    designElements,
  };
}
