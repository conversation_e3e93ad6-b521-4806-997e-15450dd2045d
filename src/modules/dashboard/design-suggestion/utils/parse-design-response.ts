interface DesignResponse {
  colorPalettes: {
    name: string;
    colors: string[];
    description: string;
  }[];
  designElements: {
    category: string;
    items: string[];
  }[];
}

interface ParsedDesignSuggestion {
  colorPalettes: DesignResponse["colorPalettes"];
  designElements: DesignResponse["designElements"];
  sections: {
    title: string;
    content: string[];
  }[];
}

export function parseDesignResponse(response: string): ParsedDesignSuggestion {
  // Extract JSON data
  const jsonMatch = response.match(/```json\n([\s\S]*?)\n```/);
  let colorPalettes = [];
  let designElements = [];

  if (jsonMatch) {
    try {
      const jsonData = JSON.parse(jsonMatch[1]);
      colorPalettes = jsonData.colorPalettes || [];
      designElements = jsonData.designElements || [];
    } catch (error) {
      console.error("Error parsing JSON data:", error);
    }
  }

  // Extract and structure the markdown content
  const markdownContent = response.replace(/```json[\s\S]*?```\s*/, "").trim();

  // Parse markdown into sections
  const sections = markdownContent.split("\n\n").reduce((acc, section) => {
    const lines = section.split("\n");
    if (lines[0].startsWith("#")) {
      // It's a heading
      acc.push({
        title: lines[0].replace(/^#+\s*/, ""),
        content: lines.slice(1).filter((line) => line.trim()),
      });
    } else if (lines[0].includes(":")) {
      // It's a section with a colon
      const [title, ...content] = lines;
      acc.push({
        title: title.replace(":", "").trim(),
        content: content.filter((line) => line.trim()),
      });
    } else {
      // Add to previous section if exists, or create new
      if (acc.length > 0) {
        acc[acc.length - 1].content.push(
          ...lines.filter((line) => line.trim())
        );
      } else {
        acc.push({
          title: "Design Overview",
          content: lines.filter((line) => line.trim()),
        });
      }
    }
    return acc;
  }, [] as { title: string; content: string[] }[]);

  return {
    colorPalettes,
    designElements,
    sections: sections.filter((section) => section.content.length > 0),
  };
}
