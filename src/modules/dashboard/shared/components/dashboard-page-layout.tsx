import React from "react";
import { ScrollArea } from "@/modules/ui/scroll-area";
import { cn } from "@/modules/ui/";

interface DashboardPageLayoutProps {
  children: React.ReactNode;
  heading: string;
  description?: string;
  actions?: React.ReactNode;
  className?: string;
}

export const DashboardPageLayout = ({
  children,
  heading,
  description,
  actions,
  className,
}: DashboardPageLayoutProps) => {
  return (
    <ScrollArea className="flex-1 h-screen ">
      <div className="h-[calc(100dvh ]  flex flex-col ">
        <div className="flex-shrink-0 px-3 py-2 ">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
            {actions && (
              <div className="flex items-center gap-4">{actions}</div>
            )}
          </div>
        </div>

        <div className="px-4 sm:px-6 pb-2 md:pb-8">
          <main className={cn("", className)}>{children}</main>
        </div>
      </div>
    </ScrollArea>
  );
};
