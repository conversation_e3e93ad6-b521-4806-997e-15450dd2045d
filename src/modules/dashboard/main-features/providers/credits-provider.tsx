"use client";

import { useCurrentUser } from "@/modules/auth/hooks/use-current-user";
import { useImageCreditsStore } from "../stores/use-image-credits-store";
import { ReactNode, useEffect } from "react";

export function CreditsProvider({ children }: { children: ReactNode }) {
  const { data: currentUser } = useCurrentUser();
  const { credits, fetchCredits } = useImageCreditsStore();

  useEffect(() => {
    if (currentUser && credits === null) {
      fetchCredits();
    }
  }, [currentUser, credits, fetchCredits]);

  return <>{children}</>;
} 