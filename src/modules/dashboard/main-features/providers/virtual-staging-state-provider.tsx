"use client";

import React, { useEffect } from "react";
import { useImageCreditsStore } from "@/modules/dashboard/main-features/stores/use-image-credits-store";
import { usePrivacySettings } from "@/modules/dashboard/main-features/store/privacy-settings-store";
import { useSubscription } from "@/modules/payments/hooks/use-subscription";
import { useSession } from "next-auth/react";
import { checkPrivacyAccess } from "@/modules/dashboard/main-features/actions/check-privacy-access";
import { useToast } from "@/modules/ui/use-toast";

interface VirtualStagingStateProviderProps {
  initialCredits: number;
  children: React.ReactNode;
}

export function VirtualStagingStateProvider({
  initialCredits,
  children,
}: VirtualStagingStateProviderProps) {
  const { setCredits } = useImageCreditsStore();
  const { setIsPublic } = usePrivacySettings();
  const { data: session } = useSession();
  const { toast } = useToast();
  const { isSubscribed, subscription } = useSubscription();
  const hasSubscription = Boolean(isSubscribed || subscription);

  // Initialize credits from server data
  useEffect(() => {
    if (initialCredits !== undefined) {
      setCredits(initialCredits);
    }
  }, [initialCredits, setCredits]);

  // Check privacy access
  useEffect(() => {
    const checkAccess = async () => {
      if (session?.user?.id) {
        try {
          const { canTogglePrivate, error } = await checkPrivacyAccess(
            session.user.id
          );

          if (!canTogglePrivate && !hasSubscription) {
            setIsPublic(true);
          }

          if (error) {
            toast({
              variant: "destructive",
              title: "Error",
              description: error,
            });
          }
        } catch (error) {
          console.error("Error checking subscription status:", error);
          if (!hasSubscription) {
            setIsPublic(true);
          }
          toast({
            variant: "destructive",
            title: "Error",
            description: "Failed to check subscription status",
          });
        }
      }
    };

    if (session?.user?.id) {
      checkAccess();
    }
  }, [session?.user?.id, hasSubscription, toast, setIsPublic]);

  return <>{children}</>;
} 