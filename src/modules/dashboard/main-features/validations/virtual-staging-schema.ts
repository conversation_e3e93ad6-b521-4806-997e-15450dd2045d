import { z } from "zod";

export const virtualStagingFormSchema = z
  .object({
    imageUrl: z.string().min(1, "Image is required"),
    maskUrl: z.string().url().optional(),
    prompt: z.string().optional(),
    room: z.string().optional(),
    style: z.string().optional(),
    excludedElements: z.string().optional(),
    resolution: z.string(),
    styleType: z.string(),
    aspectRatio: z.string(),
    negativePrompt: z.string().optional(),
    magicPromptOption: z.string(),
  })
  .superRefine((data, ctx) => {
    // If there's no imageUrl, add an error
    if (!data.imageUrl) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Image is required",
        path: ["imageUrl"],
      });
    }

    // Check if either prompt exists OR both room and style exist
    const hasPrompt = data.prompt && data.prompt.length > 0;
    const hasRoomAndStyle = data.room && data.style;

    if (!hasPrompt && !hasRoomAndStyle) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Either prompt or both room and style are required",
        path: ["prompt"],
      });
    }
  });

export type VirtualStagingFormData = z.infer<typeof virtualStagingFormSchema>;
