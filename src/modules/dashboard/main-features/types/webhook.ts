import { z } from "zod";
import { PredictionStatus } from "./base";

export interface WebhookPayload {
  id: string;
  status: PredictionStatus;
  created_at?: string;
  started_at?: string;
  completed_at?: string;
  output?: string | string[] | null;
  error?: string | null;
  logs?: string;
  metrics?: {
    predict_time?: number;
  };
  inputImage?: string;
  styleImage?: string;
  model?: string;
  structureImage?: string;
  url?: string;
  prompt?: string;
  style?: string;
  room?: string;
  excludedElements?: string;
  createdAt?: string;
}

export const WebhookPayloadSchema = z.object({
  id: z.string(),
  status: z
    .enum(["processing", "succeeded", "failed", "canceled"])
    .transform((val) => val.toUpperCase() as PredictionStatus),
  output: z
    .union([z.string(), z.array(z.string())])
    .nullable()
    .optional(),
  error: z.string().nullable().optional(),
  logs: z.string().optional(),
  metrics: z
    .object({
      predict_time: z.number().optional(),
    })
    .optional(),
  inputImage: z.string().optional(),
  styleImage: z.string().optional(),
  model: z.string().optional(),
  structureImage: z.string().optional(),
  url: z.string().optional(),
  prompt: z.string().optional(),
  style: z.string().optional(),
  room: z.string().optional(),
  excludedElements: z.string().optional(),
  createdAt: z.string().optional(),
});
