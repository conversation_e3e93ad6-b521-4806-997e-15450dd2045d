export interface FeatureCard {
  title: string;
  description: string;
  imageUrl: string;
  videoUrl?: string;
  action: string;
  link: string;
  usageExamples: string[];
  newFeature: boolean;
  beta: boolean;
  supportedFormats: string[];
  maxResolution: string;
  premium: boolean;
  newEngine?: {
    name: string;
    improvement: string;
  };
}

export interface FeatureSection {
  title: string;
  description: string;
  features: FeatureCard[];
}
