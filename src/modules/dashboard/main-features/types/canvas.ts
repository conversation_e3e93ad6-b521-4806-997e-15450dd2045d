import { AspectRatioOption } from "./image-dimensions";

export interface CanvasPosition {
  x: number;
  y: number;
}

export interface CanvasSize {
  width: number;
  height: number;
}

export interface CanvasRect {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface CanvasLine {
  tool: "brush" | "eraser" | "rectangle" | "lasso";
  points: number[];
  strokeWidth: number;
}

export type CanvasOrientation = "landscape" | "portrait";

export interface GeneratedImage {
  image: HTMLImageElement;
  element?: HTMLImageElement;
  position: CanvasPosition;
  width: number;
  height: number;
  rotation: number;
  isSelected: boolean;
  isNew?: boolean;
  id?: string;
  opacity?: number;
  src?: string;
  isUploaded?: boolean;
  scaleX?: number;
  scaleY?: number;
}

export interface TransformConfig {
  selectedImageIndex: number | null;
  isDragging: boolean;
  isResizing: boolean;
  isRotating: boolean;
}

export interface CanvasState {
  mode: CanvasMode;
  maskTool: MaskToolMode;
  brushSize: number;
  lines: CanvasLine[];
  dimensions: CanvasSize;
  selectionArea: CanvasRect | null;
  magicFillAreaSelection: boolean;
  zoom: number;
  userId: string;
  prompt: string;
  negativePrompt: string;
  generatedImages: GeneratedImage[];
  transformConfig: TransformConfig;
  inputImage: HTMLImageElement | null;
  inputImagePosition: CanvasPosition;
  isGenerating: boolean;
  isUploadingImage: boolean;
}

export interface CanvasToolbarProps {
  mode: CanvasMode;
  onModeChange: (mode: CanvasMode) => void;
  onGenerate: () => Promise<void>;
  onUpload: () => void;
  onUndo?: () => void;
  onRedo?: () => void;
  onSave?: () => void;
  onDownload?: () => void;
  canUndo?: boolean;
  canRedo?: boolean;
}

export interface ToolbarButtonProps {
  onClick: () => void;
  icon: React.ReactNode;
  label: string;
  active?: boolean;
  disabled?: boolean;
  variant?: "default" | "ghost" | "secondary";
  className?: string;
  hideLabel?: boolean;
  iconClassName?: string;
  buttonClassName?: string;
  size?: "sm" | "default" | "wide";
  disableActiveBackground?: boolean;
}

export enum CanvasMode {
  Move = "move",
  Pan = "pan",
  MagicFill = "magicfill",
  Generate = "generate",
  SelectArea = "selectarea",
}

export type MaskToolMode = "none" | "brush" | "eraser" | "lasso" | "rectangle";

export interface MagicFillToolbarProps {
  activeMaskTool: MaskToolMode;
  onMaskToolChange: (tool: MaskToolMode) => void;
  onInvert: () => void;
  onCancelMagicFill: () => void;
  onProceedMagicFill: () => void;
}
