import { z } from "zod";
import {
  BasePlaceholder,
  BaseGeneratedImage,
  GenerateImageResult,
  PredictionStatus,
} from "./base";

export interface StyleTransferPlaceholder extends BasePlaceholder {
  inputImage: string;
  styleImage: string;
  model?: string;
  structureImage?: string;
  isPublic?: boolean;
}

export interface StyleTransferGeneratedImage extends BaseGeneratedImage {
  inputImage: string;
  styleImage: string;
  model?: string;
  structureImage?: string;
}

export const StyleTransferParamsSchema = z.object({
  userId: z.string(),
  seed: z.number().optional(),
  model: z
    .enum(["fast", "high-quality", "realistic", "cinematic", "animated"])
    .default("fast"),
  width: z.number().min(128).max(2048).default(1024),
  height: z.number().min(128).max(2048).default(1024),
  prompt: z.string().default(""),
  style_image: z.string().url(),
  structure_image: z.string().url(),
  output_format: z.enum(["webp", "jpg", "png"]).default("webp"),
  output_quality: z.number().min(0).max(100).default(80),
  negative_prompt: z.string().optional(),
  number_of_images: z.number().min(1).max(10).default(1),
  structure_depth_strength: z.number().max(2).default(1),
  structure_denoising_strength: z.number().max(1).default(0.65),
});

export type StyleTransferParams = z.infer<typeof StyleTransferParamsSchema>;

export type StyleTransferResult = GenerateImageResult & {
  placeholders?: StyleTransferPlaceholder[];
};

export { PredictionStatus };
