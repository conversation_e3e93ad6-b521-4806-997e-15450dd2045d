import { z } from "zod";

export enum PredictionStatus {
  PROCESSING = "PROCESSING",
  PROCESSING_OUTPUT = "PROCESSING_OUTPUT",
  SUCCEEDED = "SUCCEEDED",
  FAILED = "FAILED",
  CANCELED = "CANCELED",
}

export interface BasePlaceholder {
  id: string;
  status: PredictionStatus;
  prompt: string;
  createdAt: Date;
  isPublic?: boolean;
}

export interface BasePlaceholderWithoutPrompt {
  id: string;
  status: PredictionStatus;
  createdAt: Date;
  isPublic?: boolean;
}

export interface BaseGeneratedImage {
  id: string;
  url: string;
  prompt: string;
  createdAt: Date;
  isPublic?: boolean;
}

export interface BaseGeneratedImageWithoutPrompt {
  id: string;
  url: string;
  createdAt: Date;
  isPublic?: boolean;
}

export type ErrorType = "INSUFFICIENT_CREDITS" | "INVALID_INPUT" | "USER_NOT_FOUND" | "USER_CREDIT_ERROR" | "VALIDATION_ERROR" | "UNKNOWN_ERROR";

export type GenerateImageResult = {
  success: boolean;
  predictionId?: string;
  replicateId?: string;
  error?: string;
  updatedCredits: number;
  type?: ErrorType;
};

export type InsufficientCreditsError = {
  success: false;
  error: string;
  updatedCredits: number;
  type: ErrorType;
};

export const PredictionStatusSchema = z.nativeEnum(PredictionStatus);
