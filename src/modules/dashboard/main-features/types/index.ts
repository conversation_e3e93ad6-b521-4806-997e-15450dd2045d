import { PredictionStatus } from "@prisma/client";

export * from "./base";
export * from "./edit-image";
export * from "./interior";
export * from "./exterior";
export * from "./virtual-staging";
export * from "./background-removal";
export * from "./style-transfer";
export * from "./upscale";
export * from "./prediction-types";

export type ValidationError = {
  [key: string]: string[];
};

export type UploadError = {
  code: "UPLOAD_FAILED" | "NETWORK_ERROR" | "INVALID_CREDENTIALS";
  message: string;
};

export type WebhookPayload = {
  id: string;
  status: PredictionStatus;
  url?: string;
  output?: string | string[];
  inputImage?: string;
  prompt?: string;
  style?: string;
  room?: string;
  building?: string;
  excludedElements?: string;
  createdAt?: string;
  styleImage?: string;
  model?: string;
  structureImage?: string;
  outputImage?: string;
  error?: string;
};

export enum CanvasMode {
  View = "view",
  Pan = "pan",
  Move = "move",
  Draw = "draw",
  Erase = "erase",
  MagicFill = "magicFill",
  Generate = "generate",
  SelectArea = "selectArea",
}

export type MaskToolMode = "brush" | "rectangle" | "lasso" | "eraser" | "invert";

export interface CanvasLine {
  tool: "brush" | "eraser" | "rectangle" | "lasso";
  points: number[];
  strokeWidth: number;
}

export interface GeneratedImage {
  image: HTMLImageElement;
  position: { x: number; y: number };
  width: number;
  height: number;
  rotation: number;
  isSelected: boolean;
  cropConfig?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

export interface TransformConfig {
  selectedImageIndex: number | null;
  isResizing: boolean;
  isCropping: boolean;
  isMoving: boolean;
  anchorPoint: { x: number; y: number } | null;
}

export interface CanvasState {
  mode: CanvasMode;
  maskTool: MaskToolMode;
  brushSize: number;
  lines: CanvasLine[];
  prompt: string;
  negativePrompt: string;
  isGenerating: boolean;
  inputImage: HTMLImageElement | null;
  generatedImages: GeneratedImage[];
  imageHistory: string[];
  currentImageIndex: number;
  dimensions: {
    width: number;
    height: number;
    scale: number;
  };
  viewportOffset: {
    x: number;
    y: number;
  };
  zoom: number;
  userId: string | null;
  lineHistory: CanvasLine[][];
  currentLineIndex: number;
  maskUrl: string | null;
  isUploadingImage: boolean;
  transformConfig: TransformConfig;
}

export interface GenerationResult {
  success: boolean;
  outputUrl?: string;
  error?: string;
  updatedCredits: number;
}

export interface EditImageResult {
  success: boolean;
  outputUrl?: string;
  replicateId?: string;
  error?: string;
  updatedCredits: number;
  type?: "INSUFFICIENT_CREDITS";
}

export interface UploadResult {
  url: string;
  error?: string;
}
