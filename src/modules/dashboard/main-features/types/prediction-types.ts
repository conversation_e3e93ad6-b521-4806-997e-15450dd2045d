import { PredictionStatus } from "@prisma/client";

export interface BasePredictionParams {
  userId: string;
  inputImage: string;
  style?: string;
}

export interface InteriorDesignParams extends BasePredictionParams {
  prompt?: string;
  room?: string;
}

export interface ExteriorDesignParams extends BasePredictionParams {
  prompt?: string;
  building?: string;
}

export interface BackgroundRemovalParams extends BasePredictionParams {}

export type PredictionParams =
  | InteriorDesignParams
  | ExteriorDesignParams
  | BackgroundRemovalParams;

export interface PredictionResult {
  success: boolean;
  predictionId?: string;
  replicateId?: string;
  error?: string;
}

export interface BasePrediction {
  id: string;
  replicate_id: string;
  userId: string;
  status: PredictionStatus;
  inputImage: string;
  createdAt: Date;
  completedAt?: Date;
}

export interface InteriorDesign extends BasePrediction {
  outputImages: string[];
  prompt?: string;
  style?: string;
  room?: string;
}

export interface ExteriorDesign extends BasePrediction {
  outputImages: string[];
  prompt?: string;
  style?: string;
  building?: string;
}

export interface BackgroundRemoval extends BasePrediction {
  outputImage?: string;
}
