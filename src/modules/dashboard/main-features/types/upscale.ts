import {
  BasePlaceholderWithoutPrompt,
  BaseGeneratedImageWithoutPrompt,
  GenerateImageResult,
  PredictionStatus,
} from "./base";

export interface UpscalePlaceholder extends BasePlaceholderWithoutPrompt {
  inputImage: string;
  outputImage?: string;
  isPublic?: boolean;
}

export interface UpscaleGeneratedImage extends BaseGeneratedImageWithoutPrompt {
  inputImage: string;
  outputImage: string;
  status: PredictionStatus;
  updatedCredits: number;
}

export interface UpscaleImageParams {
  userId: string;
  image: string;
  upscaleAmount: number;
  creativity: number;
  resemblance: number;
  sourceType?: string;
  originalImageId?: string;
}

export type UpscaleGenerateImageResult = GenerateImageResult & {
  placeholders?: UpscalePlaceholder[];
};
