import { z } from "zod";
import {
  BasePlaceholder,
  BaseGeneratedImage,
  GenerateImageResult,
  PredictionStatus,
} from "./base";

export interface EditImagePlaceholder extends BasePlaceholder {
  inputImage: string;
  maskImage?: string;
  outputImage?: string;
  status: PredictionStatus;
  url: string;
  isPublic?: boolean;
}

export interface EditImageGeneratedImage extends BaseGeneratedImage {
  inputImage: string;
  maskImage?: string;
  outputImage: string;
  replicateId: string;
  status: PredictionStatus;
}

// Base interface for common properties
export interface BaseEditImageParams {
  userId: string;
  negativePrompt?: string;
}

// Type for remove mode
export interface RemoveEditImageParams extends BaseEditImageParams {
  mode: "remove";
  image: string;
  mask: string;
}

// Type for replace mode
export interface ReplaceEditImageParams extends BaseEditImageParams {
  mode: "replace";
  prompt: string;
  image: string;
  mask: string;
  negativePrompt?: string;
  resolution?: string;
  style_type?: string;
  aspect_ratio?: string;
  magic_prompt_option?: "Auto" | "On" | "Off";
  seed?: number;
}

// Type for generate mode
export interface GenerateEditImageParams extends BaseEditImageParams {
  mode: "generate";
  prompt: string;
  negativePrompt?: string;
  resolution?: string;
  style_type?: string;
  aspect_ratio?: string;
  magic_prompt_option?: "Auto" | "On" | "Off";
  seed?: number;
}

// Combined type using discriminated union
export type EditImageParams = RemoveEditImageParams | ReplaceEditImageParams | GenerateEditImageParams;

export const EditImageParamsSchema = z.discriminatedUnion("mode", [
  // Schema for "remove" mode
  z.object({
    userId: z.string().min(1),
    image: z.string().refine((val) => val.startsWith('data:image/') || val.startsWith('http'), {
      message: "Image must be a valid URL or data URL",
    }),
    mask: z.string().refine((val) => val.startsWith('data:image/') || val.startsWith('http'), {
      message: "Mask must be a valid URL or data URL",
    }),
    mode: z.literal("remove"),
    negativePrompt: z.string().optional(),
  }),
  // Schema for "replace" mode
  z.object({
    userId: z.string().min(1),
    image: z.string().refine((val) => val.startsWith('data:image/') || val.startsWith('http'), {
      message: "Image must be a valid URL or data URL",
    }),
    mask: z.string().refine((val) => val.startsWith('data:image/') || val.startsWith('http'), {
      message: "Mask must be a valid URL or data URL",
    }),
    mode: z.literal("replace"),
    prompt: z.string().min(1),
    negativePrompt: z.string().optional(),
    resolution: z.string().optional(),
    style_type: z.string().optional(),
    aspect_ratio: z.string().optional(),
    magic_prompt_option: z.enum(["Auto", "On", "Off"]).optional(),
    seed: z.number().optional(),
  }),
  // Schema for "generate" mode
  z.object({
    userId: z.string().min(1),
    mode: z.literal("generate"),
    prompt: z.string().min(1),
    image: z.string().optional(),
    negativePrompt: z.string().optional(),
    resolution: z.string().optional(),
    style_type: z.string().optional(),
    aspect_ratio: z.string().optional(),
    magic_prompt_option: z.enum(["Auto", "On", "Off"]).optional(),
    seed: z.number().optional(),
  }),
]);

export const RemoveObjectParamsSchema = z.object({
  userId: z.string().min(1),
  image: z.string().url(),
  mask: z.string().url(),
});

export type EditImageResult = GenerateImageResult & {
  outputUrl?: string;
};
