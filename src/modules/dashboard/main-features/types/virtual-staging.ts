import { z } from "zod";
import {
  BasePlaceholder,
  BaseGeneratedImage,
  GenerateImageResult,
  PredictionStatus,
} from "./base";

export interface VirtualStagingPlaceholder extends BasePlaceholder {
  inputImage: string;
  outputImage?: string;
  rawOutputUrl?: string;
  style?: string | null;
  room?: string | null;
  excludedElements?: string | null;
  status: PredictionStatus;
  url: string;
  isPublic?: boolean;
  mask?: string | null;
}

export interface VirtualStagingGeneratedImage extends BaseGeneratedImage {
  inputImage: string;
  outputImage: string;
  url: string;
  displayUrl?: string;
  downloadUrl?: string;
  style?: string | null;
  room?: string | null;
  excludedElements?: string | null;
  status: PredictionStatus;
  isUpscaled?: boolean;
}

export type VirtualStagingParams = {
  userId: string;
  image: string;
  mask?: string;
  excludedElements?: string | null;
  resolution: string;
  style_type: string;
  aspect_ratio: string;
  negative_prompt: string;
  magic_prompt_option: string;
  isAdvancedMode: boolean;
} & (
  | { prompt: string; room?: never; style?: never }
  | { prompt?: never; room: string; style: string }
);

export const VirtualStagingParamsSchema = z
  .object({
    userId: z.string().min(1),
    image: z.string().url(),
    mask: z.string().url().optional(),
    excludedElements: z.string().nullable().optional(),
    resolution: z.string(),
    style_type: z.string(),
    aspect_ratio: z.string(),
    negative_prompt: z.string(),
    magic_prompt_option: z.string(),
    isAdvancedMode: z.boolean(),
  })
  .and(
    z.union([
      z.object({
        prompt: z.string().min(1),
        room: z.undefined(),
        style: z.undefined(),
      }),
      z.object({
        prompt: z.undefined(),
        room: z.string(),
        style: z.string(),
      }),
    ])
  );

export type VirtualStagingGenerateImageParams = VirtualStagingParams;

export type VirtualStagingResult = GenerateImageResult & {
  placeholders?: VirtualStagingPlaceholder[];
};
