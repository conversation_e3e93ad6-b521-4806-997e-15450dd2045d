import { z } from "zod";
import {
  BasePlaceholder,
  BaseGeneratedImage,
  GenerateImageResult,
} from "./base";

export interface InteriorPlaceholder extends BasePlaceholder {
  inputImage: string;
  style?: string;
  room?: string;
  excludedElements?: string;
  isPublic?: boolean;
}

export interface InteriorGeneratedImage extends BaseGeneratedImage {
  inputImage: string;
  style?: string;
  room?: string;
  excludedElements?: string;
}

export type InteriorGenerateImageParams = {
  userId: string;
  image: string;
  maskUrl?: string;
  excludedElements?: string | null;
  conditionScale: number;
  creativity: number;
} & (
  | { prompt: string; room?: never; style?: never }
  | { prompt?: never; room: string; style: string }
);

export type InteriorGenerateImageResult = GenerateImageResult & {
  placeholders?: InteriorPlaceholder[];
};

export const InteriorUserInputSchema = z
  .object({
    userId: z.string().min(1),
    image: z.string().url(),
    maskUrl: z.string().url().optional(),
    excludedElements: z.string().nullable().optional(),
    conditionScale: z.number().min(0).max(1),
    creativity: z.number().min(0).max(100),
    seed: z.number().int().optional(),
  })
  .and(
    z.union([
      z.object({
        prompt: z.string().min(1),
        room: z.undefined(),
        style: z.undefined(),
      }),
      z.object({
        prompt: z.undefined(),
        room: z.string(),
        style: z.string(),
      }),
    ])
  );

export const InteriorGenerateImageParamsSchema = InteriorUserInputSchema;
