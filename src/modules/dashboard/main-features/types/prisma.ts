import { Prisma } from "@prisma/client";

export type User = Prisma.UserGetPayload<{}>;
export type InteriorDesign = Prisma.InteriorDesignGetPayload<{}>;
export type ExteriorDesign = Prisma.ExteriorDesignGetPayload<{}>;
export type BackgroundRemoval = Prisma.BackgroundRemovalGetPayload<{}>;
export type UpscaleImage = Prisma.UpscaleImageGetPayload<{}>;
export type VirtualStaging = Prisma.VirtualStagingGetPayload<{}>;
