import { z } from "zod";
import {
  BasePlaceholderWithoutPrompt,
  BaseGeneratedImageWithoutPrompt,
  GenerateImageResult,
  PredictionStatus,
} from "./base";

export interface BackgroundRemovalPlaceholder
  extends BasePlaceholderWithoutPrompt {
  inputImage: string;
  outputImage?: string;
  isPublic?: boolean;
}

export interface BackgroundRemovalGeneratedImage
  extends BaseGeneratedImageWithoutPrompt {
  inputImage: string;
  outputImage: string;
  status: PredictionStatus;
  updatedCredits: number;
}

export type RemoveBackgroundParams = {
  userId: string;
  image: string;
};

export type BackgroundRemovalResult = GenerateImageResult & {
  placeholders?: BackgroundRemovalPlaceholder[];
};

export const RemoveBackgroundInputSchema = z.object({
  userId: z.string().min(1),
  image: z.string().url(),
});

export interface RemoveBackgroundResult {
  placeholder: BackgroundRemovalPlaceholder;
}

export type RemoveBackgroundResponse = BackgroundRemovalGeneratedImage;
