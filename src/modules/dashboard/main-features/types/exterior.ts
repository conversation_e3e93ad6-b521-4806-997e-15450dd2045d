import { z } from "zod";
import {
  BasePlaceholder,
  BaseGeneratedImage,
  GenerateImageResult,
} from "./base";

export interface ExteriorPlaceholder extends BasePlaceholder {
  inputImage: string;
  style?: string;
  building?: string;
  excludedElements?: string;
  isPublic?: boolean;
}

export interface ExteriorGeneratedImage extends BaseGeneratedImage {
  inputImage: string;
  style?: string;
  building?: string;
  excludedElements?: string;
}

export type ExteriorGenerateImageParams = {
  userId: string;
  image: string;
  excludedElements?: string | null;
  conditionScale: number;
  creativity: number;
} & (
  | { prompt: string; building?: never; style?: never }
  | { prompt?: never; building: string; style: string }
);

export type ExteriorGenerateImageResult = GenerateImageResult & {
  placeholders?: ExteriorPlaceholder[];
};

export const ExteriorUserInputSchema = z
  .object({
    userId: z.string().min(1),
    image: z.string().url(),
    excludedElements: z.string().nullable().optional(),
    conditionScale: z.number().min(0).max(1),
    creativity: z.number().min(0).max(100),
  })
  .and(
    z.union([
      z.object({
        prompt: z.string().min(1),
        building: z.undefined(),
        style: z.undefined(),
      }),
      z.object({
        prompt: z.undefined(),
        building: z.string(),
        style: z.string(),
      }),
    ])
  );

export const ExteriorGenerateImageParamsSchema = ExteriorUserInputSchema;
