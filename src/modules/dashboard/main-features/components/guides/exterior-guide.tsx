import { Image as ImageIcon, Building2, Slide<PERSON> } from "lucide-react";
import { BaseGuide } from "./base-guide";
import type { GuideProps } from "./types";

const ExteriorGuide = () => {
  const guideProps: GuideProps = {
    title: "Exterior Design Guide",
    subtitle: "Transform your exterior in simple steps",
    badge: "AI Architecture Analysis",
    steps: [
      {
        icon: <ImageIcon className="w-6 h-6 sm:w-8 sm:h-8" />,
        title: "Upload Building Photo",
        description: "Start by uploading a photo of your building's exterior",
        required: true,
        tips: [
          { text: "Use well-lit photos", important: true },
          { text: "Capture the entire facade", important: true },
          { text: "Supported formats: JPG, PNG, WEBP", important: true },
          { text: "Maximum size: 4096x4096px", important: false },
        ],
      },
      {
        icon: <Building2 className="w-6 h-6 sm:w-8 sm:h-8" />,
        title: "Choose Style",
        description: "Select building type and architectural style",
        required: true,
        tips: [
          { text: "Select building category", important: true },
          { text: "Choose architectural style", important: true },
          { text: "Or use custom prompt", important: false },
          { text: "Match neighborhood style", important: false },
        ],
      },
      {
        icon: <Sliders className="w-6 h-6 sm:w-8 sm:h-8" />,
        title: "Additional Settings",
        description: "Fine-tune your exterior design preferences",
        required: false,
        tips: [
          { text: "Exclude unwanted elements", important: true },
          { text: "Adjust condition scale", important: true },
          { text: "Set creativity level", important: true },
          { text: "Fine-tune parameters", important: false },
        ],
      },
    ],
    advancedGuide: {
      title: "Advanced Settings Guide",
      sections: [
        {
          title: "Condition Scale",
          items: [
            "0-25: Minor improvements",
            "25-50: Moderate renovation",
            "50-75: Major transformation",
            "75-100: Complete redesign",
          ],
        },
        {
          title: "Creativity Level",
          items: [
            "0-25: Conservative changes",
            "25-50: Balanced updates",
            "50-75: Creative redesign",
            "75-100: Bold transformation",
          ],
        },
      ],
    },
  };

  return <BaseGuide {...guideProps} />;
};

export default ExteriorGuide;
