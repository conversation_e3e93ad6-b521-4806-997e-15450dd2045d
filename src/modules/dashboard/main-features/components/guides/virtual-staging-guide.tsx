import { Image as ImageIcon, Wand2, Home, Sliders, Info } from "lucide-react";
import { BaseGuide } from "./base-guide";
import type { GuideProps } from "./types";

const VirtualStagingGuide = () => {
  const guideProps: GuideProps = {
    title: "Virtual Staging Guide",
    subtitle: "Transform empty spaces into furnished showrooms",
    badge: "AI Furniture Placement",
    steps: [
      {
        icon: <ImageIcon className="w-5 h-5 text-foreground/80" />,
        title: "Upload Room Photo",
        description: "Start by uploading a clear, well-lit photo of your empty space",
        required: true,
        tips: [
          { text: "Use well-lit photos for accurate furniture placement", important: true },
          { text: "Take photos at eye level for natural perspective", important: true },
          { text: "Supported formats: JPG, PNG, WEBP", important: false },
        ],
      },
      {
        icon: <Wand2 className="w-5 h-5 text-foreground/80" />,
        title: "Define Staging Areas",
        description: "Select specific areas where furniture should be placed",
        required: true,
        tips: [
          { text: "Mark areas for furniture placement with the drawing tool", important: true },
          { text: "Exclude windows and doors from selection", important: true },
          { text: "Keep ceiling areas unmarked for best results", important: true },
        ],
      },
      {
        icon: <Home className="w-5 h-5 text-foreground/80" />,
        title: "Room & Style Selection",
        description: "Choose your desired room type and design style preferences",
        required: true,
        tips: [
          { text: "Select the appropriate room type (living room, bedroom, etc.)", important: true },
          { text: "Choose a design style that matches your aesthetic", important: true },
          { text: "Consider your target audience when selecting styles", important: false },
          { text: "Pro tip: Activate custom prompt mode for more control", important: false },
        ],
      },
      {
        icon: <Sliders className="w-5 h-5 text-foreground/80" />,
        title: "Additional Settings",
        description: "Fine-tune your virtual staging with advanced preferences",
        required: false,
        tips: [
          { text: "Exclude any unwanted elements from staging", important: true },
          { text: "Adjust detail level based on your needs", important: false },
          { text: "Enable AI enhancement for more realistic results", important: false },
        ],
      },
    ],
    advancedGuide: {
      title: "Pro Tips & Best Practices",
      sections: [
        {
          title: "Photography Tips",
          items: [
            "Use natural lighting when possible for best color accuracy",
            "Clear the space of any unwanted objects before photographing",
            "Capture the entire room in the frame for complete staging",
            "Avoid extreme wide-angle lenses that distort perspective",
          ],
        },
        {
          title: "Advanced Features",
          items: [
            "Custom prompt mode for precise control over furniture style and placement",
            "Smart furniture placement algorithms adapt to your room's dimensions",
            "Generate multiple design variations from a single photo",
            "Use the comparison view to see before/after results side by side",
          ],
        },
      ],
    },
  };

  return <BaseGuide {...guideProps} />;
};

export default VirtualStagingGuide;
