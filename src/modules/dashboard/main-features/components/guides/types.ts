import { LucideIcon } from "lucide-react";

export interface Tip {
  text: string;
  important?: boolean;
}

export interface GuideStep {
  icon: React.ReactNode;
  title: string;
  description: string;
  required: boolean;
  tips: Tip[];
  color?: string;
}

export interface GuideProps {
  title: string;
  subtitle: string;
  badge: string;
  steps: GuideStep[];
  advancedGuide?: {
    title: string;
    sections: {
      title: string;
      items: string[];
    }[];
  };
}
