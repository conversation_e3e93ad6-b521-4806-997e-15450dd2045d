import { GuideProps } from "./types";
import { GuideHeader, StepsDisplay, AdvancedGuide } from "./guide-components";

export const BaseGuide = ({
  title,
  subtitle,
  badge,
  steps,
  advancedGuide,
}: GuideProps) => {
  return (
    <div className="max-w-4xl mx-auto mb-6 px-4 sm:px-6">
      <div className="relative overflow-hidden">
        <GuideHeader badge={badge} title={title} subtitle={subtitle} />
      </div>

      <StepsDisplay steps={steps} />

      {advancedGuide && <AdvancedGuide {...advancedGuide} />}
    </div>
  );
};
