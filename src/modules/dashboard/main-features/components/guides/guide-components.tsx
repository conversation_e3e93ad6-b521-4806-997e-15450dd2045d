import { useState } from "react";
import { useInView } from "react-intersection-observer";
import { ChevronDown, ChevronRight, Check } from "lucide-react";

import { Badge } from "@/modules/ui/badge";
import { H2, Lead } from "@/modules/ui/typography";
import { cn } from "@/modules/ui";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/modules/ui/collapsible";

import type { Tip, GuideStep as GuideStepType } from "./types";

export const GuideHeader = ({
  badge,
  title,
  subtitle,
}: {
  badge: string;
  title: string;
  subtitle: string;
}) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  return (
    <div
      ref={ref}
      className={cn(
        "text-center mb-8",
        "opacity-0",
        inView && "animate-fade-in"
      )}
    >
      <Badge
        variant="outline"
        className="mb-3 text-sm text-foreground/80 border-border bg-background/50 hover:bg-background/80 transition-colors duration-200"
      >
        {badge}
      </Badge>

      <H2 className="mb-3 text-2xl sm:text-3xl text-foreground">
        {title}
      </H2>

      <Lead className="max-w-2xl mx-auto text-base sm:text-lg text-muted-foreground">
        {subtitle}
      </Lead>
    </div>
  );
};

interface HorizontalStepperProps {
  steps: GuideStepType[];
  activeStep: number;
  setActiveStep: (step: number) => void;
}

const HorizontalStepper = ({ steps, activeStep, setActiveStep }: HorizontalStepperProps) => {
  return (
    <div className="flex items-center justify-center mb-8 px-4 overflow-x-auto">
      <div className="flex items-center space-x-1 sm:space-x-2">
        {steps.map((step, index) => (
          <div key={index} className="flex items-center">
            <button
              onClick={() => setActiveStep(index)}
              className={cn(
                "flex flex-col items-center justify-center rounded-lg p-2 sm:p-3 transition-all duration-200 relative",
                activeStep === index 
                  ? "bg-primary/10 text-primary" 
                  : index < activeStep
                    ? "bg-muted/50 text-muted-foreground"
                    : "bg-background text-muted-foreground hover:bg-muted/20"
              )}
            >
              <div 
                className={cn(
                  "w-10 h-10 rounded-full flex items-center justify-center transition-all duration-200",
                  activeStep === index 
                    ? "bg-primary/90 text-primary-foreground" 
                    : index < activeStep
                      ? "bg-primary/30 text-primary-foreground"
                      : "bg-muted text-muted-foreground"
                )}
              >
                {index < activeStep ? (
                  <Check className="h-5 w-5" />
                ) : (
                  <span>{index + 1}</span>
                )}
              </div>
              
              <span 
                className={cn(
                  "text-xs font-medium mt-2 hidden sm:block",
                  activeStep === index 
                    ? "text-primary" 
                    : "text-muted-foreground"
                )}
              >
                {step.title}
              </span>
              
         
            </button>
            
            {index < steps.length - 1 && (
              <div 
                className={cn(
                  "h-px w-4 sm:w-10 bg-muted-foreground/20",
                  index < activeStep && "bg-primary/30"
                )} 
              />
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

interface GuideStepContentProps {
  step: GuideStepType;
  isActive: boolean;
}

export const GuideStepContent = ({ step, isActive }: GuideStepContentProps) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  return (
    <div
      ref={ref}
      className={cn(
        "flex flex-col w-full overflow-hidden",
        "opacity-0",
        inView && "animate-fade-in",
        !isActive && "hidden"
      )}
    >
      <div className="flex items-start gap-4 mb-4">
        <div className="p-3 bg-muted rounded-lg">
          {step.icon}
        </div>
        <div className="flex-1">
          <h3 className="text-xl font-medium text-foreground flex items-center gap-2">
            {step.title}
            {step.required && (
              <Badge 
                variant="outline" 
                className="text-xs bg-muted text-muted-foreground"
              >
                Required
              </Badge>
            )}
          </h3>
          <p className="text-muted-foreground mt-1">
            {step.description}
          </p>
        </div>
      </div>
      
      <div className="bg-card border border-border rounded-lg p-4 mt-2">
        <h4 className="text-sm font-medium text-foreground mb-3">
          Guidelines
        </h4>
        <ul className="space-y-3">
          {step.tips.map((tip, index) => (
            <li 
              key={index}
              className={cn(
                "flex items-start gap-3 text-sm rounded-md p-2",
                tip.important 
                  ? "bg-primary/5 text-foreground" 
                  : "text-muted-foreground"
              )}
            >
              {tip.important ? (
                <div className="w-5 h-5 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0 mt-0.5">
                  <Check className="h-3 w-3 text-primary" />
                </div>
              ) : (
                <div className="w-5 h-5 rounded-full bg-muted flex items-center justify-center flex-shrink-0 mt-0.5">
                  <ChevronRight className="h-3 w-3 text-muted-foreground" />
                </div>
              )}
              <span className="flex-1">
                {tip.text}
              </span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

interface StepsDisplayProps {
  steps: GuideStepType[];
}

export const StepsDisplay = ({ steps }: StepsDisplayProps) => {
  const [activeStep, setActiveStep] = useState(0);

  return (
    <div className="w-full">
      <HorizontalStepper 
        steps={steps} 
        activeStep={activeStep} 
        setActiveStep={setActiveStep} 
      />
      
      <div className="relative mt-2">
        {steps.map((step, index) => (
          <GuideStepContent 
            key={index} 
            step={step} 
            isActive={activeStep === index} 
          />
        ))}
      </div>
    </div>
  );
};

interface AdvancedGuideProps {
  title: string;
  sections: {
    title: string;
    items: string[];
  }[];
}

export const AdvancedGuide = ({ title, sections }: AdvancedGuideProps) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  return (
    <div
      ref={ref}
      className={cn(
        "mt-10 rounded-lg border border-border",
        "opacity-0",
        inView && "animate-fade-in"
      )}
    >
      <div className="bg-muted p-4 rounded-t-lg flex items-center gap-2">
        <span className="text-lg">💡</span>
        <h3 className="text-base font-medium text-foreground">
          {title}
        </h3>
      </div>
      
      <div className="p-4">
        <div className="grid grid-cols-1 gap-4">
          {sections.map((section, index) => (
            <Collapsible 
              key={index} 
              className="bg-card border border-border rounded-lg overflow-hidden"
            >
              <CollapsibleTrigger className="flex items-center justify-between w-full p-4 text-left">
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-primary/70" />
                  <h4 className="font-medium text-sm text-foreground">
                    {section.title}
                  </h4>
                </div>
                <ChevronDown className="h-4 w-4 text-muted-foreground transition-transform duration-200 ui-open:rotate-180" />
              </CollapsibleTrigger>
              
              <CollapsibleContent className="px-4 pb-4 pt-0">
                <ul className="space-y-2 pl-4">
                  {section.items.map((item, itemIndex) => (
                    <li
                      key={itemIndex}
                      className="flex items-start gap-2 text-sm text-muted-foreground"
                    >
                      <span className="mt-1.5 w-1 h-1 rounded-full bg-muted-foreground/50" />
                      {item}
                    </li>
                  ))}
                </ul>
              </CollapsibleContent>
            </Collapsible>
          ))}
        </div>
      </div>
    </div>
  );
};
