import { Image as ImageIcon } from "lucide-react";
import { BaseGuide } from "./base-guide";
import type { GuideProps } from "./types";

const BackgroundRemovalGuide = () => {
  const guideProps: GuideProps = {
    title: "Background Removal Guide",
    subtitle:
      "Remove backgrounds with precision using our advanced AI technology",
    badge: "Enhanced Edge Detection",
    steps: [
      {
        icon: <ImageIcon className="w-6 h-6 sm:w-8 sm:h-8" />,
        title: "Upload Your Image",
        description: "Start by uploading the image you want to process",
        required: true,
        tips: [
          { text: "Use high contrast images", important: true },
          { text: "Ensure subject is well-lit", important: true },
          { text: "Supported formats: JPG, PNG, WEBP", important: true },
        ],
      },
    ],
    advancedGuide: {
      title: "Pro Tips for Best Results",
      sections: [
        {
          title: "Image Quality",
          items: [
            "Use high contrast photos",
            "Ensure good lighting",
            "Clear subject focus",
            "Sharp, clear images",
          ],
        },
        {
          title: "Subject Tips",
          items: [
            "Simple backgrounds work best",
            "Avoid complex patterns",
            "Keep subject distinct",
            "Check edge clarity",
          ],
        },
      ],
    },
  };

  return <BaseGuide {...guideProps} />;
};

export default BackgroundRemovalGuide;
