import { Image as ImageIcon, Maximize2, <PERSON>lide<PERSON>, ZoomIn } from "lucide-react";
import { BaseGuide } from "./base-guide";
import type { GuideProps } from "./types";

const UpscaleGuide = () => {
  const guideProps: GuideProps = {
    title: "Image Upscaling Guide",
    subtitle: "Enhance your image quality in simple steps",
    badge: "AI Image Upscaling",
    steps: [
      {
        icon: <ImageIcon className="w-6 h-6 sm:w-8 sm:h-8" />,
        title: "Upload Your Image",
        description: "Start by uploading the image you want to upscale",
        required: true,
        tips: [
          { text: "Use clear, sharp images", important: true },
          { text: "Avoid blurry or low-quality sources", important: true },
          { text: "Supported formats: JPG, PNG, WEBP", important: true },
          { text: "Maximum size: 4096x4096px", important: false },
        ],
      },
      {
        icon: <Maximize2 className="w-6 h-6 sm:w-8 sm:h-8" />,
        title: "Adjust Upscale Settings",
        description: "Configure your upscaling preferences",
        required: false,
        tips: [
          { text: "Choose upscale amount (1x to 4x)", important: true },
          { text: "Adjust detail enhancement (0-100)", important: true },
          { text: "Set input image strength (0-3)", important: true },
          { text: "Higher detail = enhanced textures", important: false },
        ],
      },
    ],
    advancedGuide: {
      title: "Advanced Settings Guide",
      sections: [
        {
          title: "Detail Enhancement",
          items: [
            "0-25: Subtle enhancement",
            "25-50: Balanced detail",
            "50-75: Enhanced textures",
            "75-100: Maximum detail",
          ],
        },
        {
          title: "Image Strength",
          items: [
            "0-1: More AI interpretation",
            "1-2: Balanced preservation",
            "2-3: High original retention",
            "Best results: 1.5-2.5",
          ],
        },
      ],
    },
  };

  return <BaseGuide {...guideProps} />;
};

export default UpscaleGuide;
