import { Image as ImageIcon, Home, Slide<PERSON> } from "lucide-react";
import { BaseGuide } from "./base-guide";
import type { GuideProps } from "./types";

const InteriorGuide = () => {
  const guideProps: GuideProps = {
    title: "Interior Design Guide",
    subtitle: "Transform your space in simple steps",
    badge: "AI Style Recommendations",
    steps: [
      {
        icon: <ImageIcon className="w-6 h-6 sm:w-8 sm:h-8" />,
        title: "Upload Room Photo",
        description: "Start by uploading a photo of your interior space",
        required: true,
        tips: [
          { text: "Use well-lit photos", important: true },
          { text: "Take photos at eye level", important: true },
          { text: "Supported formats: JPG, PNG, WEBP", important: true },
        ],
      },
      {
        icon: <Home className="w-6 h-6 sm:w-8 sm:h-8" />,
        title: "Choose Style",
        description: "Select room type and interior style preferences",
        required: true,
        tips: [
          { text: "Select room category", important: true },
          { text: "Choose design style", important: true },
          { text: "Or use custom prompt", important: false },
          { text: "Consider room function", important: false },
        ],
      },
      {
        icon: <Sliders className="w-6 h-6 sm:w-8 sm:h-8" />,
        title: "Additional Settings",
        description: "Fine-tune your interior design preferences",
        required: false,
        tips: [
          { text: "Define areas to change (mask)", important: true },
          { text: "Exclude unwanted elements", important: true },
          { text: "Adjust condition scale", important: true },
          { text: "Set creativity level", important: true },
        ],
      },
    ],
    advancedGuide: {
      title: "Advanced Settings Guide",
      sections: [
        {
          title: "Condition Scale",
          items: [
            "0-25: Minor improvements",
            "25-50: Moderate renovation",
            "50-75: Major transformation",
            "75-100: Complete redesign",
          ],
        },
        {
          title: "Creativity Level",
          items: [
            "0-25: Conservative changes",
            "25-50: Balanced updates",
            "50-75: Creative redesign",
            "75-100: Bold transformation",
          ],
        },
      ],
    },
  };

  return <BaseGuide {...guideProps} />;
};

export default InteriorGuide;
