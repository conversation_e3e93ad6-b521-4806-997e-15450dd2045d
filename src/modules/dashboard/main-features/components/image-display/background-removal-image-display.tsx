"use client";
import React, { useState, useMemo, useEffect } from "react";
import { useChannel } from "ably/react";
import {
  PredictionStatus,
  BackgroundRemovalPlaceholder,
  BackgroundRemovalGeneratedImage,
} from "@/modules/dashboard/main-features/types";
import useBackgroundRemovalStore from "@/modules/dashboard/main-features/store/background-removal-store";
import { useCurrentUser } from "@/modules/auth/hooks/use-current-user";
import { Skeleton } from "@/modules/ui/skeleton";
import { Button } from "@/modules/ui/button";
import { Loader2, Download } from "lucide-react";
import Image from "next/image";
import { useToast } from "@/modules/ui/use-toast";
import ImageDetailsDialog from "./image-details-dialog";
import BackgroundRemovalGuide from "../guides/background-removal-guide";
const BackgroundRemovalImageDisplay: React.FC = () => {
  const currentUser = useCurrentUser();
  const store = useBackgroundRemovalStore();
  const [open, setOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState<
    BackgroundRemovalPlaceholder | BackgroundRemovalGeneratedImage | null
  >(null);
  const [showComparison, setShowComparison] = useState(true);
  const [isDownloading, setIsDownloading] = useState<Record<string, boolean>>(
    {}
  );
  const { toast } = useToast();

  const channelName = useMemo(() => {
    return currentUser.data?.id
      ? `image-generation-background_removal-${currentUser.data.id}`
      : null;
  }, [currentUser.data?.id]);

  const { channel } = useChannel(channelName || "");

  useEffect(() => {
    if (channel && channelName) {
      const handleMessage = (message: any) => {
        store.processChannelMessage(message);
      };

      channel.subscribe(handleMessage);

      return () => {
        channel.unsubscribe(handleMessage);
      };
    }
  }, [channel, channelName, store]);

  const displayItems = useMemo(() => {
    const allItems = [...store.placeholders, ...store.generatedImages];
    return allItems.sort(
      (a, b) => b.createdAt.getTime() - a.createdAt.getTime()
    );
  }, [store.placeholders, store.generatedImages]);

  const handleImageClick = (
    item: BackgroundRemovalPlaceholder | BackgroundRemovalGeneratedImage
  ) => {
    setSelectedImage(item);
    setOpen(true);
  };

  const handleDownload = async (imageUrl: string, imageId: string) => {
    try {
      setIsDownloading({ ...isDownloading, [imageId]: true });
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = "background_removed.png";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Error downloading image:", error);
      toast({
        title: "Error",
        description: "Failed to download the image. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDownloading({ ...isDownloading, [imageId]: false });
    }
  };

  const renderItem = (
    item: BackgroundRemovalPlaceholder | BackgroundRemovalGeneratedImage
  ) => (
    <div
      className="relative w-full pb-[56.25%] cursor-pointer overflow-hidden rounded-lg shadow-md group"
      onClick={() => handleImageClick(item)}
    >
      {"url" in item ? (
        <>
          <Image
            src={item.url}
            alt={`Generated image ${item.id}`}
            layout="fill"
            objectFit="cover"
            className="absolute top-0 left-0 w-full h-full"
          />
          <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
            <Button
              size="sm"
              variant="secondary"
              onClick={(e) => {
                e.stopPropagation();
                handleDownload(item.url, item.id);
              }}
              disabled={isDownloading[item.id]}
            >
              {isDownloading[item.id] ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Download className="w-4 h-4 mr-2" />
              )}
              Download
            </Button>
          </div>
        </>
      ) : (
        <>
          {item.inputImage && (
            <Image
              src={item.inputImage}
              alt="Input image"
              layout="fill"
              objectFit="cover"
              className="filter blur-sm"
            />
          )}
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div
              className={`text-white text-lg font-semibold ${
                item.status === PredictionStatus.PROCESSING
                  ? "animate-pulse"
                  : ""
              }`}
            >
              {item.status}
            </div>
          </div>
        </>
      )}
    </div>
  );

  const renderMetadata = (image: BackgroundRemovalGeneratedImage) => (
    <>
      <p className="text-sm">
        <strong>Status:</strong> {image.status}
      </p>
      <p className="text-sm">
        <strong>Created At:</strong> {image.createdAt.toLocaleString()}
      </p>
    </>
  );

  if (currentUser.isLoading) {
    return (
      <div className="container mx-auto px-4 py-12 min-h-screen">
        <div className="flex flex-col items-center justify-center space-y-4">
          <Skeleton className="w-32 h-32 rounded-full" />
          <Skeleton className="w-48 h-6" />
          <Skeleton className="w-64 h-4" />
        </div>
      </div>
    );
  }

  if (!currentUser.data) {
    return (
      <div className="text-center">
        <p>Please log in to view your images.</p>
      </div>
    );
  }

  if (store.isLoading && displayItems.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-16">
        <Loader2 className="w-10 h-10 text-blue-500 animate-spin" />
        <p className="mt-4 text-xl font-semibold text-gray-700 dark:text-gray-300">
          Removing Background...
        </p>
      </div>
    );
  }

  if (!store.isLoading && displayItems.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-950">
        <BackgroundRemovalGuide />
      </div>
    );
  }

  return (
    <div className="h-full w-full">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
        {displayItems.map((item) => (
          <div key={item.id} className="relative">
            {renderItem(item)}
          </div>
        ))}
      </div>

      <ImageDetailsDialog
        open={open}
        onOpenChange={setOpen}
        selectedImage={selectedImage}
        showComparison={showComparison}
        onComparisonToggle={() => setShowComparison(!showComparison)}
        isDownloading={isDownloading}
        onDownload={handleDownload}
        renderMetadata={renderMetadata}
        imageType="background_removals"
      />
    </div>
  );
};

export default BackgroundRemovalImageDisplay;
