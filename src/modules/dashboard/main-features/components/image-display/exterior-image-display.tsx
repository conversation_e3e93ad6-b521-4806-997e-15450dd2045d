"use client";
import React, { useState, useMemo, useEffect } from "react";
import { useChannel } from "ably/react";
import {
  PredictionStatus,
  ExteriorPlaceholder,
  ExteriorGeneratedImage,
} from "@/modules/dashboard/main-features/types";
import useExteriorFormStore from "@/modules/dashboard/main-features/store/exterior-form-store";
import { useCurrentUser } from "@/modules/auth/hooks/use-current-user";
import { Skeleton } from "@/modules/ui/skeleton";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/modules/ui/dialog";
import { Button } from "@/modules/ui/button";
import { Loader2, X } from "lucide-react";
import Image from "next/image";
import { upscaleImage } from "@/modules/dashboard/main-features/actions/upscale-image";
import { Download, ZoomIn } from "lucide-react";
import { useToast } from "@/modules/ui/use-toast";
import useUpscaleFormStore from "@/modules/dashboard/main-features/store/upscale-form-store";
import { Compare } from "@/modules/ui/compare";
import { Switch } from "@/modules/ui/switch";
import { Label } from "@/modules/ui/label";
import ExteriorGuide from "../guides/exterior-guide";
import { ScrollArea } from "@/modules/ui/scroll-area";
import ImageDetailsDialog from "./image-details-dialog";
import { LoadingSpinner } from "@/modules/ui/loading-states";

const ExteriorImageDisplay: React.FC = () => {
  const currentUser = useCurrentUser();
  const store = useExteriorFormStore();
  const [open, setOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState<
    ExteriorPlaceholder | ExteriorGeneratedImage | null
  >(null);
  const [isUpscaling, setIsUpscaling] = useState<Record<string, boolean>>({});
  const [isDownloading, setIsDownloading] = useState<Record<string, boolean>>(
    {}
  );
  const upscaleStore = useUpscaleFormStore();
  const { toast } = useToast();
  const channelName = useMemo(() => {
    return currentUser.data?.id
      ? `image-generation-exterior_design-${currentUser.data.id}`
      : null;
  }, [currentUser.data?.id]);

  const { channel } = useChannel(channelName || "");
  const [showComparison, setShowComparison] = useState(true);

  useEffect(() => {
    if (channel && channelName) {
      const handleMessage = (message: any) => {
        const {
          id,
          status,
          url,
          inputImage,
          prompt,
          style,
          building,
          excludedElements,
          createdAt,
        } = message.data;

        if (status === PredictionStatus.SUCCEEDED && url) {
          store.addGeneratedImage(
            id,
            url,
            inputImage,
            prompt,
            style,
            building,
            excludedElements,
            new Date(createdAt)
          );
          store.removeImagePlaceholder(id);
        } else if (
          status === PredictionStatus.FAILED ||
          status === PredictionStatus.CANCELED
        ) {
          store.removeImagePlaceholder(id);
        } else {
          store.updateImageStatus(id, status as PredictionStatus);
        }

        if (
          [
            PredictionStatus.SUCCEEDED,
            PredictionStatus.FAILED,
            PredictionStatus.CANCELED,
          ].includes(status as PredictionStatus)
        ) {
          store.setIsLoading(false);
        }
      };

      channel.subscribe(handleMessage);

      return () => {
        channel.unsubscribe(handleMessage);
      };
    }
  }, [channel, channelName, store]);

  const displayItems = useMemo(() => {
    console.log("Placeholders:", store.imagePlaceholders);
    console.log("Generated Images:", store.generatedImages);
    const allItems = [...store.imagePlaceholders, ...store.generatedImages];
    return allItems.sort(
      (a, b) => b.createdAt.getTime() - a.createdAt.getTime()
    );
  }, [store.imagePlaceholders, store.generatedImages]);

  const renderOverlayContent = (
    item: ExteriorPlaceholder | ExteriorGeneratedImage
  ) => (
    <>
      {item.style && (
        <p>
          <strong>Style:</strong> {item.style}
        </p>
      )}
      {item.building && (
        <p>
          <strong>Building:</strong> {item.building}
        </p>
      )}
      <p>
        <strong>Prompt:</strong> {item.prompt}
      </p>
      {item.excludedElements && (
        <p>
          <strong>Excluded:</strong> {item.excludedElements}
        </p>
      )}
      <p>
        <strong>Created At:</strong> {item.createdAt.toLocaleString()}
      </p>
    </>
  );

  const handleImageClick = (
    item: ExteriorPlaceholder | ExteriorGeneratedImage
  ) => {
    setSelectedImage(item);
    setOpen(true);
  };

  const handleUpscale = async (image: ExteriorGeneratedImage) => {
    try {
      setIsUpscaling({ ...isUpscaling, [image.id]: true });
      upscaleStore.addUpscalePlaceholder(image.url);

      const result = await upscaleImage({
        userId: currentUser.data?.id || "",
        image: image.url,
        upscaleAmount: 2,
        creativity: 50,
        resemblance: 0.7,
      });

      if (result.success) {
        toast({
          title: "Image upscaling started",
          description: "Check the Upscale tab to see the result.",
        });
      } else {
        throw new Error(result.error || "Failed to start upscaling");
      }
    } catch (error) {
      console.error("Error upscaling image:", error);
      toast({
        title: "Error",
        description: "Failed to start upscaling. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUpscaling({ ...isUpscaling, [image.id]: false });
    }
  };

  const handleDownload = async (imageUrl: string, imageId: string) => {
    try {
      setIsDownloading({ ...isDownloading, [imageId]: true });
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = "exterior_image.png";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Error downloading image:", error);
      toast({
        title: "Error",
        description: "Failed to download the image. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDownloading({ ...isDownloading, [imageId]: false });
    }
  };

  const renderItem = (item: ExteriorPlaceholder | ExteriorGeneratedImage) => (
    <div
      className="relative w-full pb-[56.25%] cursor-pointer overflow-hidden rounded-lg shadow-md group"
      onClick={() => handleImageClick(item)}
    >
      {"url" in item ? (
        <>
          <Image
            src={item.url}
            alt={`Generated image ${item.id}`}
            layout="fill"
            objectFit="cover"
            className="absolute top-0 left-0 w-full h-full"
          />
          <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-4">
            <Button
              size="sm"
              variant="secondary"
              onClick={(e) => {
                e.stopPropagation();
                handleUpscale(item);
              }}
              disabled={isUpscaling[item.id]}
            >
              {isUpscaling[item.id] ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <ZoomIn className="w-4 h-4 mr-2" />
              )}
              Upscale
            </Button>
            <Button
              size="sm"
              variant="secondary"
              onClick={(e) => {
                e.stopPropagation();
                handleDownload(item.url, item.id);
              }}
              disabled={isDownloading[item.id]}
            >
              {isDownloading[item.id] ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Download className="w-4 h-4 mr-2" />
              )}
              Download
            </Button>
          </div>
        </>
      ) : (
        <>
          {item.inputImage && (
            <Image
              src={item.inputImage}
              alt="Input image"
              layout="fill"
              objectFit="cover"
              className="filter blur-sm"
            />
          )}
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div
              className={`text-white text-lg font-semibold ${
                item.status === PredictionStatus.PROCESSING
                  ? "animate-pulse"
                  : ""
              }`}
            >
              {item.status}
            </div>
          </div>
        </>
      )}
    </div>
  );

  const renderMetadata = (image: ExteriorGeneratedImage) => (
    <>
      {image.style && (
        <p>
          <strong>Style:</strong> {image.style}
        </p>
      )}
      {image.building && (
        <p>
          <strong>Building:</strong> {image.building}
        </p>
      )}
      <p>
        <strong>Prompt:</strong> {image.prompt}
      </p>
      {image.excludedElements && (
        <p>
          <strong>Excluded:</strong> {image.excludedElements}
        </p>
      )}
      <p>
        <strong>Created At:</strong> {image.createdAt.toLocaleString()}
      </p>
    </>
  );

  if (currentUser.isLoading) {
    return (
      <div className="container mx-auto px-4 py-12 min-h-screen">
        <div className="flex flex-col items-center justify-center space-y-4">
          <Skeleton className="w-32 h-32 rounded-full" />
          <Skeleton className="w-48 h-6" />
          <Skeleton className="w-64 h-4" />
        </div>
      </div>
    );
  }

  if (!currentUser.data) {
    return (
      <div className="text-center">
        <p>Please log in to view your images.</p>
      </div>
    );
  }

  if (store.isLoading && displayItems.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center">
        <LoadingSpinner />
        <p className="mt-4 text-xl font-semibold text-gray-700 dark:text-gray-300">
          Rendering Exterior...{" "}
        </p>
      </div>
    );
  }

  if (!store.isLoading && displayItems.length === 0) {
    return <ExteriorGuide />;
  }

  return (
    <div className="h-full w-full">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
        {displayItems.map((item) => (
          <div key={item.id} className="relative">
            {renderItem(item)}
          </div>
        ))}
      </div>

      <ImageDetailsDialog
        open={open}
        onOpenChange={setOpen}
        selectedImage={selectedImage}
        showComparison={showComparison}
        onComparisonToggle={() => setShowComparison(!showComparison)}
        isUpscaling={isUpscaling}
        isDownloading={isDownloading}
        onUpscale={handleUpscale}
        onDownload={handleDownload}
        renderMetadata={renderMetadata}
        imageType="exterior_designs"
      />
    </div>
  );
};

export default ExteriorImageDisplay;
