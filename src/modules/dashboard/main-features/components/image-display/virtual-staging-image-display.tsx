"use client";

import React, { useState, useMemo, useEffect } from "react";
import { useChannel } from "ably/react";
import {
  PredictionStatus,
  VirtualStagingPlaceholder,
  VirtualStagingGeneratedImage,
} from "@/modules/dashboard/main-features/types";
import useVirtualStagingFormStore from "@/modules/dashboard/main-features/store/virtual-staging-form-store";
import { useCurrentUser } from "@/modules/auth/hooks/use-current-user";
import { Button } from "@/modules/ui/button";
import { Loader2, Download, ZoomIn, ChevronDown, Copy } from "lucide-react";
import Image from "next/image";
import { useToast } from "@/modules/ui/use-toast";
import { upscaleImage } from "@/modules/dashboard/main-features/actions/upscale-image";
import useUpscaleFormStore from "@/modules/dashboard/main-features/store/upscale-form-store";
import VirtualStagingGuide from "../guides/virtual-staging-guide";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/modules/ui/collapsible";
import { ScrollArea } from "@/modules/ui/scroll-area";
import ImageDetailsDialog from "./image-details-dialog";
import { LoadingSpinner, Spinner } from "@/modules/ui/loading-states";

// Constants
const GENERATING_MESSAGES = [
  { text: "Analyzing room layout and dimensions...", duration: 8000 },
  { text: "Detecting furniture placement zones...", duration: 8000 },
  { text: "Applying virtual staging elements...", duration: 8000 },
  { text: "Enhancing lighting and shadows...", duration: 8000 },
  { text: "Adjusting material textures...", duration: 8000 },
  { text: "Fine-tuning details and perspective...", duration: 8000 },
] as const;

// Types
type ImageItem = VirtualStagingPlaceholder | VirtualStagingGeneratedImage;
type ActionStateMap = Record<string, boolean>;

// Helper Components
interface LoadingMessageProps {
  createdAt: Date;
}

const LoadingMessage: React.FC<LoadingMessageProps> = ({ createdAt }) => {
  const [messageIndex, setMessageIndex] = useState(0);
  const [elapsedTime, setElapsedTime] = useState(0);

  useEffect(() => {
    const startTime = createdAt.getTime();
    const updateElapsedTime = () => {
      const currentTime = new Date().getTime();
      setElapsedTime(Math.floor((currentTime - startTime) / 1000));
    };

    const elapsedInterval = setInterval(updateElapsedTime, 1000);
    return () => clearInterval(elapsedInterval);
  }, [createdAt]);

  useEffect(() => {
    const currentDuration = GENERATING_MESSAGES[messageIndex].duration;
    const interval = setInterval(() => {
      setMessageIndex((prev) => (prev + 1) % GENERATING_MESSAGES.length);
    }, currentDuration);
    return () => clearInterval(interval);
  }, [messageIndex]);

  const getProgressMessage = () => {
    if (elapsedTime < 45) return "Processing your image...";
    if (elapsedTime < 90) return "Almost there...";
    return "Finalizing the details...";
  };

  return (
    <>
      <LoadingSpinner />
      <div className="text-white text-lg font-semibold text-center px-4">
        {GENERATING_MESSAGES[messageIndex].text}
      </div>
      <div className="text-white text-sm mt-2 opacity-75">
        {getProgressMessage()}
      </div>
      <div className="text-white text-xs mt-1">
        Time elapsed: {elapsedTime}s
      </div>
    </>
  );
};

interface InitialLoadingStateProps {
  message?: string;
}

const InitialLoadingState: React.FC<InitialLoadingStateProps> = ({ message = "Processing Your Request" }) => (
  <div className="flex flex-col items-center justify-center h-full py-8">
    <LoadingSpinner />
    <p className="mt-4 text-xl font-semibold text-gray-700 dark:text-gray-300">
      {message}
    </p>
    <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
      Preparing to generate your image...
    </p>
  </div>
);

interface ImageMetadataProps {
  image: VirtualStagingGeneratedImage;
  toast: ReturnType<typeof useToast>["toast"];
}

const ImageMetadata: React.FC<ImageMetadataProps> = ({ image, toast }) => (
  <div className="space-y-4">
    <Collapsible>
      <div className="flex items-center space-x-2">
        <CollapsibleTrigger className="flex items-center">
          <strong>Prompt</strong>
          <ChevronDown className="h-4 w-4 ml-1" />
        </CollapsibleTrigger>
        <span className="truncate text-sm text-muted-foreground">
          {image.prompt.length > 60
            ? `${image.prompt.substring(0, 60)}...`
            : image.prompt}
        </span>
        <Button
          variant="ghost"
          size="sm"
          className="ml-auto"
          onClick={(e) => {
            e.stopPropagation();
            navigator.clipboard.writeText(image.prompt);
            toast({
              title: "Copied",
              description: "Prompt copied to clipboard",
            });
          }}
        >
          <Copy className="h-4 w-4" />
        </Button>
      </div>
      <CollapsibleContent className="mt-2">
        <ScrollArea className="h-[100px] w-full p-4">
          <div className="text-sm">{image.prompt}</div>
        </ScrollArea>
      </CollapsibleContent>
    </Collapsible>
    {image.isUpscaled && (
      <div className="mt-2 flex items-center">
        <span className="bg-primary/10 text-primary px-2 py-1 rounded-md text-xs font-medium">
          This is an upscaled version
        </span>
      </div>
    )}
    <div className="mt-2">
      <strong>Created At:</strong> {image.createdAt.toLocaleString()}
    </div>
  </div>
);

interface ImageItemProps {
  item: ImageItem;
  isUpscaling: ActionStateMap;
  isDownloading: ActionStateMap;
  onImageClick: (item: ImageItem) => void;
  onUpscale: (image: VirtualStagingGeneratedImage) => void;
  onDownload: (imageUrl: string, imageId: string) => void;
}

const ImageItemComponent: React.FC<ImageItemProps> = ({
  item,
  isUpscaling,
  isDownloading,
  onImageClick,
  onUpscale,
  onDownload,
}) => {
  const isSucceeded = item.status === PredictionStatus.SUCCEEDED && "outputImage" in item;
  const isProcessing = item.status === PredictionStatus.PROCESSING;
  const isProcessingOutput = item.status === PredictionStatus.PROCESSING_OUTPUT && "rawOutputUrl" in item;
  const isUpscaled = "isUpscaled" in item && item.isUpscaled;

  return (
    <div
      className="relative w-full pb-[56.25%] cursor-pointer overflow-hidden rounded-lg shadow-md group"
      onClick={() => onImageClick(item)}
    >
      {isSucceeded ? (
        <>
          <Image
            src={("displayUrl" in item && item.displayUrl) || (item.outputImage || "")}
            alt={`Generated image ${item.id}`}
            layout="fill"
            objectFit="cover"
            className="absolute top-0 left-0 w-full h-full"
          />
          {isUpscaled && (
            <div className="absolute top-2 right-2 bg-primary text-primary-foreground text-xs font-medium px-2 py-1 rounded-md z-10">
              Upscaled
            </div>
          )}
          <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-4">
            <Button
              size="sm"
              variant="secondary"
              onClick={(e) => {
                e.stopPropagation();
                if ("outputImage" in item) {
                  onUpscale(item as VirtualStagingGeneratedImage);
                }
              }}
              disabled={isUpscaling[item.id] || isUpscaled}
            >
              {isUpscaling[item.id] ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <ZoomIn className="w-4 h-4 mr-2" />
              )}
              {isUpscaled ? "Already Upscaled" : "Upscale"}
            </Button>
            <Button
              size="sm"
              variant="secondary"
              onClick={(e) => {
                e.stopPropagation();
                if ("downloadUrl" in item) {
                  onDownload(item.downloadUrl || "", item.id);
                }
              }}
              disabled={isDownloading[item.id]}
            >
              {isDownloading[item.id] ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Download className="w-4 h-4 mr-2" />
              )}
              Download
            </Button>
          </div>
        </>
      ) : isProcessingOutput ? (
        <>
          {/* Show raw output image with processing overlay */}
          <Image
            src={item.rawOutputUrl || ""}
            alt="Processing output"
            layout="fill"
            objectFit="cover"
            className="absolute top-0 left-0 w-full h-full"
          />
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-black bg-opacity-30">
            <Spinner size="sm" />
            <div className="text-white text-lg font-semibold text-center px-4 mt-3">
              Finalizing your image...
            </div>
            <div className="text-white text-xs mt-1 opacity-75">
              Optimizing for quality and performance
            </div>
          </div>
        </>
      ) : (
        <>
          {item.inputImage && (
            <Image
              src={item.inputImage}
              alt="Input image"
              layout="fill"
              objectFit="cover"
              className="filter blur-sm"
            />
          )}
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-black bg-opacity-50">
            {isProcessing ? (
              <LoadingMessage createdAt={item.createdAt} />
            ) : (
              <>
                <LoadingSpinner />
                <div className="text-white text-lg font-semibold text-center px-4">
                  {item.status}
                </div>
              </>
            )}
          </div>
        </>
      )}
    </div>
  );
};

// Custom hooks
const useAblyChannel = (userId: string | undefined) => {
  const channelName = useMemo(() => {
    return userId ? `image-generation-virtual_staging-${userId}` : null;
  }, [userId]);

  const { channel } = useChannel(channelName || "");
  
  return { channel, channelName };
};

const VirtualStagingImageDisplay: React.FC = () => {
  const currentUser = useCurrentUser();
  const store = useVirtualStagingFormStore();
  const upscaleStore = useUpscaleFormStore();
  const { toast } = useToast();
  
  // State
  const [dialogState, setDialogState] = useState({
    open: false,
    showComparison: true,
    selectedImage: null as ImageItem | null,
  });
  const [actionState, setActionState] = useState({
    isDownloading: {} as ActionStateMap,
    isUpscaling: {} as ActionStateMap,
  });
  
  // Ably channel setup
  const { channel, channelName } = useAblyChannel(currentUser.data?.id);
  
  // Memoized derived data
  const displayItems = useMemo(() => {
    const allItems = [...store.imagePlaceholders, ...store.generatedImages];
    return allItems.sort(
      (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
  }, [store.imagePlaceholders, store.generatedImages]);
  
  // Event handlers
  const handleImageClick = (item: ImageItem) => {
    setDialogState(prev => ({
      ...prev,
      selectedImage: item,
      open: true,
    }));
  };
  
  const handleDialogOpenChange = (open: boolean) => {
    setDialogState(prev => ({ ...prev, open }));
  };
  
  const handleComparisonToggle = () => {
    setDialogState(prev => ({ 
      ...prev, 
      showComparison: !prev.showComparison 
    }));
  };

  const handleDownload = async (imageUrl: string, imageId: string) => {
    try {
      setActionState(prev => ({
        ...prev,
        isDownloading: { ...prev.isDownloading, [imageId]: true }
      }));
      
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      const filename = `virtual_staging_${imageId}.png`;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Error downloading image:", error);
      toast({
        title: "Error",
        description: "Failed to download the image. Please try again.",
        variant: "destructive",
      });
    } finally {
      setActionState(prev => ({
        ...prev,
        isDownloading: { ...prev.isDownloading, [imageId]: false }
      }));
    }
  };

  const handleUpscale = async (image: VirtualStagingGeneratedImage) => {
    if (image.isUpscaled) return;
    
    try {
      setActionState(prev => ({
        ...prev,
        isUpscaling: { ...prev.isUpscaling, [image.id]: true }
      }));
      
      upscaleStore.addUpscalePlaceholder(image.outputImage);

      const result = await upscaleImage({
        userId: currentUser.data?.id || "",
        image: image.outputImage,
        upscaleAmount: 2,
        creativity: 50,
        resemblance: 0.7,
        sourceType: "virtual_staging",
        originalImageId: image.id,
      });

      if (result.success) {
        toast({
          title: "Image upscaling started",
          description: "Your image is being upscaled. It will appear here when ready.",
        });
      } else {
        throw new Error(result.error || "Failed to start upscaling");
      }
    } catch (error) {
      console.error("Error upscaling image:", error);
      toast({
        title: "Error",
        description: "Failed to start upscaling. Please try again.",
        variant: "destructive",
      });
    } finally {
      setActionState(prev => ({
        ...prev,
        isUpscaling: { ...prev.isUpscaling, [image.id]: false }
      }));
    }
  };
  
  // Handle prediction updates from Ably
  useEffect(() => {
    if (!channel || !channelName) return;
    
    const handlePredictionUpdate = (message: any) => {
      const {
        id,
        status,
        displayUrl,
        downloadUrl,
        rawOutputUrl,
        inputImage,
        prompt,
        style,
        room,
        excludedElements,
        createdAt,
        messageType,
      } = message.data;

      // Handle regular virtual staging updates
      if (!messageType || messageType !== "upscale") {
        if (status === PredictionStatus.SUCCEEDED && displayUrl && downloadUrl) {
          const newGeneratedImage: VirtualStagingGeneratedImage = {
            id,
            displayUrl,
            downloadUrl,
            inputImage,
            outputImage: displayUrl,
            prompt,
            style,
            room,
            excludedElements,
            createdAt: new Date(createdAt),
            status: PredictionStatus.SUCCEEDED,
            url: displayUrl,
          };
          store.addGeneratedImage(newGeneratedImage);
          store.removeImagePlaceholder(id);
        } else if (status === PredictionStatus.PROCESSING_OUTPUT && rawOutputUrl) {
          // Update placeholder with raw output image
          store.updateImagePlaceholder(id, {
            status: PredictionStatus.PROCESSING_OUTPUT,
            rawOutputUrl,
          });
        } else if (
          [PredictionStatus.FAILED, PredictionStatus.CANCELED].includes(status as PredictionStatus)
        ) {
          store.removeImagePlaceholder(id);
        } else {
          store.updateImageStatus(id, status as PredictionStatus);
        }

        if (
          [PredictionStatus.SUCCEEDED, PredictionStatus.FAILED, PredictionStatus.CANCELED].includes(
            status as PredictionStatus
          )
        ) {
          store.setIsLoading(false);
        }
      }
    };

    channel.subscribe("prediction_update", handlePredictionUpdate);
    return () => channel.unsubscribe("prediction_update", handlePredictionUpdate);
  }, [channel, channelName, store]);

  // Handle upscale updates from Ably
  useEffect(() => {
    if (!channel || !channelName) return;
    
    const handleUpscaleUpdate = (message: any) => {
      const { 
        id,
        status,
        displayUrl, 
        downloadUrl,
        inputImage,
        messageType,
        originalImageId,
      } = message.data;

      // Only process upscale messages
      if (messageType !== "upscale") return;
      
      // We only care about completed upscale jobs
      if (status === PredictionStatus.SUCCEEDED && displayUrl && downloadUrl) {
        // Find the original image that was upscaled
        let originalImage = originalImageId 
          ? store.generatedImages.find(img => img.id === originalImageId)
          : store.generatedImages.find(img => img.displayUrl === inputImage);
          
        if (originalImage) {
          // Create a new upscaled version of the image
          const upscaledImage: VirtualStagingGeneratedImage = {
            id,
            displayUrl,
            downloadUrl,
            inputImage: originalImage.displayUrl || originalImage.outputImage,
            outputImage: displayUrl,
            url: displayUrl,
            prompt: originalImage.prompt,
            style: originalImage.style,
            room: originalImage.room,
            excludedElements: originalImage.excludedElements,
            createdAt: new Date(),
            status: PredictionStatus.SUCCEEDED,
            isUpscaled: true,
          };
          
          store.addGeneratedImage(upscaledImage);
          
          toast({
            title: "Upscale complete",
            description: "Your upscaled image is now available",
          });
        }
      }
    };

    channel.subscribe("prediction_update", handleUpscaleUpdate);
    return () => channel.unsubscribe("prediction_update", handleUpscaleUpdate);
  }, [channel, channelName, store, toast]);

  // Render methods
  const renderMetadata = (image: VirtualStagingGeneratedImage) => (
    <ImageMetadata image={image} toast={toast} />
  );

  // Conditional rendering based on state
  if (store.isLoading && displayItems.length === 0) {
    return <InitialLoadingState />;
  }

  if (!store.isLoading && displayItems.length === 0) {
    return <VirtualStagingGuide />;
  }

  return (
    <div className="h-full w-full">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 2xl:grid-cols-3 gap-4">
        {displayItems.map((item) => (
          <div key={item.id} className="relative">
            <ImageItemComponent
              item={item}
              isUpscaling={actionState.isUpscaling}
              isDownloading={actionState.isDownloading}
              onImageClick={handleImageClick}
              onUpscale={handleUpscale}
              onDownload={handleDownload}
            />
          </div>
        ))}
      </div>

      <ImageDetailsDialog
        open={dialogState.open}
        onOpenChange={handleDialogOpenChange}
        selectedImage={dialogState.selectedImage}
        showComparison={dialogState.showComparison}
        onComparisonToggle={handleComparisonToggle}
        isUpscaling={actionState.isUpscaling}
        isDownloading={actionState.isDownloading}
        onUpscale={(image) => {
          if ("isUpscaled" in image && image.isUpscaled) return;
          handleUpscale(image as VirtualStagingGeneratedImage);
        }}
        onDownload={handleDownload}
        renderMetadata={renderMetadata}
        imageType="virtual_stagings"
      />
    </div>
  );
};

export default VirtualStagingImageDisplay;
