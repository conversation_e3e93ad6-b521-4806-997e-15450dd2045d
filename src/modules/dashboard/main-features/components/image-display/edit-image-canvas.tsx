"use client";

import React, { useRef, useState, useEffect, useMemo } from "react";
import { Stage, Layer, Image as KonvaImage, Line, Circle } from "react-konva";
import { Button } from "@/modules/ui/button";
import { Slider } from "@/modules/ui/slider";
import {
  Undo2,
  Redo2,
  Save,
  Move,
  ZoomIn,
  ZoomOut,
  Eraser,
  Loader2,
  RotateCcw,
  History,
  ArrowLeftToLine,
  Copy,
  Download,
} from "lucide-react";
import { handleImageUpload } from "@/modules/dashboard/main-features/actions/upload-image";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/modules/ui/tooltip";
import { cn } from "@/modules/ui";
import { getImageDimensions } from "@/modules/utils/image-utils";
import Konva from "konva";
import useEditImageFormStore from "@/modules/dashboard/main-features/store/edit-image-form-store";
import { useMediaQuery } from "@/modules/hooks/use-media-query";

interface EditImageCanvasProps {
  inputImageUrl: string;
  isGenerating?: boolean;
  generatedImageUrl?: string;
}

interface Line {
  tool: "brush" | "eraser";
  points: number[];
  strokeWidth: number;
}

interface ToolbarButtonProps {
  onClick: () => void;
  icon: React.ReactNode;
  label: string;
  active?: boolean;
  disabled?: boolean;
}

const BRUSH_COLOR = "rgba(183, 235, 52, 0.6)"; // Lime green with opacity

const ToolbarButton: React.FC<ToolbarButtonProps> = ({
  onClick,
  icon,
  label,
  active,
  disabled,
}) => (
  <TooltipProvider>
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant={active ? "default" : "outline"}
          size="sm"
          onClick={onClick}
          disabled={disabled}
          className={cn(
            "w-9 h-9",
            active && "bg-purple-500 hover:bg-purple-600"
          )}
        >
          {icon}
        </Button>
      </TooltipTrigger>
      <TooltipContent>
        <p>{label}</p>
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
);

const EditImageCanvas: React.FC<EditImageCanvasProps> = ({
  inputImageUrl,
  isGenerating = false,
  generatedImageUrl,
}) => {
  const store = useEditImageFormStore();
  const stageRef = useRef<Konva.Stage | null>(null);
  const [image, setImage] = useState<HTMLImageElement | null>(null);
  const [generatedImage, setGeneratedImage] = useState<HTMLImageElement | null>(
    null
  );
  const [brushSize, setBrushSize] = useState<number>(50);
  const [lines, setLines] = useState<Line[]>([]);
  const [history, setHistory] = useState<Line[][]>([[]]);
  const [historyStep, setHistoryStep] = useState(0);
  const [isDrawing, setIsDrawing] = useState(false);
  const [mode, setMode] = useState<"draw" | "pan">("draw");
  const [stageScale, setStageScale] = useState(1);
  const [stagePosition, setStagePosition] = useState({ x: 0, y: 0 });
  const [cursorPosition, setCursorPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Add mobile detection
  const isMobile = useMediaQuery("(max-width: 768px)");
  const isTablet = useMediaQuery("(min-width: 769px) and (max-width: 1024px)");

  // Add touch handling state
  const [lastTouch, setLastTouch] = useState<{ x: number; y: number } | null>(
    null
  );

  // Add new state for container and image dimensions
  const [containerDimensions, setContainerDimensions] = useState({
    width: 0,
    height: 0,
  });

  const [originalDimensions, setOriginalDimensions] = useState({
    width: 0,
    height: 0,
  });

  // Add this function to clear all lines
  const clearLines = () => {
    setLines([]);
    setHistory([[]]);
    setHistoryStep(0);
    store.setLines([]); // Update store as well
  };

  // Update store lines when local lines change
  useEffect(() => {
    store.setLines(lines);
  }, [lines]);

  // Update container dimensions calculation
  useEffect(() => {
    const updateDimensions = () => {
      const maxWidth = window.innerWidth * 0.8; // 80% of viewport width
      const maxHeight = window.innerHeight * 0.6; // 60% of viewport height
      setContainerDimensions({ width: maxWidth, height: maxHeight });
    };

    updateDimensions();
    window.addEventListener("resize", updateDimensions);
    return () => window.removeEventListener("resize", updateDimensions);
  }, []);

  // Update image loading and dimension calculation
  useEffect(() => {
    const img = new Image();
    img.src = inputImageUrl;
    img.onload = () => {
      setImage(img);
      setOriginalDimensions({
        width: img.naturalWidth,
        height: img.naturalHeight,
      });

      // Calculate dimensions to fit in viewport while maintaining aspect ratio
      const containerWidth =
        containerRef.current?.clientWidth || window.innerWidth;
      const containerHeight =
        containerRef.current?.clientHeight || window.innerHeight;

      const aspectRatio = img.width / img.height;
      let displayWidth = img.width;
      let displayHeight = img.height;

      if (displayWidth > containerWidth - 40) {
        // Subtract padding
        displayWidth = containerWidth - 40;
        displayHeight = displayWidth / aspectRatio;
      }

      if (displayHeight > containerHeight - 40) {
        // Subtract padding
        displayHeight = containerHeight - 40;
        displayWidth = displayHeight * aspectRatio;
      }

      store.setDimensions(displayWidth, displayHeight);
      store.setOriginalDimensions(img.naturalWidth, img.naturalHeight);

      // Center the stage
      const x = (containerWidth - displayWidth) / 2;
      const y = (containerHeight - displayHeight) / 2;
      setStagePosition({ x, y });
    };
  }, [inputImageUrl]);

  // Update the useEffect that handles generatedImageUrl
  useEffect(() => {
    if (generatedImageUrl) {
      const img = new Image();
      img.src = generatedImageUrl;
      img.onload = () => {
        setGeneratedImage(img);
        // Clear all lines when the generated image is loaded
        clearLines();
        // Keep the same dimensions as the original image
        store.setDimensions(store.dimensions.width, store.dimensions.height);
      };
    } else {
      setGeneratedImage(null);
    }
  }, [generatedImageUrl]);

  // Get relative pointer position considering stage scale and position
  const getRelativePointerPosition = (stage: Konva.Stage) => {
    const pointerPosition = stage.getPointerPosition();
    if (!pointerPosition) return null;

    return {
      x: (pointerPosition.x - stage.x()) / stage.scaleX(),
      y: (pointerPosition.y - stage.y()) / stage.scaleY(),
    };
  };

  const handleMouseDown = (e: Konva.KonvaEventObject<MouseEvent>) => {
    if (mode !== "draw") return;

    const stage = e.target.getStage();
    if (!stage) return;

    const pos = getRelativePointerPosition(stage);
    if (!pos) return;

    setIsDrawing(true);
    const newLine: Line = {
      tool: "brush",
      points: [pos.x, pos.y],
      strokeWidth: brushSize / stageScale,
    };

    const newLines = [...lines, newLine];
    setLines(newLines);
    const newHistory = history.slice(0, historyStep + 1);
    newHistory.push(newLines);
    setHistory(newHistory);
    setHistoryStep(historyStep + 1);
  };

  const handleMouseMove = (e: Konva.KonvaEventObject<MouseEvent>) => {
    const stage = e.target.getStage();
    if (!stage) return;

    const pos = getRelativePointerPosition(stage);
    if (!pos) return;

    setCursorPosition(pos);

    if (!isDrawing || mode !== "draw") return;

    const lastLine = lines[lines.length - 1];
    if (!lastLine) return;

    lastLine.points = lastLine.points.concat([pos.x, pos.y]);
    const newLines = [...lines.slice(0, -1), lastLine];
    setLines(newLines);
  };

  const handleMouseUp = () => {
    setIsDrawing(false);
  };

  const handleUndo = () => {
    if (historyStep > 0) {
      setHistoryStep(historyStep - 1);
      setLines(history[historyStep - 1]);
    }
  };

  const handleRedo = () => {
    if (historyStep < history.length - 1) {
      setHistoryStep(historyStep + 1);
      setLines(history[historyStep + 1]);
    }
  };

  const createMaskCanvas = () => {
    const tempCanvas = document.createElement("canvas");
    tempCanvas.width = store.originalDimensions.width;
    tempCanvas.height = store.originalDimensions.height;
    const tempCtx = tempCanvas.getContext("2d");

    if (tempCtx) {
      tempCtx.fillStyle = "black";
      tempCtx.fillRect(
        0,
        0,
        store.originalDimensions.width,
        store.originalDimensions.height
      );

      const scaleX = store.originalDimensions.width / store.dimensions.width;
      const scaleY = store.originalDimensions.height / store.dimensions.height;

      lines.forEach((line) => {
        tempCtx.beginPath();
        tempCtx.strokeStyle = "white";
        tempCtx.lineWidth = line.strokeWidth * scaleX;
        tempCtx.lineCap = "round";
        tempCtx.lineJoin = "round";

        for (let i = 0; i < line.points.length; i += 2) {
          const x = line.points[i] * scaleX;
          const y = line.points[i + 1] * scaleY;

          if (i === 0) {
            tempCtx.moveTo(x, y);
          } else {
            tempCtx.lineTo(x, y);
          }
        }

        tempCtx.stroke();
      });

      // Invert the colors
      tempCtx.globalCompositeOperation = "difference";
      tempCtx.fillStyle = "white";
      tempCtx.fillRect(
        0,
        0,
        store.originalDimensions.width,
        store.originalDimensions.height
      );
    }

    return tempCanvas;
  };

  const handleSaveMask = async () => {
    try {
      const tempCanvas = createMaskCanvas();
      const dataURL = tempCanvas.toDataURL();
      const response = await fetch(dataURL);
      const blob = await response.blob();
      const file = new File([blob], "mask.png", { type: "image/png" });

      const formData = new FormData();
      formData.append("file", file);

      const result = await handleImageUpload(formData);
      if ("error" in result) {
        throw new Error("Failed to upload mask");
      }

      store.setMaskUrl(result.displayUrl);
    } catch (error) {
      console.error("Error saving mask:", error);
    }
  };

  const handleWheel = (e: Konva.KonvaEventObject<WheelEvent>) => {
    e.evt.preventDefault();
    const stage = e.target.getStage();
    if (!stage) return;

    const oldScale = stage.scaleX();
    const pointer = stage.getPointerPosition();
    if (!pointer) return;

    const mousePointTo = {
      x: (pointer.x - stage.x()) / oldScale,
      y: (pointer.y - stage.y()) / oldScale,
    };

    const newScale = e.evt.deltaY < 0 ? oldScale * 1.1 : oldScale / 1.1;

    setStageScale(newScale);
    setStagePosition({
      x: pointer.x - mousePointTo.x * newScale,
      y: pointer.y - mousePointTo.y * newScale,
    });
  };

  // Update handleStartOver to use clearLines
  const handleStartOver = () => {
    if (generatedImage) {
      // Update the input image to be the generated image
      store.setImageUrl(store.generatedImageUrl);
      setImage(generatedImage);
    }
    // Reset the generated image and lines
    setGeneratedImage(null);
    store.setGeneratedImageUrl(undefined);
    clearLines(); // Use the new clearLines function
  };

  // Add new toolbar section for image history
  const renderImageHistoryControls = () => (
    <div className="absolute left-4 bottom-4 bg-white/95 dark:bg-gray-800/95 p-2 rounded-lg shadow-lg">
      <div className="flex gap-2">
        <ToolbarButton
          onClick={() => store.goBackToOriginal()}
          icon={<ArrowLeftToLine className="h-4 w-4" />}
          label="Back to Original"
          disabled={
            !store.originalInputUrl || store.imageUrl === store.originalInputUrl
          }
        />
        <ToolbarButton
          onClick={() => store.undoImageChange()}
          icon={<History className="h-4 w-4" />}
          label="Previous Version"
          disabled={store.currentImageIndex <= 0}
        />
        <ToolbarButton
          onClick={() => store.redoImageChange()}
          icon={<RotateCcw className="h-4 w-4" />}
          label="Next Version"
          disabled={store.currentImageIndex >= store.imageHistory.length - 1}
        />
      </div>
    </div>
  );

  // Add touch event handlers
  const handleTouchStart = (e: Konva.KonvaEventObject<TouchEvent>) => {
    e.evt.preventDefault(); // Prevent scrolling while drawing
    if (mode !== "draw") return;

    const stage = e.target.getStage();
    if (!stage) return;

    const touch = e.evt.touches[0];
    const pos = getRelativeTouchPosition(stage, touch);
    if (!pos) return;

    setIsDrawing(true);
    const newLine: Line = {
      tool: "brush",
      points: [pos.x, pos.y],
      strokeWidth: brushSize / stageScale,
    };

    const newLines = [...lines, newLine];
    setLines(newLines);
    const newHistory = history.slice(0, historyStep + 1);
    newHistory.push(newLines);
    setHistory(newHistory);
    setHistoryStep(historyStep + 1);
  };

  const handleTouchMove = (e: Konva.KonvaEventObject<TouchEvent>) => {
    e.evt.preventDefault();
    const stage = e.target.getStage();
    if (!stage) return;

    const touch = e.evt.touches[0];
    const pos = getRelativeTouchPosition(stage, touch);
    if (!pos) return;

    setCursorPosition(pos);

    if (!isDrawing || mode !== "draw") return;

    const lastLine = lines[lines.length - 1];
    if (!lastLine) return;

    lastLine.points = lastLine.points.concat([pos.x, pos.y]);
    const newLines = [...lines.slice(0, -1), lastLine];
    setLines(newLines);
  };

  const handleTouchEnd = () => {
    setIsDrawing(false);
    setLastTouch(null);
  };

  const getRelativeTouchPosition = (stage: Konva.Stage, touch: Touch) => {
    const rect = stage.container().getBoundingClientRect();
    return {
      x: (touch.clientX - rect.left - stage.x()) / stage.scaleX(),
      y: (touch.clientY - rect.top - stage.y()) / stage.scaleY(),
    };
  };

  // Add new functions for copy and download
  const handleCopyImage = async () => {
    try {
      if (!stageRef.current) return;

      const dataUrl = stageRef.current.toDataURL({
        pixelRatio: window.devicePixelRatio || 2,
        mimeType: "image/png",
        quality: 1,
      });
      const blob = await (await fetch(dataUrl)).blob();

      if (navigator.clipboard && window.ClipboardItem) {
        const item = new ClipboardItem({ "image/png": blob });
        await navigator.clipboard.write([item]);
      }
    } catch (error) {
      console.error("Error copying image:", error);
    }
  };

  const handleDownloadImage = () => {
    if (!stageRef.current) return;

    const dataUrl = stageRef.current.toDataURL({
      pixelRatio: window.devicePixelRatio || 2,
      mimeType: "image/png",
      quality: 1,
    });
    const link = document.createElement("a");
    link.download = "edited-image.png";
    link.href = dataUrl;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Add mobile toolbar component
  const renderMobileToolbar = () => (
    <div className="flex flex-col gap-3 p-3 bg-muted/50 border-b">
      {/* Top row - Tools and Actions */}
      <div className="flex justify-between items-center">
        {/* Left side - Drawing tools */}
        <div className="flex gap-2">
          <Button
            size="sm"
            variant={mode === "draw" ? "default" : "outline"}
            className="h-10 w-10 p-0"
            onClick={() => setMode("draw")}
          >
            <Eraser className="h-5 w-5" />
          </Button>
          <Button
            size="sm"
            variant={mode === "pan" ? "default" : "outline"}
            className="h-10 w-10 p-0"
            onClick={() => setMode("pan")}
          >
            <Move className="h-5 w-5" />
          </Button>
        </div>

        {/* Right side - Actions */}
        <div className="flex gap-2">
          <Button
            onClick={handleCopyImage}
            size="sm"
            variant="outline"
            className="h-10 w-10 p-0"
          >
            <Copy className="h-5 w-5" />
          </Button>
          <Button
            onClick={handleDownloadImage}
            size="sm"
            variant="outline"
            className="h-10 w-10 p-0"
          >
            <Download className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* Middle row - Brush size slider */}
      <div className="flex items-center gap-2">
        <span className="text-sm font-medium min-w-[80px]">
          Brush: {brushSize}px
        </span>
        <Slider
          value={[brushSize]}
          onValueChange={(value) => setBrushSize(value[0])}
          min={10}
          max={200}
          step={1}
          className="flex-1"
        />
      </div>

      {/* Bottom row - Additional controls */}
      {generatedImage && (
        <div className="flex justify-end">
          <Button
            onClick={handleStartOver}
            size="sm"
            variant="outline"
            className="gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Edit Generated
          </Button>
        </div>
      )}
    </div>
  );

  // Add helper function for calculating image dimensions
  const calculateImageDimensions = (
    imageWidth: number,
    imageHeight: number,
    containerWidth: number,
    containerHeight: number
  ) => {
    const imageAspectRatio = imageWidth / imageHeight;
    const containerAspectRatio = containerWidth / containerHeight;

    let scale: number;
    let x: number = 0;
    let y: number = 0;

    if (imageAspectRatio > containerAspectRatio) {
      // Image is wider than container
      scale = containerWidth / imageWidth;
      y = (containerHeight - imageHeight * scale) / 2;
    } else {
      // Image is taller than container
      scale = containerHeight / imageHeight;
      x = (containerWidth - imageWidth * scale) / 2;
    }

    return { scale, x, y };
  };

  // Update Stage configuration
  const stageConfig = useMemo(
    () => ({
      width: containerRef.current?.clientWidth || window.innerWidth,
      height:
        (containerRef.current?.clientHeight || window.innerHeight) -
        (isMobile ? 140 : 0),
      scaleX: stageScale,
      scaleY: stageScale,
      x: stagePosition.x,
      y: stagePosition.y,
      draggable: mode === "pan",
      pixelRatio: window.devicePixelRatio || 2, // Use device pixel ratio for better quality
    }),
    [
      containerRef.current,
      stageScale,
      stagePosition.x,
      stagePosition.y,
      mode,
      isMobile,
    ]
  );

  // Update the main render function
  return (
    <div
      ref={containerRef}
      className="absolute inset-0 flex flex-col bg-gray-100 dark:bg-gray-900"
    >
      {isGenerating && (
        <div className="absolute inset-0 z-50 bg-black/50 flex flex-col items-center justify-center">
          <Loader2 className="w-8 h-8 text-white animate-spin" />
          <p className="text-white mt-4">Generating image...</p>
        </div>
      )}

      {/* Show mobile toolbar on mobile devices */}
      {isMobile && renderMobileToolbar()}

      <div className="relative flex-1">
        {image && store.dimensions.width > 0 && store.dimensions.height > 0 && (
          <Stage
            {...stageConfig}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
            onWheel={handleWheel}
            ref={stageRef}
          >
            <Layer>
              <KonvaImage
                image={generatedImage || image}
                width={store.dimensions.width}
                height={store.dimensions.height}
              />
            </Layer>
            <Layer>
              {lines.map((line, i) => (
                <Line
                  key={i}
                  points={line.points}
                  stroke={BRUSH_COLOR}
                  strokeWidth={line.strokeWidth}
                  tension={0.5}
                  lineCap="round"
                  lineJoin="round"
                  listening={false}
                />
              ))}
              {cursorPosition && mode === "draw" && (
                <Circle
                  x={cursorPosition.x}
                  y={cursorPosition.y}
                  radius={brushSize / (2 * stageScale)}
                  stroke={BRUSH_COLOR}
                  strokeWidth={1 / stageScale}
                  dash={[2, 2]}
                  fillEnabled={false}
                />
              )}
            </Layer>
          </Stage>
        )}

        {/* Show desktop controls only on non-mobile devices */}
        {!isMobile && (
          <>
            {/* Left toolbar */}
            <div className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/95 dark:bg-gray-800/95 p-2 rounded-lg shadow-lg">
              <div className="flex flex-col gap-2">
                <ToolbarButton
                  onClick={() => setMode("draw")}
                  icon={<Eraser className="h-4 w-4" />}
                  label="Draw"
                  active={mode === "draw"}
                />
                <ToolbarButton
                  onClick={() => setMode("pan")}
                  icon={<Move className="h-4 w-4" />}
                  label="Pan"
                  active={mode === "pan"}
                />
                <div className="h-px bg-gray-200 dark:bg-gray-700" />
                <ToolbarButton
                  onClick={handleUndo}
                  icon={<Undo2 className="h-4 w-4" />}
                  label="Undo"
                  disabled={historyStep === 0}
                />
                <ToolbarButton
                  onClick={handleRedo}
                  icon={<Redo2 className="h-4 w-4" />}
                  label="Redo"
                  disabled={historyStep === history.length - 1}
                />
                <div className="h-px bg-gray-200 dark:bg-gray-700" />
                <ToolbarButton
                  onClick={handleCopyImage}
                  icon={<Copy className="h-4 w-4" />}
                  label="Copy Image"
                />
                <ToolbarButton
                  onClick={handleDownloadImage}
                  icon={<Download className="h-4 w-4" />}
                  label="Download Image"
                />
                {generatedImage && (
                  <>
                    <div className="h-px bg-gray-200 dark:bg-gray-700" />
                    <ToolbarButton
                      onClick={handleStartOver}
                      icon={<RotateCcw className="h-4 w-4" />}
                      label="Edit Generated Image"
                    />
                  </>
                )}
              </div>
            </div>

            {/* Right brush size slider */}
            <div className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/95 dark:bg-gray-800/95 p-4 rounded-lg shadow-lg w-64">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">
                    Brush Size: {brushSize}px
                  </span>
                </div>
                <Slider
                  value={[brushSize]}
                  onValueChange={(value) => setBrushSize(value[0])}
                  min={10}
                  max={200}
                  step={1}
                  className="w-full"
                />
              </div>
            </div>

            {/* Bottom image history controls */}
            {renderImageHistoryControls()}
          </>
        )}
      </div>

      {/* Add mobile image history controls at the bottom */}
      {isMobile && (
        <div className="p-3 bg-muted/50 border-t">
          <div className="flex justify-between items-center">
            <Button
              onClick={() => store.goBackToOriginal()}
              size="sm"
              variant="outline"
              className="gap-2"
              disabled={
                !store.originalInputUrl ||
                store.imageUrl === store.originalInputUrl
              }
            >
              <ArrowLeftToLine className="h-4 w-4" />
              Original
            </Button>
            <div className="flex gap-2">
              <Button
                onClick={() => store.undoImageChange()}
                size="sm"
                variant="outline"
                disabled={store.currentImageIndex <= 0}
              >
                <History className="h-4 w-4" />
              </Button>
              <Button
                onClick={() => store.redoImageChange()}
                size="sm"
                variant="outline"
                disabled={
                  store.currentImageIndex >= store.imageHistory.length - 1
                }
              >
                <RotateCcw className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EditImageCanvas;
