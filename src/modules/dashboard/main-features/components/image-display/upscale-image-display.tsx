"use client";
import React, { useState, useMemo, useEffect } from "react";
import { useChannel } from "ably/react";
import {
  PredictionStatus,
  UpscalePlaceholder,
  UpscaleGeneratedImage,
} from "@/modules/dashboard/main-features/types";
import useUpscaleFormStore from "@/modules/dashboard/main-features/store/upscale-form-store";
import { useCurrentUser } from "@/modules/auth/hooks/use-current-user";

import { Button } from "@/modules/ui/button";
import { Loader2, Download } from "lucide-react";
import Image from "next/image";
import { useToast } from "@/modules/ui/use-toast";

import { Compare } from "@/modules/ui/compare";
import UpscaleGuide from "../guides/upscale-guide";
import ImageDetailsDialog from "./image-details-dialog";
import { getOriginalImageDimensions } from "../../../../utils/image";

const UpscaleImageDisplay: React.FC = () => {
  const currentUser = useCurrentUser();
  const store = useUpscaleFormStore();
  const [open, setOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState<
    UpscalePlaceholder | UpscaleGeneratedImage | null
  >(null);
  const { toast } = useToast();
  const [isDownloading, setIsDownloading] = useState<Record<string, boolean>>(
    {}
  );
  const [showComparison, setShowComparison] = useState(true);

  const channelName = useMemo(() => {
    return currentUser.data?.id
      ? `image-generation-upscale-${currentUser.data.id}`
      : null;
  }, [currentUser.data?.id]);

  const { channel } = useChannel(channelName || "");

  useEffect(() => {
    if (channel && channelName) {
      const handleMessage = (message: any) => {
        console.log("Received Ably message:", message);
        store.processChannelMessage(message);
      };

      channel.subscribe(handleMessage);

      return () => {
        channel.unsubscribe(handleMessage);
      };
    }
  }, [channel, channelName, store]);

  const displayItems = useMemo(() => {
    const allItems = [...store.imagePlaceholders, ...store.generatedImages];
    return allItems.sort((a, b) => {
      const aIsProcessing = a.status === PredictionStatus.PROCESSING;
      const bIsProcessing = b.status === PredictionStatus.PROCESSING;

      if (aIsProcessing && !bIsProcessing) return -1;
      if (!bIsProcessing && aIsProcessing) return 1;

      return b.createdAt.getTime() - a.createdAt.getTime();
    });
  }, [store.imagePlaceholders, store.generatedImages]);

  const renderOverlayContent = (
    item: UpscalePlaceholder | UpscaleGeneratedImage
  ) => <></>;

  const handleImageClick = (
    item: UpscalePlaceholder | UpscaleGeneratedImage
  ) => {
    setSelectedImage(item);
    setOpen(true);
  };

  const renderItem = (item: UpscalePlaceholder | UpscaleGeneratedImage) => (
    <div
      className="relative w-full aspect-video cursor-pointer overflow-hidden rounded-lg shadow-md group"
      onClick={() => handleImageClick(item)}
    >
      {"url" in item ? (
        <>
          <Image
            src={item.url}
            alt={`Generated image ${item.id}`}
            layout="fill"
            objectFit="cover"
          />
          <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
            <Button
              size="sm"
              variant="secondary"
              onClick={(e) => {
                e.stopPropagation();
                handleDownload(item.url, item.id);
              }}
              disabled={isDownloading[item.id]}
            >
              {isDownloading[item.id] ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Download className="w-4 h-4 mr-2" />
              )}
              Download
            </Button>
          </div>
        </>
      ) : (
        <>
          {item.inputImage && (
            <Image
              src={item.inputImage}
              alt="Input image"
              layout="fill"
              objectFit="cover"
              className="filter blur-sm"
            />
          )}
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div
              className={`text-white text-lg font-semibold ${
                item.status === PredictionStatus.PROCESSING
                  ? "animate-pulse"
                  : ""
              }`}
            >
              {item.status}
            </div>
          </div>
        </>
      )}
    </div>
  );

  const handleDownload = async (imageUrl: string, imageId: string) => {
    try {
      setIsDownloading({ ...isDownloading, [imageId]: true });

      const response = await fetch(imageUrl, {
        headers: {
          Accept: "image/*",
        },
      });

      if (!response.ok) throw new Error("Failed to fetch image");

      const blob = await response.blob();

      const dimensions = await getOriginalImageDimensions(imageUrl);

      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      const img = new globalThis.Image();

      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = URL.createObjectURL(blob);
      });

      canvas.width = dimensions.width;
      canvas.height = dimensions.height;

      ctx?.drawImage(img, 0, 0, dimensions.width, dimensions.height);

      const finalBlob = await new Promise<Blob>((resolve) => {
        canvas.toBlob(
          (blob) => {
            resolve(blob!);
          },
          "image/png",
          1.0
        );
      });

      const downloadUrl = URL.createObjectURL(finalBlob);
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = `upscaled_image_${dimensions.width}x${dimensions.height}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      URL.revokeObjectURL(downloadUrl);
      URL.revokeObjectURL(img.src);
    } catch (error) {
      console.error("Error downloading image:", error);
      toast({
        title: "Error",
        description: "Failed to download the image. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDownloading({ ...isDownloading, [imageId]: false });
    }
  };

  const renderCompareOrOutput = (item: UpscaleGeneratedImage) => (
    <div className="relative w-full" style={{ aspectRatio: "16 / 9" }}>
      {showComparison ? (
        <div className="absolute inset-0">
          <Compare
            firstImage={item.inputImage}
            secondImage={item.url}
            className="w-full h-full"
            onSliderChange={(percent) => {
              const originalBadge = document.getElementById("original-badge");
              const upscaledBadge = document.getElementById("upscaled-badge");

              if (originalBadge) {
                originalBadge.style.opacity = percent < 25 ? "0" : "1";
              }
              if (upscaledBadge) {
                upscaledBadge.style.opacity = percent > 75 ? "0" : "1";
              }
            }}
          />
          <div
            id="original-badge"
            className="absolute top-2 left-2 z-[60] bg-black bg-opacity-50 text-white px-2 py-1 rounded transition-opacity duration-200"
          >
            Original
          </div>
          <div
            id="upscaled-badge"
            className="absolute top-2 right-2 z-[60] bg-black bg-opacity-50 text-white px-2 py-1 rounded transition-opacity duration-200"
          >
            Upscaled
          </div>
        </div>
      ) : (
        <div className="w-full h-full relative">
          <Image
            src={item.url}
            alt="Upscaled"
            layout="fill"
            objectFit="contain"
          />
        </div>
      )}
    </div>
  );

  const renderMetadata = (image: UpscaleGeneratedImage) => (
    <p className="text-sm">
      <strong>Created At:</strong> {image.createdAt.toLocaleString()}
    </p>
  );

  if (store.isLoading && displayItems.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-16">
        <Loader2 className="w-10 h-10 text-blue-500 animate-spin" />
        <p className="mt-4 text-xl font-semibold text-gray-700 dark:text-gray-300">
          Upscaling Image...
        </p>
      </div>
    );
  }

  if (!store.isLoading && displayItems.length === 0) {
    return <UpscaleGuide />;
  }

  return (
    <div className="h-full w-full">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
        {displayItems.map((item) => (
          <div key={item.id} className="relative">
            {renderItem(item)}
          </div>
        ))}
      </div>

      <ImageDetailsDialog
        open={open}
        onOpenChange={setOpen}
        selectedImage={selectedImage}
        showComparison={showComparison}
        onComparisonToggle={() => setShowComparison(!showComparison)}
        isDownloading={isDownloading}
        onDownload={handleDownload}
        renderMetadata={renderMetadata}
        imageType="upscale_images"
      />
    </div>
  );
};

export default UpscaleImageDisplay;
