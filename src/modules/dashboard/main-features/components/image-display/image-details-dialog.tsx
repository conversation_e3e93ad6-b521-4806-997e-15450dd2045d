"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from "@/modules/ui/dialog";
import { <PERSON><PERSON> } from "@/modules/ui/button";
import { Loader2, Download, ZoomIn, X } from "lucide-react";
import { ScrollArea } from "@/modules/ui/scroll-area";
import { Compare } from "@/modules/ui/compare";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { ImageRating } from "@/modules/dashboard/main-features/components/image-display/image-rating";
import { useCurrentUser } from "@/modules/auth/hooks/use-current-user";
import { getUserRating } from "@/modules/dashboard/main-features/actions/rating";
import useRatingStore from "../../store/rating-store";

interface ImageDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedImage: any | null;
  showComparison: boolean;
  onComparisonToggle: () => void;
  isUpscaling?: Record<string, boolean>;
  isDownloading: Record<string, boolean>;
  onUpscale?: (image: any) => void;
  onDownload: (url: string, id: string) => void;
  renderMetadata?: (image: any) => React.ReactNode;
  imageType:
    | "interior_designs"
    | "exterior_designs"
    | "virtual_stagings"
    | "edit_images"
    | "upscale_images"
    | "background_removals"
    | "style_transfers";
}

const ImageDetailsDialog: React.FC<ImageDetailsDialogProps> = ({
  open,
  onOpenChange,
  selectedImage,
  showComparison,
  onComparisonToggle,
  isUpscaling,
  isDownloading,
  onUpscale,
  onDownload,
  renderMetadata,
  imageType,
}) => {
  const { data: user, isLoading } = useCurrentUser();
  const { ratings, averageRatings, totalRatings } = useRatingStore();

  const handleOpenChange = (newOpen: boolean) => {
    onOpenChange(newOpen);
  };

  const handleRated = (rating: number) => {
    // No need to manage local state as it's now handled by the store
  };

  const renderCompareOrOutput = (item: any) => (
    <div className="relative w-full h-full flex items-center justify-center bg-black/5 dark:bg-white/5 rounded-lg overflow-hidden">
      {showComparison && imageType === "style_transfers" ? (
        <div className="relative w-full h-full grid grid-cols-3 gap-1">
          <div className="relative col-span-1">
            <Image
              src={item.styleImage}
              alt="Style"
              layout="fill"
              objectFit="cover"
              className="rounded-l-md"
              unoptimized
            />
            <div className="absolute bottom-0 left-0 right-0 bg-black/50 backdrop-blur-sm text-white px-3 py-1.5 text-center text-sm font-medium">
              Style
            </div>
          </div>
          <div className="relative col-span-1">
            <Image
              src={item.structureImage}
              alt="Structure"
              layout="fill"
              objectFit="cover"
              unoptimized
            />
            <div className="absolute bottom-0 left-0 right-0 bg-black/50 backdrop-blur-sm text-white px-3 py-1.5 text-center text-sm font-medium">
              Structure
            </div>
          </div>
          <div className="relative col-span-1">
            <Image
              src={item.url}
              alt="Result"
              layout="fill"
              objectFit="cover"
              className="rounded-r-md"
              unoptimized
            />
            <div className="absolute bottom-0 left-0 right-0 bg-black/50 backdrop-blur-sm text-white px-3 py-1.5 text-center text-sm font-medium">
              Result
            </div>
          </div>
        </div>
      ) : showComparison ? (
        <div className="relative w-full h-full">
          <Compare
            firstImage={item.inputImage}
            secondImage={item.displayUrl || item.outputImage}
            className="w-full h-full"
            onSliderChange={(percent) => {
              const originalBadge = document.getElementById("original-badge");
              const generatedBadge = document.getElementById("generated-badge");

              if (originalBadge) {
                originalBadge.style.opacity = percent < 25 ? "0" : "1";
              }
              if (generatedBadge) {
                generatedBadge.style.opacity = percent > 75 ? "0" : "1";
              }
            }}
          />
          <div
            id="original-badge"
            className="absolute top-4 left-4 z-[60] bg-black/50 backdrop-blur-sm text-white px-3 py-1.5 rounded-full text-sm font-medium transition-opacity duration-200"
          >
            Original
          </div>
          <div
            id="generated-badge"
            className="absolute top-4 right-4 z-[60] bg-black/50 backdrop-blur-sm text-white px-3 py-1.5 rounded-full text-sm font-medium transition-opacity duration-200"
          >
            Generated
          </div>
        </div>
      ) : (
        <div className="relative w-full h-full">
          <Image
            src={item.displayUrl || item.outputImage}
            alt="Generated"
            width={1024}
            height={1024}
            className="w-full h-full object-contain"
            unoptimized
          />
        </div>
      )}
    </div>
  );

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-5xl w-[95vw] h-[95dvh] p-6 flex rounded-lg  flex-col gap-4">
        <DialogHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <DialogTitle className="text-xl font-semibold">
            Image Details
          </DialogTitle>
        </DialogHeader>

        <DialogDescription className="flex-1 overflow-hidden flex flex-col gap-4">
          {selectedImage && (
            <div className="h-full flex flex-col gap-4">
              {"displayUrl" in selectedImage || "outputImage" in selectedImage ? (
                <>
                  <div className="relative flex-1 min-h-0">
                    {renderCompareOrOutput(selectedImage)}
                  </div>

                  <div className="flex-shrink-0 space-y-4">
                    <div className="flex flex-wrap items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        className={cn(
                          "transition-colors",
                          !showComparison &&
                            "bg-primary text-primary-foreground hover:bg-primary/90"
                        )}
                        onClick={onComparisonToggle}
                      >
                        {showComparison ? "Show Result" : "Show Comparison"}
                      </Button>

                      <div className="flex-1" />

                      <ImageRating
                        imageId={selectedImage.id}
                        imageType={imageType}
                        averageRating={
                          averageRatings[selectedImage.id] ||
                          selectedImage.averageRating
                        }
                        totalRatings={
                          totalRatings[selectedImage.id] ||
                          selectedImage.totalRatings
                        }
                        isPublic={selectedImage.isPublic}
                        currentUserId={user?.id}
                        ownerId={selectedImage.userId}
                        onRated={handleRated}
                      />

                      {onUpscale && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => onUpscale(selectedImage)}
                          disabled={isUpscaling?.[selectedImage.id]}
                        >
                          {isUpscaling?.[selectedImage.id] ? (
                            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                          ) : (
                            <ZoomIn className="w-4 h-4 mr-2" />
                          )}
                          Upscale
                        </Button>
                      )}

                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() =>
                          onDownload(
                            selectedImage.downloadUrl || selectedImage.url || selectedImage.outputImage,
                            selectedImage.id
                          )
                        }
                        disabled={isDownloading[selectedImage.id]}
                      >
                        {isDownloading[selectedImage.id] ? (
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        ) : (
                          <Download className="w-4 h-4 mr-2" />
                        )}
                        Download
                      </Button>
                    </div>

                    <ScrollArea className="h-[120px] rounded-lg border border-border/80 bg-muted/50">
                      <div className="p-4">
                        {renderMetadata && renderMetadata(selectedImage)}
                      </div>
                    </ScrollArea>
                  </div>
                </>
              ) : (
                <div className="flex-grow flex items-center justify-center">
                  <div className="text-center space-y-4">
                    <div className="relative w-20 h-20 mx-auto">
                      <Loader2 className="w-20 h-20 animate-spin text-muted-foreground" />
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="w-16 h-16 rounded-full bg-background" />
                      </div>
                    </div>
                    <p className="text-lg font-medium text-muted-foreground">
                      Processing image...
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogDescription>
      </DialogContent>
    </Dialog>
  );
};

export default ImageDetailsDialog;
