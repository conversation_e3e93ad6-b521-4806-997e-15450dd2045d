"use client";

import React, { useState, useEffect } from "react";
import useEditImageFormStore from "@/modules/dashboard/main-features/store/edit-image-form-store";
import { Loader2 } from "lucide-react";
import EditImageCanvas from "./edit-image-canvas";

const EditImageDisplay: React.FC = () => {
  const store = useEditImageFormStore();
  const [isGenerating, setIsGenerating] = useState(false);

  useEffect(() => {
    setIsGenerating(store.isLoading);
  }, [store.isLoading]);

  if (!store.imageUrl) {
    return (
      <div className="flex flex-col items-center justify-center h-full py-16">
        <p className="text-xl font-semibold text-gray-700 dark:text-gray-300">
          Upload an image to start editing
        </p>
      </div>
    );
  }

  return (
    <div className="h-full w-full">
      <div className="relative h-full w-full bg-gray-100 dark:bg-gray-900">
        <EditImageCanvas
          inputImageUrl={store.imageUrl}
          isGenerating={isGenerating}
          generatedImageUrl={store.generatedImageUrl}
        />
      </div>
    </div>
  );
};

export default EditImageDisplay;
