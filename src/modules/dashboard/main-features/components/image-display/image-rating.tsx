"use client";

import { useEffect, useState } from "react";
import { Star } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  rateImage,
  getUserRating,
} from "@/modules/dashboard/main-features/actions/rating";
import { toast } from "sonner";
import useRatingStore from "../../store/rating-store";

type ImageType =
  | "interior_designs"
  | "exterior_designs"
  | "virtual_stagings"
  | "edit_images"
  | "upscale_images"
  | "background_removals"
  | "style_transfers";

interface ImageRatingProps {
  imageId: string;
  imageType: ImageType;
  averageRating?: number;
  totalRatings?: number;
  onRated?: (rating: number) => void;
  className?: string;
  isPublic?: boolean;
  currentUserId?: string;
  ownerId?: string;
  isLoading?: boolean;
  error?: string;
}

export type { ImageType };
export type { ImageRatingProps };

export function ImageRating({
  imageId,
  imageType,
  averageRating = 0,
  totalRatings = 0,
  onRated,
  className,
  isPublic,
  currentUserId,
  ownerId,
  isLoading,
  error,
}: ImageRatingProps) {
  const [hover, setHover] = useState(0);
  const {
    ratings,
    averageRatings,
    totalRatings: storeTotalRatings,
    setRating,
    updateAverageRating,
  } = useRatingStore();

  // Initialize store with props on mount
  useEffect(() => {
    if (averageRating > 0) {
      updateAverageRating(imageId, averageRating, totalRatings);
    }
  }, [imageId, averageRating, totalRatings]);

  // Load user rating on mount
  useEffect(() => {
    const loadUserRating = async () => {
      const rating = await getUserRating(imageId, imageType);
      if (rating !== null) {
        setRating(imageId, rating);
      }
    };
    loadUserRating();
  }, [imageId, imageType]);

  const updateRatingOptimistically = (value: number) => {
    const hadPreviousRating = ratings[imageId] !== undefined;
    const currentTotal = storeTotalRatings[imageId] || totalRatings;
    const currentAvg = averageRatings[imageId] || averageRating;

    // Only increment total if this is a new rating
    const newTotal = hadPreviousRating ? currentTotal : currentTotal + 1;

    // Calculate new average
    let newAvg;
    if (hadPreviousRating) {
      // Replace old rating with new rating in average
      newAvg =
        (currentAvg * currentTotal - ratings[imageId] + value) / currentTotal;
    } else {
      // Add new rating to average
      newAvg = (currentAvg * currentTotal + value) / newTotal;
    }

    // Update store immediately
    setRating(imageId, value);
    updateAverageRating(imageId, newAvg, newTotal);
  };

  const handleRate = async (value: number) => {
    // Update UI immediately
    updateRatingOptimistically(value);
    onRated?.(value);

    try {
      const result = await rateImage({
        imageId,
        imageType,
        rating: value,
      });

      if (!result.success) {
        // If the API call fails, revert to the previous state
        toast.error("Failed to submit rating");
        loadUserRating();
      }
    } catch (error) {
      toast.error("Something went wrong");
      loadUserRating();
    }
  };

  const loadUserRating = async () => {
    const rating = await getUserRating(imageId, imageType);
    if (rating !== null) {
      setRating(imageId, rating);
    }
  };

  if (isLoading) {
    return <div className="animate-pulse">Loading rating...</div>;
  }

  if (error) {
    return <div className="text-red-500">Error: {error}</div>;
  }

  const displayRating = ratings[imageId] || 0;
  const displayAverage = averageRatings[imageId] || averageRating;
  const displayTotal = storeTotalRatings[imageId] || totalRatings;

  return (
    <div className="flex flex-col items-center gap-1">
      <div className={cn("flex items-center gap-1", className)}>
        {[1, 2, 3, 4, 5].map((value) => (
          <button
            key={value}
            type="button"
            onClick={() => handleRate(value)}
            onMouseEnter={() => setHover(value)}
            onMouseLeave={() => setHover(0)}
            className="focus:outline-none transition-opacity"
          >
            <Star
              className={cn(
                "w-5 h-5 transition-colors",
                (hover || displayRating) >= value
                  ? "fill-yellow-400 text-yellow-400"
                  : "fill-transparent text-gray-300"
              )}
            />
          </button>
        ))}
      </div>
      <div className="text-sm text-muted-foreground">
        {displayAverage > 0 && (
          <span>
            {displayAverage.toFixed(1)} ({displayTotal} rating
            {displayTotal !== 1 ? "s" : ""})
          </span>
        )}
      </div>
    </div>
  );
}
