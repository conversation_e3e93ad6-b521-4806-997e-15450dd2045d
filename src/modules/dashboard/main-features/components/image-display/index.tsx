import React from "react";
import ImageDisplayWrapper from "./image-display-wrapper";
import ExteriorImageDisplay from "./exterior-image-display";
import InteriorImageDisplay from "./interior-image-display";
import BackgroundRemovalImageDisplay from "./background-removal-image-display";
import UpscaleImageDisplay from "./upscale-image-display";
import VirtualStagingImageDisplay from "./virtual-staging-image-display";
import StyleTransferImageDisplay from "./style-transfer-image-display";

const WrappedExteriorImageDisplay = () => (
  <ImageDisplayWrapper type="exterior_design">
    <ExteriorImageDisplay />
  </ImageDisplayWrapper>
);

const WrappedInteriorImageDisplay = () => (
  <ImageDisplayWrapper type="interior_design">
    <InteriorImageDisplay />
  </ImageDisplayWrapper>
);

const WrappedBackgroundRemovalImageDisplay = () => (
  <ImageDisplayWrapper type="background_removal">
    <BackgroundRemovalImageDisplay />
  </ImageDisplayWrapper>
);

const WrappedUpscaleImageDisplay = () => (
  <ImageDisplayWrapper type="upscale">
    <UpscaleImageDisplay />
  </ImageDisplayWrapper>
);

const WrappedVirtualStagingImageDisplay = () => (
  <ImageDisplayWrapper type="virtual_staging">
    <VirtualStagingImageDisplay />
  </ImageDisplayWrapper>
);

const WrappedStyleTransferImageDisplay = () => (
  <ImageDisplayWrapper type="style_transfer">
    <StyleTransferImageDisplay />
  </ImageDisplayWrapper>
);

export {
  WrappedExteriorImageDisplay as ExteriorImageDisplay,
  WrappedInteriorImageDisplay as InteriorImageDisplay,
  WrappedBackgroundRemovalImageDisplay as BackgroundRemovalImageDisplay,
  WrappedUpscaleImageDisplay as UpscaleImageDisplay,
  WrappedVirtualStagingImageDisplay as VirtualStagingImageDisplay,
  WrappedStyleTransferImageDisplay as StyleTransferImageDisplay,
};
