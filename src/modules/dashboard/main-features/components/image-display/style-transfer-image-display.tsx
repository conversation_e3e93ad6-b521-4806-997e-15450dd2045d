"use client";
import React, { useState, useMemo, useEffect } from "react";
import { useChannel } from "ably/react";
import type { Message } from "ably";
import {
  PredictionStatus,
  StyleTransferPlaceholder,
  StyleTransferGeneratedImage,
  WebhookPayload,
} from "@/modules/dashboard/main-features/types";
import useStyleTransferStore from "@/modules/dashboard/main-features/store/style-transfer-store";
import { useCurrentUser } from "@/modules/auth/hooks/use-current-user";
import { Skeleton } from "@/modules/ui/skeleton";
import { Button } from "@/modules/ui/button";
import { Loader2 } from "lucide-react";
import Image from "next/image";
import { upscaleImage } from "@/modules/dashboard/main-features/actions/upscale-image";
import { Download, ZoomIn } from "lucide-react";
import { useToast } from "@/modules/ui/use-toast";
import useUpscaleFormStore from "@/modules/dashboard/main-features/store/upscale-form-store";
import ImageDetailsDialog from "./image-details-dialog";
import { LoadingSpinner } from "@/modules/ui/loading-states";
import { getOriginalImageDimensions } from "../../../../utils/image";

const StyleTransferImageDisplay: React.FC = () => {
  const currentUser = useCurrentUser();
  const store = useStyleTransferStore();
  const [open, setOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState<
    StyleTransferPlaceholder | StyleTransferGeneratedImage | null
  >(null);
  const [isUpscaling, setIsUpscaling] = useState<Record<string, boolean>>({});
  const [isDownloading, setIsDownloading] = useState<Record<string, boolean>>(
    {}
  );
  const upscaleStore = useUpscaleFormStore();
  const { toast } = useToast();
  const channelName = useMemo(() => {
    return currentUser.data?.id
      ? `image-generation-style_transfer-${currentUser.data.id}`
      : null;
  }, [currentUser.data?.id]);

  const { channel } = useChannel(channelName || "");
  const [showComparison, setShowComparison] = useState(true);

  useEffect(() => {
    if (channel && channelName) {
      const handleMessage = (message: Message) => {
        if (!message.data) return;

        const data = message.data as WebhookPayload;
        const {
          id,
          status,
          url,
          inputImage,
          styleImage,
          model,
          structureImage,
          createdAt,
        } = data;

        if (status === PredictionStatus.SUCCEEDED && url) {
          store.addGeneratedImage(
            id,
            url,
            inputImage || "",
            styleImage || "",
            model,
            structureImage,
            new Date(createdAt || Date.now())
          );
          store.removeImagePlaceholder(id);
        } else if (
          status === PredictionStatus.FAILED ||
          status === PredictionStatus.CANCELED
        ) {
          store.removeImagePlaceholder(id);
        } else {
          store.updateImageStatus(id, status as PredictionStatus);
        }

        if (
          [
            PredictionStatus.SUCCEEDED,
            PredictionStatus.FAILED,
            PredictionStatus.CANCELED,
          ].includes(status as PredictionStatus)
        ) {
          store.setIsLoading(false);
        }
      };

      channel.subscribe(handleMessage);

      return () => {
        channel.unsubscribe(handleMessage);
      };
    }
  }, [channel, channelName, store]);

  const displayItems = useMemo(() => {
    const allItems = [...store.imagePlaceholders, ...store.generatedImages];
    return allItems.sort((a, b) => {
      const aIsProcessing =
        "status" in a && a.status === PredictionStatus.PROCESSING;
      const bIsProcessing =
        "status" in b && b.status === PredictionStatus.PROCESSING;

      if (aIsProcessing && !bIsProcessing) return -1;
      if (!bIsProcessing && aIsProcessing) return 1;

      return b.createdAt.getTime() - a.createdAt.getTime();
    });
  }, [store.imagePlaceholders, store.generatedImages]);

  const handleImageClick = (
    item: StyleTransferPlaceholder | StyleTransferGeneratedImage
  ) => {
    setSelectedImage(item);
    setOpen(true);
  };

  const handleUpscale = async (image: StyleTransferGeneratedImage) => {
    if (!currentUser.data?.id) return;

    try {
      setIsUpscaling({ ...isUpscaling, [image.id]: true });
      upscaleStore.addUpscalePlaceholder(image.url);

      const result = await upscaleImage({
        userId: currentUser.data.id,
        image: image.url,
        upscaleAmount: 2,
        creativity: 50,
        resemblance: 0.7,
      });

      if (result.success) {
        toast({
          title: "Image upscaling started",
          description: "Check the Upscale tab to see the result.",
        });
      } else {
        throw new Error(result.error || "Failed to start upscaling");
      }
    } catch (error) {
      console.error("Error upscaling image:", error);
      toast({
        title: "Error",
        description: "Failed to start upscaling. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUpscaling({ ...isUpscaling, [image.id]: false });
    }
  };

  const handleDownload = async (
    imageUrl: string,
    imageId: string,
    type: "style" | "structure" | "result"
  ) => {
    try {
      setIsDownloading({ ...isDownloading, [imageId]: true });

      const response = await fetch(imageUrl, {
        headers: {
          Accept: "image/*",
        },
      });

      if (!response.ok) throw new Error("Failed to fetch image");

      const blob = await response.blob();

      const dimensions = await getOriginalImageDimensions(imageUrl);

      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      const img = new globalThis.Image();

      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = URL.createObjectURL(blob);
      });

      // Set canvas dimensions to match original image
      canvas.width = dimensions.width;
      canvas.height = dimensions.height;

      // Draw image at original dimensions
      ctx?.drawImage(img, 0, 0, dimensions.width, dimensions.height);

      // Convert to blob with maximum quality
      const finalBlob = await new Promise<Blob>((resolve) => {
        canvas.toBlob(
          (blob) => {
            resolve(blob!);
          },
          "image/png",
          1.0
        );
      });

      // Create and trigger download
      const downloadUrl = URL.createObjectURL(finalBlob);
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = `style_transfer_${type}_${dimensions.width}x${dimensions.height}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Cleanup
      URL.revokeObjectURL(downloadUrl);
      URL.revokeObjectURL(img.src);
    } catch (error) {
      console.error("Error downloading image:", error);
      toast({
        title: "Error",
        description: "Failed to download the image. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDownloading({ ...isDownloading, [imageId]: false });
    }
  };

  const handleDialogDownload = async (url: string, id: string) => {
    await handleDownload(url, id, "result");
  };

  const renderItem = (
    item: StyleTransferPlaceholder | StyleTransferGeneratedImage
  ) => {
    const isGenerated = "url" in item;

    return (
      <div
        className="relative w-full pb-[56.25%] cursor-pointer overflow-hidden rounded-lg shadow-md group"
        onClick={() => handleImageClick(item)}
      >
        {isGenerated ? (
          <>
            <div className="absolute inset-0 grid grid-cols-3 gap-1">
              <div className="relative col-span-1 group/style">
                <Image
                  src={item.styleImage}
                  alt="Style image"
                  layout="fill"
                  objectFit="cover"
                  className="rounded-l-md"
                />
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 text-center">
                  Style
                </div>
                <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover/style:opacity-100 transition-opacity flex items-center justify-center">
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDownload(item.styleImage, item.id, "style");
                    }}
                  >
                    <Download className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              <div className="relative col-span-1 group/structure">
                <Image
                  src={item.structureImage || ""}
                  alt="Structure image"
                  layout="fill"
                  objectFit="cover"
                />
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 text-center">
                  Structure
                </div>
                <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover/structure:opacity-100 transition-opacity flex items-center justify-center">
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDownload(
                        item.structureImage || "",
                        item.id,
                        "structure"
                      );
                    }}
                  >
                    <Download className="w-4 h-4" />
                  </Button>
                </div>
              </div>
              <div className="relative col-span-1 group/result">
                <Image
                  src={item.url}
                  alt={`Generated image ${item.id}`}
                  layout="fill"
                  objectFit="cover"
                  className="rounded-r-md"
                />
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 text-center">
                  Result
                </div>
                <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover/result:opacity-100 transition-opacity flex items-center justify-center space-x-2">
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleUpscale(item as StyleTransferGeneratedImage);
                    }}
                    disabled={isUpscaling[item.id]}
                  >
                    {isUpscaling[item.id] ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <ZoomIn className="w-4 h-4" />
                    )}
                  </Button>
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDownload(item.url, item.id, "result");
                    }}
                    disabled={isDownloading[item.id]}
                  >
                    {isDownloading[item.id] ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Download className="w-4 h-4" />
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </>
        ) : (
          <>
            <div className="absolute inset-0 grid grid-cols-3 gap-1">
              <div className="relative col-span-1">
                <Image
                  src={item.styleImage}
                  alt="Style image"
                  layout="fill"
                  objectFit="cover"
                  className="filter blur-sm rounded-l-md"
                />
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 text-center">
                  Style
                </div>
              </div>
              <div className="relative col-span-1">
                <Image
                  src={item.structureImage || ""}
                  alt="Structure image"
                  layout="fill"
                  objectFit="cover"
                  className="filter blur-sm"
                />
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 text-center">
                  Structure
                </div>
              </div>
              <div className="relative col-span-1 bg-gray-800 rounded-r-md">
                <div className="absolute inset-0 flex items-center justify-center">
                  <LoadingSpinner />
                </div>
              </div>
            </div>
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
              <div
                className={`text-white text-lg font-semibold ${
                  item.status === PredictionStatus.PROCESSING
                    ? "animate-pulse"
                    : ""
                }`}
              >
                {item.status}
              </div>
            </div>
          </>
        )}
      </div>
    );
  };

  const renderMetadata = (image: StyleTransferGeneratedImage) => (
    <>
      {image.model && (
        <p>
          <strong>Model:</strong> {image.model}
        </p>
      )}
      {image.prompt && (
        <p>
          <strong>Prompt:</strong> {image.prompt}
        </p>
      )}
      <p>
        <strong>Created At:</strong> {image.createdAt.toLocaleString()}
      </p>
    </>
  );

  if (currentUser.isLoading) {
    return (
      <div className="container mx-auto px-4 py-12 min-h-screen">
        <div className="flex flex-col items-center justify-center space-y-4">
          <Skeleton className="w-32 h-32 rounded-full" />
          <Skeleton className="w-48 h-6" />
          <Skeleton className="w-64 h-4" />
        </div>
      </div>
    );
  }

  if (!currentUser.data) {
    return (
      <div className="container mx-auto px-4 py-12 min-h-screen text-center">
        <p>Please log in to view your images.</p>
      </div>
    );
  }

  if (store.isLoading && displayItems.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-16">
        <LoadingSpinner />
        <p className="mt-4 text-xl font-semibold text-gray-700 dark:text-gray-300">
          Generating Style Transfer...
        </p>
      </div>
    );
  }

  if (!store.isLoading && displayItems.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-16">
        <p className="text-xl font-semibold text-gray-700 dark:text-gray-300">
          Upload an image and select a style to get started.
        </p>
      </div>
    );
  }

  return (
    <div className="h-full w-full">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
        {displayItems.map((item) => (
          <div key={item.id} className="relative">
            {renderItem(item)}
          </div>
        ))}
      </div>

      {selectedImage && (
        <ImageDetailsDialog
          open={open}
          onOpenChange={setOpen}
          selectedImage={selectedImage}
          showComparison={showComparison}
          onComparisonToggle={() => setShowComparison(!showComparison)}
          isUpscaling={isUpscaling}
          isDownloading={isDownloading}
          onUpscale={
            "url" in selectedImage
              ? (image) => handleUpscale(image as StyleTransferGeneratedImage)
              : undefined
          }
          onDownload={handleDialogDownload}
          renderMetadata={renderMetadata}
          imageType="style_transfers"
        />
      )}
    </div>
  );
};

export default StyleTransferImageDisplay;
