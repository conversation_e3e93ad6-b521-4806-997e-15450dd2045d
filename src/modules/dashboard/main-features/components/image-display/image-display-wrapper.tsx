"use client";
import React from "react";
import { ChannelProvider } from "ably/react";
import { useCurrentUser } from "@/modules/auth/hooks/use-current-user";
import { Skeleton } from "@/modules/ui/skeleton";

interface ImageDisplayWrapperProps {
  type:
    | "interior_design"
    | "exterior_design"
    | "background_removal"
    | "upscale"
    | "virtual_staging"
    | "style_transfer";
  children: React.ReactNode;
}

const ImageDisplayWrapper: React.FC<ImageDisplayWrapperProps> = ({
  type,
  children,
}) => {
  const { data: user, isLoading } = useCurrentUser();

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-12 min-h-screen">
        <div className="flex flex-col items-center justify-center space-y-4">
          <Skeleton className="w-32 h-32 rounded-full" />
          <Skeleton className="w-48 h-6" />
          <Skeleton className="w-64 h-4" />
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-12 min-h-screen text-center">
        <p>Please log in to view your images.</p>
      </div>
    );
  }

  const channelName = `image-generation-${type}-${user.id}`;

  return (
    <ChannelProvider channelName={channelName}>{children}</ChannelProvider>
  );
};

export default ImageDisplayWrapper;
