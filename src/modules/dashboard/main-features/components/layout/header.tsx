"use client";

import React, { useState } from "react";
import { ImageCredits } from "@/modules/dashboard/main-features/components/layout/image-credits";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { UpgradeHeader } from "./upgrade-header";
import { useSession } from "next-auth/react";
import Link from "next/link";

interface HeaderProps {
  showCredits?: boolean;
}

function Header({ showCredits = false }: HeaderProps) {
  const [isScrolled, setIsScrolled] = useState(false);
  const { status } = useSession();

  React.useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <motion.header
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      className={cn(
        "sticky top-0 z-50 w-full transition-all duration-300",
        "backdrop-blur-xl hidden sm:block ",
        isScrolled && "shadow-sm bg-background/80"
      )}
    >
      <div className="w-full px-3 sm:px-4 h-16">
        <div className="flex justify-between h-full">
          {/* Left Section - Logo */}
          <div className="h-10 flex items-center">
            <Link href="/dashboard" className="flex items-center justify-center h-10">
              <div className="flex items-center justify-center w-10 h-10">
                <img
                  src="/images/renovaitor-logo-gradient-light.png"
                  alt="Renovaitor Logo"
                  className="w-10 h-10 object-contain dark:hidden"
                />
                <img
                  src="/images/renovaitor-logo-gradient-dark.png"
                  alt="Renovaitor Logo"
                  className="w-10 h-10 object-contain hidden dark:block"
                />
              </div>
            </Link>
          </div>
          {/* Right Section */}
          <div className="flex items-center gap-2 sm:gap-6">
            {/* Always show UpgradeHeader but with conditional rendering inside */}
            <UpgradeHeader compactOnMobile={true} />
          </div>
        </div>
      </div>
    </motion.header>
  );
}

export default React.memo(Header);
