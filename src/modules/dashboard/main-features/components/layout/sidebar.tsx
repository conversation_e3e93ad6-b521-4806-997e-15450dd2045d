"use client";
import { cn } from "@/modules/ui";

import {
  Home,
  LayoutGrid,
  WandSparkles,
  BellIcon,
  Menu,
  LogOut,
  Monitor,
  Sun,
  Moon,
  Laptop,
  Settings,
  ZapFilled,
  LayoutDashboard,
  Crown,
  ImagePlus,
  Trash,
  Twitter,
  UserCircle,
} from "@/modules/ui/icons";
import { Gift, MessageCircle } from "lucide-react";
import { useCurrentUser } from "@/modules/auth/hooks/use-current-user";
import Image from "next/image";
import {
  Sidebar,
  SidebarBody,
  SidebarLink,
  SidebarProvider,
} from "@/modules/ui/sidebar";
import { usePathname } from "next/navigation";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuLabel,
} from "@/modules/ui/dropdown-menu";
import Link from "next/link";
import { signOut } from "next-auth/react";
import { useTheme } from "next-themes";
import { useSubscription } from "@/modules/payments/hooks/use-subscription";
import { Logo } from "@/modules/marketing/components/layout/header/logo";
import { Button } from "@/modules/ui/button";
import { PaywallDialog } from "@/modules/payments/components/paywall-dialog";
import { useState, useEffect } from "react";
import { useImageCreditsStore } from "@/modules/dashboard/main-features/stores/use-image-credits-store";
import { Images } from "@/modules/ui/icons";

const MenuSection = ({ currentUser }: { currentUser: any }) => {
  const { setTheme, theme } = useTheme();
  const {
    isSubscribed,
    subscription,
    isLoading: subscriptionLoading,
  } = useSubscription();
  const hasSubscription = isSubscribed || subscription;
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { credits, isLoading: creditsLoading, error } = useImageCreditsStore();

  const handleUpgradeClick = () => {
    setIsDialogOpen(true);
  };

  const getTotalCredits = () => {
    if (!hasSubscription) return 30;
    return subscription?.packageName === "premium" ? 5000 : 1000;
  };

  const isPremium = subscription?.packageName === "premium";

  return (
    <div className="px-3 py-2.5">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button
            className={cn(
              "group relative w-full flex items-center justify-center h-16",
              "text-muted-foreground hover:text-primary/90"
            )}
          >
            <Menu className="h-7 w-7" />
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          side="right"
          align="end"
          className="w-72 "
          sideOffset={32}
        >
          <DropdownMenuLabel className="font-normal">
            <div className="flex flex-col space-y-1">
              <p className="text-sm font-medium leading-none">
                {currentUser?.name}
              </p>
              <p className="text-xs leading-none text-muted-foreground">
                {currentUser?.email}
              </p>
            </div>
          </DropdownMenuLabel>

          <div className="p-2">
            <div className="flex flex-col gap-1.5">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                  <span className="text-sm font-medium">
                    {isPremium ? "Premium" : hasSubscription ? "Pro" : "Basic"}
                  </span>
                  {hasSubscription && (
                    <Crown
                      className={cn(
                        "w-3.5 h-3.5",
                        isPremium ? "text-yellow-500" : "text-primary"
                      )}
                    />
                  )}
                </div>
                <div className="flex items-center gap-1.5">
                  <div className="relative w-4 h-4">
                    <Image
                      src="/images/coin.png"
                      alt="Credits"
                      width={16}
                      height={16}
                      className="object-contain"
                      priority
                    />
                  </div>
                  <span className="text-xs text-muted-foreground">
                    {creditsLoading || error ? (
                      "Loading..."
                    ) : (
                      <span className="font-medium">
                        {credits ?? "N/A"} / {getTotalCredits()}
                      </span>
                    )}
                  </span>
                </div>
              </div>
              <Button
                variant="outline"
                className="w-full justify-center gap-2 text-sm"
                onClick={handleUpgradeClick}
              >
                <ZapFilled className="h-4 w-4" />
                {hasSubscription ? "Top up credits" : "Upgrade to Pro"}
              </Button>
            </div>
          </div>

          <DropdownMenuSeparator />

          <DropdownMenuItem asChild>
            <Link href="/dashboard" className="flex items-center">
              <LayoutDashboard className="mr-2.5 h-4 w-4" />
              Dashboard
            </Link>
          </DropdownMenuItem>

          <DropdownMenuItem asChild>
            <Link href="/dashboard/designs" className="flex items-center">
              <ImagePlus className="mr-2.5 h-4 w-4" />
              My Designs
            </Link>
          </DropdownMenuItem>

          <DropdownMenuItem asChild>
            <Link href="/dashboard/profile" className="flex items-center">
              <UserCircle className="mr-2.5 h-4 w-4" />
              Profile
            </Link>
          </DropdownMenuItem>

          <DropdownMenuItem asChild>
            <Link href="/dashboard/referral" className="flex items-center">
              <Gift className="mr-2.5 h-4 w-4" />
              Refer & Earn
            </Link>
          </DropdownMenuItem>

          <DropdownMenuSeparator />

          <DropdownMenuSub>
            <DropdownMenuSubTrigger className="flex items-center">
              <Monitor className="mr-2.5 h-4 w-4" />
              Theme
            </DropdownMenuSubTrigger>
            <DropdownMenuSubContent>
              {[
                { value: "light", label: "Light", icon: Sun },
                { value: "dark", label: "Dark", icon: Moon },
                { value: "system", label: "Auto", icon: Laptop },
              ].map(({ value, label, icon: Icon }) => (
                <DropdownMenuItem
                  key={value}
                  onClick={() => setTheme(value)}
                  className={cn(
                    "flex items-center",
                    theme === value && "bg-primary/5"
                  )}
                >
                  <Icon className="mr-2.5 h-4 w-4" />
                  {label}
                </DropdownMenuItem>
              ))}
            </DropdownMenuSubContent>
          </DropdownMenuSub>

          <DropdownMenuSeparator />

          <DropdownMenuItem
            onClick={() => signOut()}
            className="flex items-center text-red-600"
          >
            <LogOut className="mr-2.5 h-4 w-4" />
            Sign out
          </DropdownMenuItem>

          <div className="p-2">
            <div className="flex items-center justify-between">
              <div className="flex gap-3 text-xs text-muted-foreground">
                <Link
                  href="/terms"
                  className="hover:text-foreground transition-colors"
                >
                  Terms
                </Link>
                <Link
                  href="/privacy"
                  className="hover:text-foreground transition-colors"
                >
                  Privacy
                </Link>
              </div>
              <div className="flex gap-2">
                <Link
                  href="https://instagram.com/renovaitor"
                  target="_blank"
                  className="text-muted-foreground hover:text-foreground transition-colors"
                >
                  <MessageCircle className="h-4 w-4" />
                </Link>
                <Link
                  href="https://x.com/circlemileb"
                  target="_blank"
                  className="text-muted-foreground hover:text-foreground transition-colors"
                >
                  <Twitter className="h-4 w-4" />
                </Link>
              </div>
            </div>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>

      <PaywallDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        title={hasSubscription ? "Get More Credits" : "Unlock Pro Features"}
        description={
          hasSubscription
            ? "Need more credits? Top up your account to continue creating amazing designs."
            : "Take your creativity further with Pro! Enjoy a private gallery, 1,000 credits monthly for high-quality renders, and early access to exciting new features."
        }
        feature="credits"
        persistDismissal={true}
      />
    </div>
  );
};

const MobileMenu = ({ currentUser }: { currentUser: any }) => {
  const { setTheme, theme } = useTheme();
  const {
    isSubscribed,
    subscription,
    isLoading: subscriptionLoading,
  } = useSubscription();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { credits, isLoading: creditsLoading, error } = useImageCreditsStore();

  const hasSubscription = isSubscribed || subscription;
  const isPremium = subscription?.packageName === "premium";

  const handleUpgradeClick = () => {
    setIsDialogOpen(true);
  };

  const getTotalCredits = () => {
    if (!hasSubscription) return 30;
    return subscription?.packageName === "premium" ? 5000 : 1000;
  };

  if (!currentUser) {
    return (
      <Link
        href="/login"
        className="flex flex-col items-center gap-1 text-xs py-1"
      >
        <UserCircle className="h-6 w-6" />
        <span>Sign in</span>
      </Link>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button className="group relative h-16 w-16 flex items-center justify-center !p-0">
          <Menu
            className={cn(
              "h-6 w-6",
              "text-muted-foreground group-hover:text-primary/80"
            )}
          />
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="w-72"
        sideOffset={8}
        alignOffset={-40}
      >
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">
              {currentUser?.name}
            </p>
            <p className="text-xs leading-none text-muted-foreground">
              {currentUser?.email}
            </p>
          </div>
        </DropdownMenuLabel>

        <div className="p-2">
          <div className="flex flex-col gap-1.5">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1">
                <span className="text-sm font-medium">
                  {isPremium ? "Premium" : hasSubscription ? "Pro" : "Basic"}
                </span>
                {hasSubscription && (
                  <Crown
                    className={cn(
                      "w-3.5 h-3.5",
                      isPremium ? "text-yellow-500" : "text-primary"
                    )}
                  />
                )}
              </div>
              <div className="flex items-center gap-1.5">
                <div className="relative w-4 h-4">
                  <Image
                    src="/images/coin.png"
                    alt="Credits"
                    width={16}
                    height={16}
                    className="object-contain"
                    priority
                  />
                </div>
                <span className="text-xs text-muted-foreground">
                  {creditsLoading || error ? (
                    "Loading..."
                  ) : (
                    <span className="font-medium">
                      {credits ?? "N/A"} / {getTotalCredits()}
                    </span>
                  )}
                </span>
              </div>
            </div>
            <Button
              variant="outline"
              className="w-full justify-center gap-2 text-sm"
              onClick={handleUpgradeClick}
            >
              <ZapFilled className="h-4 w-4" />
              {hasSubscription ? "Top up credits" : "Upgrade to Pro"}
            </Button>
          </div>
        </div>

        <DropdownMenuSeparator />

        <DropdownMenuItem asChild>
          <Link href="/dashboard" className="flex items-center">
            <LayoutDashboard className="mr-2.5 h-4 w-4" />
            Dashboard
          </Link>
        </DropdownMenuItem>

        <DropdownMenuItem asChild>
          <Link href="/dashboard/designs" className="flex items-center">
            <ImagePlus className="mr-2.5 h-4 w-4" />
            My Designs
          </Link>
        </DropdownMenuItem>

        <DropdownMenuItem asChild>
          <Link href="/dashboard/profile" className="flex items-center">
            <UserCircle className="mr-2.5 h-4 w-4" />
            Profile
          </Link>
        </DropdownMenuItem>

        <DropdownMenuItem asChild>
          <Link href="/dashboard/referral" className="flex items-center">
            <Gift className="mr-2.5 h-4 w-4" />
            Refer & Earn
          </Link>
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuSub>
          <DropdownMenuSubTrigger className="flex items-center">
            <Monitor className="mr-2.5 h-4 w-4" />
            Theme
          </DropdownMenuSubTrigger>
          <DropdownMenuSubContent>
            {[
              { value: "light", label: "Light", icon: Sun },
              { value: "dark", label: "Dark", icon: Moon },
              { value: "system", label: "Auto", icon: Laptop },
            ].map(({ value, label, icon: Icon }) => (
              <DropdownMenuItem
                key={value}
                onClick={() => setTheme(value)}
                className={cn(
                  "flex items-center",
                  theme === value && "bg-primary/5"
                )}
              >
                <Icon className="mr-2.5 h-4 w-4" />
                {label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuSubContent>
        </DropdownMenuSub>

        <DropdownMenuSeparator />

        <DropdownMenuItem
          onClick={() => signOut()}
          className="flex items-center text-red-600"
        >
          <LogOut className="mr-2.5 h-4 w-4" />
          Sign out
        </DropdownMenuItem>

        <div className="p-2">
          <div className="flex items-center justify-between">
            <div className="flex gap-3 text-xs text-muted-foreground">
              <Link
                href="/terms"
                className="hover:text-foreground transition-colors"
              >
                Terms
              </Link>
              <Link
                href="/privacy"
                className="hover:text-foreground transition-colors"
              >
                Privacy
              </Link>
            </div>
            <div className="flex gap-2">
              <Link
                href="https://instagram.com/renovaitor"
                target="_blank"
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                <MessageCircle className="h-4 w-4" />
              </Link>
              <Link
                href="https://x.com/circlemileb"
                target="_blank"
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                <Twitter className="h-4 w-4" />
              </Link>
            </div>
          </div>
        </div>

        <PaywallDialog
          open={isDialogOpen}
          onOpenChange={setIsDialogOpen}
          title={hasSubscription ? "Get More Credits" : "Unlock Pro Features"}
          description={
            hasSubscription
              ? "Need more credits? Top up your account to continue creating amazing designs."
              : "Take your creativity further with Pro! Enjoy a private gallery, 1,000 credits monthly for high-quality renders, and early access to exciting new features."
          }
          feature="credits"
          persistDismissal={true}
        />
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default function DashboardSidebar() {
  const { data: currentUser } = useCurrentUser();
  const pathname = usePathname();
  const {
    credits,
    isLoading: creditsLoading,
    error,
    fetchCredits,
  } = useImageCreditsStore();

  useEffect(() => {
    if (currentUser && credits === null) {
      fetchCredits();
    }
  }, [currentUser, credits, fetchCredits]);

  const isActiveLink = (href: string) => {
    if (href === "/dashboard" && pathname === "/dashboard") {
      return true;
    }
    return pathname.startsWith(href) && href !== "/dashboard";
  };

  const getIconClass = (href: string, isMobile = false) => {
    return cn(
      "h-6 w-6 flex-shrink-0",
      isActiveLink(href)
        ? "text-primary"
        : "text-muted-foreground group-hover:text-primary/80"
    );
  };

  const links = [
    {
      label: "Home",
      href: "/dashboard",
      icon: <Home className={getIconClass("/dashboard")} />,
      mobileIcon: <Home className={getIconClass("/dashboard", true)} />,
    },
    {
      label: "My Designs",
      href: "/dashboard/designs",
      icon: <Images className={getIconClass("/dashboard/designs")} />,
      mobileIcon: (
        <Images className={getIconClass("/dashboard/designs", true)} />
      ),
    },
    {
      label: "Inspire",
      mobileLabel: "AI Help",
      href: "/dashboard/design-suggestions",
      icon: (
        <WandSparkles
          className={getIconClass("/dashboard/design-suggestions")}
        />
      ),
      mobileIcon: (
        <WandSparkles
          className={getIconClass("/dashboard/design-suggestions", true)}
        />
      ),
    },
  ];

  const renderNotificationsSection = () => {
    return (
      <div className="px-3 py-3">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button
              className={cn(
                "group relative w-full flex items-center justify-center h-10",
                "text-muted-foreground hover:text-primary/90"
              )}
            >
              <BellIcon className="h-5 w-5" />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            align="center"
            className="w-80 p-2"
            sideOffset={12}
          >
            <DropdownMenuItem className="flex items-center">
              <span className="text-sm">No new notifications</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    );
  };

  const renderProfileSection = () => {
    if (!currentUser) {
      return (
        <SidebarLink
          link={{
            label: "Sign In",
            href: "/login",
            icon: <UserCircle className={cn("h-7 w-7 flex-shrink-0")} />,
          }}
          className={cn(
            "group relative h-16 flex items-center justify-center !p-0",
            isActiveLink("/login")
              ? "text-primary font-medium"
              : "text-muted-foreground hover:text-primary/90"
          )}
        />
      );
    }

    return (
      <Link
        href="/dashboard/profile"
        className={cn(
          "group relative w-full flex items-center justify-center h-16",
          "text-muted-foreground hover:text-primary/90"
        )}
      >
        {currentUser.image ? (
          <Image
            src={currentUser.image}
            className="h-8 w-8 rounded-full"
            width={32}
            height={32}
            alt={currentUser.name || "User"}
          />
        ) : (
          <div className="h-10 w-10 rounded-full bg-primary flex items-center justify-center text-white text-base">
            {currentUser.name?.[0]?.toUpperCase() || ""}
          </div>
        )}
      </Link>
    );
  };

  return (
    <SidebarProvider>
      {/* Desktop Sidebar */}
      <div className="hidden md:block h-full ">
        <Sidebar className="w-20 flex-shrink-0">
          <SidebarBody className="justify-between ">
            <div className="flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
              <div className=" flex flex-col gap-1">
                <div className="flex items-center justify-center h-16">
                  <div className="h-10 flex items-center">
                    <Logo
                      width={40}
                      height={40}
                      className="transition-all duration-300"
                    />
                  </div>
                </div>
                {links.map((link, idx) => (
                  <SidebarLink
                    key={idx}
                    link={{
                      ...link,
                      icon: (
                        <div className="flex flex-col items-center gap-1">
                          {link.icon}
                          <span className="text-xs font-medium">
                            {link.label}
                          </span>
                        </div>
                      ),
                    }}
                    className={cn(
                      "group relative h-16 flex items-center justify-center !p-0",
                      "transition-colors duration-200",
                      isActiveLink(link.href)
                        ? "bg-gray-100 text-gray-900 dark:bg-gray-800 dark:text-gray-100"
                        : "text-muted-foreground hover:bg-gray-50 dark:hover:bg-gray-900"
                    )}
                  />
                ))}
              </div>
              <div className="mt-auto flex flex-col">
                {renderNotificationsSection()}
                {renderProfileSection()}
                <MenuSection currentUser={currentUser} />
              </div>
            </div>
          </SidebarBody>
        </Sidebar>
      </div>

      {/* Mobile Top Logo */}
      <div className="md:hidden fixed top-0 left-0 right-0 bg-background border-b border-border flex items-center h-14 px-4 z-[100]">
        <div className="h-10 flex items-center">
          <Logo
            width={40}
            height={40}
            className="transition-all duration-300"
          />
        </div>
      </div>

      {/* Mobile Bottom Navigation */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-black flex items-center justify-between h-16 px-2 z-[100] safe-bottom">
        {/* My Designs */}
        <SidebarLink
          link={{
            ...links[1],
            label: "",
            icon: links[1].mobileIcon,
          }}
          className={cn(
            "group relative h-16 w-14 flex items-center justify-center",
            isActiveLink(links[1].href)
              ? "text-primary"
              : "text-muted-foreground hover:text-primary/90"
          )}
        />

        {/* Inspire */}
        <SidebarLink
          link={{
            ...links[2],
            label: "",
            icon: links[2].mobileIcon,
          }}
          className={cn(
            "group relative h-16 w-14 flex items-center justify-center",
            isActiveLink(links[2].href)
              ? "text-primary"
              : "text-muted-foreground hover:text-primary/90"
          )}
        />

        {/* Home */}
        <SidebarLink
          link={{
            ...links[0],
            label: "",
            icon: (
              <Home
                className={cn(
                  "h-7 w-7",
                  isActiveLink(links[0].href)
                    ? "text-primary"
                    : "text-muted-foreground group-hover:text-primary/80"
                )}
              />
            ),
          }}
          className={cn(
            "group relative h-16 w-14 flex items-center justify-center",
            isActiveLink(links[0].href)
              ? "text-primary"
              : "text-muted-foreground hover:text-primary/90"
          )}
        />

        {/* Notifications Button */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="group relative h-16 w-14 flex items-center justify-center">
              <BellIcon
                className={cn(
                  "h-6 w-6",
                  "text-muted-foreground group-hover:text-primary/90"
                )}
              />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-80 p-2" sideOffset={12}>
            <DropdownMenuItem className="flex items-center">
              <span className="text-sm">No new notifications</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Menu Button */}
        <MobileMenu currentUser={currentUser} />
      </div>
    </SidebarProvider>
  );
}
