"use client";

import React from "react";
import Image from "next/image";
import { Skeleton } from "@/modules/ui/skeleton";
import { useImageCreditsStore } from "@/modules/dashboard/main-features/stores/use-image-credits-store";
import { useSession } from "next-auth/react";
import { PaywallDialog } from "@/modules/payments/components/paywall-dialog";

export function ImageCredits() {
  const { status } = useSession();
  const { credits, isLoading, error, fetchCredits } = useImageCreditsStore();
  const [isDialogOpen, setIsDialogOpen] = React.useState(false);

  React.useEffect(() => {
    if (status === "authenticated" && credits === null) {
      fetchCredits();
    }
  }, [status, credits, fetchCredits]);

  if (status !== "authenticated") {
    return null;
  }

  const handleCreditsClick = () => {
    setIsDialogOpen(true);
  };

  return (
    <>
      <button
        onClick={handleCreditsClick}
        className="flex items-center space-x-2
                 bg-secondary-light dark:bg-secondary-dark 
                 rounded-full px-3 py-1.5 sm:py-1
                 shadow-xs border border-border/80 transition-colors duration-200
                 hover:bg-secondary-light/80 dark:hover:bg-secondary-dark/80"
      >
        <div className="relative w-4 h-4 sm:w-5 sm:h-5">
          <Image
            src="/images/coin.png"
            alt="Credits"
            width={20}
            height={20}
            className="object-contain"
            priority
          />
        </div>
        {isLoading || error ? (
          <Skeleton className="w-6 h-4 sm:w-5 sm:h-5" />
        ) : (
          <span className="text-sm text-foreground font-semibold min-w-[32px] sm:min-w-[48px] text-right">
            {credits ?? "N/A"}
          </span>
        )}
      </button>

      <PaywallDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        title="Top Up Your Credits"
        description="Purchase additional credits to continue creating amazing designs. Choose the package that best suits your needs."
        feature="credits"
        persistDismissal={true}
      />
    </>
  );
}

export function ImageCreditsSkeleton() {
  return (
    <div className="flex items-center space-x-2 bg-secondary-light dark:bg-secondary-dark rounded-full px-3 py-1.5 sm:py-1 shadow-soft transition-colors duration-200">
      <div className="relative w-4 h-4 sm:w-5 sm:h-5">
        <Image
          src="/images/coin.png"
          alt="Credits"
          width={20}
          height={20}
          className="object-contain"
          priority
        />
      </div>
      <Skeleton className="w-8 sm:w-12 h-4 sm:h-5" />
    </div>
  );
}
