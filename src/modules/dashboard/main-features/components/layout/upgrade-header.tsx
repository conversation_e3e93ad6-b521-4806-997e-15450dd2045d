"use client";

import React from "react";
import { useSubscription } from "@/modules/payments/hooks/use-subscription";
import { PaywallDialog } from "@/modules/payments/components/paywall-dialog";
import { Button } from "@/modules/ui/button";
import { useSession } from "next-auth/react";
import { ZapFilled } from "@/modules/dashboard/main-features/components/icons/zap-filled";
import { cn } from "@/lib/utils";

interface UpgradeHeaderProps {
  compactOnMobile?: boolean;
}

export function UpgradeHeader({ compactOnMobile = false }: UpgradeHeaderProps) {
  const { isSubscribed, subscription } = useSubscription();
  const [isDialogOpen, setIsDialogOpen] = React.useState(false);
  const { data: session, status } = useSession();

  const isLoggedIn = status === "authenticated" && session?.user;
  const hasSubscription = isSubscribed || subscription;
  const isPremium = subscription?.packageName
    ?.toLowerCase()
    .includes("premium");

  const handleUpgradeClick = () => {
    setIsDialogOpen(true);
  };

  // Don't render anything if user is not logged in
  if (!isLoggedIn) {
    return null;
  }

  // Don't render if user already has Premium
  if (isPremium) {
    return null;
  }

  return (
    <div className="flex items-center gap-2 sm:gap-4">
      <Button
        size="sm"
        onClick={handleUpgradeClick}
        className={cn(
          "flex items-center gap-1.5",
          "py-5",
          "px-3",
          "bg-purple-500/10",
          "hover:bg-purple-500/20",
          "text-purple-500",
          "rounded-lg",
          "text-sm font-medium",
          "transition-colors duration-200",
          "border border-purple-500/20"
        )}
      >
        <ZapFilled className="h-7 w-7" />
        <span>{hasSubscription ? "Upgrade" : "Upgrade"}</span>
      </Button>

      <PaywallDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        title={hasSubscription ? "Upgrade to Premium" : "Unlock Pro Features"}
        description={
          hasSubscription
            ? "Take your creativity to the next level with Premium! Get 5,000 credits monthly, priority support, and exclusive features."
            : "Take your creativity further with Pro! Enjoy a private gallery, 1,000 credits monthly for high-quality renders, and early access to exciting new features as we roll them out."
        }
        feature={hasSubscription ? "premium-styles" : "pro-features"}
        defaultPlan={hasSubscription ? "premium" : "pro"}
        persistDismissal={true}
      />
    </div>
  );
}
