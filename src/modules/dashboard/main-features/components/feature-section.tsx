import React from "react";
import { motion } from "framer-motion";
import { FeatureCard } from "./features/feature-card";
import { FeatureSection as FeatureSectionType } from "../types/dashboard-types";

interface FeatureSectionProps {
  section: FeatureSectionType;
  index: number;
}

export const FeatureSection: React.FC<FeatureSectionProps> = ({
  section,
  index,
}) => (
  <motion.section
    initial={{ y: 50, opacity: 0 }}
    animate={{ y: 0, opacity: 1 }}
    transition={{ delay: index * 0.1 }}
    className="mb-12"
  >
    <h2 className="text-3xl font-bold mb-2 text-foreground ">
      {section.title}
    </h2>
    <p className="text-muted-foreground mb-6 text-lg">{section.description}</p>
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {section.features.map((feature, featureIndex) => (
        <FeatureCard key={feature.title} feature={feature} />
      ))}
    </div>
  </motion.section>
);
