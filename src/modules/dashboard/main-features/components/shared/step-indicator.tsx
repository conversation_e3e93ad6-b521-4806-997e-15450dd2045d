import { cn } from "@/modules/ui";
import { Check } from "lucide-react";
import React from "react";

interface StepIndicatorProps {
  stepNumber: number;
  title: string;
  isActive: boolean;
  isCompleted: boolean;
  orientation?: "horizontal" | "vertical";
}

export const StepIndicator = ({
  stepNumber,
  title,
  isActive,
  isCompleted,
  orientation = "horizontal",
}: StepIndicatorProps) => {
  return (
    <div
      className={cn(
        orientation === "horizontal"
          ? "flex flex-col items-center text-center gap-2"
          : "flex items-center gap-3",
        isActive ? "opacity-100" : "opacity-60"
      )}
    >
      <div
        className={cn(
          "w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300",
          isCompleted
            ? "bg-violet-600 text-white scale-100"
            : isActive
            ? "bg-violet-500 text-white ring-2 ring-violet-200 ring-offset-2"
            : "bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-500"
        )}
      >
        <div className="flex items-center justify-center">
          {isCompleted ? (
            <Check className="h-4 w-4 stroke-[3]" />
          ) : (
            <span className="text-sm font-medium">{stepNumber}</span>
          )}
        </div>
      </div>
      <span
        className={cn(
          "text-sm font-medium transition-colors duration-200 line-clamp-1",
          isCompleted && "text-violet-600 dark:text-violet-400",
          isActive && !isCompleted && "text-violet-500 dark:text-violet-400",
          !isActive && !isCompleted && "text-gray-400 dark:text-gray-500",
          orientation === "vertical" && "flex-1"
        )}
      >
        {title}
      </span>
    </div>
  );
};
