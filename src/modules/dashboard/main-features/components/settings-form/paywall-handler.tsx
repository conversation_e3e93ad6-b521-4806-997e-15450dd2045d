"use client";

import React, { useState } from "react";
import { PaywallDialog } from "@/modules/payments/components/paywall-dialog";

type PaywallFeature = "generate more images" | "private-images";

interface PaywallDialogContent {
  title: string;
  description: string;
  feature: PaywallFeature;
}

interface PaywallHandlerProps {
  children: (handlers: {
    showPaywall: (feature: "credits" | "privacy") => void;
  }) => React.ReactNode;
}

export function PaywallHandler({ children }: PaywallHandlerProps) {
  const [showPaywall, setShowPaywall] = useState(false);
  const [dialogContent, setDialogContent] = useState<PaywallDialogContent>({
    title: "Get More Credits",
    description:
      "You need 5 credits to generate an image. Upgrade to Pro to create 200 virtual stagings.",
    feature: "generate more images",
  });

  const handleShowPaywall = (feature: "credits" | "privacy") => {
    if (feature === "credits") {
      setDialogContent({
        title: "Get More Credits",
        description:
          "You need 5 credits to generate an image. Upgrade to Pro to create 200 virtual stagings.",
        feature: "generate more images",
      });
    } else if (feature === "privacy") {
      setDialogContent({
        title: "Keep Your Images Private & Secure",
        description:
          "Upgrade to Pro to make your gallery private and control exactly who can see your creations. Perfect for personal projects, client work, or content that needs an extra layer of privacy.",
        feature: "private-images",
      });
    }
    setShowPaywall(true);
  };

  return (
    <>
      {children({
        showPaywall: handleShowPaywall,
      })}
      <PaywallDialog
        open={showPaywall}
        onOpenChange={setShowPaywall}
        title={dialogContent.title}
        description={dialogContent.description}
        feature={dialogContent.feature}
      />
    </>
  );
} 