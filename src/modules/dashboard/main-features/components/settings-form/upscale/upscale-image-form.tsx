"use client";
import React, { useState } from "react";
import { But<PERSON> } from "@/modules/ui/button";
import { ChevronDown } from "lucide-react";
import { useMediaQuery } from "@/modules/hooks/use-media-query";
import useUpscaleFormStore from "@/modules/dashboard/main-features/store/upscale-form-store";
import { useImageCreditsStore } from "@/modules/dashboard/main-features/stores/use-image-credits-store";
import { useCurrentUser } from "@/modules/auth/hooks/use-current-user";
import { useToast } from "@/modules/ui/use-toast";
import { UpscaleFormContent } from "./upscale-form-content";
import { UpscaleMobileDrawer } from "./upscale-mobile-drawer";    
import { useUpscaleFormSteps } from "../../../hooks/use-upscale-form-steps";
import { useUpscaleSubmission } from "../../../hooks/use-upscale-submission";

const UpscaleImageForm: React.FC = () => {
  const store = useUpscaleFormStore();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const { credits } = useImageCreditsStore();
  const isMobile = useMediaQuery("(max-width: 768px)");
  const currentUser = useCurrentUser();
  const { toast } = useToast();
  
  // Form steps management
  const { currentStep, totalSteps, progress, isStepComplete } = useUpscaleFormSteps();
  
  // Submission handler
  const { handleSubmit, isSubmitting } = useUpscaleSubmission();
  
  // Handle image upload
  const handleImageUploaded = (url: string) => {
    const hasEnoughCredits = credits && credits >= 1;
    if (!hasEnoughCredits) {
      toast({
        title: "Insufficient Credits",
        description: "You need 1 credit to upscale an image",
        variant: "destructive",
      });
      return;
    }
    store.setImageUrl(url);
  };

  if (isMobile) {
    return (
      <>
        <UpscaleMobileDrawer
          isOpen={isDrawerOpen}
          setIsOpen={setIsDrawerOpen}
        >
          <UpscaleFormContent
            currentStep={currentStep}
            totalSteps={totalSteps}
            progress={progress}
            isStepComplete={isStepComplete}
            onSubmit={handleSubmit}
            onImageUploaded={handleImageUploaded}
            isSubmitting={isSubmitting}
            isMobile={isMobile}
          />
        </UpscaleMobileDrawer>
        
        <Button
          variant="gradient-primary"
          size="sm"
          shine
          glow
          className="text-[14px] w-full sm:text-base px-6 sm:px-8 py-5 sm:py-6 group"
          onClick={() => setIsDrawerOpen(true)}
        >
          <span>
            {store.imageUrl ? "Edit Settings" : "Upscale Image Settings"}
          </span>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </>
    );
  }

  return (
    <div className="h-full">
      <UpscaleFormContent
        currentStep={currentStep}
        totalSteps={totalSteps}
        progress={progress}
        isStepComplete={isStepComplete}
        onSubmit={handleSubmit}
        onImageUploaded={handleImageUploaded}
        isSubmitting={isSubmitting}
        isMobile={isMobile}
      />
    </div>
  );
};

export default UpscaleImageForm;
