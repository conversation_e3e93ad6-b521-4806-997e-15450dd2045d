"use client";
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/modules/ui/scroll-area";
import { Card, CardContent } from "@/modules/ui/card";
import { Progress } from "@/modules/ui/progress";
import { Badge } from "@/modules/ui/badge";
import { Loader2, Check, Info } from "lucide-react";
import ImageUploader from "../../settings-form/elements/image-uploader";
import useUpscaleFormStore from "../../../store/upscale-form-store";
import { cn } from "@/lib/utils";
import { Button } from "@/modules/ui/button";
import { useFormStatus } from "react-dom";
import { useImageCreditsStore } from "../../../stores/use-image-credits-store";
import { Alert, AlertDescription } from "@/modules/ui/alert";
import Image from "next/image";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/modules/ui/select";
import { <PERSON>lide<PERSON> } from "@/modules/ui/slider";
import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ider,
  TooltipTrigger,
} from "@/modules/ui/tooltip";

interface UpscaleFormContentProps {
  currentStep: number;
  totalSteps: number;
  progress: number;
  isStepComplete: (step: number) => boolean;
  onSubmit: (formData: FormData) => Promise<void>;
  onImageUploaded: (url: string) => void;
  isSubmitting: boolean;
  isMobile: boolean;
}

const SubmitButton = () => {
  const { pending } = useFormStatus();
  const imageUrl = useUpscaleFormStore((state) => state.imageUrl);
  const { credits } = useImageCreditsStore();
  const hasEnoughCredits = credits && credits >= 1;

  return (
    <Button
      type="submit"
      disabled={pending || !imageUrl || !hasEnoughCredits}
      variant="gradient-primary"
      size="sm"
      shine
      glow
      className="text-[15px] w-full sm:text-base px-6 sm:px-8 py-5 sm:py-6 group"
    >
      {pending ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Upscaling...
        </>
      ) : (
        <div className="flex items-center justify-center gap-2">
          <span>
            {hasEnoughCredits ? "Upscale Image" : "Insufficient Credits"}
          </span>
          <div
            className={cn(
              "flex items-center gap-1 px-2 py-0.5 rounded",
              hasEnoughCredits
                ? "bg-primary-foreground/10"
                : "bg-destructive/10 text-destructive"
            )}
          >
            <div className="relative w-4 h-4">
              <Image
                src="/images/coin.png"
                alt="Credits"
                width={16}
                height={16}
                className="object-contain"
                priority
              />
            </div>
            <span>1</span>
          </div>
        </div>
      )}
    </Button>
  );
};

const UpscaleAmountSelect = ({
  value,
  onChange,
}: {
  value: number;
  onChange: (value: number) => void;
}) => (
  <div className="space-y-2">
    <label className="text-sm font-medium">Upscale Amount</label>
    <Select
      onValueChange={(value) => onChange(Number(value))}
      value={value.toString()}
    >
      <SelectTrigger className="w-full">
        <SelectValue placeholder="Select upscale amount" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="1">1x</SelectItem>
        <SelectItem value="1.5">1.5x</SelectItem>
        <SelectItem value="2">2x</SelectItem>
        <SelectItem value="3">3x</SelectItem>
        <SelectItem value="4">4x</SelectItem>
      </SelectContent>
    </Select>
  </div>
);

const CreativitySlider = ({
  value,
  onChange,
}: {
  value: number;
  onChange: (value: number) => void;
}) => (
  <div className="space-y-2">
    <div className="flex items-center">
      <label className="text-sm font-medium">Detail Enhancement</label>
    </div>
    <p className="text-sm text-muted-foreground">
      Controls how much detail the AI adds during upscaling. Higher values
      generate more detailed results with enhanced textures and features.
    </p>
    <div className="space-y-1">
      <Slider
        min={0}
        max={100}
        step={1}
        value={[value]}
        onValueChange={(values) => onChange(values[0])}
      />
      <div className="flex justify-between text-sm text-muted-foreground">
        <span>Basic Upscale</span>
        <span>{value}</span>
        <span>Enhanced Details</span>
      </div>
    </div>
  </div>
);

const ImageStrengthSlider = ({
  value,
  onChange,
}: {
  value: number;
  onChange: (value: number) => void;
}) => (
  <div className="space-y-2">
    <div className="flex items-center">
      <label className="text-sm font-medium">Input Image Strength</label>
    </div>
    <p className="text-sm text-muted-foreground">
      Determines how much the AI relies on your input image. Higher values
      preserve more of the original image&apos;s characteristics.
    </p>
    <div className="space-y-1">
      <Slider
        min={0}
        max={3}
        step={0.01}
        value={[value]}
        onValueChange={(values) => onChange(values[0])}
      />
      <div className="flex justify-between text-sm text-muted-foreground">
        <span>Less Original</span>
        <span>{value.toFixed(2)}</span>
        <span>More Original</span>
      </div>
    </div>
  </div>
);

export const UpscaleFormContent: React.FC<UpscaleFormContentProps> = ({
  currentStep,
  totalSteps,
  progress,
  isStepComplete,
  onSubmit,
  onImageUploaded,
  isSubmitting,
  isMobile,
}) => {
  const store = useUpscaleFormStore();
  const { credits } = useImageCreditsStore();
  const hasEnoughCredits = credits && credits >= 1;

  const renderTooltip = (content: string) => (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Info className="h-4 w-4 ml-1 text-muted-foreground inline-block cursor-help" />
        </TooltipTrigger>
        <TooltipContent>
          <p className="max-w-xs">{content}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );

  return (
    <form action={onSubmit} className="flex flex-col h-full">
      <ScrollArea className="flex-1">
        <div className="space-y-6 px-4 py-4">
          {/* Step 1: Image Upload */}
          <Card
            className={cn(
              "transition-all duration-200 rounded-lg",
              currentStep === 1 ? "ring-1 ring-primary/50 shadow-md" : ""
            )}
          >
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium">Upload Image</h3>
                  {isStepComplete(1) && (
                    <Badge
                      variant="default"
                      className="ml-2 bg-green-500 hover:bg-green-600 w-6 h-6 rounded-full p-0 flex items-center justify-center"
                    >
                      <Check className="h-3 w-3 stroke-[3]" />
                    </Badge>
                  )}
                </div>
                <ImageUploader
                  existingImageUrl={store.imageUrl}
                  onImageUploaded={onImageUploaded}
                  error={store.errors?.imageUrl?.[0]}
                  disabled={!hasEnoughCredits}
                />
                {!hasEnoughCredits && (
                  <Alert variant="destructive" className="mt-2">
                    <AlertDescription className="text-sm flex items-center gap-2">
                      <div className="relative w-4 h-4">
                        <Image
                          src="/images/coin.png"
                          alt="Credits"
                          width={16}
                          height={16}
                          className="object-contain"
                          priority
                        />
                      </div>
                      You need 1 credit to upscale an image
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Step 2: Upscale Settings */}
          <Card
            className={cn(
              "transition-all duration-200 rounded-lg",
              currentStep === 2 ? "ring-1 ring-primary/50 shadow-md" : ""
            )}
          >
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium">Upscale Settings</h3>
                  <span className="text-sm text-muted-foreground">
                    (Optional)
                  </span>
                </div>

                <div className="space-y-6">
                  <div>
                    <UpscaleAmountSelect
                      value={store.upscaleAmount}
                      onChange={store.setUpscaleAmount}
                    />
                    {renderTooltip(
                      "Choose how much to increase the image size"
                    )}
                  </div>

                  <div>
                    <CreativitySlider
                      value={store.creativity}
                      onChange={store.setCreativity}
                    />
                    {renderTooltip(
                      "Higher values will result in more detailed upscaling with enhanced textures and features"
                    )}
                  </div>

                  <div>
                    <ImageStrengthSlider
                      value={store.imageStrength}
                      onChange={store.setImageStrength}
                    />
                    {renderTooltip(
                      "Controls how much the upscaler follows your input image"
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </ScrollArea>

      <div className="mt-auto bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 py-4 px-2 md:p-0 ">
        <SubmitButton />
      </div>
    </form>
  );
}; 