"use client";

import React, { RefObject } from "react";
import { But<PERSON> } from "@/modules/ui/button";
import { Drawer, DrawerContent, DrawerTrigger } from "@/modules/ui/drawer";
import { ChevronDown } from "lucide-react";
import { cn } from "@/modules/ui";

interface MobileFormDrawerProps {
  children: React.ReactNode;
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  keyboardVisible: boolean;
  keyboardHeight: number;
}

export function MobileFormDrawer({
  children,
  isOpen,
  setIsOpen,
  keyboardVisible,
  keyboardHeight,
}: MobileFormDrawerProps) {
  return (
    <Drawer
      open={isOpen}
      onOpenChange={setIsOpen}
      dismissible={true}
    >
      <DrawerTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="text-[14px] w-full sm:text-base px-6 sm:px-8 py-5 sm:py-6 group"
        >
          <span>Settings</span>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </DrawerTrigger>
      <DrawerContent
        className={cn(
          keyboardVisible
            ? "!fixed !inset-0 !m-0 !rounded-none !border-none"
            : "h-[95dvh] max-h-[95dvh]",
          "ios:pb-[env(safe-area-inset-bottom)]",
          "ios:pt-safe"
        )}
      >
        {children}
      </DrawerContent>
    </Drawer>
  );
} 