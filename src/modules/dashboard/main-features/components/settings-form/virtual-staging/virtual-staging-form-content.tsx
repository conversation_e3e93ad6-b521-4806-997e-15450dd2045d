"use client";

import React, { RefObject, useEffect } from "react";
import { Card, CardContent } from "@/modules/ui/card";
import { Alert, AlertDescription } from "@/modules/ui/alert";
import { Check, Loader2, AlertCircle } from "lucide-react";
import { Scroll<PERSON><PERSON> } from "@/modules/ui/scroll-area";
import { But<PERSON> } from "@/modules/ui/button";
import Image from "next/image";
import { cn } from "@/modules/ui";
import ImageUploader from "@/modules/dashboard/main-features/components/settings-form/elements/image-uploader";
import MaskDrawer from "@/modules/dashboard/main-features/components/masking/mask-drawer";
import PromptInput from "@/modules/dashboard/main-features/components/settings-form/elements/prompt-input";
import RoomSelection from "@/modules/dashboard/main-features/components/settings-form/elements/room-selection";
import StyleSelection from "@/modules/dashboard/main-features/components/settings-form/elements/style-selection";
import ExcludeElements from "@/modules/dashboard/main-features/components/settings-form/elements/exclude-elements";
import useVirtualStagingFormStore from "@/modules/dashboard/main-features/store/virtual-staging-form-store";
import { useFormStatus } from "react-dom";
import { useImageCreditsStore } from "@/modules/dashboard/main-features/stores/use-image-credits-store";
import { groupRoomOptions } from "../../../../../../../public/static/rooms";
import { groupStyleOptions } from "../../../../../../../public/static/styles";

interface VirtualStagingFormContentProps {
  isAdvancedMode: boolean;
  currentStep: number;
  totalSteps: number;
  progress: number;
  isStepComplete: (step: number) => boolean;
  setIsAdvancedMode: (value: boolean) => void;
  scrollAreaRef: RefObject<HTMLDivElement>;
  excludeElementsRef: RefObject<HTMLDivElement>;
  keyboardVisible: boolean;
  keyboardHeight: number;
  isMobile: boolean;
  onSubmit: (formData: FormData) => Promise<void>;
  onMaskCreated: (maskUrl: string) => void;
  onMaskUploaded: (maskUrl: string) => void;
  onMaskSelected: (maskUrl: string, isExample?: boolean) => void;
  onImageUploaded: (imageUrl: string) => void;
  selectedExampleMask: string | null;
  onPrivacyToggle?: (checked: boolean) => void;
  onShowPaywall: (feature: "credits" | "privacy") => void;
  isSubmitting?: boolean;
  isCheckingAccess?: boolean;
  hasSubscription?: boolean;
  renderPrivacyToggle?: () => React.ReactNode;
}

const SubmitButton = () => {
  const { pending } = useFormStatus();
  const store = useVirtualStagingFormStore();
  const { credits } = useImageCreditsStore();
  const hasEnoughCredits = credits && credits >= 5;

  return (
    <Button
      type="submit"
      disabled={pending || !store.imageUrl || !hasEnoughCredits}
      variant="gradient-primary"
      size="lg"
      shine
      glow
      className={cn(
        "text-[15px] w-full sm:text-base px-8 py-6 group transition-all duration-200",
        "bg-gradient-to-r from-primary/90 to-primary relative overflow-hidden",
        "hover:from-primary hover:to-primary/90",
        "disabled:from-muted disabled:to-muted disabled:opacity-70",
        pending && "opacity-90"
      )}
    >
      <div className={cn(
        "absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent",
        "transform translate-x-[-200%]",
        pending && "animate-[shimmer_3s_ease-in-out_infinite]"
      )} />
      
      <div className="relative">
        {pending ? (
          <div className="flex items-center justify-center gap-3">
            <Loader2 className="h-5 w-5 animate-spin" />
            <span className="font-medium">Generating your design...</span>
          </div>
        ) : (
          <div className="flex items-center justify-center gap-3">
            <span className="font-medium">
              {hasEnoughCredits ? "Generate Virtual Staging" : "Insufficient Credits"}
            </span>
            <div
              className={cn(
                "flex items-center gap-2 px-3 py-1 rounded-full transition-colors duration-200",
                hasEnoughCredits
                  ? "bg-primary-foreground/10 group-hover:bg-primary-foreground/15"
                  : "bg-destructive/10 text-destructive"
              )}
            >
              <div className="relative w-5 h-5">
                <Image
                  src="/images/coin.png"
                  alt="Credits"
                  width={20}
                  height={20}
                  className="object-contain"
                  priority
                />
              </div>
              <span className="font-medium">5</span>
            </div>
          </div>
        )}
      </div>
    </Button>
  );
};

export const VirtualStagingFormContent: React.FC<
  VirtualStagingFormContentProps
> = ({
  isAdvancedMode,
  currentStep,
  totalSteps,
  progress,
  isStepComplete,
  setIsAdvancedMode,
  scrollAreaRef,
  excludeElementsRef,
  keyboardVisible,
  onSubmit,
  onMaskCreated,
  onMaskUploaded,
  onMaskSelected,
  onImageUploaded,
  selectedExampleMask,
  renderPrivacyToggle,
}) => {
  const store = useVirtualStagingFormStore();
  const { credits } = useImageCreditsStore();
  const hasEnoughCredits = credits && credits >= 5;

  const options = {
    rooms: groupRoomOptions(),
    styles: groupStyleOptions(),
  };

  const getFieldError = (field: string) => {
    return store.errors[field]?.[0];
  };

  const renderFieldError = (error?: string) => {
    if (!error) return null;
    return (
      <div className="text-sm text-destructive flex items-center gap-2 mt-2">
        <div className="w-1 h-1 rounded-full bg-destructive" />
        <span>{error}</span>
      </div>
    );
  };

  const maskDrawerKey = React.useMemo(() => {
    return store.isEditingMask
      ? "editing"
      : `${store.imageUrl}-${store.maskUrl}-${store.isExampleMask}`;
  }, [store.imageUrl, store.maskUrl, store.isExampleMask, store.isEditingMask]);

  const handleMaskCreated = React.useCallback(
    (maskUrl: string) => {
      if (store.isEditingMask) {
        store.setIsMaskSaved(true);
      }
      onMaskCreated(maskUrl);
    },
    [store, onMaskCreated]
  );

  const handleMaskUploaded = React.useCallback(
    (maskUrl: string) => {
      if (store.isEditingMask) {
        store.setIsMaskSaved(true);
      }
      onMaskUploaded(maskUrl);
    },
    [store, onMaskUploaded]
  );

  useEffect(() => {
    if (keyboardVisible && scrollAreaRef.current) {
      const activeElement = document.activeElement;
      if (activeElement instanceof HTMLElement) {
        activeElement.scrollIntoView({
          behavior: "smooth",
          block: "center",
        });
      }
    }
  }, [keyboardVisible]);

  const getStepTitle = (step: number) => {
    switch (step) {
      case 1:
        return "Upload Room Photo";
      case 2:
        return "Define Staging Areas";
      case 3:
        return isAdvancedMode ? "Custom Prompt" : "Room & Style";
      case 4:
        return "Additional Settings";
      default:
        return "Upload Room Photo";
    }
  };

  return (
    <form action={onSubmit} className="flex flex-col h-full">
      {/* Progress Bar */}
      <div className="px-4 pt-4 pb-2">
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>Step {currentStep} of {totalSteps}</span>
            <span>{getStepTitle(currentStep)}</span>
          </div>
          <div className="h-2 bg-muted rounded-full overflow-hidden">
            <div 
              className="h-full bg-primary transition-all duration-300 rounded-full"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>
      </div>
      
      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-h-0">
        <ScrollArea ref={scrollAreaRef} className="flex-1">
          <div className="p-4 space-y-6">
            {/* Step Cards */}
            <div className="space-y-4 transition-all duration-300">
              {[1, 2, 3, 4].map((step) => (
                <div
                  key={step}
                  className={cn(
                    "transition-all duration-300 transform",
                    step < currentStep ? "opacity-90 translate-y-0" : "",
                    step > currentStep ? "opacity-80 translate-y-0" : "",
                    step === currentStep ? "opacity-100 translate-y-0 shadow-lg rounded-lg" : ""
                  )}
                >
                  <Card
                    className={cn(
                      "transition-all duration-200 border-border/10 backdrop-blur-sm rounded-lg",
                      "group",
                      step === currentStep 
                        ? "ring-1 ring-primary/50 shadow-md" 
                        : isStepComplete(step)
                        ? "hover:ring-1 hover:ring-primary/30 hover:shadow-sm"
                        : "hover:shadow-sm",
                      !store.imageUrl && step > 1 && "opacity-90"
                    )}
                  >
                    <CardContent className="p-6 space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={cn(
                            "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",
                            "transition-all duration-200",
                            step === currentStep 
                              ? "bg-primary/90 text-primary-foreground" 
                              : isStepComplete(step)
                              ? "bg-green-500/80 text-white group-hover:bg-green-500/90"
                              : "bg-muted text-muted-foreground",
                            step === currentStep && store.errors && Object.keys(store.errors).length > 0 
                              && "bg-destructive/90 text-destructive-foreground"
                          )}>
                            {isStepComplete(step) ? (
                              <Check className="h-4 w-4 transition-opacity duration-200" />
                            ) : step === currentStep && store.errors && Object.keys(store.errors).length > 0 ? (
                              <AlertCircle className="h-4 w-4 transition-opacity duration-200" />
                            ) : (
                              <span className="transition-opacity duration-200">{step}</span>
                            )}
                          </div>
                          <h3 className={cn(
                            "font-medium text-lg transition-colors duration-200",
                            step === currentStep 
                              ? "text-foreground" 
                              : isStepComplete(step)
                              ? "text-foreground/90 group-hover:text-foreground"
                              : "text-foreground/80"
                          )}>
                            {getStepTitle(step)}
                          </h3>
                        </div>
                        
                        {step === 3 && (
                          <Button
                            type="button"
                            variant="outline" 
                            size="sm"
                            className="text-xs gap-1.5 h-8"
                            onClick={() => setIsAdvancedMode(!isAdvancedMode)}
                          >
                            {isAdvancedMode ? (
                              <>Basic Mode</>
                            ) : (
                              <>Advanced Mode</>
                            )}
                          </Button>
                        )}
                      </div>
                      
                      <div className={cn(
                        "transition-all duration-200",
                        step === currentStep ? "opacity-100" : "opacity-85 group-hover:opacity-95"
                      )}>
                        {/* Step Content */}
                        {step === 1 && (
                          <>
                            <ImageUploader
                              existingImageUrl={store.imageUrl}
                              onImageUploaded={onImageUploaded}
                              onMaskSelected={onMaskSelected}
                              error={getFieldError("imageUrl")}
                              disabled={!hasEnoughCredits}
                              context="staging"
                            />
                            {renderFieldError(getFieldError("imageUrl"))}
                            {!hasEnoughCredits && (
                              <Alert variant="destructive">
                                <AlertDescription className="text-sm flex items-center gap-2">
                                  <div className="relative w-4 h-4">
                                    <Image
                                      src="/images/coin.png"
                                      alt="Credits"
                                      width={16}
                                      height={16}
                                      className="object-contain"
                                      priority
                                    />
                                  </div>
                                  You need 5 credits to use this feature
                                </AlertDescription>
                              </Alert>
                            )}
                          </>
                        )}
                        
                        {step === 2 && (
                          <>
                            <MaskDrawer
                              onMaskCreated={handleMaskCreated}
                              onMaskUploaded={handleMaskUploaded}
                              inputImageUrl={store.imageUrl || ""}
                              invertMask={true}
                              existingMaskUrl={store.maskUrl}
                              error={getFieldError("maskUrl")}
                              disabled={!hasEnoughCredits || !store.imageUrl}
                              isExampleMask={store.isExampleMask}
                              selectedExampleMask={selectedExampleMask}
                              key={maskDrawerKey}
                            />
                            <Alert variant="default" className="border-border/80 mt-4">
                              <AlertDescription className="text-sm">
                                Tip: Leave windows, doors, and ceilings unmarked for best results
                              </AlertDescription>
                            </Alert>
                          </>
                        )}
                        
                        {step === 3 && (
                          <div className="space-y-6">
                            {isAdvancedMode ? (
                              <>
                                <div className="mb-4">
                                  <Alert variant="default" className="border-border/80">
                                    <AlertDescription className="text-sm">
                                      Describe your desired room design in detail. Include style, colors, furniture, and arrangement preferences.
                                    </AlertDescription>
                                  </Alert>
                                </div>
                                <PromptInput
                                  prompt={store.prompt}
                                  setPrompt={store.setPrompt}
                                  error={getFieldError("prompt")}
                                />
                              </>
                            ) : (
                              <>
                                <RoomSelection
                                  isPromptActive={false}
                                  room={store.room}
                                  setRoom={store.setRoom}
                                  groupedRoomOptions={options.rooms}
                                  error={getFieldError("room")}
                                />
                                <StyleSelection
                                  isPromptActive={false}
                                  style={store.style}
                                  setStyle={store.setStyle}
                                  groupedStyleOptions={options.styles}
                                  error={getFieldError("style")}
                                />
                              </>
                            )}
                          </div>
                        )}
                        
                        {step === 4 && (
                          <div ref={excludeElementsRef}>
                            <ExcludeElements
                              excludedElements={store.excludedElements}
                              setExcludedElements={(value: string | undefined) => {
                                store.setExcludedElements(value);
                                store.validate();
                              }}
                              context="virtual-staging"
                            />
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ))}
            </div>
          </div>
        </ScrollArea>

        {/* Submit Button Area */}
        <div
          className={cn(
            "bottom-0 left-0 right-0",
            "p-4 sm:p-6 backdrop-blur-lg ",
            "border-t border-border/10 shadow-lg",
            "safe-area-bottom",
            keyboardVisible && "hidden"
          )}
        >
          <div className="flex flex-col  gap-4 max-w-3xl mx-auto">
            {renderPrivacyToggle ? (
              renderPrivacyToggle()
            ) : null}
            <SubmitButton />
          </div>
        </div>
      </div>
    </form>
  );
};
