"use client";

import React, { useRef, useCallback, useMemo, useState } from "react";
import useVirtualStagingFormStore from "@/modules/dashboard/main-features/store/virtual-staging-form-store";
import { useMediaQuery } from "@/modules/hooks/use-media-query";
import { useMobileDrawer } from "../../../hooks/use-mobile-drawer";
import { useFormSteps } from "../../../hooks/use-form-steps";
import { VirtualStagingFormContent } from "./virtual-staging-form-content";
import { MobileFormDrawer } from "./mobile-form-drawer";
import { PaywallHandler } from "../paywall-handler";
import { PrivacyToggle } from "../privacy-toggle";
import { useVirtualStagingSubmission } from "../../../hooks/use-virtual-staging-submission";

// Separate component that uses hooks and receives paywall callback as a prop
const VirtualStagingFormWrapper = ({ 
  onShowPaywall 
}: { 
  onShowPaywall: (feature: "credits" | "privacy") => void 
}) => {
  const [isAdvancedMode, setIsAdvancedModeState] = useState(false);
  const [selectedExampleMask, setSelectedExampleMask] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const store = useVirtualStagingFormStore();
  const isMobile = useMediaQuery("(max-width: 768px)");
  
  // Refs for mobile keyboard handling
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const excludeElementsRef = useRef<HTMLDivElement>(null);
  
  // Mobile drawer state management
  const { isDrawerOpen, setIsDrawerOpen, keyboardVisible, keyboardHeight } = useMobileDrawer({
    scrollAreaRef,
    excludeElementsRef,
  });

  // Form steps management
  const { currentStep, totalSteps, progress, isStepComplete } = useFormSteps({
    isAdvancedMode,
  });

  // Handle submission
  const { handleSubmit } = useVirtualStagingSubmission({
    onShowPaywall: () => onShowPaywall("credits")
  });

  // Wrap the submit handler to manage loading state
  const handleFormSubmit = useCallback(async (formData: FormData) => {
    setIsSubmitting(true);
    try {
      await handleSubmit(formData);
    } finally {
      setIsSubmitting(false);
    }
  }, [handleSubmit]);

  // Event handlers for form elements
  const handleImageUploaded = useCallback((imageUrl: string) => {
    store.setImageUrl(imageUrl);
  }, [store]);

  const handleMaskSelected = useCallback((maskUrl: string, isExample: boolean = false) => {
    if (store.maskUrl !== maskUrl) {
      store.setMaskUrl(maskUrl);
      store.setIsExampleMask(isExample);

      if (isExample && selectedExampleMask !== maskUrl) {
        setSelectedExampleMask(maskUrl);
      }
    }
  }, [store, selectedExampleMask]);

  const handleMaskCreated = useCallback((maskUrl: string) => {
    if (store.maskUrl !== maskUrl) {
      store.setMaskUrl(maskUrl);
    }
  }, [store]);

  const handleMaskUploaded = useCallback((maskUrl: string) => {
    if (store.maskUrl !== maskUrl) {
      store.setMaskUrl(maskUrl);
    }
  }, [store]);

  // Function to update both local state and store state for advanced mode
  const toggleAdvancedMode = useCallback(() => {
    const newValue = !isAdvancedMode;
    setIsAdvancedModeState(newValue);
    
    // Ensure store has setIsAdvancedMode method
    if (store.setIsAdvancedMode) {
      store.setIsAdvancedMode(newValue);
    } else {
      console.warn("useVirtualStagingFormStore does not have setIsAdvancedMode method.");
    }

    // Clear irrelevant fields and re-validate
    if (newValue) {
      // Switched TO advanced: clear room/style
      store.setRoom(undefined);
      store.setStyle(undefined);
    } else {
      // Switched TO basic: clear prompt
      store.setPrompt("");
    }
    // Assuming store has a validation method
    if (store.validate) {
      store.validate(); 
    }

  }, [isAdvancedMode, store, setIsAdvancedModeState]);

  // Clean up on unmount
  React.useEffect(() => {
    return () => {
      setSelectedExampleMask(null);
    };
  }, []);

  // Privacy toggle component
  const renderPrivacyToggle = useCallback(() => (
    <PrivacyToggle onShowPaywall={() => onShowPaywall("privacy")} />
  ), [onShowPaywall]);

  // Common props for form
  const formProps = {
    isAdvancedMode,
    currentStep,
    totalSteps,
    progress,
    isStepComplete,
    setIsAdvancedMode: toggleAdvancedMode,
    scrollAreaRef,
    excludeElementsRef,
    keyboardVisible,
    keyboardHeight,
    isMobile,
    onMaskCreated: handleMaskCreated,
    onMaskUploaded: handleMaskUploaded,
    onMaskSelected: handleMaskSelected,
    onImageUploaded: handleImageUploaded,
    selectedExampleMask,
    onShowPaywall,
    onSubmit: handleFormSubmit,
    isSubmitting,
    renderPrivacyToggle
  };

  if (isMobile) {
    return (
      <MobileFormDrawer
        isOpen={isDrawerOpen}
        setIsOpen={setIsDrawerOpen}
        keyboardVisible={keyboardVisible}
        keyboardHeight={keyboardHeight}
      >
        <VirtualStagingFormContent {...formProps} />
      </MobileFormDrawer>
    );
  }

  return (
    <div className="h-full flex flex-col overflow-hidden">
      <VirtualStagingFormContent {...formProps} />
    </div>
  );
};

// Main component that passes paywall callback
const VirtualStagingSettingsForm = () => {
  return (
    <PaywallHandler>
      {({ showPaywall }) => (
        <VirtualStagingFormWrapper onShowPaywall={showPaywall} />
      )}
    </PaywallHandler>
  );
};

export default React.memo(VirtualStagingSettingsForm);
