"use client";

import React, { useState, useEffect } from "react";
import { Switch } from "@/modules/ui/switch";
import { Label } from "@/modules/ui/label";
import { usePrivacySettings } from "@/modules/dashboard/main-features/store/privacy-settings-store";
import { useSubscription } from "@/modules/payments/hooks/use-subscription";
import { useSession } from "next-auth/react";
import { useToast } from "@/modules/ui/use-toast";
import { checkPrivacyAccess } from "@/modules/dashboard/main-features/actions/check-privacy-access";
import { cn } from "@/modules/ui";
import { Globe, Lock } from "lucide-react";

interface PrivacyToggleProps {
  onShowPaywall: () => void;
}

export function PrivacyToggle({ onShowPaywall }: PrivacyToggleProps) {
  const [isCheckingAccess, setIsCheckingAccess] = useState(false);
  const [canTogglePrivate, setCanTogglePrivate] = useState(false);
  const { data: session } = useSession();
  const { toast } = useToast();
  const { isSubscribed, subscription } = useSubscription();
  const hasSubscription = Boolean(isSubscribed || subscription);
  const {
    state: { isPublic },
    setIsPublic,
  } = usePrivacySettings();

  useEffect(() => {
    const checkAccess = async () => {
      if (session?.user?.id) {
        setIsCheckingAccess(true);
        try {
          const { canTogglePrivate: hasAccess, error } = await checkPrivacyAccess(session.user.id);
          setCanTogglePrivate(hasAccess);

          if (!hasAccess && !hasSubscription) {
            setIsPublic(true);
          }

          if (error) {
            toast({
              variant: "destructive",
              title: "Error",
              description: error,
            });
          }
        } catch (error) {
          console.error("Error checking subscription status:", error);
          setCanTogglePrivate(false);
          if (!hasSubscription) {
            setIsPublic(true);
          }
          toast({
            variant: "destructive",
            title: "Error",
            description: "Failed to check subscription status",
          });
        } finally {
          setIsCheckingAccess(false);
        }
      }
    };

    if (session?.user?.id) {
      checkAccess();
    }
  }, [session?.user?.id, hasSubscription, toast, setIsPublic]);

  const handlePrivacyToggle = async (checked: boolean) => {
    if (isCheckingAccess) {
      toast({
        variant: "destructive",
        title: "Please wait",
        description: "We're checking your subscription status",
      });
      return;
    }

    if (!hasSubscription && checked) {
      onShowPaywall();
      return;
    }

    if (hasSubscription) {
      try {
        setIsPublic(!checked);
        toast({
          title: "Success",
          description: `Images will now be ${checked ? "private" : "public"}`,
        });
      } catch (error) {
        console.error("Error toggling privacy:", error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to update privacy settings",
        });
        setIsPublic(checked);
      }
    }
  };

  return (
    <div className="flex flex-row items-center justify-between gap-4">
      <Label className="text-sm font-medium text-muted-foreground">
        Gallery Visibility
      </Label>

      <div className="flex items-center justify-end">
        <div
          className={cn(
            "flex items-center gap-2 rounded-full transition-all duration-200",
            "px-4 py-2",
            hasSubscription
              ? "bg-primary/5 border-primary/10 hover:bg-primary/10"
              : "bg-muted border-muted/20 hover:bg-muted/80"
          )}
        >
          <div className="flex items-center gap-2">
            {isPublic ? (
              <Globe className="h-4 w-4 text-muted-foreground" />
            ) : (
              <Lock className="h-4 w-4 text-primary" />
            )}
            <span className="text-sm font-medium whitespace-nowrap">
              {isPublic ? "Public Gallery" : "Private Gallery"}
            </span>
          </div>
          <div className="relative">
            <Switch
              checked={!isPublic}
              onCheckedChange={handlePrivacyToggle}
              disabled={isCheckingAccess}
              className={cn(
                "data-[state=checked]:bg-primary",
                isCheckingAccess && "cursor-wait opacity-50"
              )}
            />
            {!hasSubscription && (
              <Lock className="h-3.5 w-3.5 absolute -right-1 -bottom-1 text-muted-foreground" />
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 