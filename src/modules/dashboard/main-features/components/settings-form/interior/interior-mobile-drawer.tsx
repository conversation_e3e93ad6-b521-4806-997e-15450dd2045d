"use client";

import React from "react";
import { Drawer, DrawerContent } from "@/modules/ui/drawer";
import { cn } from "@/modules/ui";

interface InteriorMobileDrawerProps {
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  keyboardVisible?: boolean;
  keyboardHeight?: number;
  children: React.ReactNode;
}

export const InteriorMobileDrawer: React.FC<InteriorMobileDrawerProps> = ({
  isOpen,
  setIsOpen,
  keyboardVisible = false,
  keyboardHeight = 0,
  children,
}) => {
  return (
    <Drawer open={isOpen} onOpenChange={setIsOpen} dismissible={true}>
      <DrawerContent
        className={cn(
          "h-[85vh]",
          keyboardVisible && "h-[calc(100vh-var(--safe-area-inset-top)-var(--safe-area-inset-bottom))]"
        )}
        style={
          keyboardVisible
            ? {
                height: `calc(100vh - ${keyboardHeight}px - var(--safe-area-inset-top) - var(--safe-area-inset-bottom))`,
                transition: "height 150ms ease-out",
              }
            : undefined
        }
      >
        <div className="px-4 py-2 border-b"></div>
        <div className="flex-1 h-[calc(85vh-30px)]">{children}</div>
      </DrawerContent>
    </Drawer>
  );
}; 