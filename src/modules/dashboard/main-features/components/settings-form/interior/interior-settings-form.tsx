"use client";
import React, { useRef, useState } from "react";
import { But<PERSON> } from "@/modules/ui/button";
import useInteriorFormStore from "@/modules/dashboard/main-features/store/interior-form-store";
import { useImageCreditsStore } from "@/modules/dashboard/main-features/stores/use-image-credits-store";
import { useMediaQuery } from "@/modules/hooks/use-media-query";
import { ChevronDown } from "lucide-react";
import { groupRoomOptions } from "../../../../../../../public/static/rooms";
import { groupStyleOptions } from "../../../../../../../public/static/styles";
import { PaywallDialog } from "@/modules/payments/components/paywall-dialog";
import { InteriorFormContent } from "./interior-form-content";
import { InteriorMobileDrawer } from "./interior-mobile-drawer";
import { useInteriorFormSteps } from "../../../hooks/use-interior-form-steps";
import { useInteriorMobileDrawer } from "../../../hooks/use-interior-mobile-drawer";
import { useInteriorSubmission } from "../../../hooks/use-interior-submission";

const InteriorSettingsForm: React.FC = () => {
  const store = useInteriorFormStore();
  const [showPaywall, setShowPaywall] = useState(false);
  const { credits } = useImageCreditsStore();
  const isMobile = useMediaQuery("(max-width: 768px)");
  
  // Use isPromptActive directly from the store instead of a separate state
  const isAdvancedMode = store.isPromptActive;
  
  // Update the store's isPromptActive
  const setIsAdvancedMode = (value: boolean) => {
    store.setIsPromptActive(value);
  };
  
  // Refs for mobile keyboard handling
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  
  // Get room and style options
  const groupedRoomOptions = groupRoomOptions();
  const groupedStyleOptions = groupStyleOptions();
  
  // Mobile drawer state management
  const { isDrawerOpen, setIsDrawerOpen, keyboardVisible, keyboardHeight } = useInteriorMobileDrawer({
    scrollAreaRef,
  });
  
  // Form steps management
  const { currentStep, totalSteps, progress, isStepComplete } = useInteriorFormSteps({
    isAdvancedMode,
  });
  
  // Submission handler
  const showPaywallHandler = (feature: "credits" | "privacy") => {
    setShowPaywall(true);
  };
  
  const { handleSubmit, isSubmitting } = useInteriorSubmission({
    onShowPaywall: showPaywallHandler
  });
  
  // Event handlers for form elements
  const handleImageUploaded = (imageUrl: string) => {
    const hasEnoughCredits = credits && credits >= 1;
    if (!hasEnoughCredits) {
      showPaywallHandler("credits");
      return;
    }
    store.setImageUrl(imageUrl);
  };

  if (isMobile) {
    return (
      <>
        <InteriorMobileDrawer
          isOpen={isDrawerOpen}
          setIsOpen={setIsDrawerOpen}
          keyboardVisible={keyboardVisible}
          keyboardHeight={keyboardHeight}
        >
          <InteriorFormContent
            isAdvancedMode={isAdvancedMode}
            currentStep={currentStep}
            totalSteps={totalSteps}
            progress={progress}
            isStepComplete={isStepComplete}
            setIsAdvancedMode={setIsAdvancedMode}
            scrollAreaRef={scrollAreaRef}
            keyboardVisible={keyboardVisible}
            isMobile={isMobile}
            onSubmit={handleSubmit}
            onImageUploaded={handleImageUploaded}
            isSubmitting={isSubmitting}
            onShowPaywall={showPaywallHandler}
            groupedRoomOptions={groupedRoomOptions}
            groupedStyleOptions={groupedStyleOptions}
          />
        </InteriorMobileDrawer>
        
        <Button
          variant="gradient-primary"
          size="sm"
          shine
          glow
          className="text-[14px] w-full sm:text-base px-6 sm:px-8 py-5 sm:py-6 group"
          onClick={() => setIsDrawerOpen(true)}
        >
          <span>
            {store.imageUrl ? "Edit Interior Design" : "Interior Design Settings"}
          </span>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
        
        <PaywallDialog
          open={showPaywall}
          onOpenChange={setShowPaywall}
          title="Get More Credits"
          description="You need 1 credit to generate an image. Upgrade to Pro to create 200 interior designs."
          feature="generate more images"
        />
      </>
    );
  }

  return (
    <div className="h-full">
      <InteriorFormContent
        isAdvancedMode={isAdvancedMode}
        currentStep={currentStep}
        totalSteps={totalSteps}
        progress={progress}
        isStepComplete={isStepComplete}
        setIsAdvancedMode={setIsAdvancedMode}
        scrollAreaRef={scrollAreaRef}
        isMobile={isMobile}
        onSubmit={handleSubmit}
        onImageUploaded={handleImageUploaded}
        isSubmitting={isSubmitting}
        onShowPaywall={showPaywallHandler}
        groupedRoomOptions={groupedRoomOptions}
        groupedStyleOptions={groupedStyleOptions}
      />
      
      <PaywallDialog
        open={showPaywall}
        onOpenChange={setShowPaywall}
        title="Get More Credits"
        description="You need 1 credit to generate an image. Upgrade to Pro to create 200 interior designs."
        feature="generate more images"
      />
    </div>
  );
};

export default InteriorSettingsForm;
