import React from "react";
import { <PERSON>lide<PERSON> } from "@/modules/ui/slider";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/modules/ui/tooltip";
import { Info } from "lucide-react";

interface CreativitySliderProps {
  value: number;
  onChange: (value: number) => void;
  className?: string;
}

const CreativitySlider = ({
  value,
  onChange,
  className,
}: CreativitySliderProps) => {
  return (
    <div className={className}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium">Creativity Level</label>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <Info className="h-4 w-4 text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <p>Controls how creative the AI can be with your image:</p>
                <ul className="list-disc ml-4 mt-1 text-sm">
                  <li>
                    Lower values (30-50): More realistic, closer to original
                  </li>
                  <li>
                    Higher values (70-100): More artistic, creative changes
                  </li>
                  <li>Default (50): Balanced results for most images</li>
                </ul>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <span className="text-sm text-muted-foreground">{value}</span>
      </div>
      <Slider
        value={[value]}
        onValueChange={(values) => onChange(values[0])}
        min={0}
        max={100}
        step={1}
        className="my-4"
      />
      <p className="text-sm text-muted-foreground mt-1">
        {value < 40
          ? "Conservative changes, highly realistic"
          : value < 60
          ? "Balanced creativity (Recommended)"
          : "More artistic freedom, creative interpretation"}
      </p>
    </div>
  );
};

export default CreativitySlider;
