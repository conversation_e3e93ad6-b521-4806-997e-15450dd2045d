import React from "react";
import { Slider } from "@/modules/ui/slider";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/modules/ui/tooltip";
import { Info } from "lucide-react";

interface ConditionScaleSliderProps {
  value: number;
  onChange: (value: number) => void;
  className?: string;
}

const ConditionScaleSlider = ({
  value,
  onChange,
  className,
}: ConditionScaleSliderProps) => {
  return (
    <div className={className}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium">Image Strength</label>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <Info className="h-4 w-4 text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <p>Controls how much of the original image to preserve:</p>
                <ul className="list-disc ml-4 mt-1 text-sm">
                  <li>Lower values (0.3-0.5): More dramatic changes</li>
                  <li>Higher values (0.7-0.9): Subtle refinements</li>
                  <li>Default (0.7): Good balance for most images</li>
                </ul>
                <p className="mt-1 text-sm">
                  Tip: Use higher values for minor improvements, lower for major
                  changes
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <span className="text-sm text-muted-foreground">
          {value.toFixed(1)}
        </span>
      </div>
      <Slider
        value={[value]}
        onValueChange={(values) => onChange(values[0])}
        min={0.1}
        max={0.9}
        step={0.1}
        className="my-4"
      />
      <p className="text-sm text-muted-foreground mt-1">
        {value < 0.4
          ? "Major transformations, less preservation"
          : value < 0.6
          ? "Balanced changes"
          : value < 0.8
          ? "Subtle improvements (Recommended)"
          : "Very subtle refinements"}
      </p>
    </div>
  );
};

export default ConditionScaleSlider;
