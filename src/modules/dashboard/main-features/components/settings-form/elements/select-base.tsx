"use client";

import React from "react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/modules/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/modules/ui/tooltip";
import { Portal } from "@radix-ui/react-portal";
import { cn } from "@/modules/ui/utils/cn";

interface Option {
  value: string;
  label: string;
  isSubcategoryLabel?: boolean;
  tooltip?: string;
}

interface GroupedOptions {
  [key: string]: Option[];
}

interface SelectBaseProps {
  value: string | undefined;
  onValueChange: (value: string | undefined) => void;
  groupedOptions: GroupedOptions;
  placeholder: string;
  disabled?: boolean;
  error?: string;
  className?: string;
}

export const SelectBase = ({
  value,
  onValueChange,
  groupedOptions,
  placeholder,
  disabled,
  error,
  className,
}: SelectBaseProps) => {
  return (
    <Select disabled={disabled} value={value} onValueChange={onValueChange}>
      <SelectTrigger className={className}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <Portal>
        <SelectContent
          className="max-h-[300px] overflow-y-auto z-[9999]"
          position="popper"
          sideOffset={4}
        >
          {Object.entries(groupedOptions).map(([group, options]) => (
            <SelectGroup key={group}>
              <SelectLabel
                className={cn(
                  "flex items-center px-1.5 py-2 text-xs font-medium",
                  "text-muted-foreground/80 bg-muted/40 sticky top-0",
                  "border-y border-border/20",
                  "z-10 backdrop-blur-[2px]"
                )}
              >
                <div className="h-px flex-grow bg-border/40 mr-1.5" />
                {group}
                <div className="h-px flex-grow bg-border/40 ml-1.5" />
              </SelectLabel>
              <div className="relative bg-gradient-to-b from-muted/10 to-transparent">
                {options.map((option) =>
                  option.isSubcategoryLabel ? (
                    <SelectLabel
                      key={option.value}
                      className={cn(
                        "py-1.5 px-1.5 text-xs font-medium",
                        "text-muted-foreground/70",
                        "bg-muted/20"
                      )}
                    >
                      {option.label}
                    </SelectLabel>
                  ) : option.tooltip ? (
                    <TooltipProvider key={option.value}>
                      <Tooltip delayDuration={300}>
                        <TooltipTrigger asChild>
                          <SelectItem
                            value={option.value}
                            className={cn(
                              "relative flex w-full cursor-default select-none items-center",
                              "py-1.5 pl-4 pr-2 text-sm outline-none",
                              "focus:bg-accent/50 focus:text-accent-foreground",
                              "data-[highlighted]:bg-accent/50",
                              "ml-1 transition-colors duration-150"
                            )}
                          >
                            {option.label}
                          </SelectItem>
                        </TooltipTrigger>
                        <Portal>
                          <TooltipContent
                            side="right"
                            align="center"
                            className="z-[9999] w-[250px] rounded-md border border-border/80 bg-popover px-2.5 py-3 text-popover-foreground shadow-md"
                            sideOffset={5}
                          >
                            <p className="text-sm whitespace-pre-line leading-relaxed">
                              {option.tooltip}
                            </p>
                          </TooltipContent>
                        </Portal>
                      </Tooltip>
                    </TooltipProvider>
                  ) : (
                    <SelectItem
                      key={option.value}
                      value={option.value}
                      className={cn(
                        "relative flex w-full cursor-default select-none items-center",
                        "py-1.5 pl-4 pr-2 text-sm outline-none",
                        "focus:bg-accent/50 focus:text-accent-foreground",
                        "data-[highlighted]:bg-accent/50",
                        "ml-1 transition-colors duration-150"
                      )}
                    >
                      {option.label}
                    </SelectItem>
                  )
                )}
              </div>
            </SelectGroup>
          ))}
        </SelectContent>
      </Portal>
    </Select>
  );
};
