"use client";
import React, { useCallback, useState, useEffect, useRef } from "react";
import { useDropzone } from "react-dropzone";
import {
  handleImageUpload,
  UploadError,
} from "@/modules/dashboard/main-features/actions/upload-image";
import { But<PERSON> } from "@/modules/ui/button";
import { Loader2, Upload, Image as ImageIcon } from "lucide-react";
import Image from "next/image";
import { cn } from "@/modules/ui";
import { ScrollArea, ScrollBar } from "@/modules/ui/scroll-area";

interface ExampleImage {
  url: string;
  alt: string;
  category: string;
  maskUrl?: string;
}

const getExampleImages = (context?: string): ExampleImage[] => {
  switch (context) {
    case "interior":
      return [
        {
          url: "https://your-interior-living-room-url.jpg",
          alt: "Modern Living Room",
          category: "Living Room",
          maskUrl: "https://your-interior-living-room-mask-url.jpg",
        },
        {
          url: "https://your-interior-bedroom-url.jpg",
          alt: "Cozy Bedroom",
          category: "Bedroom",
          maskUrl: "https://your-interior-bedroom-mask-url.jpg",
        },
        {
          url: "https://your-interior-kitchen-url.jpg",
          alt: "Contemporary Kitchen",
          category: "Kitchen",
          maskUrl: "https://your-interior-kitchen-mask-url.jpg",
        },
        {
          url: "https://your-interior-bathroom-url.jpg",
          alt: "Luxury Bathroom",
          category: "Bathroom",
          maskUrl: "https://your-interior-bathroom-mask-url.jpg",
        },
      ];
    case "staging":
      return [
        {
          url: "https://imagedelivery.net/QnAHiMddHGu5qJ9DvFs5Lw/ebfac9dc-c7ed-46d7-231d-b57fade95e00/public",
          alt: "Empty Living Room",
          category: "Living Room",
          maskUrl:
            "https://imagedelivery.net/QnAHiMddHGu5qJ9DvFs5Lw/f80d727a-6f21-4958-b53c-fc7898e4b000/public",
        },
      ];
    default:
      return [];
  }
};

interface ExampleImagesProps {
  onSelect: (url: string) => void;
  context?: string;
  disabled?: boolean;
}

const ExampleImages: React.FC<ExampleImagesProps> = ({
  onSelect,
  context,
  disabled,
}) => {
  const examples = getExampleImages(context);

  if (examples.length === 0) return null;

  const handleExampleClick = (
    e: React.MouseEvent<HTMLButtonElement>,
    url: string
  ) => {
    e.preventDefault();
    onSelect(url);
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center gap-2">
        <ImageIcon className="h-4 w-4" />
        <span className="text-sm font-medium">Example Images</span>
      </div>
      <ScrollArea className="w-full whitespace-nowrap">
        <div className="flex gap-2 pb-4">
          {examples.map((example, index) => (
            <button
              key={index}
              onClick={(e) => handleExampleClick(e, example.url)}
              disabled={disabled}
              type="button"
              className={cn(
                "relative group rounded-lg overflow-hidden focus:outline-none focus:ring-2 focus:ring-primary",
                "transition-opacity hover:opacity-90",
                disabled && "opacity-50 cursor-not-allowed"
              )}
            >
              <div className="relative w-[120px] h-[90px]">
                <Image
                  src={example.url}
                  alt={example.alt}
                  fill
                  className="object-cover"
                  sizes="120px"
                />
              </div>
              <div className="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black/60 to-transparent p-2">
                <p className="text-xs text-white truncate">
                  {example.category}
                </p>
              </div>
            </button>
          ))}
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  );
};

interface ImageUploaderProps {
  onImageUploaded: (url: string) => void;
  onMaskSelected?: (maskUrl: string, isExample?: boolean) => void;
  existingImageUrl?: string;
  error?: string | undefined;
  disabled?: boolean;
  context?:
    | "upscale"
    | "interior"
    | "exterior"
    | "staging"
    | "edit"
    | "background";
}

const ImageUploader: React.FC<ImageUploaderProps> = ({
  onImageUploaded,
  onMaskSelected,
  existingImageUrl,
  error,
  disabled,
  context,
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isFocused, setIsFocused] = useState(false);

  useEffect(() => {
    if (existingImageUrl) {
      setPreviewUrl(existingImageUrl);
    }
  }, [existingImageUrl]);

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      const file = acceptedFiles[0];
      if (!file) return;

      try {
        setIsUploading(true);
        setUploadError(null);
        const previewUrl = URL.createObjectURL(file);
        setPreviewUrl(previewUrl);

        const formData = new FormData();
        formData.append("file", file);

        const result = await handleImageUpload(formData);

        if ("error" in result) {
          setUploadError(result.error.message);
          setPreviewUrl(null);
        } else if (result.displayUrl) {
          onImageUploaded(result.displayUrl);
          if (onMaskSelected) {
            onMaskSelected("", false);
          }
        }
      } catch (error) {
        console.error("Upload error:", error);
        setUploadError("An unexpected error occurred. Please try again.");
        setPreviewUrl(null);
      } finally {
        setIsUploading(false);
      }
    },
    [onImageUploaded, onMaskSelected]
  );

  const handleExampleSelect = async (url: string) => {
    try {
      setIsUploading(true);
      setUploadError(null);
      setPreviewUrl(url);

      onImageUploaded(url);

      const selectedExample = getExampleImages(context).find(
        (example) => example.url === url
      );

      if (selectedExample?.maskUrl && onMaskSelected && context === "staging") {
        await new Promise((resolve) => setTimeout(resolve, 100));
        onMaskSelected(selectedExample.maskUrl, true);
      }
    } catch (error) {
      console.error("Example image selection error:", error);
      setUploadError("Failed to select example image. Please try again.");
      setPreviewUrl(null);
    } finally {
      setIsUploading(false);
    }
  };

  // Only prevent form submission
  const handleButtonClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (e.target instanceof HTMLButtonElement) {
      e.preventDefault(); // Prevent form submission
    }
  };

  // Update handlePaste to handle errors
  const handlePaste = useCallback(
    async (event: ClipboardEvent) => {
      if (!isFocused) return;

      const items = event.clipboardData?.items;
      if (!items) return;

      const imageItem = Array.from(items).find(
        (item) => item.type.indexOf("image") !== -1
      );

      if (imageItem) {
        const file = imageItem.getAsFile();
        if (!file) return;

        try {
          setIsUploading(true);
          setUploadError(null);
          const previewUrl = URL.createObjectURL(file);
          setPreviewUrl(previewUrl);

          const formData = new FormData();
          formData.append("file", file);

          const result = await handleImageUpload(formData);

          if ("error" in result) {
            setUploadError(result.error.message);
            setPreviewUrl(null);
          } else if (result.displayUrl) {
            onImageUploaded(result.displayUrl);
          }
        } catch (error) {
          console.error("Upload error:", error);
          setUploadError("Failed to upload pasted image. Please try again.");
          setPreviewUrl(null);
        } finally {
          setIsUploading(false);
        }
      }
    },
    [onImageUploaded, isFocused]
  );

  // Add and remove paste event listener
  useEffect(() => {
    document.addEventListener("paste", handlePaste);
    return () => {
      document.removeEventListener("paste", handlePaste);
    };
  }, [handlePaste]);

  // Handle focus events
  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => setIsFocused(false);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener("focusin", handleFocus);
    container.addEventListener("focusout", handleBlur);
    container.addEventListener("mouseenter", handleFocus);
    container.addEventListener("mouseleave", handleBlur);

    return () => {
      container.removeEventListener("focusin", handleFocus);
      container.removeEventListener("focusout", handleBlur);
      container.removeEventListener("mouseenter", handleFocus);
      container.removeEventListener("mouseleave", handleBlur);
    };
  }, []);

  // Initialize dropzone
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "image/*": [".png", ".jpg", ".jpeg", ".gif", ".webp", ".svg"],
    },
    maxFiles: 1,
    multiple: false,
    noClick: false,
    preventDropOnDocument: true,
    disabled,
  });

  const getDisabledMessage = (context?: string) => {
    switch (context) {
      case "upscale":
        return "Purchase credits to upscale images";
      case "interior":
        return "Purchase credits to redesign rooms";
      case "exterior":
        return "Purchase credits to redesign exteriors";
      case "edit":
        return "Purchase credits to edit images";
      case "background":
        return "Purchase credits to remove backgrounds";
      case "staging":
      default:
        return "Purchase credits to start staging rooms";
    }
  };

  // Show preview if available
  if (previewUrl || existingImageUrl) {
    return (
      <div ref={containerRef} className="space-y-4">
        <div className="relative w-full aspect-video rounded-lg overflow-hidden">
          {isUploading ? (
            <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
              <div className="flex items-center gap-2 text-white">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span>Uploading...</span>
              </div>
            </div>
          ) : (
            <Image
              src={previewUrl || existingImageUrl!}
              alt="Preview"
              fill
              className={cn("object-cover", disabled && "opacity-50")}
              sizes="(max-width: 768px) 100vw, 50vw"
              priority
            />
          )}

          {/* Controls overlay - always visible */}
          <div className="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black/60 to-transparent p-4">
            <div className="flex justify-end gap-2" {...getRootProps()}>
              <Button
                variant="secondary"
                size="sm"
                className="z-10"
                disabled={isUploading || disabled}
                onClick={handleButtonClick}
                type="button"
              >
                <Upload className="mr-2 h-4 w-4" />
                Change
              </Button>
              <input {...getInputProps()} />
            </div>
          </div>
        </div>
        <ExampleImages
          onSelect={handleExampleSelect}
          context={context}
          disabled={disabled}
        />
        {(error || uploadError) && (
          <p className="text-sm text-destructive">{error || uploadError}</p>
        )}
      </div>
    );
  }

  // Show dropzone if no image
  return (
    <div ref={containerRef} className="space-y-4">
      <div
        {...getRootProps()}
        className={cn(
          "border-2 border-dashed rounded-lg p-6 text-center transition-colors",
          isDragActive
            ? "border-primary bg-primary/10"
            : error || uploadError
            ? "border-destructive"
            : disabled
            ? "bg-muted cursor-not-allowed opacity-50 border-muted-foreground/25"
            : "border-muted-foreground/25 hover:bg-accent/50",
          disabled && "pointer-events-none"
        )}
      >
        <input {...getInputProps()} />
        {isUploading ? (
          <div className="flex items-center justify-center gap-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <p>Uploading...</p>
          </div>
        ) : (
          <div className="space-y-1">
            <p className="text-sm text-muted-foreground">
              {disabled
                ? getDisabledMessage(context)
                : "Drag & drop an image here, or click to select"}
            </p>
            {!disabled && (
              <p className="text-xs text-muted-foreground">
                You can also paste an image (Ctrl+V)
              </p>
            )}
          </div>
        )}
      </div>
      <ExampleImages
        onSelect={handleExampleSelect}
        context={context}
        disabled={disabled}
      />
      {(error || uploadError) && (
        <p className="text-sm text-destructive">{error || uploadError}</p>
      )}
    </div>
  );
};

export default ImageUploader;
