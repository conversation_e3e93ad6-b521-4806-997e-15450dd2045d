import React from "react";
import { Textarea } from "@/modules/ui/textarea";
import { Label } from "@/modules/ui/label";
import { Info } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/modules/ui/tooltip";
import { cn } from "@/modules/ui/utils/cn";

export type ExcludeElementsProps = {
  excludedElements: string | undefined;
  setExcludedElements: (excludedElements: string | undefined) => void;
  context?: "interior" | "exterior" | "virtual-staging";
};

const ExcludeElements: React.FC<ExcludeElementsProps> = ({
  excludedElements,
  setExcludedElements,
  context = "interior",
}) => {
  const isVirtualStaging = context === "virtual-staging";

  const getPlaceholder = () => {
    switch (context) {
      case "interior":
        return "e.g., chandelier, wallpaper, ceiling fan";
      case "exterior":
        return "e.g., fence, mailbox, garden furniture";
      case "virtual-staging":
        return "e.g. I want to exclude the old furniture and wall decorations";
      default:
        return isVirtualStaging
          ? "Describe elements you want to exclude"
          : "Enter elements to exclude (comma separated)";
    }
  };

  const getTooltipContent = () => {
    switch (context) {
      case "interior":
        return "List items you want to exclude from the generated design. Separate multiple items with commas.";
      case "exterior":
        return "Specify exterior elements you want to exclude from the final design. Separate multiple items with commas.";
      case "virtual-staging":
        return "Describe which items you want to exclude from the staged room.";
      default:
        return isVirtualStaging
          ? "Describe elements to exclude"
          : "List elements to exclude, separated by commas";
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;

    if (isVirtualStaging) {
      // For virtual staging, keep natural language
      setExcludedElements(value || undefined);
    } else {
      // For interior/exterior, handle comma separation
      const cleanedValue = value
        .split(",")
        .map((item) => item.trim())
        .filter(Boolean)
        .join(", ");
      setExcludedElements(cleanedValue || undefined);
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <Label htmlFor="exclude-elements" className="text-sm font-medium">
          Exclude Elements
        </Label>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Info className="h-4 w-4 text-muted-foreground cursor-help" />
            </TooltipTrigger>
            <TooltipContent
              side="top"
              className="max-w-[300px] text-xs sm:text-sm"
            >
              <p>{getTooltipContent()}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <Textarea
        id="exclude-elements"
        placeholder={getPlaceholder()}
        value={excludedElements || ""}
        onChange={handleChange}
        className={cn(
          "min-h-[80px] resize-y",
          "text-sm",
          "placeholder:text-sm text-muted-foreground",
          "ios:py-3",
          "ios:text-base"
        )}
        onFocus={(e) => {
          // Only scroll if element is not visible in the viewport
          const rect = e.target.getBoundingClientRect();
          const isVisible = rect.top >= 0 && rect.top <= window.innerHeight;

          if (!isVisible) {
            e.target.scrollIntoView({
              behavior: "smooth",
              block: "start",
            });
          }
        }}
      />
      <p className="text-xs text-muted-foreground mt-1">
        {isVirtualStaging
          ? "Describe which elements you want to exclude"
          : "Separate multiple items with commas"}
      </p>
    </div>
  );
};

export default ExcludeElements;
