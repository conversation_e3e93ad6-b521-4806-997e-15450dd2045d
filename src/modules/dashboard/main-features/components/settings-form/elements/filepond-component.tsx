"use client";
import React, { useEffect, useState } from "react";
import { FilePond, registerPlugin } from "react-filepond";
import FilePondPluginImageExifOrientation from "filepond-plugin-image-exif-orientation";
import FilePondPluginImagePreview from "filepond-plugin-image-preview";
import { useTheme } from "next-themes";

import "filepond/dist/filepond.min.css";
import "filepond-plugin-image-preview/dist/filepond-plugin-image-preview.css";

registerPlugin(FilePondPluginImageExifOrientation, FilePondPluginImagePreview);

interface FilePondComponentProps {
  onImageUploaded: (imageUrl: string, maskUrl?: string) => void;
  handleImageUpload: (
    formData: FormData
  ) => Promise<{ id: string; url: string }>;
  allowMultiple?: boolean;
  labelIdle?: string;
}

const FilePondComponent: React.FC<FilePondComponentProps> = ({
  onImageUploaded,
  handleImageUpload,
  allowMultiple = false,
  labelIdle = 'Drag & Drop your image or <span class="filepond--label-action">Browse</span>',
}) => {
  const { theme, systemTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const [key, setKey] = useState(0);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted) {
      setKey((prev) => prev + 1);
    }
  }, [theme, systemTheme, mounted]);

  const currentTheme = theme === "system" ? systemTheme : theme;

  if (!mounted) {
    return null;
  }

  return (
    <FilePond
      key={key}
      className={`w-full h-full ${
        currentTheme === "dark" ? "filepond-dark" : "filepond-light"
      } filepond-custom`}
      allowMultiple={allowMultiple}
      maxFiles={allowMultiple ? 2 : 1}
      credits={false}
      name="files"
      labelIdle={labelIdle}
      acceptedFileTypes={["image/*"]}
      styleButtonRemoveItemPosition="right"
      styleLoadIndicatorPosition="center"
      styleProgressIndicatorPosition="center"
      server={{
        process: async (
          fieldName,
          file,
          metadata,
          load,
          error,
          progress,
          abort,
          transfer,
          options
        ) => {
          try {
            const formData = new FormData();
            formData.append("file", file);

            const { id, url } = await handleImageUpload(formData);

            load(id);
            onImageUploaded(url);
          } catch (err) {
            console.error(err);
            error("Upload failed");
          }
        },
      }}
    />
  );
};

export default FilePondComponent;
