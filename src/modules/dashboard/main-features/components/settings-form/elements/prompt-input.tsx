"use client";
import React from "react";
import { Textarea } from "@/modules/ui/textarea";
import { cn } from "@/modules/ui/utils/cn";

interface PromptInputProps {
  prompt: string;
  setPrompt: (prompt: string) => void;
  error?: string;
  context?: "interior" | "exterior" | "virtual-staging";
}

const PromptInput: React.FC<PromptInputProps> = ({
  prompt,
  setPrompt,
  error,
  context = "interior",
}) => {
  const getPlaceholder = () => {
    switch (context) {
      case "interior":
        return "Describe your interior design vision (e.g., 'Modern minimalist living room with natural light and wooden accents')";
      case "exterior":
        return "Describe your exterior design vision (e.g., 'Modern farmhouse with white siding and black trim')";
      case "virtual-staging":
        return "Describe your staging vision (e.g., 'Cozy modern living room with neutral colors and contemporary furniture')";
      default:
        return "Describe your vision in detail...";
    }
  };

  return (
    <div className="space-y-2">
      <Textarea
        id="prompt"
        name="prompt"
        placeholder={getPlaceholder()}
        value={prompt}
        onChange={(e) => setPrompt(e.target.value)}
        className={cn(
          "min-h-[100px] resize-y",
          "text-sm",
          "placeholder:text-sm text-muted-foreground",
          "ios:py-3",
          "ios:text-base",
          error && "border-destructive"
        )}
        onFocus={(e) => {
          const rect = e.target.getBoundingClientRect();
          const isFullyVisible =
            rect.top >= 0 && rect.bottom <= window.innerHeight;

          if (!isFullyVisible) {
            e.target.scrollIntoView({
              behavior: "smooth",
              block: "nearest",
            });
          }
        }}
      />
      {error && <p className="text-xs text-destructive mt-1">{error}</p>}
    </div>
  );
};

export default PromptInput;
