"use client";

import React, { useState, useEffect } from "react";
import { Label } from "@/modules/ui/label";
import { cn } from "@/modules/ui/utils/cn";
import { Button } from "@/modules/ui/button";
import {
  ChevronRight,
  Check,
  Lock,
  Search,
  SlidersHorizontal,
  X,
} from "lucide-react";
import {
  <PERSON>et,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/modules/ui/sheet";
import {
  getStyleById,
  type StyleDetails,
  StyleCategory,
} from "../../../../../../../public/static/styles";
import { useSubscription } from "@/modules/payments/hooks/use-subscription";
import { Badge } from "@/modules/ui/badge";
import Image from "next/image";
import { ScrollArea } from "@/modules/ui/scroll-area";
import { PaywallDialog } from "@/modules/payments/components/paywall-dialog";
import { Input } from "@/modules/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/modules/ui/dropdown-menu";
import { useDebounce } from "@/modules/admin-dashboard/hooks/use-debounce";
import { Skeleton } from "@/modules/ui/skeleton";
import { AnimatePresence, motion } from "framer-motion";

interface StyleSelectionProps {
  isPromptActive: boolean;
  style: string | undefined;
  setStyle: (style: string | undefined) => void;
  groupedStyleOptions: Record<string, any[]>;
  error?: string;
}

const StyleSelection: React.FC<StyleSelectionProps> = ({
  isPromptActive,
  style,
  setStyle,
  groupedStyleOptions,
  error,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [showPaywall, setShowPaywall] = useState(false);
  const [selectedLockedStyle, setSelectedLockedStyle] =
    useState<StyleDetails | null>(null);
  const { subscription, isSubscribed } = useSubscription();
  const userAccessLevel = isSubscribed
    ? subscription?.packageName === "premium"
      ? "premium"
      : "pro"
    : "free";

  const selectedStyle = style ? getStyleById(style) : undefined;

  const hasAccess = (
    requiredLevel: "free" | "pro" | "premium",
    userLevel: "free" | "pro" | "premium"
  ) => {
    const levels = { free: 0, pro: 1, premium: 2 };
    return levels[userLevel] >= levels[requiredLevel];
  };

  const handleStyleClick = (style: StyleDetails) => {
    if (hasAccess(style.accessLevel, userAccessLevel)) {
      setStyle(style.id);
      setIsOpen(false);
    } else {
      setSelectedLockedStyle(style);
      setShowPaywall(true);
    }
  };

  const [searchQuery, setSearchQuery] = useState("");
  const [filters, setFilters] = useState({
    accessLevel: [] as ("free" | "pro" | "premium")[],
    categories: [] as string[],
    isNew: false,
    isFeatured: false,
  });

  const [isSearching, setIsSearching] = useState(false);
  const [noResults, setNoResults] = useState(false);
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  const filterStyles = React.useCallback(
    (styles: any[]) => {
      return styles
        .filter((option) => !option.isSubcategoryLabel)
        .map((option) => getStyleById(option.value))
        .filter((styleDetails): styleDetails is StyleDetails => {
          if (!styleDetails) return false;

          const matchesSearch: boolean = debouncedSearchQuery
            ? [
                styleDetails.name,
                styleDetails.description || "",
                styleDetails.feeling,
                ...(styleDetails.tags || []),
              ].some((text) =>
                text.toLowerCase().includes(debouncedSearchQuery.toLowerCase())
              )
            : true;

          const matchesAccessLevel: boolean =
            filters.accessLevel.length === 0 ||
            filters.accessLevel.includes(styleDetails.accessLevel);

          const matchesCategory: boolean =
            filters.categories.length === 0 ||
            filters.categories.includes(styleDetails.category);

          const matchesNew: boolean =
            !filters.isNew || Boolean(styleDetails.isNew);
          const matchesFeatured: boolean =
            !filters.isFeatured || Boolean(styleDetails.isFeatured);

          return (
            matchesSearch &&
            matchesAccessLevel &&
            matchesCategory &&
            matchesNew &&
            matchesFeatured
          );
        });
    },
    [debouncedSearchQuery, filters]
  );

  useEffect(() => {
    setIsSearching(true);
    const timer = setTimeout(() => {
      const hasResults = Object.entries(groupedStyleOptions).some(
        ([_, styles]) => filterStyles(styles).length > 0
      );
      setNoResults(!hasResults);
      setIsSearching(false);
    }, 100);

    return () => clearTimeout(timer);
  }, [debouncedSearchQuery, filters, filterStyles, groupedStyleOptions]);

  const EmptyState = ({ type }: { type: "search" | "filter" }) => (
    <div className="flex flex-col items-center justify-center py-12 px-4">
      <div className="h-12 w-12 rounded-full bg-muted/30 flex items-center justify-center mb-4">
        {type === "search" ? (
          <Search className="h-6 w-6 text-muted-foreground" />
        ) : (
          <SlidersHorizontal className="h-6 w-6 text-muted-foreground" />
        )}
      </div>
      <h4 className="text-base font-medium mb-2">No styles found</h4>
      <p className="text-sm text-muted-foreground text-center">
        {type === "search"
          ? "Try adjusting your search terms or filters"
          : "Try changing or clearing your filters"}
      </p>
      <Button
        variant="outline"
        className="mt-4"
        onClick={() => {
          if (type === "search") {
            setSearchQuery("");
          }
          setFilters({
            accessLevel: [],
            categories: [],
            isNew: false,
            isFeatured: false,
          });
        }}
      >
        {type === "search" ? "Clear search" : "Clear filters"}
      </Button>
    </div>
  );

  const paywallProps = React.useMemo(
    () => ({
      open: showPaywall,
      onOpenChange: setShowPaywall,
      title: selectedLockedStyle
        ? `Unlock ${selectedLockedStyle.name}`
        : undefined,
      description: selectedLockedStyle
        ? `Upgrade to ${selectedLockedStyle.accessLevel} to access this style and more premium features.`
        : undefined,
      feature:
        selectedLockedStyle?.accessLevel === "premium"
          ? "premium-styles"
          : "pro-styles",
      defaultPlan:
        selectedLockedStyle?.accessLevel === "premium"
          ? ("premium" as const)
          : ("pro" as const),
      persistDismissal: false,
    }),
    [showPaywall, selectedLockedStyle]
  );

  return (
    <div className="space-y-2">
      <Label
        className={cn(
          "text-sm font-semibold tracking-tight",
          error && "text-destructive"
        )}
      >
        Style
      </Label>

      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-full justify-between",
              !selectedStyle && "text-muted-foreground"
            )}
          >
            <div className="flex items-center gap-2">
              {selectedStyle?.thumbnailImage && (
                <div className="relative w-8 h-8 rounded-xl overflow-hidden">
                  <Image
                    src={selectedStyle.thumbnailImage}
                    alt={selectedStyle.name}
                    fill
                    className="object-cover"
                  />
                </div>
              )}
              <span>{selectedStyle?.name || "Select a style"}</span>
              {selectedStyle?.accessLevel !== "free" && (
                <Badge variant="secondary" className="ml-2">
                  {selectedStyle?.accessLevel}
                </Badge>
              )}
            </div>
            <ChevronRight className="h-4 w-4 opacity-50" />
          </Button>
        </SheetTrigger>
        <SheetContent
          className={cn(
            "w-full sm:w-[540px] p-0 border-0",
            "bg-background rounded-l-2xl"
          )}
        >
          <SheetHeader className="p-6 pb-2">
            <SheetTitle>Choose a Style</SheetTitle>

            <div className="mt-4 space-y-4">
              <div className="flex gap-2">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                  <Input
                    placeholder="Search styles..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-9 w-full"
                    autoFocus={false}
                  />
                  {searchQuery && (
                    <button
                      onClick={() => setSearchQuery("")}
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  )}
                </div>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="icon">
                      <SlidersHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <DropdownMenuLabel>Access Level</DropdownMenuLabel>
                    {["free", "pro", "premium"].map((level) => (
                      <DropdownMenuCheckboxItem
                        key={level}
                        checked={filters.accessLevel.includes(level as any)}
                        onCheckedChange={(checked) => {
                          setFilters((prev) => ({
                            ...prev,
                            accessLevel: checked
                              ? [...prev.accessLevel, level as any]
                              : prev.accessLevel.filter((l) => l !== level),
                          }));
                        }}
                      >
                        {level.charAt(0).toUpperCase() + level.slice(1)}
                      </DropdownMenuCheckboxItem>
                    ))}

                    <DropdownMenuSeparator />
                    <DropdownMenuLabel>Categories</DropdownMenuLabel>
                    {Object.values(StyleCategory).map((category: string) => (
                      <DropdownMenuCheckboxItem
                        key={category}
                        checked={filters.categories.includes(category)}
                        onCheckedChange={(checked) => {
                          setFilters((prev) => ({
                            ...prev,
                            categories: checked
                              ? [...prev.categories, category]
                              : prev.categories.filter((c) => c !== category),
                          }));
                        }}
                      >
                        {category}
                      </DropdownMenuCheckboxItem>
                    ))}

                    <DropdownMenuSeparator />
                    <DropdownMenuCheckboxItem
                      checked={filters.isNew}
                      onCheckedChange={(checked) => {
                        setFilters((prev) => ({ ...prev, isNew: checked }));
                      }}
                    >
                      New Styles
                    </DropdownMenuCheckboxItem>

                    <DropdownMenuCheckboxItem
                      checked={filters.isFeatured}
                      onCheckedChange={(checked) => {
                        setFilters((prev) => ({
                          ...prev,
                          isFeatured: checked,
                        }));
                      }}
                    >
                      Featured Styles
                    </DropdownMenuCheckboxItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {(filters.accessLevel.length > 0 ||
                filters.categories.length > 0 ||
                filters.isNew ||
                filters.isFeatured) && (
                <div className="flex flex-wrap gap-2">
                  {filters.accessLevel.map((level) => (
                    <Badge
                      key={level}
                      variant="secondary"
                      className="cursor-pointer"
                      onClick={() => {
                        setFilters((prev) => ({
                          ...prev,
                          accessLevel: prev.accessLevel.filter(
                            (l) => l !== level
                          ),
                        }));
                      }}
                    >
                      {level}
                      <X className="ml-1 h-3 w-3" />
                    </Badge>
                  ))}
                  {filters.categories.map((category) => (
                    <Badge
                      key={category}
                      variant="secondary"
                      className="cursor-pointer"
                      onClick={() => {
                        setFilters((prev) => ({
                          ...prev,
                          categories: prev.categories.filter(
                            (c) => c !== category
                          ),
                        }));
                      }}
                    >
                      {category}
                      <X className="ml-1 h-3 w-3" />
                    </Badge>
                  ))}
                  {filters.isNew && (
                    <Badge
                      variant="secondary"
                      className="cursor-pointer"
                      onClick={() =>
                        setFilters((prev) => ({ ...prev, isNew: false }))
                      }
                    >
                      New
                      <X className="ml-1 h-3 w-3" />
                    </Badge>
                  )}
                  {filters.isFeatured && (
                    <Badge
                      variant="secondary"
                      className="cursor-pointer"
                      onClick={() =>
                        setFilters((prev) => ({ ...prev, isFeatured: false }))
                      }
                    >
                      Featured
                      <X className="ml-1 h-3 w-3" />
                    </Badge>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setFilters({
                        accessLevel: [],
                        categories: [],
                        isNew: false,
                        isFeatured: false,
                      });
                    }}
                  >
                    Clear all
                  </Button>
                </div>
              )}
            </div>
          </SheetHeader>

          <ScrollArea className="h-[100vh]">
            <div className="p-6 pt-2 pb-0">
              {isSearching ? (
                <div className="space-y-8">
                  {[1, 2].map((i) => (
                    <div key={i}>
                      <Skeleton className="h-6 w-32 mb-3" />
                      <div className="grid grid-cols-2 gap-3">
                        {[1, 2, 3, 4].map((j) => (
                          <Skeleton
                            key={j}
                            className="aspect-[4/3] rounded-lg w-full"
                          />
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              ) : noResults ? (
                <EmptyState type={searchQuery ? "search" : "filter"} />
              ) : (
                Object.entries(groupedStyleOptions).map(
                  ([category, styles]) => {
                    const filteredStyles = filterStyles(styles);
                    if (filteredStyles.length === 0) return null;

                    return (
                      <AnimatePresence
                        mode="wait"
                        key={`animatePresence-${category}`}
                      >
                        <motion.div
                          key={category}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          transition={{
                            duration: 0.3,
                            ease: "easeOut",
                          }}
                          className="mb-8 last:mb-0 last:pb-6"
                        >
                          <h3 className="font-semibold mb-3">{category}</h3>
                          <div className="grid grid-cols-2 gap-3">
                            {filteredStyles.map((styleDetails, index) => {
                              const isLocked = !hasAccess(
                                styleDetails.accessLevel,
                                userAccessLevel
                              );
                              const isSelected = styleDetails.id === style;

                              return (
                                <motion.button
                                  key={styleDetails.id}
                                  initial={{ opacity: 0, scale: 0.97 }}
                                  animate={{ opacity: 1, scale: 1 }}
                                  transition={{
                                    duration: 0.2,
                                    delay: index * 0.03,
                                    ease: "easeOut",
                                  }}
                                  whileHover={{
                                    scale: 1.02,
                                    transition: {
                                      duration: 0.2,
                                      ease: "easeOut",
                                    },
                                  }}
                                  whileTap={{
                                    scale: 0.98,
                                    transition: { duration: 0.1 },
                                  }}
                                  onClick={() => handleStyleClick(styleDetails)}
                                  className={cn(
                                    "relative group p-3 rounded-2xl border-2 transition-all duration-200",
                                    "hover:border-primary/50 text-left",
                                    isSelected
                                      ? "border-primary ring-2 ring-primary/20"
                                      : "border-border",
                                    isLocked && "opacity-80"
                                  )}
                                >
                                  <div className="relative aspect-[4/3] rounded-xl overflow-hidden mb-3">
                                    {styleDetails.thumbnailImage ? (
                                      <Image
                                        src={styleDetails.thumbnailImage}
                                        alt={styleDetails.name}
                                        fill
                                        className="object-cover"
                                      />
                                    ) : (
                                      <div className="w-full h-full bg-muted/30" />
                                    )}

                                    {isLocked && (
                                      <div className="absolute inset-0 bg-background/80 backdrop-blur-[2px] flex items-center justify-center">
                                        <Lock className="w-6 h-6 text-primary" />
                                      </div>
                                    )}

                                    {isSelected && (
                                      <div className="absolute top-2 right-2">
                                        <Badge
                                          variant="default"
                                          className="bg-primary"
                                        >
                                          <Check className="h-3 w-3" />
                                        </Badge>
                                      </div>
                                    )}
                                  </div>

                                  <div className="space-y-1">
                                    <div className="flex items-center justify-between">
                                      <p className="font-medium text-sm">
                                        {styleDetails.name}
                                      </p>
                                      {styleDetails.accessLevel !== "free" && (
                                        <Badge
                                          variant="secondary"
                                          className="text-xs"
                                        >
                                          {styleDetails.accessLevel}
                                        </Badge>
                                      )}
                                    </div>
                                    <p className="text-xs text-muted-foreground line-clamp-2">
                                      {styleDetails.description ||
                                        styleDetails.feeling}
                                    </p>
                                  </div>
                                </motion.button>
                              );
                            })}
                          </div>
                        </motion.div>
                      </AnimatePresence>
                    );
                  }
                )
              )}
            </div>
          </ScrollArea>
        </SheetContent>
      </Sheet>

      {error && <p className="text-sm text-destructive">{error}</p>}

      <PaywallDialog {...paywallProps} />
    </div>
  );
};

export default StyleSelection;
