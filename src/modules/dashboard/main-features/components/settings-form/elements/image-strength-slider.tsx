import { Slider } from "@/modules/ui/slider";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/modules/ui/tooltip";
import { Info } from "lucide-react";

interface ImageStrengthSliderProps {
  value: number;
  onChange: (value: number) => void;
  className?: string;
}

const ImageStrengthSlider = ({
  value,
  onChange,
  className,
}: ImageStrengthSliderProps) => {
  return (
    <div className={className}>
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium">Image Resemblance</label>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <Info className="h-4 w-4 text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <p>
                  Controls how closely the upscaled image matches the original:
                </p>
                <ul className="list-disc ml-4 mt-1 text-sm">
                  <li>
                    Lower values (0.1-0.3): More enhancement, less accuracy
                  </li>
                  <li>Medium values (0.4-0.6): Balanced enhancement</li>
                  <li>Higher values (0.7-1.0): Very close to original</li>
                </ul>
                <p className="mt-1 text-sm">
                  Default (0.5): Recommended for most upscaling needs
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <span className="text-sm text-muted-foreground">
          {value.toFixed(2)}
        </span>
      </div>
      <Slider
        min={0}
        max={3}
        step={0.01}
        value={[value]}
        onValueChange={(values) => onChange(values[0])}
        className="my-4"
      />
      <p className="text-sm text-muted-foreground mt-1">
        {value < 0.3
          ? "Enhanced output with more artistic freedom"
          : value < 0.6
          ? "Balanced enhancement (Recommended)"
          : value < 0.8
          ? "Close resemblance to original"
          : "Very high accuracy to source"}
      </p>
    </div>
  );
};

export default ImageStrengthSlider;
