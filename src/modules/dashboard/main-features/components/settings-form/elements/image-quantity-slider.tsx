import React from "react";
import { Slider } from "@/modules/ui/slider";
import { Label } from "@/modules/ui/label";

interface ImageQuantitySliderProps {
  numImages: number;
  setNumImages: (numImages: number) => void;
}

const ImageQuantitySlider: React.FC<ImageQuantitySliderProps> = ({
  numImages,
  setNumImages,
}) => (
  <div className="mb-4">
    <Label>Image count ({numImages})</Label>
    <Slider
      className="p-2"
      defaultValue={[numImages]}
      step={1}
      min={1}
      max={3}
      onValueChange={(values) => setNumImages(values[0])}
    />
  </div>
);

export default ImageQuantitySlider;
