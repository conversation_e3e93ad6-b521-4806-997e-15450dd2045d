"use client";

import React, { useState, useEffect, memo } from "react";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";
import { Lock } from "lucide-react";
import { cn } from "@/modules/ui";
import type { StyleDetails } from "../../../../../../../public/static/styles";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/modules/ui/tooltip";
import { Badge } from "@/modules/ui/badge";
import { ScrollArea, ScrollBar } from "@/modules/ui/scroll-area";
import { PaywallDialog } from "@/modules/payments/components/paywall-dialog";

interface VisualStyleSelectorProps {
  styles: StyleDetails[];
  selectedStyle?: string;
  onStyleSelect: (styleId: string) => void;
  userAccessLevel: "free" | "pro" | "premium";
}

// Memoized style card component to prevent unnecessary re-renders
const StyleCard = memo(
  ({
    style,
    selectedStyle,
    hasAccess,
    userAccessLevel,
    onClick,
  }: {
    style: StyleDetails;
    selectedStyle?: string;
    hasAccess: (
      required: "free" | "pro" | "premium",
      user: "free" | "pro" | "premium"
    ) => boolean;
    userAccessLevel: "free" | "pro" | "premium";
    onClick: (style: StyleDetails) => void;
  }) => {
    const [imageLoaded, setImageLoaded] = useState(false);

    return (
      <motion.div
        key={style.id}
        initial={false}
        animate={{
          opacity: 1,
          scale: 1,
          transition: { duration: 0.2 },
        }}
        whileHover={{
          scale: 1.02,
          transition: { duration: 0.15 },
        }}
        whileTap={{
          scale: 0.98,
          transition: { duration: 0.1 },
        }}
        className={cn(
          "relative group cursor-pointer rounded-2xl overflow-hidden",
          "border-2 transition-colors duration-200",
          selectedStyle === style.id
            ? "border-primary ring-2 ring-primary/20"
            : "border-border hover:border-primary/50",
          !hasAccess(style.accessLevel, userAccessLevel) && "opacity-80"
        )}
        onClick={() => onClick(style)}
        tabIndex={-1}
      >
        <motion.div
          className="relative aspect-[4/3]"
          initial={false}
          whileHover={{ scale: 1.03 }}
          transition={{ duration: 0.15 }}
        >
          {style.thumbnailImage && (
            <>
              <div
                className={cn(
                  "absolute inset-0 bg-muted/30 transition-opacity duration-300",
                  imageLoaded ? "opacity-0" : "opacity-100"
                )}
              />
              <Image
                src={style.thumbnailImage}
                alt={style.name}
                fill
                className={cn(
                  "object-cover transition-opacity duration-300",
                  imageLoaded ? "opacity-100" : "opacity-0"
                )}
                onLoadingComplete={() => setImageLoaded(true)}
                priority={true}
              />
            </>
          )}

          <AnimatePresence>
            {!hasAccess(style.accessLevel, userAccessLevel) && (
              <motion.div
                className="absolute inset-0 bg-background/80 backdrop-blur-[2px] flex items-center justify-center"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <Lock className="w-6 h-6 text-primary" />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Quick Preview on Hover */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <motion.div
                  className="absolute inset-0 bg-background/80"
                  initial={{ opacity: 0 }}
                  whileHover={{ opacity: 1 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="p-3 text-xs">
                    <motion.p
                      className="font-medium"
                      initial={{ y: 5, opacity: 0 }}
                      whileHover={{ y: 0, opacity: 1 }}
                      transition={{ duration: 0.2, delay: 0.1 }}
                    >
                      {style.feeling}
                    </motion.p>
                    <motion.p
                      className="text-muted-foreground mt-1"
                      initial={{ y: 5, opacity: 0 }}
                      whileHover={{ y: 0, opacity: 1 }}
                      transition={{ duration: 0.2, delay: 0.15 }}
                    >
                      {style.colorPalette}
                    </motion.p>
                  </div>
                </motion.div>
              </TooltipTrigger>
              <TooltipContent side="right" className="w-[300px] p-4">
                <StylePreview style={style} />
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </motion.div>

        {/* Style Info */}
        <div className="p-3 space-y-1">
          <h3 className="font-medium text-sm">{style.name}</h3>
          <p className="text-xs text-muted-foreground">{style.subcategory}</p>
        </div>
      </motion.div>
    );
  }
);

StyleCard.displayName = "StyleCard";

export const VisualStyleSelector = ({
  styles,
  selectedStyle,
  onStyleSelect,
  userAccessLevel,
}: VisualStyleSelectorProps) => {
  const [isClient, setIsClient] = useState(false);
  const [showPaywall, setShowPaywall] = useState(false);
  const [selectedLockedStyle, setSelectedLockedStyle] =
    useState<StyleDetails | null>(null);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleStyleClick = (style: StyleDetails) => {
    if (hasAccess(style.accessLevel, userAccessLevel)) {
      onStyleSelect(style.id);
    } else {
      setSelectedLockedStyle(style);
      setShowPaywall(true);
    }
  };

  const handlePaywallOpenChange = (open: boolean) => {
    if (!open) {
      setTimeout(() => {
        setShowPaywall(false);
        setSelectedLockedStyle(null);
      }, 0);
    } else {
      setShowPaywall(true);
    }
  };

  const hasAccess = (
    requiredLevel: "free" | "pro" | "premium",
    userLevel: "free" | "pro" | "premium"
  ) => {
    const levels = { free: 0, pro: 1, premium: 2 };
    return levels[userLevel] >= levels[requiredLevel];
  };

  // Memoize the PaywallDialog props to prevent unnecessary rerenders
  const paywallProps = React.useMemo(
    () => ({
      open: showPaywall,
      onOpenChange: handlePaywallOpenChange,
      title: selectedLockedStyle
        ? `Unlock ${selectedLockedStyle.name}`
        : undefined,
      description: selectedLockedStyle
        ? `Upgrade to ${selectedLockedStyle.accessLevel} to access this style and more premium features.`
        : undefined,
      feature:
        selectedLockedStyle?.accessLevel === "premium"
          ? "premium-styles"
          : "pro-styles",
      defaultPlan:
        selectedLockedStyle?.accessLevel === "premium"
          ? ("premium" as const)
          : ("pro" as const),
      persistDismissal: false,
    }),
    [showPaywall, selectedLockedStyle, handlePaywallOpenChange]
  );

  return (
    <div className="space-y-4" tabIndex={-1}>
      <ScrollArea className="w-full">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-1">
          {isClient &&
            styles.map((style) => (
              <StyleCard
                key={style.id}
                style={style}
                selectedStyle={selectedStyle}
                hasAccess={hasAccess}
                userAccessLevel={userAccessLevel}
                onClick={handleStyleClick}
              />
            ))}
        </div>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>

      <PaywallDialog {...paywallProps} />
    </div>
  );
};

// Style Preview Component for Tooltip
const StylePreview = ({ style }: { style: StyleDetails }) => (
  <div className="space-y-3">
    <div className="space-y-1.5">
      <h4 className="font-medium">{style.name}</h4>
      <p className="text-sm text-muted-foreground">{style.description}</p>
    </div>

    {style.previewImages && style.previewImages.length > 0 && (
      <div className="grid grid-cols-2 gap-2">
        {style.previewImages.slice(0, 2).map((image: string, i: number) => (
          <div
            key={i}
            className="relative aspect-[4/3] rounded-xl overflow-hidden"
          >
            <Image
              src={image}
              alt={`${style.name} example ${i + 1}`}
              fill
              className="object-cover"
            />
          </div>
        ))}
      </div>
    )}

    <div className="flex flex-wrap gap-1">
      {style.tags?.map((tag: string) => (
        <Badge key={tag} variant="secondary" className="text-xs">
          {tag}
        </Badge>
      ))}
    </div>
  </div>
);
