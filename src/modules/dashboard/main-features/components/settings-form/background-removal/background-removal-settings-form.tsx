"use client";
import React, { useState } from "react";
import { But<PERSON> } from "@/modules/ui/button";
import { ChevronDown } from "lucide-react";
import { useMediaQuery } from "@/modules/hooks/use-media-query";
import { Drawer, DrawerContent, DrawerTrigger } from "@/modules/ui/drawer";
import useBackgroundRemovalStore from "@/modules/dashboard/main-features/store/background-removal-store";
import { useImageCreditsStore } from "@/modules/dashboard/main-features/stores/use-image-credits-store";
import { useToast } from "@/modules/ui/use-toast";
import { useCurrentUser } from "@/modules/auth/hooks/use-current-user";
import { PaywallDialog } from "@/modules/payments/components/paywall-dialog";
import { BackgroundRemovalFormContent } from "./background-removal-form-content";
import { BackgroundRemovalMobileDrawer } from "./background-removal-mobile-drawer";
import { useBackgroundRemovalFormSteps } from "../../../hooks/use-background-removal-form-steps";
import { useBackgroundRemovalSubmission } from "../../../hooks/use-background-removal-submission";

const BackgroundRemovalSettingsForm: React.FC = () => {
  const store = useBackgroundRemovalStore();
  const [showPaywall, setShowPaywall] = useState(false);
  const { credits } = useImageCreditsStore();
  const isMobile = useMediaQuery("(max-width: 768px)");
  const { toast } = useToast();
  const currentUser = useCurrentUser();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  
  // Form steps management
  const { currentStep, totalSteps, progress, isStepComplete } = useBackgroundRemovalFormSteps();
  
  // Handle paywall
  const showPaywallHandler = () => {
    setShowPaywall(true);
  };
  
  // Submission handler
  const { handleSubmit, isSubmitting } = useBackgroundRemovalSubmission({
    onShowPaywall: showPaywallHandler
  });
  
  // Handle image upload
  const handleImageUploaded = (url: string) => {
    const hasEnoughCredits = credits && credits >= 1;
    if (!hasEnoughCredits) {
      showPaywallHandler();
      return;
    }
    store.setImageUrl(url);
  };

  if (isMobile) {
    return (
      <>
        <BackgroundRemovalMobileDrawer
          isOpen={isDrawerOpen}
          setIsOpen={setIsDrawerOpen}
        >
          <BackgroundRemovalFormContent
            currentStep={currentStep}
            totalSteps={totalSteps}
            progress={progress}
            isStepComplete={isStepComplete}
            onSubmit={handleSubmit}
            onImageUploaded={handleImageUploaded}
            isSubmitting={isSubmitting}
            onShowPaywall={showPaywallHandler}
            isMobile={isMobile}
          />
        </BackgroundRemovalMobileDrawer>
        
        <Button
          variant="outline"
          className="w-full mb-4 flex items-center justify-between"
          onClick={() => setIsDrawerOpen(true)}
        >
          <span>
            {store.imageUrl ? "Edit Settings" : "Background Removal Settings"}
          </span>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
        
        <PaywallDialog
          open={showPaywall}
          onOpenChange={setShowPaywall}
          title="Get More Credits"
          description="You need 1 credit to remove background. Upgrade to Pro to process more images."
          feature="remove more backgrounds"
        />
      </>
    );
  }

  return (
    <div className="h-full">
      <BackgroundRemovalFormContent
        currentStep={currentStep}
        totalSteps={totalSteps}
        progress={progress}
        isStepComplete={isStepComplete}
        onSubmit={handleSubmit}
        onImageUploaded={handleImageUploaded}
        isSubmitting={isSubmitting}
        onShowPaywall={showPaywallHandler}
        isMobile={isMobile}
      />
      
      <PaywallDialog
        open={showPaywall}
        onOpenChange={setShowPaywall}
        title="Get More Credits"
        description="You need 1 credit to remove background. Upgrade to Pro to process more images."
        feature="remove more backgrounds"
      />
    </div>
  );
};

export default BackgroundRemovalSettingsForm;
