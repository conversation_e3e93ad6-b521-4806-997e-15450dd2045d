"use client";
import React from "react";
import { ScrollArea } from "@/modules/ui/scroll-area";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/modules/ui/card";
import { Progress } from "@/modules/ui/progress";
import { Badge } from "@/modules/ui/badge";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/modules/ui/tooltip";
import { Info, Loader2 } from "lucide-react";
import ImageUploader from "../../settings-form/elements/image-uploader";
import useBackgroundRemovalStore from "../../../store/background-removal-store";
import { cn } from "@/lib/utils";
import { Button } from "@/modules/ui/button";
import { useFormStatus } from "react-dom";
import { useImageCreditsStore } from "../../../stores/use-image-credits-store";
import { Alert, AlertDescription } from "@/modules/ui/alert";
import Image from "next/image";

interface BackgroundRemovalFormContentProps {
  currentStep: number;
  totalSteps: number;
  progress: number;
  isStepComplete: (step: number) => boolean;
  onSubmit: (formData: FormData) => Promise<void>;
  onImageUploaded: (url: string) => void;
  isSubmitting: boolean;
  onShowPaywall: () => void;
  isMobile: boolean;
}

const SubmitButton = () => {
  const { pending } = useFormStatus();
  const imageUrl = useBackgroundRemovalStore((state) => state.imageUrl);
  const { credits } = useImageCreditsStore();
  const hasEnoughCredits = credits && credits >= 1;

  return (
    <Button
      type="submit"
      disabled={pending || !imageUrl || !hasEnoughCredits}
      variant="gradient-primary"
      size="sm"
      shine
      glow
      className="text-[15px] w-full sm:text-base px-6 sm:px-8 py-5 sm:py-6 group"
    >
      {pending ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Removing Background...
        </>
      ) : (
        <div className="flex items-center justify-center gap-2">
          <span>
            {hasEnoughCredits ? "Remove Background" : "Insufficient Credits"}
          </span>
          <div
            className={cn(
              "flex items-center gap-1 px-2 py-0.5 rounded",
              hasEnoughCredits
                ? "bg-primary-foreground/10"
                : "bg-destructive/10 text-destructive"
            )}
          >
            <div className="relative w-4 h-4">
              <Image
                src="/images/coin.png"
                alt="Credits"
                width={16}
                height={16}
                className="object-contain"
                priority
              />
            </div>
            <span>1</span>
          </div>
        </div>
      )}
    </Button>
  );
};

export const BackgroundRemovalFormContent: React.FC<BackgroundRemovalFormContentProps> = ({
  currentStep,
  totalSteps,
  progress,
  isStepComplete,
  onSubmit,
  onImageUploaded,
  isSubmitting,
  onShowPaywall,
  isMobile,
}) => {
  const store = useBackgroundRemovalStore();
  const { credits } = useImageCreditsStore();
  const hasEnoughCredits = credits && credits >= 1;

  const renderTooltip = (content: string) => (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Info className="h-4 w-4 ml-1 text-muted-foreground inline-block cursor-help" />
        </TooltipTrigger>
        <TooltipContent>
          <p className="max-w-xs">{content}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );

  return (
    <form action={onSubmit} className="h-full flex flex-col bg-background">
      <div className="px-4">
        <Card className="mb-4">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">Remove Background</CardTitle>
              {renderTooltip(
                "Upload an image to automatically remove its background"
              )}
            </div>
            <div className="mt-4">
              <Progress value={progress} className="h-1.5" />
              <p className="text-sm text-muted-foreground mt-2">
                Step {currentStep} of {totalSteps}
              </p>
            </div>
          </CardHeader>
        </Card>
      </div>

      <ScrollArea className="flex-grow px-4">
        <div className="space-y-4 py-4">
          {/* Image Upload */}
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium">Upload Image</h3>
                  {isStepComplete(1) && (
                    <Badge variant="default" className="ml-2">
                      ✓
                    </Badge>
                  )}
                </div>
                <ImageUploader
                  existingImageUrl={store.imageUrl || ""}
                  onImageUploaded={onImageUploaded}
                  error={store.errors?.imageUrl?.[0]}
                  disabled={!hasEnoughCredits}
                  context="background"
                />
                {!hasEnoughCredits && (
                  <Alert variant="destructive" className="mt-2">
                    <AlertDescription className="text-sm flex items-center gap-2">
                      <div className="relative w-4 h-4">
                        <Image
                          src="/images/coin.png"
                          alt="Credits"
                          width={16}
                          height={16}
                          className="object-contain"
                          priority
                        />
                      </div>
                      You need 1 credit to remove the background
                    </AlertDescription>
                  </Alert>
                )}
                <p className="text-sm text-muted-foreground">
                  Upload an image to remove its background. The process will
                  preserve the main subject while removing the background
                  completely.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </ScrollArea>

      <div className="mt-auto bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 py-4 px-2 md:p-0 ">
        <SubmitButton />
      </div>
    </form>
  );
}; 