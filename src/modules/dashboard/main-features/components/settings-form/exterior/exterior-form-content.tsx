"use client";

import React, { RefObject } from "react";
import { Card, CardContent } from "@/modules/ui/card";
import { Alert, AlertDescription } from "@/modules/ui/alert";
import { Badge } from "@/modules/ui/badge";
import { Check, Loader2 } from "lucide-react";
import { ScrollArea } from "@/modules/ui/scroll-area";
import { Button } from "@/modules/ui/button";
import Image from "next/image";
import { cn } from "@/modules/ui";
import { useFormStatus } from "react-dom";
import useExteriorFormStore from "@/modules/dashboard/main-features/store/exterior-form-store";
import { useImageCreditsStore } from "@/modules/dashboard/main-features/stores/use-image-credits-store";
import ImageUploader from "../elements/image-uploader";
import BuildingSelection from "./building-selection";
import ExteriorStyleSelection from "./exterior-style-selection";
import PromptInput from "../elements/prompt-input";
import ExcludeElements from "../elements/exclude-elements";
import ConditionScaleSlider from "../elements/condition-scale-slider";
import CreativitySlider from "../elements/creativity-slider";

interface ExteriorFormContentProps {
  isAdvancedMode: boolean;
  currentStep: number;
  totalSteps: number;
  progress: number;
  isStepComplete: (step: number) => boolean;
  setIsAdvancedMode: (value: boolean) => void;
  scrollAreaRef: RefObject<HTMLDivElement>;
  keyboardVisible?: boolean;
  isMobile: boolean;
  onSubmit: (formData: FormData) => Promise<void>;
  onImageUploaded: (imageUrl: string) => void;
  isSubmitting?: boolean;
  onShowPaywall: (feature: "credits" | "privacy") => void;
  groupedBuildingOptions: any;
  groupedStyleOptions: any;
}

const SubmitButton = () => {
  const { pending } = useFormStatus();
  const store = useExteriorFormStore();
  const { credits } = useImageCreditsStore();
  const hasEnoughCredits = credits && credits >= 1;

  return (
    <Button
      type="submit"
      disabled={pending || !store.imageUrl || !hasEnoughCredits}
      variant="gradient-primary"
      size="lg"
      shine
      glow
      className={cn(
        "text-[15px] w-full sm:text-base px-8 py-6 group transition-all duration-200",
        "bg-gradient-to-r from-primary/90 to-primary relative overflow-hidden",
        "hover:from-primary hover:to-primary/90",
        "disabled:from-muted disabled:to-muted disabled:opacity-70",
        pending && "opacity-90"
      )}
    >
      <div className={cn(
        "absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent",
        "transform translate-x-[-200%]",
        pending && "animate-[shimmer_3s_ease-in-out_infinite]"
      )} />
      
      <div className="relative">
        {pending ? (
          <div className="flex items-center justify-center gap-3">
            <Loader2 className="h-5 w-5 animate-spin" />
            <span className="font-medium">Generating your design...</span>
          </div>
        ) : (
          <div className="flex items-center justify-center gap-3">
            <span className="font-medium">
              {hasEnoughCredits ? "Generate Exterior Design" : "Insufficient Credits"}
            </span>
            <div
              className={cn(
                "flex items-center gap-2 px-3 py-1 rounded-full transition-colors duration-200",
                hasEnoughCredits
                  ? "bg-primary-foreground/10 group-hover:bg-primary-foreground/15"
                  : "bg-destructive/10 text-destructive"
              )}
            >
              <div className="relative w-5 h-5">
                <Image
                  src="/images/coin.png"
                  alt="Credits"
                  width={20}
                  height={20}
                  className="object-contain"
                  priority
                />
              </div>
              <span className="font-medium">1</span>
            </div>
          </div>
        )}
      </div>
    </Button>
  );
};

export const ExteriorFormContent: React.FC<ExteriorFormContentProps> = ({
  isAdvancedMode,
  currentStep,
  totalSteps,
  progress,
  isStepComplete,
  setIsAdvancedMode,
  scrollAreaRef,
  keyboardVisible = false,
  isMobile,
  onSubmit,
  onImageUploaded,
  isSubmitting,
  onShowPaywall,
  groupedBuildingOptions,
  groupedStyleOptions,
}) => {
  const store = useExteriorFormStore();
  const { credits } = useImageCreditsStore();
  const hasEnoughCredits = credits && credits >= 1;

  const getFieldError = (field: string) => {
    return store.errors?.[field]?.[0];
  };

  const renderFieldError = (error?: string) => {
    if (!error) return null;
    return (
      <div className="text-sm text-destructive flex items-center gap-2 mt-2">
        <div className="w-1 h-1 rounded-full bg-destructive" />
        <span>{error}</span>
      </div>
    );
  };

  const getStepTitle = (step: number) => {
    switch (step) {
      case 1:
        return "Upload Building Photo";
      case 2:
        return isAdvancedMode ? "Custom Prompt" : "Building & Style";
      case 3:
        return "Additional Settings";
      default:
        return "Upload Building Photo";
    }
  };

  const toggleAdvancedMode = () => {
    setIsAdvancedMode(!isAdvancedMode);
  };

  return (
    <form action={onSubmit} className="flex flex-col h-full">
      {/* Progress Bar */}
      <div className="px-4 pt-4 pb-2">
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>Step {currentStep} of {totalSteps}</span>
            <span>{getStepTitle(currentStep)}</span>
          </div>
          <div className="h-2 bg-muted rounded-full overflow-hidden">
            <div 
              className="h-full bg-primary transition-all duration-300 rounded-full"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>
      </div>
      
      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-h-0">
        <ScrollArea ref={scrollAreaRef} className="flex-1">
          <div className="p-4 space-y-6">
            {/* Step Cards */}
            <div className="space-y-4 transition-all duration-300">
              {/* Step 1: Image Upload */}
              <Card
                className={cn(
                  "transition-all duration-200 border-border/10 backdrop-blur-sm rounded-lg",
                  "group",
                  currentStep === 1 
                    ? "ring-1 ring-primary/50 shadow-md" 
                    : isStepComplete(1)
                    ? "hover:ring-1 hover:ring-primary/30 hover:shadow-sm"
                    : "hover:shadow-sm"
                )}
              >
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium">Upload Building Photo</h3>
                      {isStepComplete(1) && (
                        <Badge
                          variant="default"
                          className="ml-2 bg-green-500 hover:bg-green-600 w-6 h-6 rounded-full p-0 flex items-center justify-center"
                        >
                          <Check className="h-3 w-3 stroke-[3]" />
                        </Badge>
                      )}
                    </div>
                    <ImageUploader
                      existingImageUrl={store.imageUrl}
                      onImageUploaded={onImageUploaded}
                      error={getFieldError("imageUrl")}
                      disabled={!hasEnoughCredits}
                      context="exterior"
                    />
                    {!hasEnoughCredits && (
                      <Alert variant="destructive" className="mt-2">
                        <AlertDescription className="text-sm flex items-center gap-2">
                          <div className="relative w-4 h-4">
                            <Image
                              src="/images/coin.png"
                              alt="Credits"
                              width={16}
                              height={16}
                              className="object-contain"
                              priority
                            />
                          </div>
                          You need 1 credit to redesign your exterior
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Step 2: Building & Style or Prompt */}
              <Card
                className={cn(
                  "transition-all duration-200 border-border/10 backdrop-blur-sm rounded-lg",
                  "group",
                  currentStep === 2 
                    ? "ring-1 ring-primary/50 shadow-md" 
                    : isStepComplete(2)
                    ? "hover:ring-1 hover:ring-primary/30 hover:shadow-sm"
                    : "hover:shadow-sm",
                  !store.imageUrl && "opacity-90 pointer-events-none"
                )}
              >
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium">
                        {isAdvancedMode ? "Custom Prompt" : "Building & Style"}
                      </h3>
                      <div className="flex items-center">
                        {isStepComplete(2) && (
                          <Badge
                            variant="default"
                            className="ml-2 bg-green-500 hover:bg-green-600 w-6 h-6 rounded-full p-0 flex items-center justify-center"
                          >
                            <Check className="h-3 w-3 stroke-[3]" />
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Mode Toggle */}
                    <div className="flex items-center justify-between mb-4">
                      <span className="text-sm font-medium text-muted-foreground">
                        {isAdvancedMode ? "Advanced Mode" : "Basic Mode"}
                      </span>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={toggleAdvancedMode}
                        className="text-xs"
                      >
                        Switch to {isAdvancedMode ? "Basic" : "Advanced"} Mode
                      </Button>
                    </div>

                    {isAdvancedMode ? (
                      <PromptInput
                        prompt={store.prompt}
                        setPrompt={store.setPrompt}
                        error={getFieldError("prompt")}
                      />
                    ) : (
                      <div className="space-y-4">
                        <BuildingSelection
                          isPromptActive={store.isPromptActive}
                          building={store.building}
                          setBuilding={store.setBuilding}
                          groupedBuildingOptions={groupedBuildingOptions}
                          error={getFieldError("building")}
                        />
                        <ExteriorStyleSelection
                          isPromptActive={store.isPromptActive}
                          style={store.style}
                          setStyle={store.setStyle}
                          groupedStyleOptions={groupedStyleOptions}
                          error={getFieldError("style")}
                        />
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Step 3: Additional Settings */}
              <Card
                className={cn(
                  "transition-all duration-200 border-border/10 backdrop-blur-sm rounded-lg",
                  "group",
                  currentStep === 3 
                    ? "ring-1 ring-primary/50 shadow-md" 
                    : isStepComplete(3)
                    ? "hover:ring-1 hover:ring-primary/30 hover:shadow-sm"
                    : "hover:shadow-sm",
                  (!store.imageUrl || !isStepComplete(2)) && "opacity-90 pointer-events-none"
                )}
              >
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium">Additional Settings</h3>
                      <span className="text-sm text-muted-foreground">
                        (Optional)
                      </span>
                    </div>
                    <ExcludeElements
                      excludedElements={store.excludedElements}
                      setExcludedElements={store.setExcludedElements}
                    />
                    <ConditionScaleSlider
                      value={store.conditionScale}
                      onChange={store.setConditionScale}
                    />
                    <CreativitySlider 
                      value={store.creativity} 
                      onChange={store.setCreativity} 
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </ScrollArea>
      </div>

      {/* Submit Button */}
      <div className="mt-auto bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 py-4 px-4">
        <SubmitButton />
      </div>
    </form>
  );
}; 