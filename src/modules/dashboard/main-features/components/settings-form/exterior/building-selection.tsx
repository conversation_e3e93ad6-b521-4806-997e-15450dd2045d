"use client";

import React from "react";
import { Label } from "@/modules/ui/label";
import { cn } from "@/modules/ui/utils/cn";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/modules/ui/tooltip";
import { InfoIcon } from "lucide-react";
import { getBuildingById } from "../../../../../../../public/static/buildings";
import { SelectBase } from "../elements/select-base";
import { Portal } from "@radix-ui/react-portal";

interface BuildingSelectionProps {
  isPromptActive: boolean;
  building: string | undefined;
  setBuilding: (building: string | undefined) => void;
  groupedBuildingOptions: Record<string, any[]>;
  error?: string;
}

const BuildingSelection: React.FC<BuildingSelectionProps> = ({
  isPromptActive,
  building,
  setBuilding,
  groupedBuildingOptions,
  error,
}) => {
  const selectedBuilding = building ? getBuildingById(building) : undefined;

  // Transform options to include tooltips
  const optionsWithTooltips = Object.entries(groupedBuildingOptions).reduce(
    (acc, [group, options]) => {
      acc[group] = options.map((option) => {
        if (option.isSubcategoryLabel) return option;

        const building = getBuildingById(option.value);
        return {
          ...option,
          tooltip: building
            ? `${building.description}\n\nKey Features:\n${building.keyFeatures
                ?.slice(0, 2)
                .join("\n")}`
            : undefined,
        };
      });
      return acc;
    },
    {} as Record<string, any[]>
  );

  return (
    <div className="relative space-y-3">
      <div className="flex items-center justify-between mb-2">
        <Label
          className={cn(
            "text-sm font-semibold tracking-tight",
            error && "text-destructive"
          )}
        >
          Building
        </Label>
        {selectedBuilding && (
          <TooltipProvider>
            <Tooltip delayDuration={300}>
              <TooltipTrigger asChild>
                <button
                  type="button"
                  className="inline-flex items-center justify-center h-6 w-6 rounded-full text-sm transition-colors hover:bg-muted/50 hover:text-primary focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
                >
                  <InfoIcon className="h-3.5 w-3.5" />
                </button>
              </TooltipTrigger>
              <Portal>
                <TooltipContent
                  side="right"
                  align="center"
                  className="z-[9999] w-[300px] rounded-md border bg-popover px-3 py-4 text-popover-foreground shadow-md"
                  sideOffset={5}
                >
                  <div className="space-y-2.5">
                    <div className="flex items-center justify-between">
                      <p className="font-medium leading-none">
                        {selectedBuilding.name}
                      </p>
                      <span className="text-xs text-muted-foreground">
                        {selectedBuilding.category}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground leading-snug">
                      {selectedBuilding.description}
                    </p>
                    {selectedBuilding.keyFeatures &&
                      selectedBuilding.keyFeatures.length > 0 && (
                        <div className="text-sm">
                          <p className="text-muted-foreground font-medium mb-1.5">
                            Key Features:
                          </p>
                          <ul className="list-disc list-inside space-y-1">
                            {selectedBuilding.keyFeatures
                              .slice(0, 3)
                              .map((item) => (
                                <li key={item} className="text-sm leading-none">
                                  {item}
                                </li>
                              ))}
                          </ul>
                        </div>
                      )}
                    <div className="text-sm">
                      <p className="text-muted-foreground font-medium mb-1.5">
                        Common Materials:
                      </p>
                      <ul className="list-disc list-inside space-y-1">
                        {selectedBuilding.materials.slice(0, 3).map((item) => (
                          <li key={item} className="text-sm leading-none">
                            {item}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </TooltipContent>
              </Portal>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>

      <SelectBase
        value={building}
        onValueChange={setBuilding}
        groupedOptions={optionsWithTooltips}
        placeholder="Select a building"
        disabled={isPromptActive}
        error={error}
        className={cn(
          "w-full h-10 px-3 py-2 text-sm rounded-md border border-input bg-background ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          error && "border-destructive focus:ring-destructive",
          !building && "text-muted-foreground"
        )}
      />

      {selectedBuilding &&
        selectedBuilding.tags &&
        selectedBuilding.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-3">
            {selectedBuilding.tags.map((tag) => (
              <span
                key={tag}
                className={cn(
                  "inline-flex items-center rounded-full px-2.5 py-0.5",
                  "text-xs font-medium transition-colors duration-200",
                  "border border-primary/20",
                  "bg-primary/5 text-primary hover:text-primary-foreground",
                  "hover:bg-primary hover:border-primary",
                  "cursor-default select-none"
                )}
              >
                {tag}
              </span>
            ))}
          </div>
        )}

      {error && (
        <p
          className={cn(
            "text-sm font-medium text-destructive",
            "flex items-center gap-1.5 mt-2",
            "animate-in slide-in-from-top-1 duration-200"
          )}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
            className="w-4 h-4"
          >
            <path
              fillRule="evenodd"
              d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z"
              clipRule="evenodd"
            />
          </svg>
          {error}
        </p>
      )}
    </div>
  );
};

export default BuildingSelection;
