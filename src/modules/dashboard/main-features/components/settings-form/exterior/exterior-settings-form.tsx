"use client";
import React, { useRef, useState } from "react";
import { But<PERSON> } from "@/modules/ui/button";
import useExteriorFormStore from "@/modules/dashboard/main-features/store/exterior-form-store";
import { useImageCreditsStore } from "@/modules/dashboard/main-features/stores/use-image-credits-store";
import { useMediaQuery } from "@/modules/hooks/use-media-query";
import { ChevronDown } from "lucide-react";
import { groupBuildingOptions } from "../../../../../../../public/static/buildings";
import { groupExteriorStyleOptions } from "../../../../../../../public/static/exterior-styles";
import { PaywallDialog } from "@/modules/payments/components/paywall-dialog";
import { ExteriorFormContent } from "./exterior-form-content";
import { ExteriorMobileDrawer } from "./exterior-mobile-drawer";
import { useExteriorFormSteps } from "../../../hooks/use-exterior-form-steps";
import { useExteriorMobileDrawer } from "../../../hooks/use-exterior-mobile-drawer";
import { useExteriorSubmission } from "../../../hooks/use-exterior-submission";

const ExteriorSettingsForm: React.FC = () => {
  const store = useExteriorFormStore();
  const [showPaywall, setShowPaywall] = useState(false);
  const { credits } = useImageCreditsStore();
  const isMobile = useMediaQuery("(max-width: 768px)");
  
  // Use isPromptActive directly from the store
  const isAdvancedMode = store.isPromptActive;
  
  // To set isPromptActive, we can use setPrompt with an empty string to disable or a non-empty string to enable
  const setIsAdvancedMode = (value: boolean) => {
    if (value) {
      // Set a space in prompt to enable advanced mode
      store.setPrompt(" ");
    } else {
      // Clear prompt to disable advanced mode
      store.setPrompt("");
    }
  };
  
  // Refs for mobile keyboard handling
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  
  // Get building and style options
  const groupedBuildingOptions = groupBuildingOptions();
  const groupedStyleOptions = groupExteriorStyleOptions();
  
  // Mobile drawer state management
  const { isDrawerOpen, setIsDrawerOpen, keyboardVisible, keyboardHeight } = useExteriorMobileDrawer({
    scrollAreaRef,
  });
  
  // Form steps management
  const { currentStep, totalSteps, progress, isStepComplete } = useExteriorFormSteps({
    isAdvancedMode,
  });
  
  // Submission handler
  const showPaywallHandler = (feature: "credits" | "privacy") => {
    setShowPaywall(true);
  };
  
  const { handleSubmit, isSubmitting } = useExteriorSubmission({
    onShowPaywall: showPaywallHandler
  });
  
  // Event handlers for form elements
  const handleImageUploaded = (imageUrl: string) => {
    const hasEnoughCredits = credits && credits >= 1;
    if (!hasEnoughCredits) {
      showPaywallHandler("credits");
      return;
    }
    store.setImageUrl(imageUrl);
  };

  if (isMobile) {
    return (
      <>
        <ExteriorMobileDrawer
          isOpen={isDrawerOpen}
          setIsOpen={setIsDrawerOpen}
          keyboardVisible={keyboardVisible}
          keyboardHeight={keyboardHeight}
        >
          <ExteriorFormContent
            isAdvancedMode={isAdvancedMode}
            currentStep={currentStep}
            totalSteps={totalSteps}
            progress={progress}
            isStepComplete={isStepComplete}
            setIsAdvancedMode={setIsAdvancedMode}
            scrollAreaRef={scrollAreaRef}
            keyboardVisible={keyboardVisible}
            isMobile={isMobile}
            onSubmit={handleSubmit}
            onImageUploaded={handleImageUploaded}
            isSubmitting={isSubmitting}
            onShowPaywall={showPaywallHandler}
            groupedBuildingOptions={groupedBuildingOptions}
            groupedStyleOptions={groupedStyleOptions}
          />
        </ExteriorMobileDrawer>
        
        <Button
          variant="gradient-primary"
          size="sm"
          shine
          glow
          className="text-[14px] w-full sm:text-base px-6 sm:px-8 py-5 sm:py-6 group"
          onClick={() => setIsDrawerOpen(true)}
        >
          <span>
            {store.imageUrl ? "Edit Exterior Design" : "Exterior Design Settings"}
          </span>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
        
        <PaywallDialog
          open={showPaywall}
          onOpenChange={setShowPaywall}
          title="Get More Credits"
          description="You need 1 credit to generate an image. Upgrade to Pro to create 200 exterior designs."
          feature="generate more images"
        />
      </>
    );
  }

  return (
    <div className="h-full">
      <ExteriorFormContent
        isAdvancedMode={isAdvancedMode}
        currentStep={currentStep}
        totalSteps={totalSteps}
        progress={progress}
        isStepComplete={isStepComplete}
        setIsAdvancedMode={setIsAdvancedMode}
        scrollAreaRef={scrollAreaRef}
        isMobile={isMobile}
        onSubmit={handleSubmit}
        onImageUploaded={handleImageUploaded}
        isSubmitting={isSubmitting}
        onShowPaywall={showPaywallHandler}
        groupedBuildingOptions={groupedBuildingOptions}
        groupedStyleOptions={groupedStyleOptions}
      />
      
      <PaywallDialog
        open={showPaywall}
        onOpenChange={setShowPaywall}
        title="Get More Credits"
        description="You need 1 credit to generate an image. Upgrade to Pro to create 200 exterior designs."
        feature="generate more images"
      />
    </div>
  );
};

export default ExteriorSettingsForm;
