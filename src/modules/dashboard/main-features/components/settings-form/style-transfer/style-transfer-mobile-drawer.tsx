"use client";
import React from "react";
import { Drawer, DrawerContent } from "@/modules/ui/drawer";

interface StyleTransferMobileDrawerProps {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  children: React.ReactNode;
}

export const StyleTransferMobileDrawer: React.FC<StyleTransferMobileDrawerProps> = ({
  isOpen,
  setIsOpen,
  children,
}) => {
  return (
    <Drawer
      open={isOpen}
      onOpenChange={setIsOpen}
      dismissible={true}
    >
      <DrawerContent className="h-[85vh]">
        <div className="px-4 py-2 border-b"></div>
        <div className="flex-1 h-[calc(85vh-30px)]">{children}</div>
      </DrawerContent>
    </Drawer>
  );
}; 