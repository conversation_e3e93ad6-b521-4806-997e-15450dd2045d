"use client";
import React from "react";
import { <PERSON>roll<PERSON><PERSON> } from "@/modules/ui/scroll-area";
import { Card, CardContent } from "@/modules/ui/card";
import { Progress } from "@/modules/ui/progress";
import { Badge } from "@/modules/ui/badge";
import { Loader2, Check } from "lucide-react";
import ImageUploader from "../../settings-form/elements/image-uploader";
import useStyleTransferStore from "../../../store/style-transfer-store";
import { cn } from "@/lib/utils";
import { Button } from "@/modules/ui/button";
import { useFormStatus } from "react-dom";
import { useImageCreditsStore } from "../../../stores/use-image-credits-store";
import { Alert, AlertDescription } from "@/modules/ui/alert";
import Image from "next/image";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/modules/ui/select";
import { Label } from "@/modules/ui/label";
import { Input } from "@/modules/ui/input";
import { Textarea } from "@/modules/ui/textarea";
import { Slider } from "@/modules/ui/slider";

interface StyleTransferFormContentProps {
  currentStep: number;
  totalSteps: number;
  progress: number;
  isStepComplete: (step: number) => boolean;
  onSubmit: (formData: FormData) => Promise<void>;
  onStyleImageUploaded: (url: string) => void;
  onStructureImageUploaded: (url: string) => void;
  isSubmitting: boolean;
  onShowPaywall: () => void;
  isMobile: boolean;
}

const SubmitButton = () => {
  const { pending } = useFormStatus();
  const style_image = useStyleTransferStore((state) => state.style_image);
  const { credits } = useImageCreditsStore();
  const hasEnoughCredits = credits && credits >= 1;

  return (
    <Button
      type="submit"
      disabled={pending || !style_image || !hasEnoughCredits}
      variant="gradient-primary"
      size="sm"
      shine
      glow
      className="text-[15px] w-full sm:text-base px-6 sm:px-8 py-5 sm:py-6 group"
    >
      {pending ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Generating...
        </>
      ) : (
        <div className="flex items-center justify-center gap-2">
          <span>
            {hasEnoughCredits ? "Generate Image" : "Insufficient Credits"}
          </span>
          <div
            className={cn(
              "flex items-center gap-1 px-2 py-0.5 rounded",
              hasEnoughCredits
                ? "bg-primary-foreground/10"
                : "bg-destructive/10 text-destructive"
            )}
          >
            <div className="relative w-4 h-4">
              <Image
                src="/images/coin.png"
                alt="Credits"
                width={16}
                height={16}
                className="object-contain"
                priority
              />
            </div>
            <span>1</span>
          </div>
        </div>
      )}
    </Button>
  );
};

export const StyleTransferFormContent: React.FC<StyleTransferFormContentProps> = ({
  currentStep,
  totalSteps,
  progress,
  isStepComplete,
  onSubmit,
  onStyleImageUploaded,
  onStructureImageUploaded,
  isSubmitting,
  onShowPaywall,
  isMobile,
}) => {
  const store = useStyleTransferStore();
  const { credits } = useImageCreditsStore();
  const hasEnoughCredits = credits && credits >= 1;
  
  const getFieldError = (field: string) => {
    return store.errors?.[field]?.[0];
  };

  return (
    <form action={onSubmit} className="flex flex-col h-full">
      <ScrollArea className="flex-1">
        <div className="space-y-6 px-4 py-4">
          {/* Step 1: Image Upload */}
          <Card
            className={cn(
              "transition-all duration-200 rounded-lg",
              currentStep === 1 ? "ring-1 ring-primary/50 shadow-md" : ""
            )}
          >
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium">Upload Style Image</h3>
                  {store.style_image && (
                    <Badge
                      variant="default"
                      className="ml-2 bg-green-500 hover:bg-green-600 w-6 h-6 rounded-full p-0 flex items-center justify-center"
                    >
                      <Check className="h-3 w-3 stroke-[3]" />
                    </Badge>
                  )}
                </div>
                <ImageUploader
                  existingImageUrl={store.style_image}
                  onImageUploaded={onStyleImageUploaded}
                  error={getFieldError("style_image")}
                  disabled={!hasEnoughCredits}
                />

                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium">
                    Upload Structure Image (Optional)
                  </h3>
                  {store.structure_image && (
                    <Badge
                      variant="default"
                      className="ml-2 bg-green-500 hover:bg-green-600 w-6 h-6 rounded-full p-0 flex items-center justify-center"
                    >
                      <Check className="h-3 w-3 stroke-[3]" />
                    </Badge>
                  )}
                </div>
                <ImageUploader
                  existingImageUrl={store.structure_image}
                  onImageUploaded={onStructureImageUploaded}
                  error={getFieldError("structure_image")}
                  disabled={!hasEnoughCredits}
                />

                {!hasEnoughCredits && (
                  <Alert variant="destructive" className="mt-2">
                    <AlertDescription className="text-sm flex items-center gap-2">
                      <div className="relative w-4 h-4">
                        <Image
                          src="/images/coin.png"
                          alt="Credits"
                          width={16}
                          height={16}
                          className="object-contain"
                          priority
                        />
                      </div>
                      You need 1 credit to transfer style
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Step 2: Model Selection */}
          <Card
            className={cn(
              "transition-all duration-200 rounded-lg",
              currentStep === 2 ? "ring-1 ring-primary/50 shadow-md" : ""
            )}
          >
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium">Style Settings</h3>
                  {isStepComplete(2) && (
                    <Badge
                      variant="default"
                      className="ml-2 bg-green-500 hover:bg-green-600 w-6 h-6 rounded-full p-0 flex items-center justify-center"
                    >
                      <Check className="h-3 w-3 stroke-[3]" />
                    </Badge>
                  )}
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Model</Label>
                    <Select
                      value={store.model}
                      onValueChange={(value: any) => store.setModel(value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a model" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="fast">Fast</SelectItem>
                        <SelectItem value="high-quality">
                          High Quality
                        </SelectItem>
                        <SelectItem value="realistic">Realistic</SelectItem>
                        <SelectItem value="cinematic">Cinematic</SelectItem>
                        <SelectItem value="animated">Animated</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Prompt (Optional)</Label>
                    <Textarea
                      value={store.prompt}
                      onChange={(e) => store.setPrompt(e.target.value)}
                      placeholder="Describe what you want to see in the image"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Negative Prompt (Optional)</Label>
                    <Textarea
                      value={store.negative_prompt}
                      onChange={(e) => store.setNegativePrompt(e.target.value)}
                      placeholder="Describe what you don't want to see in the image"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Step 3: Additional Settings */}
          <Card
            className={cn(
              "transition-all duration-200 rounded-lg",
              currentStep === 3 ? "ring-1 ring-primary/50 shadow-md" : ""
            )}
          >
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium">Additional Settings</h3>
                  <span className="text-sm text-muted-foreground">
                    (Optional)
                  </span>
                </div>

                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Width</Label>
                      <Input
                        type="number"
                        value={store.width}
                        onChange={(e) =>
                          store.setWidth(parseInt(e.target.value))
                        }
                        min={128}
                        max={2048}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Height</Label>
                      <Input
                        type="number"
                        value={store.height}
                        onChange={(e) =>
                          store.setHeight(parseInt(e.target.value))
                        }
                        min={128}
                        max={2048}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Structure Depth Strength</Label>
                    <Slider
                      value={[store.structure_depth_strength]}
                      onValueChange={([value]) =>
                        store.setStructureDepthStrength(value)
                      }
                      min={0}
                      max={2}
                      step={0.1}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Structure Denoising Strength</Label>
                    <Slider
                      value={[store.structure_denoising_strength]}
                      onValueChange={([value]) =>
                        store.setStructureDenosingStrength(value)
                      }
                      min={0}
                      max={1}
                      step={0.05}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Output Format</Label>
                    <Select
                      value={store.output_format}
                      onValueChange={(value: any) =>
                        store.setOutputFormat(value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select output format" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="webp">WebP</SelectItem>
                        <SelectItem value="jpg">JPG</SelectItem>
                        <SelectItem value="png">PNG</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Output Quality</Label>
                    <Slider
                      value={[store.output_quality]}
                      onValueChange={([value]) => store.setOutputQuality(value)}
                      min={0}
                      max={100}
                      step={1}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Seed (Optional)</Label>
                    <Input
                      type="number"
                      value={store.seed || ""}
                      onChange={(e) =>
                        store.setSeed(
                          e.target.value ? parseInt(e.target.value) : undefined
                        )
                      }
                      placeholder="Random seed"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </ScrollArea>

      <div className="mt-auto bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 py-4 px-2 md:p-0 ">
        <SubmitButton />
      </div>
    </form>
  );
}; 