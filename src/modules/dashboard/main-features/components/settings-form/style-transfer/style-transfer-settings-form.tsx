"use client";
import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/modules/ui/button";
import { generateStyleTransfer } from "@/modules/dashboard/main-features/actions/render-style-transfer";
import useStyleTransferStore from "@/modules/dashboard/main-features/store/style-transfer-store";
import { useFormStatus } from "react-dom";
import ImageUploader from "@/modules/dashboard/main-features/components/settings-form/elements/image-uploader";
import { ScrollArea } from "@/modules/ui/scroll-area";
import { useCurrentUser } from "@/modules/auth/hooks/use-current-user";
import { useToast } from "@/modules/ui/use-toast";
import { Loader2 } from "lucide-react";
import { useImageCreditsStore } from "@/modules/dashboard/main-features/stores/use-image-credits-store";
import { Drawer, DrawerContent, DrawerTrigger } from "@/modules/ui/drawer";
import { useMediaQuery } from "@/modules/hooks/use-media-query";
import { Card, CardContent } from "@/modules/ui/card";
import { Badge } from "@/modules/ui/badge";
import { Check, ChevronDown } from "lucide-react";
import { FormHeader } from "../../shared/form-header";
import { cn } from "@/modules/ui";
import Image from "next/image";
import { Alert, AlertDescription } from "@/modules/ui/alert";
import { PaywallDialog } from "@/modules/payments/components/paywall-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/modules/ui/select";
import { Label } from "@/modules/ui/label";
import { Input } from "@/modules/ui/input";
import { Textarea } from "@/modules/ui/textarea";
import { Slider } from "@/modules/ui/slider";
import {
  StyleTransferParams,
  StyleTransferResult,
  PredictionStatus,
} from "@/modules/dashboard/main-features/types";
import { StyleTransferFormContent } from "./style-transfer-form-content";
import { StyleTransferMobileDrawer } from "./style-transfer-mobile-drawer";
import { useStyleTransferFormSteps } from "../../../hooks/use-style-transfer-form-steps";
import { useStyleTransferSubmission } from "../../../hooks/use-style-transfer-submission";

const SubmitButton = () => {
  const { pending } = useFormStatus();
  const style_image = useStyleTransferStore((state) => state.style_image);
  const { credits } = useImageCreditsStore();
  const hasEnoughCredits = credits && credits >= 1;

  return (
    <Button
      type="submit"
      disabled={pending || !style_image || !hasEnoughCredits}
      variant="gradient-primary"
      size="sm"
      shine
      glow
      className="text-[15px] w-full sm:text-base px-6 sm:px-8 py-5 sm:py-6 group"
    >
      {pending ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Generating...
        </>
      ) : (
        <div className="flex items-center justify-center gap-2">
          <span>
            {hasEnoughCredits ? "Generate Image" : "Insufficient Credits"}
          </span>
          <div
            className={cn(
              "flex items-center gap-1 px-2 py-0.5 rounded",
              hasEnoughCredits
                ? "bg-primary-foreground/10"
                : "bg-destructive/10 text-destructive"
            )}
          >
            <div className="relative w-4 h-4">
              <Image
                src="/images/coin.png"
                alt="Credits"
                width={16}
                height={16}
                className="object-contain"
                priority
              />
            </div>
            <span>1</span>
          </div>
        </div>
      )}
    </Button>
  );
};

const StyleTransferSettingsForm: React.FC = () => {
  const store = useStyleTransferStore();
  const [showPaywall, setShowPaywall] = useState(false);
  const { credits } = useImageCreditsStore();
  const isMobile = useMediaQuery("(max-width: 768px)");
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const currentUser = useCurrentUser();
  const { toast } = useToast();
  
  // Form steps management
  const { currentStep, totalSteps, progress, isStepComplete } = useStyleTransferFormSteps();
  
  // Handle paywall
  const showPaywallHandler = () => {
    setShowPaywall(true);
    toast({
      title: "Insufficient Credits",
      description: "You need 1 credit to generate an image",
      variant: "destructive",
    });
  };
  
  // Submission handler
  const { handleSubmit, isSubmitting } = useStyleTransferSubmission({
    onShowPaywall: showPaywallHandler
  });
  
  // Handle style image upload
  const handleStyleImageUpload = (url: string) => {
    const hasEnoughCredits = credits && credits >= 1;
    if (!hasEnoughCredits) {
      showPaywallHandler();
      return;
    }
    if (!url) {
      store.setError("style_image", ["Style image is required"]);
      return;
    }
    store.setStyleImage(url);
    store.clearErrors();
  };

  // Handle structure image upload
  const handleStructureImageUpload = (url: string) => {
    const hasEnoughCredits = credits && credits >= 1;
    if (!hasEnoughCredits) {
      showPaywallHandler();
      return;
    }
    store.setStructureImage(url);
  };

  if (isMobile) {
    return (
      <>
        <StyleTransferMobileDrawer
          isOpen={isDrawerOpen}
          setIsOpen={setIsDrawerOpen}
        >
          <StyleTransferFormContent
            currentStep={currentStep}
            totalSteps={totalSteps}
            progress={progress}
            isStepComplete={isStepComplete}
            onSubmit={handleSubmit}
            onStyleImageUploaded={handleStyleImageUpload}
            onStructureImageUploaded={handleStructureImageUpload}
            isSubmitting={isSubmitting}
            onShowPaywall={showPaywallHandler}
            isMobile={isMobile}
          />
        </StyleTransferMobileDrawer>
        
        <Button
          variant="gradient-primary"
          size="sm"
          shine
          glow
          className="text-[14px] w-full sm:text-base px-6 sm:px-8 py-5 sm:py-6 group"
          onClick={() => setIsDrawerOpen(true)}
        >
          <span>
            {store.style_image ? "Edit Settings" : "Style Transfer Settings"}
          </span>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
        
        <PaywallDialog
          open={showPaywall}
          onOpenChange={setShowPaywall}
          title="Get More Credits"
          description="You need 1 credit to generate a style transfer. Upgrade to Pro to create 200 style transfers."
          feature="generate more images"
        />
      </>
    );
  }

  return (
    <div className="h-full">
      <StyleTransferFormContent
        currentStep={currentStep}
        totalSteps={totalSteps}
        progress={progress}
        isStepComplete={isStepComplete}
        onSubmit={handleSubmit}
        onStyleImageUploaded={handleStyleImageUpload}
        onStructureImageUploaded={handleStructureImageUpload}
        isSubmitting={isSubmitting}
        onShowPaywall={showPaywallHandler}
        isMobile={isMobile}
      />
      
      <PaywallDialog
        open={showPaywall}
        onOpenChange={setShowPaywall}
        title="Get More Credits"
        description="You need 1 credit to generate a style transfer. Upgrade to Pro to create 200 style transfers."
        feature="generate more images"
      />
    </div>
  );
};

export default StyleTransferSettingsForm;
