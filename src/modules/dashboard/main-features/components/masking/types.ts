import { Line as KonvaLine } from "react-konva";

export interface MaskDrawerProps {
  onMaskCreated: (maskUrl: string) => void;
  onMaskUploaded: (maskUrl: string) => void;
  inputImageUrl: string;
  invertMask?: boolean;
  existingMaskUrl?: string;
  error?: string;
  disabled?: boolean;
  isExampleMask?: boolean;
}

export interface Line {
  tool: "brush" | "eraser";
  points: number[];
  color: string;
  strokeWidth: number;
  borderColor?: string;
}

export interface ToolbarButtonProps {
  onClick: () => void;
  icon: React.ReactNode;
  label: string;
  active?: boolean;
  disabled?: boolean;
}

export interface BrushPreviewProps {
  cursorPosition: { x: number; y: number } | null;
  size: number;
  color: string;
  stageScale: number;
}

export interface DrawingLayerProps {
  lines: Line[];
}
