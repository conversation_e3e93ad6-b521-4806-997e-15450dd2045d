export const STYLES = {
  button: {
    base: "w-full h-[120px] flex flex-col items-center justify-center gap-2 border border-dashed transition-all duration-200 ease-in-out relative overflow-hidden group rounded-lg p-4",
    icon: "w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center transform group-hover:scale-110 transition-transform duration-200",
    content: "flex flex-col space-y-1 text-center",
    title: "text-sm font-medium",
    description: "text-xs text-muted-foreground",
    overlay:
      "absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/5 to-primary/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300",
    states: {
      default:
        "bg-background hover:bg-accent/5 border-border hover:border-primary/50 hover:shadow-md",
      success:
        "bg-primary/5 border-primary/50 hover:bg-primary/10 hover:shadow-md",
      error: "bg-destructive/5 border-destructive/50 hover:bg-destructive/10",
      disabled: "bg-muted cursor-not-allowed opacity-50",
    },
  },
  status: {
    container: "space-y-1",
    header:
      "flex items-center gap-2 p-3 bg-primary/5 rounded-lg border border-primary/20",
    icon: {
      wrapper:
        "h-7 w-7 rounded-full bg-primary/20 flex items-center justify-center",
      size: "h-3.5 w-3.5 text-primary",
    },
    content: {
      wrapper: "flex-1",
      title: "text-xs font-medium",
      description: "text-[11px] text-muted-foreground",
    },
    alert: "border-primary/20",
  },
} as const;
