import { Line } from "../types";

export async function maskToStrokes(
  maskUrl: string,
  canvasSize: { width: number; height: number },
  shouldInvert: boolean = false
): Promise<Line[]> {
  const response = await fetch(maskUrl);
  const blob = await response.blob();

  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => {
      const canvas = document.createElement("canvas");
      canvas.width = img.width;
      canvas.height = img.height;
      const ctx = canvas.getContext("2d")!;
      ctx.drawImage(img, 0, 0);

      // Invert the image if needed (for example masks)
      if (shouldInvert) {
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;
        for (let i = 0; i < data.length; i += 4) {
          data[i] = 255 - data[i]; // Red
          data[i + 1] = 255 - data[i + 1]; // Green
          data[i + 2] = 255 - data[i + 2]; // Blue
          // Alpha stays the same
        }
        ctx.putImageData(imageData, 0, 0);
      }

      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;
      const strokes: Line[] = [];

      // Calculate scaling factors
      const scaleX = canvasSize.width / img.width;
      const scaleY = canvasSize.height / img.height;

      // Use a larger grid size for better performance
      const gridSize = Math.max(
        10,
        Math.floor(Math.min(img.width, img.height) / 30)
      );
      const threshold = 128;

      // Create filled strokes for white areas
      for (let y = 0; y < canvas.height; y += gridSize) {
        for (let x = 0; x < canvas.width; x += gridSize) {
          const i = (y * canvas.width + x) * 4;

          if (data[i] > threshold) {
            // Scale coordinates to canvas size
            const scaledX = x * scaleX;
            const scaledY = y * scaleY;
            const scaledGridSize = gridSize * Math.min(scaleX, scaleY);

            // Create a filled rectangle stroke
            strokes.push({
              tool: "brush",
              points: [
                scaledX,
                scaledY,
                scaledX + scaledGridSize,
                scaledY,
                scaledX + scaledGridSize,
                scaledY + scaledGridSize,
                scaledX,
                scaledY + scaledGridSize,
                scaledX,
                scaledY, // Close the path
              ],
              color: "white",
              strokeWidth: scaledGridSize * 1.5, // Slightly larger to ensure overlap
            });
          }
        }
      }

      resolve(strokes);
    };
    img.src = URL.createObjectURL(blob);
  });
}
