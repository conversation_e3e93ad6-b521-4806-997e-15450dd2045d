"use client";

import React, {
  useRef,
  useState,
  useEffect,
  useCallback,
  useMemo,
} from "react";
import { Stage, StageProps } from "react-konva";
import { But<PERSON> } from "@/modules/ui/button";
import { Dialog } from "@/modules/ui/dialog";
import {
  WandSparklesIcon,
  Check,
  FileEdit,
  FileIcon,
  Loader2,
  ImageIcon,
  Paintbrush2,
} from "lucide-react";
import { handleImageUpload } from "@/modules/dashboard/main-features/actions/upload-image";
import { useMediaQuery } from "@/modules/hooks/use-media-query";
import { cn } from "@/lib/utils";
import { generateAutoMask } from "../../actions/generate-auto-mask";
import { useToast } from "@/modules/ui/use-toast";
import useVirtualStagingFormStore from "../../store/virtual-staging-form-store";
import { MaskCanvas } from "./components/mask-canvas";
import { MaskToolbar } from "./components/mask-toolbar";
import { useMaskDrawing } from "./hooks/use-mask-drawing";
import { useCanvasState } from "./hooks/use-canvas-state";
import { DEFAULT_BRUSH_SIZE, DEFAULT_ERASER_SIZE } from "./constants";
import MaskUploader from "./mask-uploader";
import { maskToStrokes } from "./utils/mask-utils";
import { LoadingOverlay } from "./components/loading-overlay";
import { MaskEditorDialog } from "./components/mask-editor-dialog";
import { MESSAGES } from "./constants/messages";
import { STYLES } from "./constants/styles";
import type { KonvaEventObject } from "konva/lib/Node";
import type { Stage as StageType } from "konva/lib/Stage";

interface Point {
  x: number;
  y: number;
}

type DrawMode = "draw" | "pan";

interface Line {
  tool: "brush" | "eraser";
  points: number[];
  color: string;
  strokeWidth: number;
}

interface CanvasSize {
  width: number;
  height: number;
}

const KEYBOARD_SHORTCUTS = {
  UNDO: { key: "z", ctrl: true },
  REDO: { key: "z", ctrl: true, shift: true },
  CLEAR: { key: "c", ctrl: true },
  SAVE: { key: "s", ctrl: true },
  TOGGLE_ERASER: { key: "e" },
} as const;

export interface MaskDrawerProps extends React.HTMLAttributes<HTMLDivElement> {
  onMaskCreated: (maskUrl: string) => void;
  onMaskUploaded: (maskUrl: string) => void;
  inputImageUrl: string;
  invertMask?: boolean;
  existingMaskUrl?: string;
  error?: string;
  disabled?: boolean;
  isExampleMask?: boolean;
  selectedExampleMask?: string | null;
}

const MaskDrawer = ({
  onMaskCreated,
  onMaskUploaded,
  inputImageUrl,
  invertMask = true,
  existingMaskUrl,
  error,
  disabled,
  isExampleMask,
  selectedExampleMask,
}: MaskDrawerProps): JSX.Element => {
  // Refs
  const stageRef = useRef<StageType | null>(null);

  // State management hooks
  const store = useVirtualStagingFormStore();
  const {
    lines,
    setLines,
    history,
    historyStep,
    isDrawing,
    setIsDrawing,
    handleUndo,
    handleRedo,
    addLine,
    updateLastLine,
  } = useMaskDrawing({
    initialLines: store.maskLines || [],
    onLinesChange: (newLines) => {
      store.setMaskLines(newLines);
    },
  });

  const {
    stageScale,
    setStageScale,
    stagePosition,
    setStagePosition,
    mode,
    setMode,
    zoomLevel,
    handleZoomIn,
    handleZoomOut,
    handleResetZoom,
    handleDragEnd,
  } = useCanvasState();

  // Local state
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [canvasSize, setCanvasSize] = useState<CanvasSize>({
    width: 0,
    height: 0,
  });
  const [selectedColor, setSelectedColor] = useState("white");
  const [isEraser, setIsEraser] = useState(false);
  const [brushSize, setBrushSize] = useState(DEFAULT_BRUSH_SIZE);
  const [eraserSize, setEraserSize] = useState(DEFAULT_ERASER_SIZE);
  const [image, setImage] = useState<HTMLImageElement | null>(null);
  const [cursorPosition, setCursorPosition] = useState<Point | null>(null);
  const [isGeneratingAutoMask, setIsGeneratingAutoMask] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isAutoMaskGenerated, setIsAutoMaskGenerated] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState<string>(
    MESSAGES.LOADING[0]
  );
  const loadingInterval = useRef<NodeJS.Timeout>();
  const [isLoading, setIsLoading] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [progress, setProgress] = useState(0);
  const [hasLoadedMask, setHasLoadedMask] = useState(false);
  const [lastLoadedMaskUrl, setLastLoadedMaskUrl] = useState<string | null>(
    null
  );
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  const [maskEverLoaded, setMaskEverLoaded] = useState(false);

  // Hooks
  const isMobile = useMediaQuery("(max-width: 768px)");
  const { toast } = useToast();

  // Load image and calculate dimensions
  useEffect(() => {
    const img = new Image();
    img.src = inputImageUrl;
    img.onload = () => {
      setImage(img);
      const maxWidth = window.innerWidth * 0.8;
      const maxHeight = window.innerHeight * 0.6;
      const aspectRatio = img.width / img.height;
      let displayWidth = img.width;
      let displayHeight = img.height;

      if (displayWidth > maxWidth) {
        displayWidth = maxWidth;
        displayHeight = displayWidth / aspectRatio;
      }

      if (displayHeight > maxHeight) {
        displayHeight = maxHeight;
        displayWidth = displayHeight * aspectRatio;
      }

      setCanvasSize({
        width: displayWidth,
        height: displayHeight,
      });
    };
  }, [inputImageUrl]);

  // Mouse and touch event handlers
  const handleMouseDown = useCallback(
    (e: KonvaEventObject<MouseEvent | TouchEvent>) => {
      if (mode !== "draw" || isSaving) return;
      const stage = e.target.getStage();
      if (!stage) return;

      const pos = stage.getRelativePointerPosition();
      if (pos) {
        setCursorPosition(pos);
        setIsDrawing(true);
        addLine({
          tool: isEraser ? "eraser" : "brush",
          points: [pos.x, pos.y],
          color: selectedColor,
          strokeWidth: isEraser ? eraserSize : brushSize,
        });
      }
    },
    [mode, isSaving, isEraser, selectedColor, eraserSize, brushSize, addLine]
  );

  const handleMouseMove = useCallback(
    (e: KonvaEventObject<MouseEvent | TouchEvent>) => {
      const stage = e.target.getStage();
      if (!stage) return;

      const pos = stage.getRelativePointerPosition();
      if (pos) {
        setCursorPosition(pos);

        // Only update line if we're drawing
        if (mode === "draw" && isDrawing && !isSaving) {
          updateLastLine([pos.x, pos.y]);
        }
      }
    },
    [mode, isDrawing, isSaving, updateLastLine]
  );

  const handleMouseUp = useCallback(() => {
    if (mode !== "draw") return;
    setIsDrawing(false);
  }, [mode]);

  const handleMouseLeave = useCallback(() => {
    setCursorPosition(null);
    if (mode === "draw") {
      setIsDrawing(false);
    }
  }, [mode]);

  // Touch event handlers
  const handleTouchStart = useCallback(
    (e: KonvaEventObject<TouchEvent>) => {
      e.evt.preventDefault();
      handleMouseDown(e);
    },
    [handleMouseDown]
  );

  const handleTouchMove = useCallback(
    (e: KonvaEventObject<TouchEvent>) => {
      e.evt.preventDefault();
      handleMouseMove(e);
    },
    [handleMouseMove]
  );

  const handleTouchEnd = useCallback(
    (e: KonvaEventObject<TouchEvent>) => {
      e.evt.preventDefault();
      handleMouseUp();
      setCursorPosition(null);
    },
    [handleMouseUp]
  );

  const handleColorChange = useCallback((value: string) => {
    if (value === "eraser") {
      setIsEraser(true);
    } else {
      setIsEraser(false);
      setSelectedColor(value);
    }
  }, []);

  // Window resize handler
  useEffect(() => {
    const handleResize = () => {
      if (image) {
        const maxWidth = window.innerWidth * 0.8;
        const maxHeight = window.innerHeight * 0.6;
        const aspectRatio = image.width / image.height;
        let displayWidth = image.width;
        let displayHeight = image.height;

        if (displayWidth > maxWidth) {
          displayWidth = maxWidth;
          displayHeight = displayWidth / aspectRatio;
        }

        if (displayHeight > maxHeight) {
          displayHeight = maxHeight;
          displayWidth = displayHeight * aspectRatio;
        }

        setCanvasSize({
          width: displayWidth,
          height: displayHeight,
        });
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [image]);

  const createMaskCanvas = useCallback(
    (shouldInvert: boolean = true): HTMLCanvasElement => {
      const tempCanvas = document.createElement("canvas");
      const { width, height } = canvasSize;
      tempCanvas.width = width;
      tempCanvas.height = height;
      const ctx = tempCanvas.getContext("2d");

      if (!ctx) {
        throw new Error("Could not get canvas context");
      }

      // For example masks, start with white background (since we don't invert)
      // For regular masks, start with black background
      ctx.fillStyle = isExampleMask ? "#FFFFFF" : "#000000";
      ctx.fillRect(0, 0, width, height);

      // Create a separate canvas for drawing
      const drawingCanvas = document.createElement("canvas");
      drawingCanvas.width = width;
      drawingCanvas.height = height;
      const drawingCtx = drawingCanvas.getContext("2d");

      if (!drawingCtx) {
        throw new Error("Could not get drawing context");
      }

      // Clear and set background
      drawingCtx.clearRect(0, 0, width, height);
      drawingCtx.fillStyle = isExampleMask ? "#FFFFFF" : "#000000";
      drawingCtx.fillRect(0, 0, width, height);

      // Set up drawing properties
      drawingCtx.lineCap = "round";
      drawingCtx.lineJoin = "round";

      // Draw all lines with inverted colors for example masks
      lines.forEach((line: Line) => {
        drawingCtx.beginPath();
        if (isExampleMask) {
          // For example masks: black for brush (areas to remove), white for eraser
          drawingCtx.strokeStyle =
            line.tool === "eraser" ? "#FFFFFF" : "#000000";
        } else {
          // For regular masks: white for brush (areas to keep), black for eraser
          drawingCtx.strokeStyle =
            line.tool === "eraser" ? "#000000" : "#FFFFFF";
        }
        drawingCtx.lineWidth = line.strokeWidth;

        for (let i = 0; i < line.points.length; i += 2) {
          const x = line.points[i];
          const y = line.points[i + 1];
          if (i === 0) {
            drawingCtx.moveTo(x, y);
          } else {
            drawingCtx.lineTo(x, y);
          }
        }
        drawingCtx.stroke();
      });

      // Copy the drawing to the main canvas
      ctx.drawImage(drawingCanvas, 0, 0);

      // Only invert for regular masks when requested
      if (shouldInvert && !isExampleMask) {
        const imageData = ctx.getImageData(0, 0, width, height);
        const data = imageData.data;
        for (let i = 0; i < data.length; i += 4) {
          data[i] = 255 - data[i];
          data[i + 1] = 255 - data[i + 1];
          data[i + 2] = 255 - data[i + 2];
        }
        ctx.putImageData(imageData, 0, 0);
      }

      // Ensure strictly black and white output
      const imageData = ctx.getImageData(0, 0, width, height);
      const data = imageData.data;
      for (let i = 0; i < data.length; i += 4) {
        const avg = (data[i] + data[i + 1] + data[i + 2]) / 3;
        const value = avg > 128 ? 255 : 0;
        data[i] = value;
        data[i + 1] = value;
        data[i + 2] = value;
        data[i + 3] = 255;
      }
      ctx.putImageData(imageData, 0, 0);

      return tempCanvas;
    },
    [canvasSize, lines, isExampleMask]
  );

  const handleSaveMask = useCallback(async (): Promise<void> => {
    try {
      setIsSaving(true);
      // For example masks: never invert when saving
      // For regular masks: follow the invertMask parameter
      const shouldInvertMask = !isExampleMask && invertMask;
      const tempCanvas = createMaskCanvas(shouldInvertMask);
      const dataURL = tempCanvas.toDataURL();
      const response = await fetch(dataURL);
      const blob = await response.blob();
      const file = new File([blob], "mask.png", { type: "image/png" });

      const formData = new FormData();
      formData.append("file", file);

      const result = await handleImageUpload(formData);
      if ("error" in result) {
        throw new Error("Failed to upload mask");
      }

      setLastLoadedMaskUrl(result.displayUrl);
      setHasLoadedMask(true);
      store.setIsMaskSaved(true);

      // If this was an example mask being edited, treat it as a manual mask now
      if (isExampleMask) {
        store.setIsExampleMask(false);
        store.setIsEditingMask(true);
      }

      onMaskCreated(result.displayUrl);
      onMaskUploaded(result.displayUrl);
      setIsSaved(true);

      toast({
        title: "Success",
        description: "Mask saved successfully",
      });

      setIsDialogOpen(false);
    } catch (e) {
      console.error("Error saving mask:", e);
      toast({
        title: "Error",
        description: "Failed to save mask",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  }, [
    createMaskCanvas,
    onMaskCreated,
    onMaskUploaded,
    toast,
    isExampleMask,
    invertMask,
    store,
  ]);

  const handleDownloadMask = () => {
    const tempCanvas = createMaskCanvas(true);
    const dataURL = tempCanvas.toDataURL();
    const link = document.createElement("a");
    link.href = dataURL;
    link.download = "mask.png";
    link.click();
  };

  const handleAutoMaskGeneration = useCallback(async (): Promise<void> => {
    try {
      setIsLoading(true);
      setIsGeneratingAutoMask(true);
      setProgress(0);

      const progressInterval = setInterval(() => {
        setProgress((prev) => Math.min(prev + 10, 90));
      }, 500);

      const { mergedMask } = await generateAutoMask(inputImageUrl);
      const autoStrokes = await maskToStrokes(mergedMask, canvasSize);

      clearInterval(progressInterval);
      setProgress(100);

      if (!autoStrokes) {
        throw new Error("Failed to generate mask strokes");
      }

      setLines(autoStrokes);
      setIsAutoMaskGenerated(true);
      toast({
        title: "Success",
        description:
          "Auto mask generated! You can now refine it with the brush tools.",
      });
      setIsDialogOpen(true);
    } catch (error) {
      console.error("Error generating auto mask:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to generate automatic mask",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
      setIsGeneratingAutoMask(false);
      setProgress(0);
    }
  }, [inputImageUrl, canvasSize, setLines, toast]);

  const handleClear = useCallback(() => {
    setLines([]);
    setIsAutoMaskGenerated(false);
    setHasLoadedMask(false);
    setLastLoadedMaskUrl(null);
    toast({
      title: "Success",
      description: "All brush strokes cleared",
    });
  }, []);

  const stageConfig = useMemo(
    (): StageProps => ({
      width: canvasSize.width,
      height: canvasSize.height,
      scaleX: stageScale,
      scaleY: stageScale,
      x: stagePosition.x,
      y: stagePosition.y,
      draggable: mode === "pan",
    }),
    [canvasSize, stageScale, stagePosition, mode]
  );

  const renderError = () => {
    if (!error) return null;
    return (
      <div className="mt-2 text-sm text-destructive flex items-center gap-2">
        <div className="w-2 h-2 rounded-full bg-destructive animate-pulse" />
        {error}
      </div>
    );
  };

  useEffect(() => {
    if (isGeneratingAutoMask) {
      let messageIndex = 0;
      setLoadingMessage(MESSAGES.LOADING[0]);

      loadingInterval.current = setInterval(() => {
        messageIndex = (messageIndex + 1) % MESSAGES.LOADING.length;
        setLoadingMessage(MESSAGES.LOADING[messageIndex]);
      }, 2000);

      return () => {
        if (loadingInterval.current) {
          clearInterval(loadingInterval.current);
        }
      };
    }
  }, [isGeneratingAutoMask]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (!isDialogOpen) return;

      const isCtrlPressed = e.ctrlKey || e.metaKey;

      if (
        isCtrlPressed &&
        e.key === KEYBOARD_SHORTCUTS.UNDO.key &&
        !e.shiftKey
      ) {
        e.preventDefault();
        handleUndo();
      } else if (
        isCtrlPressed &&
        e.key === KEYBOARD_SHORTCUTS.REDO.key &&
        e.shiftKey
      ) {
        e.preventDefault();
        handleRedo();
      } else if (isCtrlPressed && e.key === KEYBOARD_SHORTCUTS.SAVE.key) {
        e.preventDefault();
        handleSaveMask();
      } else if (e.key === KEYBOARD_SHORTCUTS.TOGGLE_ERASER.key) {
        e.preventDefault();
        setIsEraser((prev) => !prev);
      }
    };

    window.addEventListener("keydown", handleKeyPress);
    return () => window.removeEventListener("keydown", handleKeyPress);
  }, [isDialogOpen, handleUndo, handleRedo, handleSaveMask]);

  // Unsaved changes warning
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (lines.length > 0 && !isSaved) {
        e.preventDefault();
        e.returnValue = "";
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => window.removeEventListener("beforeunload", handleBeforeUnload);
  }, [lines, isSaved]);

  const handleBrushSizeChange = useCallback((value: number[]) => {
    setBrushSize(value[0]);
  }, []);

  const handleEraserSizeChange = useCallback((value: number[]) => {
    setEraserSize(value[0]);
  }, []);

  // Add a ref to track the last processed mask URL
  const lastProcessedMaskUrlRef = useRef<string | null>(null);

  // Update the example mask loading effect
  useEffect(() => {
    const loadExampleMask = async () => {
      // Skip if:
      // 1. Already processed this mask
      // 2. Currently editing
      // 3. No mask URL
      // 4. Not an example mask
      // 5. No canvas size
      if (
        lastProcessedMaskUrlRef.current === existingMaskUrl ||
        store.isEditingMask ||
        !existingMaskUrl ||
        !isExampleMask ||
        canvasSize.width === 0
      ) {
        return;
      }

      try {
        setIsLoading(true);
        const strokes = await maskToStrokes(existingMaskUrl, canvasSize, true);

        if (strokes) {
          setLines(strokes);
          setIsAutoMaskGenerated(true);
          setHasLoadedMask(true);
          setLastLoadedMaskUrl(existingMaskUrl);
          lastProcessedMaskUrlRef.current = existingMaskUrl;

          // Set initial state for editing
          store.setIsMaskSaved(true);
          store.setIsEditingMask(false);
        }
      } catch (error) {
        console.error("Error loading example mask:", error);
        toast({
          title: "Error",
          description: "Failed to load example mask",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadExampleMask();
  }, [existingMaskUrl, canvasSize.width, setLines, isExampleMask, store]);

  // Reset states when example mask changes
  useEffect(() => {
    if (!isExampleMask) {
      setHasLoadedMask(false);
      setLastLoadedMaskUrl(null);
      lastProcessedMaskUrlRef.current = null;
    }
  }, [isExampleMask]);

  // Update button text based on example mask and auto-generated state
  const getButtonText = () => {
    if (isExampleMask) {
      return MESSAGES.AUTO_MASK.initial; // Show normal AI mask text for example masks
    }
    return isAutoMaskGenerated
      ? MESSAGES.AUTO_MASK.success
      : MESSAGES.AUTO_MASK.initial;
  };

  // Get status messages based on mask type
  const getStatusMessages = () => {
    if (isExampleMask) {
      // If it's an example mask but has been edited and saved, show edited state
      if (store.isMaskSaved && store.isEditingMask) {
        return MESSAGES.STATUS.example.edited;
      }
      return MESSAGES.STATUS.example.initial;
    }
    if (isAutoMaskGenerated) return MESSAGES.STATUS.auto;
    return MESSAGES.STATUS.manual;
  };

  // Handle mask removal
  const handleMaskRemoval = () => {
    setLines([]);
    setIsAutoMaskGenerated(false);
    setHasLoadedMask(false);
    setLastLoadedMaskUrl(null);
    store.setIsMaskSaved(false);
    store.setIsEditingMask(false);
    store.setIsExampleMask(false);
    onMaskUploaded("");
  };

  return (
    <section
      className="relative space-y-6"
      role="region"
      aria-label="Mask Drawing Tool"
    >
      {isGeneratingAutoMask && (
        <LoadingOverlay message={loadingMessage} progress={progress} />
      )}

      {/* Status Message */}
      {(existingMaskUrl || isAutoMaskGenerated) && (
        <div className={STYLES.status.container}>
          <div className={STYLES.status.header}>
            <div className="flex items-center gap-2 flex-1">
              <div className={STYLES.status.icon.wrapper}>
                {isExampleMask ? (
                  <ImageIcon className={STYLES.status.icon.size} />
                ) : isAutoMaskGenerated ? (
                  <Paintbrush2 className={STYLES.status.icon.size} />
                ) : (
                  <Check className={STYLES.status.icon.size} />
                )}
              </div>
              <div className={STYLES.status.content.wrapper}>
                <p className={STYLES.status.content.title}>
                  {getStatusMessages().title}
                </p>
                <p className={STYLES.status.content.description}>
                  {getStatusMessages().description}
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="ml-auto shrink-0"
              onClick={handleMaskRemoval}
            >
              Remove
            </Button>
          </div>
        </div>
      )}

      <div className="space-y-6">
        <div
          className="grid grid-cols-2 gap-4"
          role="group"
          aria-label="Mask generation options"
        >
          <Button
            onClick={handleAutoMaskGeneration}
            disabled={isLoading || disabled}
            type="button"
            className={cn(
              STYLES.button.base,
              isAutoMaskGenerated
                ? STYLES.button.states.success
                : error
                ? STYLES.button.states.error
                : disabled || isLoading
                ? STYLES.button.states.disabled
                : STYLES.button.states.default
            )}
            variant="outline"
          >
            <div className={STYLES.button.icon}>
              {isLoading ? (
                <Loader2 className="h-5 w-5 text-primary animate-spin" />
              ) : isAutoMaskGenerated ? (
                <Check className="h-5 w-5 text-primary" />
              ) : (
                <WandSparklesIcon className="h-5 w-5 text-primary" />
              )}
            </div>
            <div className={STYLES.button.content}>
              <span className={STYLES.button.title}>
                {getButtonText().title}
              </span>
              <span className={STYLES.button.description}>
                {getButtonText().description}
              </span>
            </div>
            <div className={STYLES.button.overlay} />
          </Button>

          <Button
            onClick={() => !disabled && setIsDialogOpen(true)}
            disabled={isGeneratingAutoMask || disabled}
            type="button"
            className={cn(
              STYLES.button.base,
              error
                ? STYLES.button.states.error
                : disabled
                ? STYLES.button.states.disabled
                : STYLES.button.states.default
            )}
            variant="outline"
          >
            <div className={STYLES.button.icon}>
              {isAutoMaskGenerated || isExampleMask ? (
                <FileEdit className="h-5 w-5 text-primary" />
              ) : (
                <FileIcon className="h-5 w-5 text-primary" />
              )}
            </div>
            <div className={STYLES.button.content}>
              <span className={STYLES.button.title}>
                {isAutoMaskGenerated || isExampleMask
                  ? MESSAGES.MANUAL_MASK.edit.title
                  : MESSAGES.MANUAL_MASK.initial.title}
              </span>
              <span className={STYLES.button.description}>
                {isAutoMaskGenerated || isExampleMask
                  ? MESSAGES.MANUAL_MASK.edit.description
                  : MESSAGES.MANUAL_MASK.initial.description}
              </span>
            </div>
            <div className={STYLES.button.overlay} />
          </Button>
        </div>

        {renderError()}
      </div>

      <Dialog
        open={isDialogOpen}
        onOpenChange={(open) => {
          if (!open) {
            if (!store.isMaskSaved && lines.length > 0) {
              const confirmed = window.confirm(
                "You have unsaved changes. Are you sure you want to close?"
              );
              if (!confirmed) {
                return;
              }
            }
            store.setIsEditingMask(false);
          }
          setIsDialogOpen(open);
        }}
      >
        <MaskEditorDialog isMobile={isMobile}>
          <div className="flex flex-col h-[calc(100vh-57px)] overflow-hidden">
            <MaskToolbar
              isEraser={isEraser}
              selectedColor={selectedColor}
              brushSize={brushSize}
              eraserSize={eraserSize}
              historyStep={historyStep}
              historyLength={history.length}
              isSaving={isSaving}
              onColorChange={handleColorChange}
              onBrushSizeChange={handleBrushSizeChange}
              onEraserSizeChange={handleEraserSizeChange}
              onUndo={handleUndo}
              onRedo={handleRedo}
              onSave={handleSaveMask}
              onDownload={handleDownloadMask}
              onClear={handleClear}
            />

            <div className="relative flex-1 bg-muted/5">
              <MaskCanvas
                stageConfig={stageConfig}
                image={image}
                canvasSize={canvasSize}
                lines={lines}
                cursorPosition={cursorPosition}
                isEraser={isEraser}
                brushSize={brushSize}
                eraserSize={eraserSize}
                selectedColor={selectedColor}
                stageScale={stageScale}
                mode={mode}
                setMode={setMode}
                onZoomIn={handleZoomIn}
                onZoomOut={handleZoomOut}
                onResetZoom={handleResetZoom}
                onClear={handleClear}
                isSaving={isSaving}
                isMobile={isMobile}
                handleMouseDown={handleMouseDown}
                handleMouseMove={handleMouseMove}
                handleMouseUp={handleMouseUp}
                handleMouseLeave={handleMouseLeave}
                handleTouchStart={handleTouchStart}
                handleTouchMove={handleTouchMove}
                handleTouchEnd={handleTouchEnd}
              />
            </div>
          </div>
        </MaskEditorDialog>
      </Dialog>
    </section>
  );
};

export default MaskDrawer;
