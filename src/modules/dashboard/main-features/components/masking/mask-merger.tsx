"use client";

import sharp from "sharp";

export async function mergeMasks(
  ceilingMaskUrl: string,
  structuralMaskUrl: string
): Promise<{
  mergedBlob: Blob;
  width: number;
  height: number;
}> {
  // Fetch both mask images
  const [ceilingResponse, structuralResponse] = await Promise.all([
    fetch(ceilingMaskUrl),
    fetch(structuralMaskUrl),
  ]);

  if (!ceilingResponse.ok || !structuralResponse.ok) {
    throw new Error("Failed to fetch mask images");
  }

  const [ceilingBuffer, structuralBuffer] = await Promise.all([
    ceilingResponse.arrayBuffer(),
    structuralResponse.arrayBuffer(),
  ]);

  // Use sharp to process images
  const ceilingImage = sharp(Buffer.from(ceilingBuffer));
  const structuralImage = sharp(Buffer.from(structuralBuffer));

  // Get image metadata
  const { width, height } = await ceilingImage.metadata();

  if (!width || !height) {
    throw new Error("Invalid image dimensions");
  }

  // Composite the images
  const mergedBuffer = await ceilingImage
    .composite([
      {
        input: await structuralImage.toBuffer(),
        blend: "lighten",
      },
    ])
    .jpeg()
    .toBuffer();

  // Convert to Blob
  const mergedBlob = new Blob([mergedBuffer], { type: "image/jpeg" });

  return {
    mergedBlob,
    width,
    height,
  };
}
