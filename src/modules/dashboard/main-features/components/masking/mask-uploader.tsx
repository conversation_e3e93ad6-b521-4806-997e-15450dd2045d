"use client";

import React, { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import { handleImageUpload } from "@/modules/dashboard/main-features/actions/upload-image";
import {
  Loader2,
} from "lucide-react";
import { cn } from "@/modules/ui";

interface MaskUploaderProps {
  existingMaskUrl?: string;
  onMaskUploaded: (url: string) => void;
  error?: string;
  disabled?: boolean;
  isAutoMaskGenerated?: boolean;
  isExampleMask?: boolean;
}

const MaskUploader: React.FC<MaskUploaderProps> = ({
  existingMaskUrl,
  onMaskUploaded,
  error,
  disabled,
}) => {
  const [isUploading, setIsUploading] = useState(false);

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      const file = acceptedFiles[0];
      if (!file) return;

      try {
        setIsUploading(true);
        const formData = new FormData();
        formData.append("file", file);

        const result = await handleImageUpload(formData);
        if ("error" in result) {
          throw new Error("Failed to upload mask");
        }

        onMaskUploaded(result.displayUrl);
      } catch (error) {
        console.error("Upload error:", error);
      } finally {
        setIsUploading(false);
      }
    },
    [onMaskUploaded]
  );

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      "image/*": [".png", ".jpg", ".jpeg", ".webp"],
    },
    maxFiles: 1,
    multiple: false,
    disabled,
  });

  return (
    <div
      {...getRootProps()}
      className={cn(
        "relative border border-dashed rounded-lg p-3 cursor-pointer transition-colors duration-200 text-center",
        error
          ? "border-destructive/50 bg-destructive/5"
          : disabled
          ? "bg-muted cursor-not-allowed opacity-50"
          : "border-border hover:bg-accent/5 hover:border-primary/50"
      )}
    >
      <input {...getInputProps()} />
      {isUploading ? (
        <div className="flex items-center justify-center gap-2">
          <Loader2 className="h-4 w-4 animate-spin" />
          <p className="text-xs">Uploading...</p>
        </div>
      ) : (
        <p className="text-xs text-muted-foreground">
          Click to browse or drag and drop
        </p>
      )}
      {error && <p className="text-xs text-destructive mt-2">{error}</p>}
    </div>
  );
};

export default MaskUploader;
