import React from "react";
import { Stage, Layer, Image as KonvaImage, Rect, Text } from "react-konva";
import { BrushPreview } from "./brush-preview";
import { DrawingLayer } from "./drawing-layer";
import { ToolbarButton } from "./toolbar-button";
import { Eraser, Move, ZoomIn, ZoomOut, Trash2 } from "lucide-react";
import { Line } from "../types";
import type { KonvaEventObject } from "konva/lib/Node";

interface MaskCanvasProps {
  stageConfig: any;
  image: HTMLImageElement | null;
  canvasSize: { width: number; height: number };
  lines: Line[];
  cursorPosition: { x: number; y: number } | null;
  isEraser: boolean;
  brushSize: number;
  eraserSize: number;
  selectedColor: string;
  stageScale: number;
  mode: "draw" | "pan";
  setMode: (mode: "draw" | "pan") => void;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetZoom: () => void;
  onClear: () => void;
  isSaving: boolean;
  isMobile: boolean;
  handleMouseDown: (e: KonvaEventObject<MouseEvent | TouchEvent>) => void;
  handleMouseMove: (e: KonvaEventObject<MouseEvent | TouchEvent>) => void;
  handleMouseUp: () => void;
  handleMouseLeave: () => void;
  handleTouchStart: (e: KonvaEventObject<TouchEvent>) => void;
  handleTouchMove: (e: KonvaEventObject<TouchEvent>) => void;
  handleTouchEnd: (e: KonvaEventObject<TouchEvent>) => void;
}

export const MaskCanvas: React.FC<MaskCanvasProps> = ({
  stageConfig,
  image,
  canvasSize,
  lines,
  cursorPosition,
  isEraser,
  brushSize,
  eraserSize,
  selectedColor,
  stageScale,
  mode,
  setMode,
  onZoomIn,
  onZoomOut,
  onResetZoom,
  onClear,
  isSaving,
  isMobile,
  handleMouseDown,
  handleMouseMove,
  handleMouseUp,
  handleMouseLeave,
  handleTouchStart,
  handleTouchMove,
  handleTouchEnd,
}) => {
  // Define the staging area overlay colors
  const stagingAreaColor = "rgba(206, 255, 122, 0.3)"; // Light green with 30% opacity
  const stagingAreaStroke = "rgba(206, 255, 122, 0.8)"; // Stronger green for borders
  const hasDefinedAreas = lines.length > 0;

  return (
    <div className="relative w-full h-full flex items-center justify-center bg-muted">
      <Stage
        {...stageConfig}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseLeave}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        style={{ touchAction: "none" }}
      >
        {/* Base Image Layer */}
        <Layer>
          {image && (
            <KonvaImage
              image={image}
              width={canvasSize.width}
              height={canvasSize.height}
              opacity={1}
              perfectDrawEnabled={false}
              transformsEnabled="position"
            />
          )}
        </Layer>

        {/* Staging Area Overlay Layer */}
        <Layer>
          <Rect
            width={canvasSize.width}
            height={canvasSize.height}
            fill={hasDefinedAreas ? "rgba(0, 0, 0, 0.1)" : "rgba(0, 0, 0, 0.2)"}
          />

          {/* Show helper text when no areas are defined */}
          {!hasDefinedAreas && mode === "draw" && (
            <>
              <Text
                text="Draw over areas you want to stage"
                fontSize={16}
                fontFamily="system-ui"
                fill="white"
                x={canvasSize.width / 2}
                y={canvasSize.height / 2 - 20}
                align="center"
                width={canvasSize.width}
                offsetX={canvasSize.width / 2}
              />
              <Text
                text="Leave windows, doors, and ceilings unmarked"
                fontSize={14}
                fontFamily="system-ui"
                fill="rgba(255, 255, 255, 0.7)"
                x={canvasSize.width / 2}
                y={canvasSize.height / 2 + 10}
                align="center"
                width={canvasSize.width}
                offsetX={canvasSize.width / 2}
              />
            </>
          )}
        </Layer>

        {/* Mask Drawing Layer with enhanced visualization */}
        <Layer>
          <DrawingLayer
            lines={lines.map((line) => ({
              ...line,
              color: line.tool === "eraser" ? "transparent" : stagingAreaColor,
              strokeWidth: line.strokeWidth,
              borderColor: stagingAreaStroke,
            }))}
          />
        </Layer>

        {/* Brush Preview Layer */}
        <Layer>
          <BrushPreview
            cursorPosition={cursorPosition}
            size={isEraser ? eraserSize * 1.5 : brushSize}
            color={isEraser ? "#FF3333" : stagingAreaColor}
            stageScale={stageScale}
          />
        </Layer>
      </Stage>

      {/* Toolbar */}
      {!isMobile && (
        <div className="absolute top-2 right-2 bg-background/95 p-1.5 rounded-lg shadow-lg border border-border">
          <div className="flex flex-col gap-1.5">
            <ToolbarButton
              onClick={() => setMode("draw")}
              icon={<Eraser className="h-4 w-4" />}
              label="Draw"
              active={mode === "draw"}
            />
            <ToolbarButton
              onClick={() => setMode("pan")}
              icon={<Move className="h-4 w-4" />}
              label="Pan"
              active={mode === "pan"}
            />
            <div className="h-px bg-gray-200 dark:bg-gray-700 my-1" />
            <ToolbarButton
              onClick={onZoomIn}
              icon={<ZoomIn className="h-4 w-4" />}
              label="Zoom In"
            />
            <ToolbarButton
              onClick={onZoomOut}
              icon={<ZoomOut className="h-4 w-4" />}
              label="Zoom Out"
            />
            <ToolbarButton
              onClick={onResetZoom}
              icon={<ZoomOut className="h-4 w-4" />}
              label="Reset"
            />
            {hasDefinedAreas && (
              <>
                <div className="h-px bg-gray-200 dark:bg-gray-700 my-1" />
                <ToolbarButton
                  onClick={onClear}
                  icon={<Trash2 className="h-4 w-4" />}
                  label="Clear"
                />
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
