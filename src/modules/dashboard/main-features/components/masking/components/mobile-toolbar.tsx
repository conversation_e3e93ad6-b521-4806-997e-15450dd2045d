import React from "react";
import { <PERSON><PERSON> } from "@/modules/ui/button";
import { <PERSON>lider } from "@/modules/ui/slider";
import { ToggleGroup, ToggleGroupItem } from "@/modules/ui/toggle-group";
import {
  Eraser,
  Save,
  Download,
  Undo2,
  Redo2,
  Loader2,
  Trash2,
} from "lucide-react";
import { BRUSH_COLORS } from "../constants";
import { Drawer, DrawerContent, DrawerTrigger } from "@/modules/ui/drawer";
import { Settings2 } from "lucide-react";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/modules/ui/tooltip";

interface MobileToolbarProps {
  isEraser: boolean;
  selectedColor: string;
  brushSize: number;
  eraserSize: number;
  historyStep: number;
  historyLength: number;
  isSaving: boolean;
  onColorChange: (value: string) => void;
  onBrushSizeChange: (value: number[]) => void;
  onEraserSizeChange: (value: number[]) => void;
  onUndo: () => void;
  onRedo: () => void;
  onSave: () => void;
  onDownload: () => void;
  onClear: () => void;
}

export const MobileToolbar: React.FC<MobileToolbarProps> = (props) => {
  return (
    <div className="flex flex-col gap-2">
      {/* Top toolbar with essential controls */}
      <div className="flex items-center justify-between p-2 bg-muted/50 border-b">
        <div className="flex items-center gap-2">
          <ToggleGroup
            type="single"
            value={props.isEraser ? "eraser" : props.selectedColor}
            onValueChange={(value) => {
              if (value === "eraser" || value) {
                props.onColorChange(value);
              }
            }}
            className="flex-wrap"
          >
            {BRUSH_COLORS.slice(0, 3).map((brush) => (
              <ToggleGroupItem
                key={brush.color}
                value={brush.color}
                aria-label={brush.label}
                className="p-1.5"
              >
                <div
                  className="w-5 h-5 rounded-full border-2 border-gray-200 dark:border-gray-700"
                  style={{ backgroundColor: brush.color }}
                />
              </ToggleGroupItem>
            ))}
            <ToggleGroupItem
              value="eraser"
              aria-label="Eraser"
              className="p-1.5"
            >
              <Eraser className="h-4 w-4" />
            </ToggleGroupItem>
          </ToggleGroup>

          <div className="flex gap-1">
            <Button
              size="icon"
              variant="ghost"
              onClick={props.onUndo}
              disabled={props.historyStep === 0}
            >
              <Undo2 className="h-4 w-4" />
            </Button>
            <Button
              size="icon"
              variant="ghost"
              onClick={props.onRedo}
              disabled={props.historyStep === props.historyLength - 1}
            >
              <Redo2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={props.onClear}
                size="icon"
                variant="outline"
                className="hover:bg-destructive/10 hover:text-destructive hover:border-destructive/50"
                disabled={props.isSaving}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Clear All</TooltipContent>
          </Tooltip>

          <Button onClick={props.onSave} size="icon" disabled={props.isSaving}>
            {props.isSaving ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Save className="h-4 w-4" />
            )}
          </Button>

          <Drawer>
            <DrawerTrigger asChild>
              <Button size="icon" variant="outline">
                <Settings2 className="h-4 w-4" />
              </Button>
            </DrawerTrigger>
            <DrawerContent>
              <div className="max-w-md w-full mx-auto">
                <div className="space-y-4 p-4">
                  <div className="px-1">
                    <ToggleGroup
                      type="single"
                      value={props.isEraser ? "eraser" : props.selectedColor}
                      onValueChange={(value) => {
                        if (value === "eraser" || value) {
                          props.onColorChange(value);
                        }
                      }}
                      className="flex-wrap gap-1"
                    ></ToggleGroup>
                  </div>

                  <div className="px-1">
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium min-w-[100px]">
                          {props.isEraser ? "Eraser" : "Brush"} Size:
                        </span>
                        <Slider
                          value={[
                            props.isEraser ? props.eraserSize : props.brushSize,
                          ]}
                          onValueChange={
                            props.isEraser
                              ? props.onEraserSizeChange
                              : props.onBrushSizeChange
                          }
                          min={1}
                          max={100}
                          step={1}
                          className="flex-1"
                        />
                        <span className="text-sm min-w-[40px] text-right">
                          {props.isEraser ? props.eraserSize : props.brushSize}
                          px
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="px-1 pt-4">
                    <Button
                      onClick={props.onDownload}
                      variant="outline"
                      className="w-full gap-2"
                      disabled={props.isSaving}
                    >
                      <Download className="h-4 w-4" /> Download Mask
                    </Button>
                  </div>
                </div>
              </div>
            </DrawerContent>
          </Drawer>
        </div>
      </div>
    </div>
  );
};
