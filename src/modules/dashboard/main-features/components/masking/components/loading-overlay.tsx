"use client";

import React from "react";
import { Wand<PERSON>parklesIcon, Loader2 } from "lucide-react";

interface LoadingOverlayProps {
  message: string;
  progress?: number;
  children?: React.ReactNode;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  message,
  progress,
  children,
}) => (
  <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-[100] flex flex-col items-center justify-center gap-8">
    <div className="flex flex-col items-center gap-8">
      <div className="relative flex items-center justify-center">
        <div className="w-20 h-20 rounded-full bg-primary/10 flex items-center justify-center">
          <div className="absolute inset-0 rounded-full border-t-2 border-primary animate-spin" />
          <WandSparklesIcon className="h-8 w-8 text-primary animate-pulse" />
        </div>
        <div className="absolute -inset-1 w-[88px] h-[88px] rounded-full bg-primary/5 animate-pulse blur-xl" />
      </div>

      <div className="text-center flex flex-col items-center max-w-xs space-y-2">
        <span className="text-lg font-medium text-foreground">{message}</span>
        <span className="text-sm text-muted-foreground/80">
          This process will take a few seconds...
        </span>
      </div>

      <div className="flex gap-2">
        {[1, 2, 3].map((i) => (
          <div
            key={i}
            className="w-2 h-2 rounded-full bg-primary/50 animate-bounce"
            style={{ animationDelay: `${i * 0.2}s` }}
          />
        ))}
      </div>
    </div>

    {progress !== undefined && (
      <div className="w-full max-w-xs mt-4">
        <div className="h-1 bg-muted rounded-full overflow-hidden">
          <div
            className="h-full bg-primary transition-all duration-300 ease-out"
            style={{ width: `${progress}%` }}
          />
        </div>
      </div>
    )}

    {children}
  </div>
);
