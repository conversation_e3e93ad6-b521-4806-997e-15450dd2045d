import React from "react";
import { Circle } from "react-konva";
import { BrushPreviewProps } from "../types";

export const BrushPreview: React.FC<BrushPreviewProps> = ({
  cursorPosition,
  size,
  color,
  stageScale,
}) => {
  if (!cursorPosition) return null;

  return (
    <Circle
      x={cursorPosition.x}
      y={cursorPosition.y}
      radius={size / 2}
      stroke={color}
      strokeWidth={1 / stageScale}
      fillEnabled={false}
      listening={false}
    />
  );
};
