"use client";

import React from "react";
import {
  Dialog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from "@/modules/ui/dialog";
import { HelpCircle } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/modules/ui/tooltip";

interface MaskEditorDialogProps {
  isMobile: boolean;
  children: React.ReactNode;
}

export const MaskEditorDialog: React.FC<MaskEditorDialogProps> = ({
  isMobile,
  children,
}) => (
  <DialogContent className="max-w-screen max-h-[100dvh] sm:max-h-[95dvh] md:max-h-[90dvh] w-screen h-screen shadow-sm p-0 overflow-hidden bg-background rounded-none border-border/60 dark:border-border/40">
    <DialogHeader className="px-4 py-2 border-b border-border shrink-0">
      <div className="flex items-center gap-2">
        <DialogTitle>Mask Editor</DialogTitle>
        <TooltipProvider>
          <Tooltip delayDuration={300}>
            <TooltipTrigger asChild>
              <button
                type="button"
                className="inline-flex items-center justify-center rounded-full hover:bg-muted p-1 transition-colors"
              >
                <HelpCircle className="h-4 w-4 text-muted-foreground" />
                <span className="sr-only">Help</span>
              </button>
            </TooltipTrigger>
            <TooltipContent
              side="bottom"
              sideOffset={4}
              className="max-w-[200px] text-xs"
            >
              Draw or upload a mask to define staging areas
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      {!isMobile && (
        <DialogDescription className="text-muted-foreground">
          Draw over areas you want to stage. Leave windows and doors unmarked.
        </DialogDescription>
      )}
    </DialogHeader>

    <div className="flex flex-col h-[calc(100vh-57px)] overflow-hidden">
      {children}
    </div>
  </DialogContent>
);
