import React from "react";
import { Line as KonvaLine } from "react-konva";
import { DrawingLayerProps } from "../types";

export const DrawingLayer: React.FC<DrawingLayerProps> = ({ lines }) => {
  return (
    <>
      {lines.map((line, i) => {
        const isEraser = line.tool === "eraser";
        return (
          <React.Fragment key={i}>
            {/* Main stroke */}
            <KonvaLine
              points={line.points}
              stroke={isEraser ? "black" : line.color}
              strokeWidth={line.strokeWidth}
              tension={0.5}
              lineCap="round"
              lineJoin="round"
              globalCompositeOperation={
                isEraser ? "destination-out" : "source-over"
              }
              listening={false}
              perfectDrawEnabled={false}
              shadowForStrokeEnabled={false}
              opacity={isEraser ? 1 : 0.6}
              bezier={true}
              strokeScaleEnabled={false}
              hitStrokeWidth={line.strokeWidth}
            />

            {/* Border stroke - only for brush strokes */}
            {!isEraser && line.borderColor && (
              <KonvaLine
                points={line.points}
                stroke={line.borderColor}
                strokeWidth={line.strokeWidth + 2}
                tension={0.5}
                lineCap="round"
                lineJoin="round"
                globalCompositeOperation="source-over"
                listening={false}
                perfectDrawEnabled={false}
                shadowForStrokeEnabled={false}
                opacity={0.4}
                bezier={true}
                strokeScaleEnabled={false}
                hitStrokeWidth={0}
              />
            )}
          </React.Fragment>
        );
      })}
    </>
  );
};
