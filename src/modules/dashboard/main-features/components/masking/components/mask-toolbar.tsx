import React from "react";
import { <PERSON><PERSON> } from "@/modules/ui/button";
import { <PERSON>lider } from "@/modules/ui/slider";
import { ToggleGroup, ToggleGroupItem } from "@/modules/ui/toggle-group";
import { Eraser, Save, Download, Undo2, Redo2, Loader2 } from "lucide-react";
import { BRUSH_COLORS } from "../constants";
import { ToolbarButton } from "./toolbar-button";
import { MobileToolbar } from "./mobile-toolbar";

interface MaskToolbarProps {
  isEraser: boolean;
  selectedColor: string;
  brushSize: number;
  eraserSize: number;
  historyStep: number;
  historyLength: number;
  isSaving: boolean;
  onColorChange: (value: string) => void;
  onBrushSizeChange: (value: number[]) => void;
  onEraserSizeChange: (value: number[]) => void;
  onUndo: () => void;
  onRedo: () => void;
  onSave: () => void;
  onDownload: () => void;
  onClear: () => void;
}

export const MaskToolbar: React.FC<MaskToolbarProps> = (props) => {
  return (
    <>
      {/* Desktop Toolbar */}
      <div className="hidden md:flex items-center justify-between gap-2 p-3 bg-muted/50 border-b border-muted-foreground/10">
        <div className="flex items-center gap-4">
          <ToggleGroup
            type="single"
            value={props.isEraser ? "eraser" : props.selectedColor}
            onValueChange={(value) => {
              if (value === "eraser" || value) {
                props.onColorChange(value);
              }
            }}
            className="flex-wrap"
          >
            {BRUSH_COLORS.map((brush) => (
              <ToggleGroupItem
                key={brush.color}
                value={brush.color}
                aria-label={brush.label}
                className="p-2"
              >
                <div
                  className="w-6 h-6 rounded-full border-2 border-gray-200 dark:border-gray-700"
                  style={{ backgroundColor: brush.color }}
                />
              </ToggleGroupItem>
            ))}
            <ToggleGroupItem value="eraser" aria-label="Eraser">
              <Eraser className="h-4 w-4" />
            </ToggleGroupItem>
          </ToggleGroup>

          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium whitespace-nowrap">
                {props.isEraser ? "Eraser" : "Brush"}:{" "}
                {props.isEraser ? props.eraserSize : props.brushSize}
                px
              </span>
              <Slider
                value={[props.isEraser ? props.eraserSize : props.brushSize]}
                onValueChange={
                  props.isEraser
                    ? props.onEraserSizeChange
                    : props.onBrushSizeChange
                }
                min={1}
                max={200}
                step={1}
                className="w-[120px]"
              />
            </div>
          </div>

          <div className="flex gap-1">
            <ToolbarButton
              onClick={props.onUndo}
              disabled={props.historyStep === 0}
              icon={<Undo2 className="h-4 w-4" />}
              label={`Undo (Step ${props.historyStep} of ${
                props.historyLength - 1
              })`}
            />
            <ToolbarButton
              onClick={props.onRedo}
              disabled={props.historyStep === props.historyLength - 1}
              icon={<Redo2 className="h-4 w-4" />}
              label={`Redo (Step ${props.historyStep} of ${
                props.historyLength - 1
              })`}
            />
          </div>
        </div>

        <div className="flex gap-2">
          <Button
            onClick={props.onClear}
            variant="outline"
            size="sm"
            className="gap-2"
            disabled={props.isSaving}
          >
            <Eraser className="h-4 w-4" /> Clear All
          </Button>
          <Button
            onClick={props.onDownload}
            variant="outline"
            size="sm"
            className="gap-2"
            disabled={props.isSaving}
          >
            <Download className="h-4 w-4" /> Download
          </Button>
          <Button
            onClick={props.onSave}
            size="sm"
            className="gap-2"
            disabled={props.isSaving}
          >
            {props.isSaving ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4" />
                Save
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Mobile Toolbar */}
      <div className="md:hidden">
        <MobileToolbar {...props} />
      </div>
    </>
  );
};
