import { useState, useCallback } from "react";
import {
  DEFAULT_SCALE,
  DEFAULT_POSITION,
  ZOOM_SCALE_FACTOR,
  MIN_ZOOM,
  MAX_ZOOM,
} from "../constants";

export const useCanvasState = () => {
  const [stageScale, setStageScale] = useState(DEFAULT_SCALE);
  const [stagePosition, setStagePosition] = useState(DEFAULT_POSITION);
  const [mode, setMode] = useState<"draw" | "pan">("draw");
  const [zoomLevel, setZoomLevel] = useState(1);

  const handleZoomIn = useCallback(() => {
    setZoomLevel((prev) => Math.min(prev * ZOOM_SCALE_FACTOR, MAX_ZOOM));
  }, []);

  const handleZoomOut = useCallback(() => {
    setZoomLevel((prev) => Math.max(prev / ZOOM_SCALE_FACTOR, MIN_ZOOM));
  }, []);

  const handleResetZoom = useCallback(() => {
    setZoomLevel(1);
    setStagePosition(DEFAULT_POSITION);
    setStageScale(DEFAULT_SCALE);
  }, []);

  const handleDragEnd = useCallback((newPosition: { x: number; y: number }) => {
    setStagePosition(newPosition);
  }, []);

  return {
    stageScale,
    setStageScale,
    stagePosition,
    setStagePosition,
    mode,
    setMode,
    zoomLevel,
    setZoomLevel,
    handleZoomIn,
    handleZoomOut,
    handleResetZoom,
    handleDragEnd,
  };
};
