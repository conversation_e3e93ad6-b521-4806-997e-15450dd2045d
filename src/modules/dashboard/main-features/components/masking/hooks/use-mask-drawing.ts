import { useState, useCallback, useEffect, useRef } from "react";
import { Line } from "../types";
import { throttle } from "lodash";
import { isEqual } from "lodash";

interface UseMaskDrawingProps {
  initialLines?: Line[];
  onLinesChange?: (lines: Line[]) => void;
}

export const useMaskDrawing = ({
  initialLines = [],
  onLinesChange,
}: UseMaskDrawingProps = {}) => {
  const [lines, setLines] = useState<Line[]>(initialLines);
  const [history, setHistory] = useState<Line[][]>([initialLines]);
  const [historyStep, setHistoryStep] = useState(0);
  const [isDrawing, setIsDrawing] = useState(false);
  const previousLinesRef = useRef<Line[]>(initialLines);

  useEffect(() => {
    if (onLinesChange && !isEqual(previousLinesRef.current, lines)) {
      previousLinesRef.current = lines;
      onLinesChange(lines);
    }
  }, [lines, onLinesChange]);

  // Initialize with new initialLines if they change
  useEffect(() => {
    if (!isEqual(initialLines, lines) && !isDrawing) {
      setLines(initialLines);
      setHistory([initialLines]);
      setHistoryStep(0);
      previousLinesRef.current = initialLines;
    }
  }, [initialLines]);

  const handleUndo = useCallback(() => {
    if (historyStep > 0) {
      const newStep = historyStep - 1;
      setHistoryStep(newStep);
      setLines(history[newStep]);
    }
  }, [historyStep, history]);

  const handleRedo = useCallback(() => {
    if (historyStep < history.length - 1) {
      const newStep = historyStep + 1;
      setHistoryStep(newStep);
      setLines(history[newStep]);
    }
  }, [historyStep, history]);

  const addLine = useCallback(
    (newLine: Line) => {
      setLines((prevLines) => [...prevLines, newLine]);
      setHistory((prevHistory) => {
        const newHistory = prevHistory.slice(0, historyStep + 1);
        newHistory.push([...lines, newLine]);
        return newHistory;
      });
      setHistoryStep((prev) => prev + 1);
    },
    [lines, historyStep]
  );

  const updateLastLine = useCallback(
    throttle((newPoints: number[]) => {
      setLines((prevLines) => {
        const lastLine = prevLines[prevLines.length - 1];
        if (!lastLine) return prevLines;

        const newLastLine = {
          ...lastLine,
          points: lastLine.points.concat(newPoints),
        };
        return [...prevLines.slice(0, -1), newLastLine];
      });
    }, 16),
    []
  );

  return {
    lines,
    setLines,
    history,
    historyStep,
    isDrawing,
    setIsDrawing,
    handleUndo,
    handleRedo,
    addLine,
    updateLastLine,
  };
};
