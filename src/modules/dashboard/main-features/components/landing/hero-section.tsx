"use client";

import React from "react";
import { motion } from "framer-motion";
import Image from "next/image";

import { H1, Lead } from "@/modules/ui/typography";

export const HeroSection = () => {
  return (
    <motion.section
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="py-4 xl:container sm:py-8"
    >
      <div className="mx-auto px-4 relative">
        {/* Background gradient */}
        <div className="absolute inset-0 -z-10 bg-gradient-subtle rounded-xl opacity-75"></div>
        
        <div className="flex items-center gap-8 lg:flex-row p-4 sm:p-6">
          <div className="flex-1 space-y-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="space-y-2"
            >
              <H1>
                Welcome to Your Design Dashboard
                <span className="block text-primary text-lg md:text-xl font-medium mt-1">
                  Discover our latest AI tools and updates
                </span>
              </H1>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <Lead className="text-muted-foreground">
                Get started with our AI-powered design tools. Create, visualize,
                and transform your spaces effortlessly.
              </Lead>
            </motion.div>
          </div>
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.5 }}
            className="flex-shrink-0 relative hidden lg:block"
          >
            <motion.div
              initial={{ opacity: 0.15 }}
              animate={{ opacity: [0.15, 0.25, 0.15] }}
              transition={{ duration: 6, repeat: Infinity, ease: "easeInOut" }}
              className="absolute -top-16 -left-12 w-48 h-48 bg-primary/30 rounded-full blur-[120px]"
            />
            <Image
              src="/images/dashboard/dashboard-hero.png"
              alt="Design tools illustration"
              width={300}
              height={100}
              priority
              className="object-contain relative z-10"
            />
          </motion.div>
        </div>
      </div>
    </motion.section>
  );
};

export default HeroSection;
