"use client";

import React from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { Card } from "@/modules/ui/card";
import { ArrowRight, Wand2, Image as ImageIcon, Lightbulb } from "lucide-react";
import { H3, BodyText } from "@/modules/ui/typography";

interface Tool {
  title: string;
  description: string;
  icon: React.ReactNode;
  link: string;
  isNew?: boolean;
}

const tools: Tool[] = [
  {
    title: "Render Interior",
    description: "Transform your room with AI-powered redesign",
    icon: <Wand2 className="w-6 h-6 text-primary" />,
    link: "/tools/interior",
  },
  {
    title: "Virtual Staging",
    description: "Stage empty spaces instantly with AI",
    icon: <ImageIcon className="w-6 h-6 text-primary" />,
    link: "/tools/virtual-staging",
  },
  {
    title: "AI Design Inspire",
    description: "Get personalized design suggestions",
    icon: <Lightbulb className="w-6 h-6 text-primary" />,
    link: "/dashboard/design-suggestions",
    isNew: true,
  },
];

export const MainToolsGrid = () => {
  return (
    <section className="py-0 sm:py-4">
      <div className="xl:container mx-auto px-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {tools.map((tool, index) => (
            <motion.div
              key={tool.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Link href={tool.link} className="block h-full">
                <Card className="group h-full bg-card/80 hover:bg-card shadow-sm hover:shadow-md border border-primary/10 hover:border-primary/20 transition-all duration-300 p-6 relative overflow-hidden">
                  {tool.isNew && (
                    <span className="absolute top-4 right-4 bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full">
                      NEW
                    </span>
                  )}

                  <div className="flex flex-col h-full">
                    <div className="mb-4">
                      {tool.icon}
                    </div>
                    
                    <H3 className="mb-2">{tool.title}</H3>

                    <BodyText variant="default" className="mb-4 flex-grow text-muted-foreground">
                      {tool.description}
                    </BodyText>

                    <div className="flex items-center text-primary group-hover:translate-x-1 transition-transform duration-300">
                      <ArrowRight className="w-5 h-5" />
                    </div>
                  </div>
                </Card>
              </Link>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default MainToolsGrid;
