"use client";
import React, { useState, useRef, useEffect } from "react";
import { motion } from "framer-motion";
import { Zap, Info, ArrowRight } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
} from "@/modules/ui/card";
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/modules/ui/tooltip";
import { Badge } from "@/modules/ui/badge";
import { Button } from "@/modules/ui/button";
import Link from "next/link";
import Image from "next/image";
import { H3, BodyText, Small } from "@/modules/ui/typography";

// Updated type definition
interface FeatureCardType {
  title: string;
  description: string;
  imageUrl: string;
  videoUrl?: string;
  action: string;
  link: string;
  // Removed processingTime and creditCost
  supportedFormats?: string[];
  maxResolution?: string;
  premium?: boolean;
  usageExamples?: string[];
  newFeature?: boolean;
  beta?: boolean;
  newEngine?: {
    name: string;
    improvement: string;
  };
}

interface FeatureCardProps {
  feature: FeatureCardType;
}

export const FeatureCard: React.FC<FeatureCardProps> = ({ feature }) => {
  const [isHovered, setIsHovered] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const [isImageLoading, setIsImageLoading] = useState(true);

  useEffect(() => {
    const video = videoRef.current;
    if (!video || !feature.videoUrl) return;

    const handleLoadedData = () => {
      setIsVideoLoaded(true);
      if (document.visibilityState === "visible") {
        const playPromise = video.play();
        if (playPromise !== undefined) {
          playPromise.catch((error) => {
            console.log("Autoplay prevented:", error);
          });
        }
      }
    };

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            video.play();
          } else {
            video.pause();
          }
        });
      },
      { threshold: 0.5 }
    );

    observer.observe(video);
    video.addEventListener("loadeddata", handleLoadedData);

    return () => {
      observer.disconnect();
      video.removeEventListener("loadeddata", handleLoadedData);
      video.pause();
    };
  }, [feature.videoUrl]);

  return (
    <TooltipProvider>
      <motion.div
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
        className="h-full focus-within:ring-2 focus-within:ring-primary/50 rounded-lg"
      >
        <Card
          className="relative flex flex-col h-full overflow-hidden bg-card/60 backdrop-blur-sm border border-primary/10 dark:border-primary/20 hover:border-primary/30 focus-within:border-primary/50 shadow-sm hover:shadow-md transition-all duration-300 ease-out group"
          role="article"
          aria-labelledby={`feature-title-${feature.title
            .toLowerCase()
            .replace(/\s+/g, "-")}`}
        >
          {/* Media Wrapper */}
          <div className="relative h-56 sm:h-64 overflow-hidden">
            <div className="absolute inset-0">
              <div className="relative w-full h-full transform-gpu transition-transform duration-500 ease-out group-hover:scale-[1.07]">
                {/* New Engine Badge */}
                {feature.newEngine && (
                  <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="absolute top-4 left-4 z-30"
                  >
                    <Tooltip delayDuration={100}>
                      <TooltipTrigger asChild>
                        <Badge
                          className="px-3 py-1 bg-gradient-to-r from-orange-500 to-pink-500 hover:from-orange-400 hover:to-pink-400
                            text-white border-none shadow-lg hover:shadow-xl transition-all duration-300
                            flex items-center gap-2 backdrop-blur-sm cursor-pointer select-none"
                        >
                          <Zap className="w-3.5 h-3.5 animate-pulse" />
                          <span>New {feature.newEngine.name}</span>
                        </Badge>
                      </TooltipTrigger>
                      <TooltipContent
                        side="bottom"
                        align="start"
                        sideOffset={5}
                        className="bg-card/95 text-card-foreground px-4 py-3 shadow-xl border border-primary/20 backdrop-blur-md
                          max-w-[200px] break-words animate-in fade-in-0 zoom-in-95 z-50"
                      >
                        <div className="flex flex-col gap-1.5">
                          <div className="flex items-center gap-2">
                            <Zap className="w-3.5 h-3.5 text-primary" />
                            <p className="font-semibold text-sm">
                              {feature.newEngine.name}
                            </p>
                          </div>
                          <p className="text-xs text-muted-foreground leading-relaxed">
                            {feature.newEngine.improvement}
                          </p>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </motion.div>
                )}

                {/* Video */}
                {feature.videoUrl && (
                  <video
                    ref={videoRef}
                    playsInline
                    muted
                    loop
                    autoPlay
                    webkit-playsinline="true"
                    preload="auto"
                    className={`absolute inset-0 w-full h-full object-cover transition-opacity duration-500 ${
                      isVideoLoaded ? "opacity-100" : "opacity-0"
                    }`}
                    poster={feature.imageUrl}
                  >
                    <source src={feature.videoUrl} type="video/mp4" />
                  </video>
                )}

                {/* Fallback Image */}
                <Image
                  src={feature.imageUrl}
                  alt={feature.title}
                  fill
                  className={`
                    object-cover transition-opacity duration-500
                    ${
                      isVideoLoaded && feature.videoUrl
                        ? "opacity-0"
                        : "opacity-100"
                    }
                    ${isImageLoading ? "blur-sm" : "blur-0"}
                  `}
                  priority
                  onLoadingComplete={() => setIsImageLoading(false)}
                />

                {isImageLoading && (
                  <div className="absolute inset-0 bg-muted/50 animate-pulse" />
                )}

                {/* Colored Overlay on Hover */}
                <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                {/* Bottom Gradient */}
                <div className="absolute inset-x-0 -bottom-1 h-40 bg-gradient-to-t from-background/80 via-background/50 to-transparent" />
              </div>
            </div>

            {/* Action Button */}
            <div className="absolute bottom-4 right-4 z-20">
              <Link href={feature.link}>
                <Button
                  aria-label={`${feature.action} - ${feature.title}`}
                  variant="default"
                  size="default"
                  className="h-10 px-4 shadow-lg hover:shadow-xl
                    backdrop-blur-sm bg-primary hover:bg-primary/90 text-primary-foreground
                    transition-all duration-300 flex items-center gap-2"
                >
                  {feature.action}
                  <ArrowRight className="w-4 h-4" />
                </Button>
              </Link>
            </div>
          </div>

          {/* Title & Description */}
          <CardHeader className="p-4 pb-0 relative h-auto min-h-[5rem] z-10">
            <div className="flex justify-between items-start gap-3">
              <div className="flex-1">
                <H3
                  id={`feature-title-${feature.title
                    .toLowerCase()
                    .replace(/\s+/g, "-")}`}
                  className="group-hover:text-primary transition-colors duration-300 leading-tight"
                >
                  {feature.title}
                </H3>
                <BodyText
                  variant="default"
                  className="text-muted-foreground mt-1.5 line-clamp-2"
                >
                  {feature.description}
                </BodyText>
              </div>
            </div>
          </CardHeader>

          {/* Usage Examples */}
          <CardContent className="px-4 py-3 relative z-10">
            <div className="space-y-2.5">
              {feature.usageExamples && (
                <div className="bg-secondary/50 rounded-lg p-3 hover:bg-secondary/70 transition-colors duration-300">
                  <div className="font-medium mb-2 text-foreground/90 flex items-center gap-2">
                    <Info className="w-4 h-4 text-primary" />
                    <Small>Example uses:</Small>
                  </div>
                  <ul className="list-none space-y-1.5">
                    {feature.usageExamples.map((example, index) => (
                      <li
                        key={index}
                        className="flex items-center gap-2 text-muted-foreground hover:text-primary transition-colors duration-300"
                      >
                        <span className="w-1.5 h-1.5 rounded-full bg-primary/50" />
                        <Small>{example}</Small>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </TooltipProvider>
  );
};

export default FeatureCard;
