"use client";
import React from "react";
import { motion } from "framer-motion";
import { FeatureCard } from "./feature-card";
import { FeatureSection as FeatureSectionType } from "../../types/dashboard-types";
import { H2, Lead } from "@/modules/ui/typography";

interface FeatureSectionProps {
  section: FeatureSectionType;
  index: number;
  viewMode: "all" | "use-cases";
}

const animationVariants = {
  container: {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  },
  item: {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  },
} as const;

export const FeatureSection = ({
  section,
  index,
  viewMode,
}: FeatureSectionProps): React.ReactNode => {
  // Only show sections that start with "For " in use-cases mode
  if (viewMode === "use-cases" && !section.title.startsWith("For ")) {
    return null;
  }

  return (
    <motion.section
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.2, duration: 0.5 }}
      className="relative"
    >
      <div className="relative mb-6 p-6 rounded-lg bg-gradient-to-r from-primary/5 via-purple-500/5 to-transparent">
        <div className="space-y-2">
          <H2>{section.title}</H2>
          <Lead>{section.description}</Lead>
        </div>
      </div>

      <motion.div
        variants={animationVariants.container}
        initial="hidden"
        animate="show"
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
      >
        {section.features.map((feature) => (
          <motion.div key={feature.title} variants={animationVariants.item}>
            <FeatureCard feature={feature} />
          </motion.div>
        ))}
      </motion.div>
    </motion.section>
  );
};
