"use client";

import { useState } from "react";
import { FeatureSection } from "./feature-section";
import { sections } from "../../data/dashboard-sections";
import { ViewMode } from "../../data/dashboard-sections";
import { FeatureCard } from "./feature-card";
import { motion, AnimatePresence } from "framer-motion";
import { Tabs, TabsList, TabsTrigger } from "@/modules/ui/tabs";
import { GradientHeading } from "@/modules/ui/typography";

const animationVariants = {
  container: {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: { staggerChildren: 0.1 },
    },
  },
  item: {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  },
};

export const FeaturesContainer = () => {
  const [viewMode, setViewMode] = useState<ViewMode>("all");

  const allTools = sections
    .flatMap((section) => section.features)
    .filter(
      (feature, index, self) =>
        index === self.findIndex((f) => f.title === feature.title)
    );

  return (
    <div className="container space-y-8">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="space-y-8 mb-16 md:mb-0"
      >
        <div className="space-y-4 text-center max-w-full mx-auto">
          <GradientHeading variant="primary" align="center" className="p-2">
            View our AI tools for interior and architecture design 🛋️
          </GradientHeading>
        </div>

        <div className="flex justify-end">
          <Tabs
            defaultValue="all"
            value={viewMode}
            onValueChange={(value) => setViewMode(value as ViewMode)}
            className="w-auto"
          >
            <TabsList className="grid w-[300px] grid-cols-2 bg-muted/50 backdrop-blur-sm border border-muted">
              <TabsTrigger
                value="all"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground transition-all duration-300"
              >
                All Tools
              </TabsTrigger>
              <TabsTrigger
                value="use-cases"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground transition-all duration-300"
              >
                Use Cases
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        <div className="relative min-h-[200px]">
          <div className="absolute inset-0 bg-gradient-to-b from-primary/5 via-muted/10 to-transparent -z-10 rounded-xl" />

          <AnimatePresence mode="wait">
            <motion.div
              key={viewMode}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              {viewMode === "all" ? (
                <motion.div
                  variants={animationVariants.container}
                  initial="hidden"
                  animate="show"
                  className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
                >
                  {allTools.map((feature) => (
                    <motion.div
                      key={feature.title}
                      variants={animationVariants.item}
                    >
                      <FeatureCard feature={feature} />
                    </motion.div>
                  ))}
                </motion.div>
              ) : (
                <div className="space-y-12">
                  {sections.map((section, index) => (
                    <FeatureSection
                      key={section.title}
                      section={section}
                      index={index}
                      viewMode={viewMode}
                    />
                  ))}
                </div>
              )}
            </motion.div>
          </AnimatePresence>
        </div>
      </motion.div>
    </div>
  );
};
