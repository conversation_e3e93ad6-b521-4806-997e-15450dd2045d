"use client";

import React, { useRef, useEffect, useState, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/modules/ui/tabs";
import PaginatedDesignList from "@/modules/dashboard/main-features/components/designs/paginated-design-list";
import {
  InteriorDesign,
  ExteriorDesign,
  BackgroundRemoval,
  UpscaleImage,
  VirtualStaging,
  StyleTransfer,
} from "@prisma/client";
import { Skeleton } from "@/modules/ui/skeleton";
import { toggleFavorite } from "@/modules/dashboard/main-features/actions/favorite-actions";
import { useTransition } from "react";
import Image from "next/image";
import DesignCard from "@/modules/dashboard/main-features/components/designs/design-card";
import { Switch } from "@/modules/ui/switch";
import { Label } from "@/modules/ui/label";
import { Loader2 } from "lucide-react";
import { Heart } from "lucide-react";

type DesignType =
  | "virtual-staging"
  | "interior"
  | "exterior"
  | "remove-background"
  | "upscale"
  | "style-transfer";

type Design = (
  | VirtualStaging
  | InteriorDesign
  | ExteriorDesign
  | BackgroundRemoval
  | UpscaleImage
  | StyleTransfer
) & { isFavorite: boolean };

interface DesignsPageWrapperProps {
  designs: Design[];
  totalPages: number;
  currentPage: number;
  type: DesignType;
  searchParams: { [key: string]: string | string[] | undefined };
  totalCount: number;
}

export default function DesignsPageWrapper({
  designs,
  totalPages,
  currentPage,
  type,
  searchParams,
  totalCount,
}: DesignsPageWrapperProps) {
  const router = useRouter();
  const searchParamsObj = useSearchParams();
  const wrapperRef = useRef<HTMLDivElement>(null);

  const [isLoading, setIsLoading] = useState(true);
  const [activeType, setActiveType] = useState(type);
  const [isPending, startTransition] = useTransition();
  const [isFavoritesToggling, setIsFavoritesToggling] = useState(false);

  const showFavorites = searchParamsObj?.get("favorites") === "true";

  const updateUrl = (newParams: Record<string, string>) => {
    const current = new URLSearchParams(
      searchParamsObj ? Array.from(searchParamsObj.entries()) : []
    );
    Object.entries(newParams).forEach(([key, value]) => {
      current.set(key, value);
    });
    const search = current.toString();
    const query = search ? `?${search}` : "";
    router.push(`/dashboard/designs${query}`);
  };

  const handleTabChange = (value: string) => {
    if (document.activeElement instanceof HTMLElement) {
      document.activeElement.blur();
    }

    setIsLoading(true);
    setActiveType(value as DesignType);
    if (value === "remove-background" || value === "upscale") {
      router.push(`/dashboard/designs?type=${value}`);
    } else {
      updateUrl({ type: value, page: "1" });
    }
  };

  const handlePageChange = (page: number) => {
    const newSearchParams = new URLSearchParams(
      searchParams as Record<string, string>
    );
    newSearchParams.set("page", page.toString());
    router.push(`/dashboard/designs?${newSearchParams.toString()}`);
  };

  useEffect(() => {
    setIsLoading(true);
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);
    return () => clearTimeout(timer);
  }, [type, currentPage]);

  useEffect(() => {
    if (wrapperRef.current) {
      const rect = wrapperRef.current.getBoundingClientRect();
      if (rect.top < 0) {
        wrapperRef.current.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    }
  }, [type, currentPage]);

  const handleToggleFavorite = useCallback(
    async (designId: string, designType: DesignType): Promise<boolean> => {
      try {
        const newFavoriteStatus = await toggleFavorite(designId, designType);
        return newFavoriteStatus;
      } catch (error) {
        console.error("Error toggling favorite:", error);
        return false;
      }
    },
    []
  );

  const renderDesignItem = (design: Design) => {
    console.log("Rendering design:", design);
    return (
      <DesignCard design={design} type={type} isFavorite={design.isFavorite} />
    );
  };

  const handleFavoritesToggle = async (checked: boolean) => {
    setIsFavoritesToggling(true);
    const current = new URLSearchParams(
      searchParamsObj ? Array.from(searchParamsObj.entries()) : []
    );
    if (checked) {
      current.set("favorites", "true");
      current.set("page", "1");
    } else {
      current.delete("favorites");
    }
    const search = current.toString();
    const query = search ? `?${search}` : "";
    await router.push(`/dashboard/designs${query}`);
    setIsFavoritesToggling(false);
  };

  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.key === "f" && (event.ctrlKey || event.metaKey)) {
        event.preventDefault();
        handleFavoritesToggle(!showFavorites);
      }
    };

    window.addEventListener("keydown", handleKeyPress);
    return () => window.removeEventListener("keydown", handleKeyPress);
  }, [showFavorites]);

  return (
    <div ref={wrapperRef} className="flex flex-col w-full">
      <Tabs
        defaultValue={type}
        onValueChange={handleTabChange}
        className="w-full"
      >
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between w-full mb-6 gap-4">
          {/* Favorites Toggle */}
          <div className="flex items-center order-2 sm:order-1">
            <div className="flex items-center bg-muted/50 rounded-lg px-4 py-2 shadow-sm">
              <div className="relative flex items-center space-x-3">
                <div className="relative">
                  <Switch
                    id="favorites-filter"
                    checked={showFavorites}
                    onCheckedChange={handleFavoritesToggle}
                    disabled={isFavoritesToggling}
                    className="data-[state=checked]:bg-red-500"
                  />
                  {isFavoritesToggling && (
                    <Loader2 className="w-4 h-4 absolute right-[-24px] top-1/2 transform -translate-y-1/2 animate-spin" />
                  )}
                </div>
                <Label
                  htmlFor="favorites-filter"
                  className={`${
                    isFavoritesToggling ? "text-muted-foreground" : ""
                  } flex items-center space-x-2 whitespace-nowrap`}
                >
                  <Heart
                    className={`w-4 h-4 ${
                      showFavorites ? "fill-red-500 text-red-500" : ""
                    }`}
                  />
                  <span className="hidden sm:inline">Favorites</span>
                  <kbd className="hidden lg:inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100">
                    <span className="text-xs">
                      {navigator.platform.includes("Mac") ? "⌘" : "Ctrl"}
                    </span>
                    F
                  </kbd>
                </Label>
              </div>
            </div>
          </div>

          {/* Tabs List */}
          <TabsList
            className="order-1 sm:order-2 h-auto sm:h-10 p-1 grid grid-cols-3 sm:flex sm:flex-row gap-1 bg-muted/60"
            style={{ WebkitTapHighlightColor: "transparent" }}
          >
            <TabsTrigger
              value="style-transfer"
              className="px-3 py-2 text-xs sm:text-sm data-[state=active]:bg-background"
            >
              Style Transfer
            </TabsTrigger>
            <TabsTrigger
              value="virtual-staging"
              className="px-3 py-2 text-xs sm:text-sm data-[state=active]:bg-background"
            >
              Virtual Staging
            </TabsTrigger>
            <TabsTrigger
              value="interior"
              className="px-3 py-2 text-xs sm:text-sm data-[state=active]:bg-background"
            >
              Interior
            </TabsTrigger>
            <TabsTrigger
              value="exterior"
              className="px-3 py-2 text-xs sm:text-sm data-[state=active]:bg-background"
            >
              Exterior
            </TabsTrigger>
            <TabsTrigger
              value="upscale"
              className="px-3 py-2 text-xs sm:text-sm data-[state=active]:bg-background"
            >
              Upscale
            </TabsTrigger>
            <TabsTrigger
              value="remove-background"
              className="px-3 py-2 text-xs sm:text-sm data-[state=active]:bg-background"
            >
              Remove BG
            </TabsTrigger>
          </TabsList>
        </div>

        {showFavorites && (
          <div className="flex items-center px-4 py-2 bg-muted/30 rounded-lg text-sm text-muted-foreground mb-6">
            <Heart className="w-4 h-4 mr-2 fill-red-500 text-red-500" />
            <span>Showing favorite {type.replace("-", " ")} designs</span>
            <button
              onClick={() => handleFavoritesToggle(false)}
              className="ml-auto hover:text-foreground transition-colors"
            >
              Clear filter
            </button>
          </div>
        )}

        <TabsContent value="virtual-staging">
          <PaginatedDesignList
            designs={designs}
            type={type}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            isLoading={isLoading}
            totalCount={totalCount}
            toggleFavorite={handleToggleFavorite}
            isPending={isPending}
            renderDesignItem={renderDesignItem}
            showFavorites={showFavorites}
            searchParams={searchParams}
          />
        </TabsContent>
        <TabsContent value="interior">
          <PaginatedDesignList
            designs={designs}
            type={type}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            isLoading={isLoading}
            totalCount={totalCount}
            toggleFavorite={handleToggleFavorite}
            isPending={isPending}
            renderDesignItem={renderDesignItem}
            showFavorites={showFavorites}
            searchParams={searchParams}
          />
        </TabsContent>
        <TabsContent value="exterior">
          <PaginatedDesignList
            designs={designs}
            type={type}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            isLoading={isLoading}
            totalCount={totalCount}
            toggleFavorite={handleToggleFavorite}
            isPending={isPending}
            renderDesignItem={renderDesignItem}
            showFavorites={showFavorites}
            searchParams={searchParams}
          />
        </TabsContent>
        <TabsContent value="remove-background">
          <PaginatedDesignList
            designs={designs}
            type={type}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            isLoading={isLoading}
            totalCount={totalCount}
            toggleFavorite={handleToggleFavorite}
            isPending={isPending}
            renderDesignItem={renderDesignItem}
            showFavorites={showFavorites}
            searchParams={searchParams}
          />
        </TabsContent>
        <TabsContent value="upscale">
          <PaginatedDesignList
            designs={designs}
            type={type}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            isLoading={isLoading}
            totalCount={totalCount}
            toggleFavorite={handleToggleFavorite}
            isPending={isPending}
            renderDesignItem={renderDesignItem}
            showFavorites={showFavorites}
            searchParams={searchParams}
          />
        </TabsContent>
        <TabsContent value="style-transfer">
          <PaginatedDesignList
            designs={designs}
            type={type}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            isLoading={isLoading}
            totalCount={totalCount}
            toggleFavorite={handleToggleFavorite}
            isPending={isPending}
            renderDesignItem={renderDesignItem}
            showFavorites={showFavorites}
            searchParams={searchParams}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
