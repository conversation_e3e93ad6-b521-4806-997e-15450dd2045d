import React, { useLayoutEffect, useRef, useState } from "react";
import DesignCard from "@/modules/dashboard/main-features/components/designs/design-card";
import { Button } from "@/modules/ui/button";
import { ScrollArea } from "@/modules/ui/scroll-area";
import {
  InteriorDesign,
  ExteriorDesign,
  BackgroundRemoval,
  UpscaleImage,
  VirtualStaging,
  StyleTransfer,
} from "@prisma/client";
import { Skeleton } from "@/modules/ui/skeleton";
import { Heart } from "lucide-react";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/modules/ui/pagination";
import { useRouter } from "next/navigation";

type DesignType =
  | "virtual-staging"
  | "interior"
  | "exterior"
  | "remove-background"
  | "upscale"
  | "style-transfer";
type Design = (
  | VirtualStaging
  | InteriorDesign
  | ExteriorDesign
  | BackgroundRemoval
  | UpscaleImage
  | StyleTransfer
) & { isFavorite: boolean };

interface PaginatedDesignListProps {
  designs: Design[];
  type: DesignType;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  isLoading: boolean;
  totalCount: number;
  isPending: boolean;
  toggleFavorite: (
    designId: string,
    designType: DesignType
  ) => Promise<boolean>;
  renderDesignItem: (design: Design) => React.ReactNode;
  showFavorites?: boolean;
  searchParams: { [key: string]: string | string[] | undefined };
}

const PaginatedDesignList: React.FC<PaginatedDesignListProps> = ({
  designs,
  type,
  currentPage,
  totalPages,
  onPageChange,
  isLoading,
  totalCount,
  toggleFavorite,
  isPending,
  renderDesignItem,
  showFavorites,
  searchParams,
}) => {
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const [favorites, setFavorites] = useState<Record<string, boolean>>({});
  const [pendingFavorites, setPendingFavorites] = useState<Set<string>>(
    new Set()
  );
  const router = useRouter();

  useLayoutEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = 0;
    }
  }, [currentPage, type]);

  useLayoutEffect(() => {
    const newFavorites = designs.reduce((acc, design) => {
      acc[design.id] = design.isFavorite;
      return acc;
    }, {} as Record<string, boolean>);
    setFavorites(newFavorites);
  }, [designs]);

  const handleFavoriteToggle = async (designId: string) => {
    if (pendingFavorites.has(designId)) return;

    setPendingFavorites((prev) => new Set(prev).add(designId));
    const currentFavoriteStatus = favorites[designId];

    // Optimistic update
    setFavorites((prev) => ({ ...prev, [designId]: !currentFavoriteStatus }));

    try {
      const newFavoriteStatus = await toggleFavorite(designId, type);
      setFavorites((prev) => ({ ...prev, [designId]: newFavoriteStatus }));
    } catch (error) {
      console.error("Error toggling favorite:", error);
      // Revert the optimistic update if there's an error
      setFavorites((prev) => ({ ...prev, [designId]: currentFavoriteStatus }));
    } finally {
      setPendingFavorites((prev) => {
        const newSet = new Set(prev);
        newSet.delete(designId);
        return newSet;
      });
    }
  };

  const startIndex = (currentPage - 1) * designs.length + 1;
  const endIndex = Math.min(startIndex + designs.length - 1, totalCount);

  const renderPagination = () => {
    // Don't render pagination if there's only one page or no items
    if (totalPages <= 1 || totalCount === 0) {
      return null;
    }

    return (
      <Pagination className="py-2 pb-2 sm:pb-4">
        <PaginationContent className="flex flex-wrap justify-center items-center gap-2">
          {currentPage > 1 && (
            <PaginationItem>
              <PaginationPrevious
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  onPageChange(currentPage - 1);
                }}
                className="h-8 min-h-8 px-2 sm:px-4"
              />
            </PaginationItem>
          )}
          {[...Array(totalPages)].map((_, index) => {
            const pageNumber = index + 1;
            // Show fewer pagination items on mobile
            const shouldShowOnMobile =
              pageNumber === 1 ||
              pageNumber === totalPages ||
              pageNumber === currentPage;
            const shouldShowOnDesktop =
              pageNumber === 1 ||
              pageNumber === totalPages ||
              (pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1);

            if (shouldShowOnDesktop) {
              return (
                <PaginationItem key={index} className="hidden sm:block">
                  <PaginationLink
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      onPageChange(pageNumber);
                    }}
                    isActive={currentPage === pageNumber}
                    className="h-8 min-h-8"
                  >
                    {pageNumber}
                  </PaginationLink>
                </PaginationItem>
              );
            } else if (shouldShowOnMobile) {
              return (
                <PaginationItem key={index} className="block sm:hidden">
                  <PaginationLink
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      onPageChange(pageNumber);
                    }}
                    isActive={currentPage === pageNumber}
                    className="h-8 min-h-8 px-2"
                  >
                    {pageNumber}
                  </PaginationLink>
                </PaginationItem>
              );
            } else if (
              (pageNumber === currentPage - 2 ||
                pageNumber === currentPage + 2) &&
              window.innerWidth > 640
            ) {
              return (
                <PaginationItem key={index} className="hidden sm:block">
                  <PaginationEllipsis
                    className="h-8 min-h-8 w-8 flex items-center justify-center"
                    aria-label={`Additional pages between ${
                      pageNumber - 1
                    } and ${pageNumber + 1}`}
                  >
                    <div className="flex h-full w-full items-center justify-center">
                      ...
                    </div>
                  </PaginationEllipsis>
                </PaginationItem>
              );
            }
            return null;
          })}
          {currentPage < totalPages && (
            <PaginationItem>
              <PaginationNext
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  onPageChange(currentPage + 1);
                }}
                className="h-8 min-h-8 px-2 sm:px-4"
              />
            </PaginationItem>
          )}
        </PaginationContent>
      </Pagination>
    );
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 p-2 sm:p-4">
          {Array.from({ length: 6 }).map((_, index) => (
            <Skeleton key={index} className="h-[200px] sm:h-[300px] w-full" />
          ))}
        </div>
      );
    }

    if (designs.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center py-12 px-4">
          <div className="bg-muted/50 rounded-full p-4 mb-4">
            <Heart
              className={`h-8 w-8 ${
                showFavorites ? "text-red-500" : "text-muted-foreground"
              }`}
            />
          </div>
          <h3 className="text-lg font-medium mb-2">
            {showFavorites
              ? `No favorite ${type.replace("-", " ")} designs yet`
              : `No ${type.replace("-", " ")} designs yet`}
          </h3>
          {showFavorites ? (
            <p className="text-sm text-muted-foreground text-center max-w-md">
              Add designs to your favorites by clicking the heart icon on any
              design. Your favorite designs will appear here for quick access.
            </p>
          ) : (
            <p className="text-sm text-muted-foreground text-center max-w-md">
              {type === "style-transfer"
                ? "Transform your images with AI style transfer. Upload an image and choose from various artistic styles."
                : type === "virtual-staging"
                ? "Stage empty rooms with virtual furniture. Upload a photo of an empty room to get started."
                : type === "interior"
                ? "Create stunning interior designs. Start by uploading a photo or describing your ideal room."
                : type === "exterior"
                ? "Transform building exteriors. Upload a photo or describe your desired architectural style."
                : type === "remove-background"
                ? "Remove backgrounds from your images with AI. Upload any image to get started."
                : "Enhance your images with AI. Upload a photo to begin."}
            </p>
          )}
        </div>
      );
    }

    return (
      <div
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 p-2 sm:p-4"
        style={{
          transition: "all 0.3s ease-in-out",
          opacity: isLoading ? 0.5 : 1,
          transform: isLoading ? "scale(0.98)" : "scale(1)",
        }}
      >
        {designs.map((design) => (
          <div
            key={design.id}
            className="relative group rounded-lg overflow-hidden ring-1 ring-muted transition-all duration-200 hover:ring-2 hover:ring-primary/20 hover:shadow-lg"
          >
            {renderDesignItem(design)}
            <Button
              variant="ghost"
              size="sm"
              className={`absolute top-3 right-3 h-8 w-8 p-0 rounded-full bg-background/80 hover:bg-background transition-all duration-200 ${
                favorites[design.id]
                  ? "opacity-100"
                  : "opacity-70 group-hover:opacity-100"
              }`}
              onClick={() => handleFavoriteToggle(design.id)}
            >
              <Heart
                className={`h-4 w-4 sm:h-5 sm:w-5 transition-colors duration-200 ${
                  favorites[design.id]
                    ? "fill-red-500 text-red-500"
                    : "text-foreground"
                } ${pendingFavorites.has(design.id) ? "animate-pulse" : ""}`}
              />
            </Button>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="flex flex-col h-[calc(100vh-250px)] sm:h-[calc(100vh-200px)]">
      {renderContent()}
      {totalCount > 0 && ( // Only show the bottom section if there are items
        <div className="flex flex-col sm:flex-row justify-between items-center gap-2 sm:gap-4 mt-2 sm:mt-4 px-2 pb-2 sm:px-4">
          <p className="text-xs sm:text-sm text-muted-foreground order-2 sm:order-1">
            Showing {startIndex} to {endIndex} of {totalCount} designs
          </p>
          <div className="order-1 flex items-center justify-center sm:order-2 w-full sm:w-auto">
            {renderPagination()}
          </div>
        </div>
      )}
    </div>
  );
};

export default PaginatedDesignList;
