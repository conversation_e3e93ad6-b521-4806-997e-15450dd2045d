import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>itle,
} from "@/modules/ui/card";
import { Button } from "@/modules/ui/button";
import {
  InteriorDesign,
  ExteriorDesign,
  BackgroundRemoval,
  UpscaleImage,
  VirtualStaging,
  StyleTransfer,
} from "@prisma/client";
import { Skeleton } from "@/modules/ui/skeleton";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { Eye, Calendar, Download, Share2 } from "lucide-react";
import { toast } from "sonner";
import { DesignType } from "@/types/designs";

interface DesignCardProps {
  design:
    | InteriorDesign
    | ExteriorDesign
    | BackgroundRemoval
    | UpscaleImage
    | VirtualStaging
    | StyleTransfer;
  type: DesignType;
  isFavorite: boolean;
}

const DesignCard: React.FC<DesignCardProps> = ({
  design,
  type,
  isFavorite,
}) => {
  const router = useRouter();
  const [imageLoaded, setImageLoaded] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);

  const getCardTitle = () => {
    switch (type) {
      case "interior":
        return "Interior Design";
      case "exterior":
        return "Exterior Design";
      case "remove-background":
        return "Background Removal";
      case "upscale":
        return "Upscaled Image";
      case "virtual-staging":
        return "Virtual Staging";
      case "style-transfer":
        return "Style Transfer";
      default:
        return "Design";
    }
  };

  const renderDesignDetails = () => {
    switch (type) {
      case "interior":
      case "exterior":
        return (
          <>
            <p>
              <strong>Style:</strong>{" "}
              {(design as InteriorDesign | ExteriorDesign).style || "N/A"}
            </p>
            <p>
              <strong>{type === "interior" ? "Room" : "Building"}:</strong>{" "}
              {(design as InteriorDesign).room ||
                (design as ExteriorDesign).building ||
                "N/A"}
            </p>
          </>
        );
      case "virtual-staging":
        return (
          <>
            <p>
              <strong>Room Type:</strong>{" "}
              {(design as VirtualStaging).room || "N/A"}
            </p>
            <p>
              <strong>Style:</strong>{" "}
              {(design as VirtualStaging).style || "N/A"}
            </p>
          </>
        );
      case "style-transfer":
        return (
          <>
            <p>
              <strong>Model:</strong>{" "}
              {(design as StyleTransfer).model || "Default"}
            </p>
            {(design as StyleTransfer).prompt && (
              <p>
                <strong>Prompt:</strong> {(design as StyleTransfer).prompt}
              </p>
            )}
          </>
        );
      case "remove-background":
        return (
          <p>
            <strong>Original Image:</strong>{" "}
            {(design as BackgroundRemoval).inputImage || "N/A"}
          </p>
        );
      case "upscale":
        return (
          <p>
            <strong>Scale Factor:</strong>{" "}
            {(design as UpscaleImage).upscaleAmount || "N/A"}
          </p>
        );
      default:
        return null;
    }
  };

  const getImageSrc = () => {
    if (type === "interior" || type === "exterior") {
      return (
        (design as InteriorDesign | ExteriorDesign).outputImages[0] ||
        "/placeholder-image.jpg"
      );
    } else if (type === "style-transfer") {
      return (design as StyleTransfer).outputImage || "/placeholder-image.jpg";
    } else if (
      type === "remove-background" ||
      type === "upscale" ||
      type === "virtual-staging"
    ) {
      const outputImage = (
        design as BackgroundRemoval | UpscaleImage | VirtualStaging
      ).outputImage;
      return outputImage || "/placeholder-image.jpg";
    }
    return "/placeholder-image.jpg";
  };

  const handleViewDetails = () => {
    router.push(`/dashboard/designs/${type}-${design.id}`);
  };

  const handleShare = async () => {
    try {
      await navigator.share({
        title: getCardTitle(),
        text: "Check out this design!",
        url: `${window.location.origin}/dashboard/designs/${type}-${design.id}`,
      });
    } catch (error) {
      // Copy to clipboard if share API is not available
      const url = `${window.location.origin}/dashboard/designs/${type}-${design.id}`;
      await navigator.clipboard.writeText(url);
      toast.success("Link copied to clipboard!");
    }
  };

  const handleDownload = async () => {
    try {
      setIsDownloading(true);
      const imageSrc = getImageSrc();

      // Convert AVIF to PNG using the server
      const response = await fetch("/api/convert-image", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ imageUrl: imageSrc }),
      });

      if (!response.ok) throw new Error("Failed to convert image");

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `${type}-${design.id}.png`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast.success("Image downloaded successfully!");
    } catch (error) {
      console.error("Download error:", error);
      toast.error("Failed to download image. Please try again.");
    } finally {
      setIsDownloading(false);
    }
  };

  const formattedDate = new Date(design.createdAt).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });

  return (
    <Card className="group hover:shadow-lg transition-shadow duration-200">
      <CardHeader className="p-4">
        <CardTitle className="text-lg font-semibold">
          {getCardTitle()}
        </CardTitle>
      </CardHeader>
      <CardContent className="p-4 pt-0">
        <div className="relative w-full h-0 pb-[56.25%] mb-4 overflow-hidden rounded-md">
          {!imageLoaded && (
            <Skeleton className="absolute inset-0 w-full h-full rounded-md" />
          )}
          <Image
            src={getImageSrc()}
            alt={`${type} design`}
            fill
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            className={`object-cover transition-all duration-300 group-hover:scale-105 ${
              imageLoaded ? "opacity-100" : "opacity-0"
            }`}
            onLoadingComplete={() => setImageLoaded(true)}
            priority={false}
            style={{ objectFit: "cover" }}
          />
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200" />
        </div>
        <div className="space-y-2">
          {renderDesignDetails()}
          <div className="flex items-center text-sm text-muted-foreground">
            <Calendar className="w-4 h-4 mr-2" />
            {formattedDate}
          </div>
        </div>
      </CardContent>
      <CardFooter className="p-4 pt-0 flex gap-2">
        <Button
          onClick={handleViewDetails}
          className="flex-1 group-hover:bg-primary group-hover:text-primary-foreground"
          variant="outline"
        >
          <Eye className="w-4 h-4 mr-2" />
          View
        </Button>
        <Button
          onClick={handleShare}
          variant="outline"
          size="icon"
          className="group-hover:bg-primary group-hover:text-primary-foreground"
        >
          <Share2 className="w-4 h-4" />
        </Button>
        <Button
          onClick={handleDownload}
          variant="outline"
          size="icon"
          className="group-hover:bg-primary group-hover:text-primary-foreground"
          disabled={isDownloading}
        >
          <Download className="w-4 h-4" />
        </Button>
      </CardFooter>
    </Card>
  );
};

export default DesignCard;
