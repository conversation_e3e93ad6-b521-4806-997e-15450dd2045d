import { useCallback } from 'react';
import Konva from 'konva';
import { CanvasPosition } from '../types/canvas';

interface UseCanvasZoomProps {
  onZoomChange?: (scale: number) => void;
}

export const useCanvasZoom = ({ onZoomChange }: UseCanvasZoomProps = {}) => {
  const handleWheel = useCallback((e: Konva.KonvaEventObject<WheelEvent>, stage: Konva.Stage) => {
    if (!e.evt.ctrlKey) return;

    e.evt.preventDefault();
    const oldScale = stage.scaleX();
    const pointer = stage.getPointerPosition();
    if (!pointer) return;

    const scaleBy = 1.02;
    const direction = e.evt.deltaY > 0 ? 1 / scaleBy : scaleBy;
    const newScale = oldScale * direction;

    const mousePointTo = {
      x: (pointer.x - stage.x()) / oldScale,
      y: (pointer.y - stage.y()) / oldScale,
    };

    stage.scale({ x: newScale, y: newScale });

    const newPos = {
      x: pointer.x - mousePointTo.x * newScale,
      y: pointer.y - mousePointTo.y * newScale,
    };
    
    stage.position(newPos);
    stage.batchDraw();
    onZoomChange?.(newScale);
  }, [onZoomChange]);

  return {
    handleWheel,
  };
}; 