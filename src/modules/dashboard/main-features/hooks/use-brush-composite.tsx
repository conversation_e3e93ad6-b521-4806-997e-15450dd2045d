import React from "react";
import { Layer, Shape } from "react-konva";
import { CanvasLine, CanvasSize } from "../types/canvas";

export interface MagicFillCompositeLayerProps {
  lines: CanvasLine[];
  dimensions: CanvasSize;
  isDrawing: boolean;
  x?: number;
  y?: number;
}

/**
 * MagicFillCompositeLayer combines all mask strokes—in any tool—from
 * brush/eraser and rectangle/lasso into one composited mask.
 *
 * Drawing all strokes in order ensures that eraser or brush strokes
 * correctly modify underlying rectangle and lasso masks.
 */
export const MagicFillCompositeLayer: React.FC<
  MagicFillCompositeLayerProps
> = ({ lines, dimensions, isDrawing, x = 0, y = 0 }) => {
  // Exclude the active (in-progress) stroke.
  const finalizedLines = isDrawing ? lines.slice(0, lines.length - 1) : lines;

  return (
    <Layer>
      <Shape
        x={x}
        y={y}
        listening={false}
        sceneFunc={(ctx, shape) => {
          // Clear the canvas first
          ctx.clearRect(0, 0, dimensions.width, dimensions.height);

          finalizedLines.forEach((line) => {
            ctx.save();

            if (line.tool === "rectangle" || line.tool === "lasso") {
              ctx.globalCompositeOperation = "source-over";
              // Compute gradient fill
              const xs = line.points.filter((_, idx) => idx % 2 === 0);
              const ys = line.points.filter((_, idx) => idx % 2 === 1);
              const minX = Math.min(...xs);
              const minY = Math.min(...ys);
              const maxX = Math.max(...xs);
              const maxY = Math.max(...ys);
              const grad = ctx.createLinearGradient(minX, minY, maxX, maxY);
              grad.addColorStop(0, "#8B5CF6");
              grad.addColorStop(1, "#4F46E5");
              ctx.fillStyle = grad;

              ctx.beginPath();
              ctx.moveTo(line.points[0], line.points[1]);
              for (let i = 2; i < line.points.length; i += 2) {
                ctx.lineTo(line.points[i], line.points[i + 1]);
              }
              ctx.closePath();
              ctx.fill();
            } else {
              // For brush and eraser
              ctx.lineWidth = line.strokeWidth;
              ctx.lineCap = "round";
              ctx.lineJoin = "round";

              if (line.tool === "eraser") {
                ctx.globalCompositeOperation = "destination-out";
                ctx.strokeStyle = "rgba(0, 0, 0, 1)";
              } else {
                // brush
                ctx.globalCompositeOperation = "source-over";
                const gradient = ctx.createLinearGradient(
                  line.points[0],
                  line.points[1],
                  line.points[line.points.length - 2],
                  line.points[line.points.length - 1]
                );
                gradient.addColorStop(0, "#8B5CF6");
                gradient.addColorStop(1, "#4F46E5");
                ctx.strokeStyle = gradient;
              }

              ctx.beginPath();
              ctx.moveTo(line.points[0], line.points[1]);

              if (line.points.length >= 4) {
                for (let i = 2; i < line.points.length - 2; i += 2) {
                  const x = line.points[i];
                  const y = line.points[i + 1];
                  const nextX = line.points[i + 2];
                  const nextY = line.points[i + 3];
                  const midX = (x + nextX) / 2;
                  const midY = (y + nextY) / 2;
                  ctx.quadraticCurveTo(x, y, midX, midY);
                }
                ctx.lineTo(
                  line.points[line.points.length - 2],
                  line.points[line.points.length - 1]
                );
              } else {
                for (let i = 2; i < line.points.length; i += 2) {
                  ctx.lineTo(line.points[i], line.points[i + 1]);
                }
              }
              ctx.stroke();
            }
            ctx.restore();
          });

          // Update the hit region only if the cached hit canvas is available.
          if (
            shape &&
            typeof shape.drawHitFromCache === "function" &&
            (shape._cache as any)?.hitCanvas
          ) {
            shape.drawHitFromCache();
          }
        }}
      />
    </Layer>
  );
};

export default MagicFillCompositeLayer;
