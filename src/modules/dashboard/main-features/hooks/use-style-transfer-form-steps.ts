import { useState, useEffect } from "react";
import useStyleTransferStore from "../store/style-transfer-store";

export const useStyleTransferFormSteps = () => {
  const store = useStyleTransferStore();
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 3;

  // Calculate progress
  const progress = (currentStep / totalSteps) * 100;

  // Helper function to determine if a step is complete
  const isStepComplete = (step: number) => {
    switch (step) {
      case 1:
        return !!store.style_image;
      case 2:
        return !!store.model;
      case 3:
        return true; // Optional step
      default:
        return false;
    }
  };

  // Update current step based on form state
  useEffect(() => {
    if (!store.style_image) {
      setCurrentStep(1);
    } else if (!store.model) {
      setCurrentStep(2);
    } else {
      setCurrentStep(3);
    }
  }, [store.style_image, store.model]);

  return {
    currentStep,
    totalSteps,
    progress,
    isStepComplete,
  };
}; 