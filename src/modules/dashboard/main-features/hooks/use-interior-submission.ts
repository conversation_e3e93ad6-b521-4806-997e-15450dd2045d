import { useToast } from "@/modules/ui/use-toast";
import useInteriorFormStore from "../store/interior-form-store";
import { generateInteriorImage } from "../actions/render-interior-image";
import { useCurrentUser } from "@/modules/auth/hooks/use-current-user";
import { useState } from "react";
import { useImageCreditsStore } from "../stores/use-image-credits-store";
import { InteriorGenerateImageParams, PredictionStatus, InteriorPlaceholder } from "../types";

interface UseInteriorSubmissionProps {
  onShowPaywall: (feature: "credits" | "privacy") => void;
}

export const useInteriorSubmission = ({ onShowPaywall }: UseInteriorSubmissionProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const store = useInteriorFormStore();
  const { toast } = useToast();
  const currentUser = useCurrentUser();
  const { credits, setCredits } = useImageCreditsStore();
  const isAdvancedMode = store.isPromptActive;

  const validateForm = () => {
    store.clearErrors();
    const hasEnoughCredits = credits && credits >= 1;

    if (!store.imageUrl) {
      store.setError("imageUrl", ["Image is required"]);
      toast({
        title: "Validation Error",
        description: "Please upload an image",
        variant: "destructive",
      });
      return false;
    }

    if (!hasEnoughCredits) {
      onShowPaywall("credits");
      toast({
        title: "Insufficient Credits",
        description: "You need 1 credit to generate an image",
        variant: "destructive",
      });
      return false;
    }

    // Clear prompt when not in advanced mode, and room/style when in advanced mode
    if (isAdvancedMode) {
      store.setRoom(undefined);
      store.setStyle(undefined);
      if (!store.prompt) {
        store.setError("prompt", ["Prompt is required in advanced mode"]);
        toast({
          title: "Validation Error",
          description: "Please enter a prompt in advanced mode",
          variant: "destructive",
        });
        return false;
      }
    } else {
      store.setPrompt("");
      if (!store.room || !store.style) {
        if (!store.room) store.setError("room", ["Room selection is required"]);
        if (!store.style) store.setError("style", ["Style selection is required"]);
        toast({
          title: "Validation Error",
          description: "Please select both room and style",
          variant: "destructive",
        });
        return false;
      }
    }

    if (!currentUser.data?.id) {
      toast({
        title: "Authentication Error",
        description: "User not authenticated. Please log in.",
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  const handleSubmit = async (formData: FormData) => {
    if (!validateForm()) return;
    
    try {
      setIsSubmitting(true);
      store.setIsLoading(true);
      
      const params: InteriorGenerateImageParams = {
        userId: currentUser.data!.id,
        image: store.imageUrl || "",
        excludedElements: store.excludedElements || null,
        conditionScale: store.conditionScale,
        creativity: store.creativity,
        ...(isAdvancedMode
          ? { prompt: store.prompt }
          : { room: store.room!, style: store.style! }),
      };

      const result = await generateInteriorImage(params);

      if (!result.success) {
        if ("type" in result && result.type === "INSUFFICIENT_CREDITS") {
          onShowPaywall("credits");
        }
        toast({
          title: "Generation Failed",
          description: result.error,
          variant: "destructive",
        });
        return;
      }

      if (result.predictionId && result.replicateId) {
        store.setCurrentPredictionId(result.replicateId);

        // Generate a prompt description for the placeholder based on available data
        let promptText = "Interior design";
        if (params.prompt) {
          promptText = params.prompt;
        } else if (params.room && params.style) {
          promptText = `${params.room} in ${params.style} style`;
        }
        
        // Call addImagePlaceholders with properly typed object
        // Use a simple approach without trying to make TypeScript too happy
        try {
          // @ts-ignore: Ignore type mismatch issues
          store.addImagePlaceholders([{
            inputImage: params.image,
            id: result.replicateId,
            status: PredictionStatus.PROCESSING,
            prompt: promptText,
            // Optional properties
            style: params.style || undefined,
            room: params.room || undefined,
            excludedElements: params.excludedElements || undefined,
            createdAt: new Date(),
          }]);
        } catch (err) {
          console.error("Error adding placeholder:", err);
        }

        if (result.updatedCredits !== undefined) {
          setCredits(result.updatedCredits);
        }
      }
    } catch (error) {
      toast({
        title: "Unexpected Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
      console.error("An unexpected error occurred:", error);
    } finally {
      setIsSubmitting(false);
      store.setIsLoading(false);
    }
  };

  return {
    handleSubmit,
    isSubmitting,
  };
}; 