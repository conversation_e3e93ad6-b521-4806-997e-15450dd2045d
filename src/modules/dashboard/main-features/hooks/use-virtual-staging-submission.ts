"use client";

import { useState } from "react";
import { useCurrentUser } from "@/modules/auth/hooks/use-current-user";
import { useToast } from "@/modules/ui/use-toast";
import useVirtualStagingFormStore from "@/modules/dashboard/main-features/store/virtual-staging-form-store";
import { useImageCreditsStore } from "@/modules/dashboard/main-features/stores/use-image-credits-store";
import { VirtualStagingGenerateImageParams } from "@/modules/dashboard/main-features/types";
import { generateVirtualStagingImage } from "@/modules/dashboard/main-features/actions/render-virtual-staging";
import { useImageCreditsValidation } from "./use-image-credits-validation";

export interface UseVirtualStagingSubmissionProps {
  onShowPaywall: () => void;
}

export function useVirtualStagingSubmission({ onShowPaywall }: UseVirtualStagingSubmissionProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const currentUser = useCurrentUser();
  const { toast } = useToast();
  const store = useVirtualStagingFormStore();
  const { setCredits } = useImageCreditsStore();
  const { validateCredits } = useImageCreditsValidation({
    onInsufficientCredits: onShowPaywall
  });

  const handleSubmit = async (formData: FormData) => {
    store.clearErrors();
    
    // Validate form completion
    if (!store.validateMask()) {
      toast({
        title: "Validation Error",
        description: "Please create or upload a mask for the image",
        variant: "destructive",
      });
      return;
    }

    if (store.isAdvancedMode && !store.prompt) {
      store.setError("prompt", ["Prompt is required in advanced mode"]);
      toast({
        title: "Validation Error",
        description: "Please enter a prompt in advanced mode",
        variant: "destructive",
      });
      return;
    }

    if (!store.isAdvancedMode && (!store.room || !store.style)) {
      if (!store.room) store.setError("room", ["Room selection is required"]);
      if (!store.style) store.setError("style", ["Style selection is required"]);
      toast({
        title: "Validation Error",
        description: "Please select both room and style",
        variant: "destructive",
      });
      return;
    }

    if (!store.imageUrl) {
      store.setError("imageUrl", ["Image is required"]);
      toast({
        title: "Validation Error",
        description: "Please upload an image",
        variant: "destructive",
      });
      return;
    }

    const isValid = store.validate();
    if (!isValid) {
      toast({
        title: "Validation Error",
        description: "Please check the form for errors",
        variant: "destructive",
      });
      return;
    }

    if (!currentUser.data?.id) {
      toast({
        title: "Authentication Error",
        description: "User not authenticated. Please log in.",
        variant: "destructive",
      });
      return;
    }

    // Check credits
    if (!validateCredits()) {
      return;
    }

    try {
      setIsSubmitting(true);
      store.setIsLoading(true);
      
      const params: VirtualStagingGenerateImageParams = {
        userId: currentUser.data.id,
        image: store.imageUrl || "",
        mask: store.maskUrl,
        excludedElements: store.excludedElements || null,
        resolution: store.resolution,
        style_type: store.styleType,
        aspect_ratio: store.aspectRatio,
        negative_prompt: store.negativePrompt,
        magic_prompt_option: store.magicPromptOption,
        isAdvancedMode: store.isAdvancedMode,
        ...(store.isAdvancedMode
          ? { prompt: store.prompt }
          : { room: store.room!, style: store.style! }),
      };

      const result = await generateVirtualStagingImage(params);

      if (!result.success) {
        if ("type" in result && result.type === "INSUFFICIENT_CREDITS") {
          onShowPaywall();
        }
        toast({
          title: "Generation Failed",
          description: result.error,
          variant: "destructive",
        });
        return;
      }

      if (result.predictionId && result.placeholders) {
        store.setCurrentPredictionId(result.predictionId);
        store.addImagePlaceholders(result.placeholders);

        if (result.updatedCredits !== undefined) {
          setCredits(result.updatedCredits);
        }
      }
    } catch (error) {
      toast({
        title: "Unexpected Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
      console.error("An unexpected error occurred:", error);
    } finally {
      setIsSubmitting(false);
      store.setIsLoading(false);
    }
  };

  return {
    handleSubmit,
    isSubmitting
  };
} 