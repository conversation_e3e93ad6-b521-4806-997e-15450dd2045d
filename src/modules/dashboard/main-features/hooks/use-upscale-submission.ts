import { useState } from "react";
import { useToast } from "@/modules/ui/use-toast";
import { useCurrentUser } from "@/modules/auth/hooks/use-current-user";
import useUpscaleFormStore from "../store/upscale-form-store";
import { upscaleImage } from "../actions/upscale-image";
import { useImageCreditsStore } from "../stores/use-image-credits-store";

export const useUpscaleSubmission = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const {
    imageUrl,
    upscaleAmount,
    creativity,
    imageStrength,
    setIsLoading,
    addImagePlaceholders,
    setCurrentPredictionId,
  } = useUpscaleFormStore();
  const currentUser = useCurrentUser();
  const { toast } = useToast();
  const { credits } = useImageCreditsStore();

  const handleSubmit = async (formData: FormData) => {
    if (!imageUrl) {
      toast({
        title: "Image Required",
        description: "Please upload a valid image first",
        variant: "destructive",
      });
      return;
    }

    if (!currentUser.data?.id) {
      toast({
        title: "Authentication Error",
        description: "User not authenticated. Please log in.",
        variant: "destructive",
      });
      return;
    }

    // Check for credits
    const hasEnoughCredits = credits && credits >= 1;
    if (!hasEnoughCredits) {
      toast({
        title: "Insufficient Credits",
        description: "You need 1 credit to upscale an image",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);
      setIsLoading(true);
      
      const result = await upscaleImage({
        userId: currentUser.data.id,
        image: imageUrl,
        upscaleAmount,
        creativity,
        resemblance: imageStrength,
      });

      if (result.success && result.predictionId && result.placeholder) {
        setCurrentPredictionId(result.predictionId);
        addImagePlaceholders([result.placeholder]);
      } else {
        toast({
          title: "Upscaling Failed",
          description: result.error || "Failed to upscale image",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Unexpected Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
      console.error("An unexpected error occurred:", error);
    } finally {
      setIsSubmitting(false);
      setIsLoading(false);
    }
  };

  return {
    handleSubmit,
    isSubmitting,
  };
}; 