import { useState, useEffect, RefObject, useCallback } from "react";
import { useMediaQuery } from "@/modules/hooks/use-media-query";
import debounce from "lodash/debounce";

interface UseMobileDrawerProps {
  scrollAreaRef: RefObject<HTMLDivElement>;
  excludeElementsRef: RefObject<HTMLDivElement>;
  headerHeight?: number;
  debounceMs?: number;
}

interface UseMobileDrawerReturn {
  isDrawerOpen: boolean;
  setIsDrawerOpen: (open: boolean) => void;
  drawerHeight: string;
  keyboardVisible: boolean;
  keyboardHeight: number;
  activeInput: string | null;
  setActiveInput: (input: string | null) => void;
  handleDrawerScroll: () => void;
}

export const useMobileDrawer = ({
  scrollAreaRef,
  excludeElementsRef,
  headerHeight = 64,
  debounceMs = 150,
}: UseMobileDrawerProps): UseMobileDrawerReturn => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [drawerHeight, setDrawerHeight] = useState("85vh");
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [activeInput, setActiveInput] = useState<string | null>(null);
  const isMobile = useMediaQuery("(max-width: 768px)");

  const KEYBOARD_PADDING = 20;

  // Memoize the scroll handler
  const handleDrawerScroll = useCallback(() => {
    if (keyboardVisible && activeInput?.includes("exclude")) {
      window.scrollTo(0, 0);
    }
  }, [keyboardVisible, activeInput]);

  // Memoize the viewport resize handler
  const handleVisualViewportResize = useCallback(
    debounce(() => {
      if (!window.visualViewport) return;

      const viewport = window.visualViewport;
      const windowHeight = window.innerHeight;
      const isKeyboardOpen = windowHeight - viewport.height > 100;

      setKeyboardHeight(windowHeight - viewport.height);
      setKeyboardVisible(isKeyboardOpen);

      if (isKeyboardOpen) {
        setDrawerHeight(`${viewport.height}px`);
        requestAnimationFrame(() => {
          const element = excludeElementsRef.current;
          const scrollArea = scrollAreaRef.current;

          if (element && scrollArea) {
            const elementRect = element.getBoundingClientRect();
            const viewportOffset = elementRect.top;
            const desiredPosition = viewport.height * 0.3;
            const scrollOffset = viewportOffset - desiredPosition;

            scrollArea.scrollBy({
              top: scrollOffset,
              behavior: "smooth",
            });
          }
        });
      } else {
        setDrawerHeight("85vh");
      }
    }, debounceMs),
    [excludeElementsRef, scrollAreaRef]
  );

  // Handle viewport meta tag
  useEffect(() => {
    if (typeof window === "undefined" || !isMobile) return;

    const meta = document.createElement("meta");
    meta.name = "viewport";
    meta.content =
      "width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover";
    document.getElementsByTagName("head")[0].appendChild(meta);

    return () => {
      document.getElementsByTagName("head")[0].removeChild(meta);
    };
  }, [isMobile]);

  // Handle viewport resize
  useEffect(() => {
    if (typeof window === "undefined" || !window.visualViewport || !isMobile)
      return;

    const viewport = window.visualViewport;
    viewport.addEventListener("resize", handleVisualViewportResize);

    return () => {
      viewport.removeEventListener("resize", handleVisualViewportResize);
      handleVisualViewportResize.cancel();
    };
  }, [handleVisualViewportResize, isMobile]);

  // Handle input focus
  useEffect(() => {
    if (!isMobile) return;

    const handleFocus = (e: FocusEvent) => {
      if (
        e.target instanceof HTMLInputElement ||
        e.target instanceof HTMLTextAreaElement
      ) {
        setActiveInput(e.target.id);

        if (e.target.closest(".exclude-elements-container")) {
          e.preventDefault();
          requestAnimationFrame(() => {
            const element = excludeElementsRef.current;
            const scrollArea = scrollAreaRef.current;

            if (element && scrollArea && window.visualViewport) {
              const elementRect = element.getBoundingClientRect();
              const viewportHeight = window.visualViewport.height;
              const desiredPosition = viewportHeight * 0.3;
              const scrollOffset = elementRect.top - desiredPosition;

              scrollArea.scrollBy({
                top: scrollOffset,
                behavior: "smooth",
              });
            }
          });
        }
      }
    };

    document.addEventListener("focus", handleFocus, true);
    return () => document.removeEventListener("focus", handleFocus, true);
  }, [excludeElementsRef, scrollAreaRef, isMobile]);

  return {
    isDrawerOpen,
    setIsDrawerOpen,
    drawerHeight,
    keyboardVisible,
    keyboardHeight,
    activeInput,
    setActiveInput,
    handleDrawerScroll,
  };
};
