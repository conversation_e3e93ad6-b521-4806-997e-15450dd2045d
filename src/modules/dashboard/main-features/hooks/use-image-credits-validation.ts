"use client";

import { useImageCreditsStore } from "@/modules/dashboard/main-features/stores/use-image-credits-store";
import { useToast } from "@/modules/ui/use-toast";

interface UseImageCreditsValidationOptions {
  requiredCredits?: number;
  onInsufficientCredits?: () => void;
}

export function useImageCreditsValidation({
  requiredCredits = 5,
  onInsufficientCredits,
}: UseImageCreditsValidationOptions = {}) {
  const { credits } = useImageCreditsStore();
  const { toast } = useToast();

  const hasEnoughCredits = credits !== null && credits >= requiredCredits;

  const validateCredits = (customMessage?: string): boolean => {
    if (!hasEnoughCredits) {
      if (onInsufficientCredits) {
        onInsufficientCredits();
      } else {
        toast({
          title: "Insufficient Credits",
          description: customMessage || `You need ${requiredCredits} credits to use this feature`,
          variant: "destructive",
        });
      }
      return false;
    }
    return true;
  };

  return {
    hasEnoughCredits,
    validateCredits,
    requiredCredits,
    currentCredits: credits,
  };
} 