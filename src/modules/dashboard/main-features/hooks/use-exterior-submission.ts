import { useToast } from "@/modules/ui/use-toast";
import useExteriorFormStore from "../store/exterior-form-store";
import { generateExteriorImage } from "../actions/render-exterior-image";
import { useCurrentUser } from "@/modules/auth/hooks/use-current-user";
import { useState } from "react";
import { useImageCreditsStore } from "../stores/use-image-credits-store";
import { 
  ExteriorGenerateImageParams, 
  PredictionStatus, 
  ExteriorPlaceholder,
  ExteriorUserInputSchema
} from "../types";

interface UseExteriorSubmissionProps {
  onShowPaywall: (feature: "credits" | "privacy") => void;
}

export const useExteriorSubmission = ({ onShowPaywall }: UseExteriorSubmissionProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const store = useExteriorFormStore();
  const { toast } = useToast();
  const currentUser = useCurrentUser();
  const { credits, setCredits } = useImageCreditsStore();
  const isAdvancedMode = store.isPromptActive;

  const validateForm = () => {
    store.clearErrors();
    const hasEnoughCredits = credits && credits >= 1;

    if (!store.imageUrl) {
      store.setError("imageUrl", ["Image is required"]);
      toast({
        title: "Validation Error",
        description: "Please upload an image",
        variant: "destructive",
      });
      return false;
    }

    if (!hasEnoughCredits) {
      onShowPaywall("credits");
      toast({
        title: "Insufficient Credits",
        description: "You need 1 credit to generate an image",
        variant: "destructive",
      });
      return false;
    }

    // Clear prompt when not in advanced mode, and building/style when in advanced mode
    if (isAdvancedMode) {
      store.setBuilding(undefined);
      store.setStyle(undefined);
      if (!store.prompt) {
        store.setError("prompt", ["Prompt is required in advanced mode"]);
        toast({
          title: "Validation Error",
          description: "Please enter a prompt in advanced mode",
          variant: "destructive",
        });
        return false;
      }
    } else {
      store.setPrompt("");
      if (!store.building || !store.style) {
        if (!store.building) store.setError("building", ["Building selection is required"]);
        if (!store.style) store.setError("style", ["Style selection is required"]);
        toast({
          title: "Validation Error",
          description: "Please select both building and style",
          variant: "destructive",
        });
        return false;
      }
    }

    if (!currentUser.data?.id) {
      toast({
        title: "Authentication Error",
        description: "User not authenticated. Please log in.",
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  const handleSubmit = async (formData: FormData) => {
    if (!validateForm()) return;
    
    try {
      setIsSubmitting(true);
      store.setIsLoading(true);
      
      // Ensure imageUrl is defined before creating params
      if (!store.imageUrl || !currentUser.data?.id) {
        throw new Error("Image or user ID is missing");
      }
      
      const baseParams = {
        userId: currentUser.data.id,
        image: store.imageUrl,
        excludedElements: store.excludedElements || null,
        conditionScale: store.conditionScale,
        creativity: store.creativity,
      };

      let params: ExteriorGenerateImageParams;
      
      if (isAdvancedMode && store.prompt) {
        params = {
          ...baseParams,
          prompt: store.prompt
        };
      } else if (store.building && store.style) {
        params = {
          ...baseParams,
          building: store.building,
          style: store.style
        };
      } else {
        throw new Error("Missing required parameters");
      }

      // Validate with Zod schema
      try {
        ExteriorUserInputSchema.parse(params);
      } catch (error) {
        toast({
          title: "Invalid Input",
          description: error instanceof Error ? error.message : "Please check your settings.",
          variant: "destructive",
        });
        return;
      }

      const result = await generateExteriorImage(params);

      if (!result.success) {
        if ("type" in result && result.type === "INSUFFICIENT_CREDITS") {
          onShowPaywall("credits");
        }
        toast({
          title: "Generation Failed",
          description: result.error || "Failed to generate image",
          variant: "destructive",
        });
        return;
      }

      if (result.predictionId && result.replicateId) {
        store.setCurrentPredictionId(result.replicateId);

        // Generate a prompt description for the placeholder based on available data
        let promptText = "Exterior design";
        if (params.prompt) {
          promptText = params.prompt;
        } else if ('building' in params && 'style' in params) {
          promptText = `${params.building} in ${params.style} style`;
        }
        
        // Create placeholder
        const placeholder: ExteriorPlaceholder = {
          inputImage: params.image,
          id: result.replicateId,
          status: PredictionStatus.PROCESSING,
          prompt: promptText,
          style: 'style' in params ? params.style : undefined,
          building: 'building' in params ? params.building : undefined,
          excludedElements: params.excludedElements || undefined,
          createdAt: new Date(),
        };

        // Add placeholder
        store.addImagePlaceholders([placeholder]);

        // Update credits if returned
        if (result.updatedCredits !== undefined) {
          setCredits(result.updatedCredits);
        }
      }
    } catch (error) {
      toast({
        title: "Unexpected Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
      console.error("An unexpected error occurred:", error);
    } finally {
      setIsSubmitting(false);
      store.setIsLoading(false);
    }
  };

  return {
    handleSubmit,
    isSubmitting,
  };
}; 