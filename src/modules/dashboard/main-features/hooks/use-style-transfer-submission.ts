import { useState } from "react";
import { useToast } from "@/modules/ui/use-toast";
import { useCurrentUser } from "@/modules/auth/hooks/use-current-user";
import useStyleTransferStore from "../store/style-transfer-store";
import { generateStyleTransfer } from "../actions/render-style-transfer";
import { StyleTransferParams } from "../types";
import { useImageCreditsStore } from "../stores/use-image-credits-store";

interface UseStyleTransferSubmissionProps {
  onShowPaywall: () => void;
}

export const useStyleTransferSubmission = ({
  onShowPaywall,
}: UseStyleTransferSubmissionProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const store = useStyleTransferStore();
  const currentUser = useCurrentUser();
  const { toast } = useToast();
  const { setCredits, credits } = useImageCreditsStore();

  const handleSubmit = async (formData: FormData) => {
    store.clearErrors();

    // Validate style image
    if (!store.style_image) {
      store.setError("style_image", ["Style image is required"]);
      toast({
        title: "Validation Error",
        description: "Please upload a style image",
        variant: "destructive",
      });
      return;
    }

    // Validate structure image
    if (!store.structure_image) {
      store.setError("structure_image", ["Structure image is required"]);
      toast({
        title: "Validation Error",
        description: "Please upload a structure image",
        variant: "destructive",
      });
      return;
    }

    // Validate user
    if (!currentUser.data?.id) {
      toast({
        title: "Authentication Error",
        description: "User not authenticated. Please log in.",
        variant: "destructive",
      });
      return;
    }

    // Validate credits
    if (credits && credits < 1) {
      onShowPaywall();
      return;
    }

    try {
      setIsSubmitting(true);
      store.setIsLoading(true);

      // Ensure style_image is a valid URL
      if (!store.style_image.startsWith("http")) {
        toast({
          title: "Invalid Style Image",
          description: "Please upload a valid image",
          variant: "destructive",
        });
        return;
      }

      // Create the params object with all required fields
      const params: StyleTransferParams = {
        userId: currentUser.data?.id || "",
        style_image: store.style_image,
        structure_image: store.structure_image,
        model: store.model,
        width: store.width,
        height: store.height,
        prompt: store.prompt,
        negative_prompt: store.negative_prompt,
        number_of_images: store.number_of_images,
        structure_depth_strength: store.structure_depth_strength,
        structure_denoising_strength: store.structure_denoising_strength,
        output_format: store.output_format,
        output_quality: store.output_quality,
        seed: store.seed,
      };

      const result = await generateStyleTransfer(params);

      if (!result.success) {
        if ("type" in result && result.type === "INSUFFICIENT_CREDITS") {
          onShowPaywall();
        }
        toast({
          title: "Generation Failed",
          description: result.error,
          variant: "destructive",
        });
        return;
      }

      if (result.predictionId && result.replicateId) {
        store.setCurrentPredictionId(result.replicateId);

        if (result.placeholders) {
          store.addImagePlaceholders(result.placeholders);
        }

        if (result.updatedCredits !== undefined) {
          setCredits(result.updatedCredits);
        }
      }
    } catch (error) {
      console.error("Error generating style transfer:", error);
      if (error instanceof Error) {
        toast({
          title: "Error",
          description: error.message,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Unexpected Error",
          description: "An unexpected error occurred. Please try again.",
          variant: "destructive",
        });
      }
    } finally {
      setIsSubmitting(false);
      store.setIsLoading(false);
    }
  };

  return {
    handleSubmit,
    isSubmitting,
  };
}; 