import { useState } from "react";
import { useToast } from "@/modules/ui/use-toast";
import { useCurrentUser } from "@/modules/auth/hooks/use-current-user";
import useBackgroundRemovalStore from "../store/background-removal-store";
import { removeBackground } from "../actions/remove-background";
import { BackgroundRemovalPlaceholder, PredictionStatus } from "../types";
import { useImageCreditsStore } from "../stores/use-image-credits-store";

interface UseBackgroundRemovalSubmissionProps {
  onShowPaywall: () => void;
}

export const useBackgroundRemovalSubmission = ({
  onShowPaywall,
}: UseBackgroundRemovalSubmissionProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { imageUrl, setIsLoading, addPlaceholder } = useBackgroundRemovalStore();
  const currentUser = useCurrentUser();
  const { toast } = useToast();
  const { credits } = useImageCreditsStore();

  const handleSubmit = async (formData: FormData) => {
    if (!imageUrl) {
      toast({
        title: "Image Required",
        description: "Please upload a valid image first",
        variant: "destructive",
      });
      return;
    }

    if (!currentUser.data?.id) {
      toast({
        title: "Authentication Error",
        description: "User not authenticated. Please log in.",
        variant: "destructive",
      });
      return;
    }

    // Check for credits
    const hasEnoughCredits = credits && credits >= 1;
    if (!hasEnoughCredits) {
      onShowPaywall();
      toast({
        title: "Insufficient Credits",
        description: "You need 1 credit to remove background",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);
      setIsLoading(true);
      
      const result = await removeBackground({
        userId: currentUser.data.id,
        image: imageUrl,
      });

      const placeholder: BackgroundRemovalPlaceholder = {
        id: result.id,
        status: PredictionStatus.PROCESSING,
        inputImage: imageUrl,
        createdAt: new Date(),
      };
      
      addPlaceholder(placeholder);

      if (result.status === PredictionStatus.FAILED) {
        toast({
          title: "Background Removal Failed",
          description: "Failed to remove background. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Unexpected Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
      console.error("An unexpected error occurred:", error);
    } finally {
      setIsSubmitting(false);
      setIsLoading(false);
    }
  };

  return {
    handleSubmit,
    isSubmitting,
  };
}; 