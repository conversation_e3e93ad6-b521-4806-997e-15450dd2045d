import { useState, useEffect } from "react";
import useBackgroundRemovalStore from "../store/background-removal-store";

export const useBackgroundRemovalFormSteps = () => {
  const { imageUrl } = useBackgroundRemovalStore();
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 1;

  // Calculate progress
  const progress = (currentStep / totalSteps) * 100;

  // Helper function to determine if a step is complete
  const isStepComplete = (step: number) => {
    return step === 1 ? !!imageUrl : false;
  };

  // Update current step based on form state
  useEffect(() => {
    setCurrentStep(imageUrl ? 1 : 1);
  }, [imageUrl]);

  return {
    currentStep,
    totalSteps,
    progress,
    isStepComplete,
  };
}; 