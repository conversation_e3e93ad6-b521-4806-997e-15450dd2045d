import { useState, useEffect } from "react";
import useUpscaleFormStore from "../store/upscale-form-store";

export const useUpscaleFormSteps = () => {
  const { imageUrl } = useUpscaleFormStore();
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 2;

  // Calculate progress
  const progress = (currentStep / totalSteps) * 100;

  // Helper function to determine if a step is complete
  const isStepComplete = (step: number) => {
    switch (step) {
      case 1:
        return !!imageUrl;
      case 2:
        return true; // Optional settings
      default:
        return false;
    }
  };

  // Update current step based on form state
  useEffect(() => {
    if (!imageUrl) {
      setCurrentStep(1);
    } else {
      setCurrentStep(2);
    }
  }, [imageUrl]);

  return {
    currentStep,
    totalSteps,
    progress,
    isStepComplete,
  };
}; 