"use client";
import { useEffect } from "react";
import { useImageCreditsStore } from "@/modules/dashboard/main-features/stores/use-image-credits-store";
import { useSession } from "next-auth/react";

export function useImageCredits() {
  const { credits, isLoading, error, fetchCredits } = useImageCreditsStore();
  const { status } = useSession();

  useEffect(() => {
    if (status === "authenticated" && credits === null) {
      fetchCredits();
    }
  }, [status, credits, fetchCredits]);

  return { credits, isLoading, error };
}
