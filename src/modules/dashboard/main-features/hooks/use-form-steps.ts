import { useState, useEffect, useCallback } from "react";
import useVirtualStagingFormStore from "../store/virtual-staging-form-store";

interface Step {
  num: number;
  title: string;
  isRequired: boolean;
  validate: () => boolean;
}

interface UseFormStepsProps {
  isAdvancedMode: boolean;
}

interface UseFormStepsReturn {
  currentStep: number;
  totalSteps: number;
  progress: number;
  isStepComplete: (step: number) => boolean;
  goToStep: (step: number) => void;
  nextStep: () => void;
  prevStep: () => void;
  canProceed: boolean;
  steps: Step[];
}

export const useFormSteps = ({
  isAdvancedMode,
}: UseFormStepsProps): UseFormStepsReturn => {
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 4;
  const store = useVirtualStagingFormStore();

  // Define steps with validation logic
  const steps: Step[] = [
    {
      num: 1,
      title: "Upload",
      isRequired: true,
      validate: () => !!store.imageUrl,
    },
    {
      num: 2,
      title: "Mask",
      isRequired: true,
      validate: () => !!store.maskUrl,
    },
    {
      num: 3,
      title: isAdvancedMode ? "Prompt" : "Style",
      isRequired: true,
      validate: () => {
        if (isAdvancedMode) {
          return !!store.prompt;
        }
        return !!(store.room && store.style);
      },
    },
    {
      num: 4,
      title: "Settings",
      isRequired: false,
      validate: () => true, // Optional step is always valid
    },
  ];

  const isStepComplete = useCallback(
    (step: number) => {
      const targetStep = steps.find((s) => s.num === step);
      return targetStep ? targetStep.validate() : false;
    },
    [steps]
  );

  // Navigation functions
  const goToStep = useCallback(
    (step: number) => {
      if (step >= 1 && step <= totalSteps) {
        setCurrentStep(step);
      }
    },
    [totalSteps]
  );

  const nextStep = useCallback(() => {
    if (currentStep < totalSteps && isStepComplete(currentStep)) {
      setCurrentStep((prev) => prev + 1);
    }
  }, [currentStep, totalSteps, isStepComplete]);

  const prevStep = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep((prev) => prev - 1);
    }
  }, [currentStep]);

  // Auto-advance logic
  useEffect(() => {
    if (!store.imageUrl) {
      goToStep(1);
    } else if (!store.maskUrl) {
      goToStep(2);
    } else if (isAdvancedMode ? !store.prompt : !(store.room && store.style)) {
      goToStep(3);
    } else {
      goToStep(4);
    }
  }, [
    store.imageUrl,
    store.maskUrl,
    store.prompt,
    store.room,
    store.style,
    isAdvancedMode,
    goToStep,
  ]);

  // Calculate if we can proceed based on current step validation
  const canProceed = isStepComplete(currentStep);

  // Calculate progress
  const progress = (currentStep / totalSteps) * 100;

  return {
    currentStep,
    totalSteps,
    progress,
    isStepComplete,
    goToStep,
    nextStep,
    prevStep,
    canProceed,
    steps,
  };
};
