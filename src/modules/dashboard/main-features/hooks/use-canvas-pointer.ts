import { useCallback } from 'react';
import Konva from 'konva';
import { CanvasPosition } from '../types/canvas';

export const useCanvasPointer = () => {
  const getRelativePointerPosition = useCallback((stage: Konva.Stage): CanvasPosition | null => {
    const pointer = stage.getPointerPosition();
    if (pointer) {
      return {
        x: (pointer.x - stage.x()) / stage.scaleX(),
        y: (pointer.y - stage.y()) / stage.scaleY(),
      };
    }
    return null;
  }, []);

  const snapToGrid = useCallback((pos: CanvasPosition, gridSize: number = 1): CanvasPosition => {
    return {
      x: Math.round(pos.x / gridSize) * gridSize,
      y: Math.round(pos.y / gridSize) * gridSize
    };
  }, []);

  return {
    getRelativePointerPosition,
    snapToGrid,
  };
}; 