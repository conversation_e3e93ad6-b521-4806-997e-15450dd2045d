import { useState, useEffect, useCallback, useRef } from "react";
import useInteriorFormStore from "../store/interior-form-store";

interface Step {
  num: number;
  title: string;
  isRequired: boolean;
  validate: () => boolean;
}

interface UseInteriorFormStepsProps {
  isAdvancedMode: boolean;
}

interface UseInteriorFormStepsReturn {
  currentStep: number;
  totalSteps: number;
  progress: number;
  isStepComplete: (step: number) => boolean;
  goToStep: (step: number) => void;
  nextStep: () => void;
  prevStep: () => void;
  canProceed: boolean;
  steps: Step[];
}

export const useInteriorFormSteps = ({
  isAdvancedMode,
}: UseInteriorFormStepsProps): UseInteriorFormStepsReturn => {
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 3;
  const store = useInteriorFormStore();
  const previousStep = useRef<number>(1);

  // Define steps with validation logic
  const steps: Step[] = [
    {
      num: 1,
      title: "Upload Room Photo",
      isRequired: true,
      validate: () => !!store.imageUrl,
    },
    {
      num: 2,
      title: isAdvancedMode ? "Custom Prompt" : "Room & Style",
      isRequired: true,
      validate: () => {
        if (isAdvancedMode) {
          return !!store.prompt;
        }
        return !!(store.room && store.style);
      },
    },
    {
      num: 3,
      title: "Additional Settings",
      isRequired: false,
      validate: () => true, // Optional step is always valid
    },
  ];

  const isStepComplete = useCallback(
    (step: number) => {
      const targetStep = steps.find((s) => s.num === step);
      return targetStep ? targetStep.validate() : false;
    },
    [steps]
  );

  // Navigation functions
  const goToStep = useCallback(
    (step: number) => {
      if (step >= 1 && step <= totalSteps && step !== currentStep) {
        setCurrentStep(step);
      }
    },
    [totalSteps, currentStep]
  );

  const nextStep = useCallback(() => {
    if (currentStep < totalSteps && isStepComplete(currentStep)) {
      setCurrentStep((prev) => prev + 1);
    }
  }, [currentStep, totalSteps, isStepComplete]);

  const prevStep = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep((prev) => prev - 1);
    }
  }, [currentStep]);

  // Determine which step we should be on based on form state
  const determineCurrentStep = useCallback(() => {
    if (!store.imageUrl) {
      return 1;
    } else if (isAdvancedMode ? !store.prompt : !(store.room && store.style)) {
      return 2;
    } else {
      return 3;
    }
  }, [store.imageUrl, store.prompt, store.room, store.style, isAdvancedMode]);

  // Auto-advance logic with optimization to prevent unnecessary updates
  useEffect(() => {
    const newStep = determineCurrentStep();
    
    // Only update if the step actually changes to avoid infinite loops
    if (newStep !== currentStep && newStep !== previousStep.current) {
      previousStep.current = newStep;
      goToStep(newStep);
    }
  }, [
    determineCurrentStep,
    goToStep,
    currentStep
  ]);

  // Calculate if we can proceed based on current step validation
  const canProceed = isStepComplete(currentStep);

  // Calculate progress
  const progress = (currentStep / totalSteps) * 100;

  return {
    currentStep,
    totalSteps,
    progress,
    isStepComplete,
    goToStep,
    nextStep,
    prevStep,
    canProceed,
    steps,
  };
}; 