import { useState, useEffect, RefObject } from "react";

interface UseExteriorMobileDrawerProps {
  scrollAreaRef?: RefObject<HTMLDivElement>;
}

interface UseExteriorMobileDrawerReturn {
  isDrawerOpen: boolean;
  setIsDrawerOpen: (value: boolean) => void;
  keyboardVisible: boolean;
  keyboardHeight: number;
}

export const useExteriorMobileDrawer = ({
  scrollAreaRef,
}: UseExteriorMobileDrawerProps): UseExteriorMobileDrawerReturn => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);

  // Handle keyboard visibility
  useEffect(() => {
    if (!window.visualViewport) return;
    
    const handleResize = () => {
      // Check if viewport height has significantly changed (keyboard is shown)
      const visualViewport = window.visualViewport;
      if (!visualViewport) return;

      const isKeyboardVisible = visualViewport.height < window.innerHeight * 0.8;
      
      // Only update state if there's an actual change to avoid infinite renders
      if (isKeyboardVisible !== keyboardVisible) {
        setKeyboardVisible(isKeyboardVisible);
      }

      // Only update keyboard height if it's actually changed
      const newKeyboardHeight = isKeyboardVisible 
        ? window.innerHeight - visualViewport.height 
        : 0;
        
      if (Math.abs(newKeyboardHeight - keyboardHeight) > 20) { // Add threshold to prevent minor fluctuations
        setKeyboardHeight(newKeyboardHeight);
      }

      // Scroll to active element if keyboard is visible
      if (isKeyboardVisible && scrollAreaRef?.current) {
        const activeElement = document.activeElement;
        if (activeElement instanceof HTMLElement) {
          setTimeout(() => {
            activeElement.scrollIntoView({
              behavior: "smooth",
              block: "center",
            });
          }, 300);
        }
      }
    };

    // Add event listeners
    const visualViewport = window.visualViewport;
    visualViewport.addEventListener("resize", handleResize);
    visualViewport.addEventListener("scroll", handleResize);

    // Initial check
    handleResize();

    // Cleanup
    return () => {
      visualViewport.removeEventListener("resize", handleResize);
      visualViewport.removeEventListener("scroll", handleResize);
    };
  }, [scrollAreaRef, keyboardVisible, keyboardHeight]);

  return {
    isDrawerOpen,
    setIsDrawerOpen,
    keyboardVisible,
    keyboardHeight,
  };
}; 