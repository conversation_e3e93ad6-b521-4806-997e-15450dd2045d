import { create } from "zustand";
import {
  CanvasMode,
  MaskToolMode,
  CanvasLine,
  CanvasRect,
  CanvasState,
  GeneratedImage,
  TransformConfig,
  CanvasPosition,
  CanvasSize,
} from "../types/canvas";
import {
  computeAspectRatio,
  isValidAspectRatio,
  isValidResolution,
  prepareGenerationParameters,
  STANDARD_ASPECT_RATIOS
} from "../utils/canvas-utils";

// Type definition for aspect ratio using the constants
export type AspectRatioType = typeof STANDARD_ASPECT_RATIOS[number] | "None";

// Interface for tracking generation tasks
interface GenerationTask {
  id: string;
  predictionId?: string;
  selectionArea: CanvasRect;
  prompt: string;
  status: 'pending' | 'generating' | 'complete' | 'error';
  imageId?: string;
  imageUrl?: string;
  type?: 'generate' | 'magic-fill';
}

interface CanvasStore extends CanvasState {
  // Additional state
  imageHistory: string[];
  currentImageIndex: number;
  viewportOffset: CanvasPosition;
  lineHistory: CanvasLine[][];
  currentLineIndex: number;
  maskUrl: string | null;
  inputImageDimensions: { width: number; height: number } | null;
  
  // API related states
  resolution: string;
  styleType: "None" | "Auto" | "General" | "Realistic" | "Design" | "Render 3D" | "Anime";
  aspectRatio: AspectRatioType;
  magicPromptOption: "Auto" | "On" | "Off";
  seed?: number;
  
  // Generation tasks for better coordination
  generationTasks: GenerationTask[];
  transitioningImages: Set<string>;

  // Methods
  setMode: (mode: CanvasMode) => void;
  setMaskTool: (tool: MaskToolMode) => void;
  setBrushSize: (size: number) => void;
  setPrompt: (prompt: string) => void;
  setNegativePrompt: (prompt: string) => void;
  setUserId: (id: string) => void;
  setZoom: (zoom: number) => void;
  startNewLine: (line: CanvasLine) => void;
  updateActiveLine: (line: CanvasLine) => void;
  clearLines: () => void;
  setDimensions: (dimensions: CanvasSize) => void;
  setSelectionArea: (area: CanvasRect | null) => void;
  setMagicFillAreaSelection: (value: boolean) => void;
  setGeneratedImages: (images: GeneratedImage[]) => void;
  setTransformConfig: (config: TransformConfig) => void;
  setInputImage: (image: HTMLImageElement | null) => void;
  setInputImagePosition: (position: CanvasPosition) => void;
  setInputImageDimensions: (dimensions: { width: number; height: number }) => void;
  setIsGenerating: (value: boolean) => void;
  setIsUploadingImage: (value: boolean) => void;
  
  // API related methods
  setResolution: (resolution: string) => void;
  setStyleType: (styleType: "None" | "Auto" | "General" | "Realistic" | "Design" | "Render 3D" | "Anime") => void;
  setAspectRatio: (ratio: AspectRatioType) => void;
  setMagicPromptOption: (option: "Auto" | "On" | "Off") => void;
  setSeed: (seed?: number) => void;
  invertMask: () => void;
  undo: () => void;
  redo: () => void;
  setImagePosition: (index: number, position: CanvasPosition) => void;
  updateImageTransform: (index: number, updates: Partial<GeneratedImage>) => void;
  addGeneratedImage: (image: GeneratedImage) => void;
  
  // Task management methods
  addGenerationTask: (task: GenerationTask) => void;
  updateGenerationTask: (id: string, updates: Partial<GenerationTask>) => void;
  removeGenerationTask: (id: string) => void;
  
  updateDimensions: (
    width: number,
    height: number,
    updateSelectionArea?: boolean
  ) => void;
}

// Helper function to persist aspect ratio to localStorage
const persistAspectRatio = (ratio: string) => {
  if (ratio === "None") return;
  
  try {
    localStorage.setItem('lastAspectRatio', ratio);
  } catch (e) {
    console.warn('Failed to save aspect ratio to localStorage:', e);
  }
};

// Default aspect ratio when none is specified
const DEFAULT_ASPECT_RATIO: AspectRatioType = "16:9";

const useCanvasStore = create<CanvasStore>((set, get) => ({
  // Base state from CanvasState
  mode: CanvasMode.Move,
  maskTool: "brush",
  brushSize: 20,
  lines: [],
  dimensions: { width: 0, height: 0 },
  selectionArea: null,
  magicFillAreaSelection: false,
  zoom: 1,
  userId: "",
  prompt: "",
  negativePrompt: "",
  generatedImages: [],
  transformConfig: {
    selectedImageIndex: null,
    isDragging: false,
    isResizing: false,
    isRotating: false,
  },
  inputImage: null,
  inputImagePosition: { x: 0, y: 0 },
  inputImageDimensions: null,
  isGenerating: false,
  isUploadingImage: false,

  // Additional state
  imageHistory: [],
  currentImageIndex: -1,
  viewportOffset: { x: 0, y: 0 },
  lineHistory: [],
  currentLineIndex: -1,
  maskUrl: null,
  
  // Generation tasks
  generationTasks: [],
  transitioningImages: new Set<string>(),

  // API related states with defaults
  resolution: "None",
  styleType: "None",
  aspectRatio: DEFAULT_ASPECT_RATIO,
  magicPromptOption: "Auto",
  seed: undefined,

  // Methods
  setMode: (mode) => {
    const currentState = get();
    const currentAspectRatio = currentState.aspectRatio;
    
    // Apply the mode change and preserve aspect ratio
    set({ 
      mode,
      // Always maintain a valid aspect ratio
      aspectRatio: currentAspectRatio === "None" ? DEFAULT_ASPECT_RATIO : currentAspectRatio 
    });
    
    // Persist aspect ratio
    persistAspectRatio(currentAspectRatio === "None" ? DEFAULT_ASPECT_RATIO : currentAspectRatio);
  },
  
  setMaskTool: (tool) => set({ maskTool: tool }),
  setBrushSize: (size) => set({ brushSize: size }),
  setPrompt: (prompt) => set({ prompt }),
  setNegativePrompt: (prompt) => set({ negativePrompt: prompt }),
  setUserId: (id) => set({ userId: id }),
  setZoom: (zoom) => set({ zoom }),
  
  // API related methods
  setResolution: (resolution) => {
    const currentState = get();
    
    // If the resolution is valid, set it and update aspect ratio for consistency
    if (resolution !== "None" && isValidResolution(resolution)) {
      const [width, height] = resolution.split('x').map(Number);
      
      if (width && height) {
        const derivedRatio = computeAspectRatio(width, height);
        
        // If the derived ratio is a valid standard one, update both
        if (isValidAspectRatio(derivedRatio)) {
          set({ resolution, aspectRatio: derivedRatio as AspectRatioType });
          persistAspectRatio(derivedRatio);
          return;
        }
      }
      
      // If we couldn't derive a valid aspect ratio, just set the resolution
      set({ resolution });
      return;
    }
    
    // For invalid or "None" resolutions, ensure we have a valid aspect ratio
    const currentAspectRatio = currentState.aspectRatio;
    set({ 
      resolution,
      aspectRatio: currentAspectRatio === "None" ? DEFAULT_ASPECT_RATIO : currentAspectRatio  
    });
  },
  
  setStyleType: (styleType) => set({ styleType }),
  
  setAspectRatio: (aspectRatio) => {
    set({ aspectRatio });
    persistAspectRatio(aspectRatio);
  },
  
  setMagicPromptOption: (option) => set({ magicPromptOption: option }),
  setSeed: (seed) => set({ seed }),

  startNewLine: (line) => set((state) => {
    const newLines = [...state.lines, line];
    return {
      lines: newLines,
      lineHistory: [...state.lineHistory, newLines],
      currentLineIndex: state.currentLineIndex + 1,
    };
  }),
  
  updateActiveLine: (line) => set((state) => {
    const newLines = [...state.lines.slice(0, -1), line];
    return {
      lines: newLines,
      lineHistory: [...state.lineHistory.slice(0, state.currentLineIndex + 1), newLines],
      currentLineIndex: state.currentLineIndex,
    };
  }),
  
  clearLines: () => set({
    lines: [],
    lineHistory: [],
    currentLineIndex: -1,
    maskUrl: null,
  }),
  
  setDimensions: (dimensions) => set({ dimensions }),
  
  setSelectionArea: (area: CanvasRect | null) => set((state) => {
    if (!area) {
      return { selectionArea: null };
    }
    
    // Use prepareGenerationParameters to derive resolution and aspect ratio
    const { resolution, aspectRatio } = prepareGenerationParameters(area.width, area.height);
    
    // Update related state based on selection area
    const updates: Partial<CanvasStore> = { 
      selectionArea: area
    };
    
    // Only update resolution and aspect ratio if they're significant changes
    // This prevents overriding user-selected values when just moving selection areas
    if (resolution !== state.resolution) {
      updates.resolution = resolution;
    }
    
    // Only update aspect ratio if it's changed and valid
    if (aspectRatio !== "None" && aspectRatio !== state.aspectRatio) {
      updates.aspectRatio = aspectRatio;
      persistAspectRatio(aspectRatio);
    } else if (state.aspectRatio === "None") {
      // Ensure we always have a valid aspect ratio
      updates.aspectRatio = DEFAULT_ASPECT_RATIO;
      persistAspectRatio(DEFAULT_ASPECT_RATIO);
    }
    
    return updates;
  }),
  
  setMagicFillAreaSelection: (value) => set({ magicFillAreaSelection: value }),
  setGeneratedImages: (images) => set({ generatedImages: images }),
  setTransformConfig: (config) => set({ transformConfig: config }),
  setInputImage: (image) => set({ inputImage: image }),
  setInputImagePosition: (position) => set({ inputImagePosition: position }),
  setInputImageDimensions: (dimensions) => set({ inputImageDimensions: dimensions }),
  setIsGenerating: (value) => set({ isGenerating: value }),
  setIsUploadingImage: (value) => set({ isUploadingImage: value }),
  
  invertMask: () => set((state) => ({
    lines: state.lines.map((line) => ({
      ...line,
      tool: line.tool === "eraser" ? "brush" : line.tool === "brush" ? "eraser" : line.tool,
    })),
  })),
  
  undo: () => {
    const state = get();
    if (state.currentLineIndex > 0) {
      set({
        currentLineIndex: state.currentLineIndex - 1,
        lines: state.lineHistory[state.currentLineIndex - 1],
      });
    }
  },
  
  redo: () => {
    const state = get();
    if (state.currentLineIndex < state.lineHistory.length - 1) {
      set({
        currentLineIndex: state.currentLineIndex + 1,
        lines: state.lineHistory[state.currentLineIndex + 1],
      });
    }
  },
  
  setImagePosition: (index: number, position: CanvasPosition) => set((state) => {
    if (index === -1) {
      return { inputImagePosition: position };
    }
    
    const newImages = [...state.generatedImages];
    if (newImages[index]) {
      newImages[index] = { ...newImages[index], position };
    }
    return { generatedImages: newImages };
  }),
  
  updateImageTransform: (index: number, updates: Partial<GeneratedImage>) => set((state) => {
    if (index === -1) return state;
    
    const newImages = [...state.generatedImages];
    if (!newImages[index]) return state;
    
    const currentImage = newImages[index];
    const updatedImage = { ...currentImage, ...updates };
    newImages[index] = updatedImage;
    
    // Check if this is a transition completion (isNew flag cleared)
    if (currentImage.isNew && !updatedImage.isNew && updatedImage.id) {
      // Remove from transitioning set
      const newTransitioning = new Set(state.transitioningImages);
      newTransitioning.delete(updatedImage.id);
      
      // Find and remove completed task
      const matchingTask = state.generationTasks.find(
        t => t.imageId === updatedImage.id || isTaskAtSamePosition(t, updatedImage)
      );
      
      if (matchingTask) {
        return {
          generatedImages: newImages,
          transitioningImages: newTransitioning,
          generationTasks: state.generationTasks.filter(t => t.id !== matchingTask.id)
        };
      }
      
      return {
        generatedImages: newImages,
        transitioningImages: newTransitioning
      };
    }
    
    return { generatedImages: newImages };
  }),
  
  addGeneratedImage: (image: GeneratedImage) => set((state) => {
    if (!image.id) {
      image.id = `gen-${crypto.randomUUID()}`;
    }
    
    // Find matching task by position
    const matchingTask = state.generationTasks.find(task => 
      isTaskAtSamePosition(task, image)
    );
    
    if (matchingTask) {
      // Update the task with the image reference
      const updatedTasks = state.generationTasks.map(task => 
        task.id === matchingTask.id 
          ? { ...task, status: 'complete' as const, imageId: image.id } 
          : task
      );
      
      // Add to transitioning images
      const newTransitioning = new Set(state.transitioningImages);
      newTransitioning.add(image.id);
      
      return {
        generatedImages: [...state.generatedImages, image],
        generationTasks: updatedTasks,
        transitioningImages: newTransitioning
      };
    }
    
    return {
      generatedImages: [...state.generatedImages, image]
    };
  }),
  
  addGenerationTask: (task) => set((state) => ({
    generationTasks: [...state.generationTasks, task]
  })),
  
  updateGenerationTask: (id, updates) => set((state) => {
    // Find the task first
    const existingTask = state.generationTasks.find(task => task.id === id);
    if (!existingTask) return state;
    
    // Handle imageId updates
    if (updates.imageId) {
      const newTransitioning = new Set(state.transitioningImages);
      newTransitioning.add(updates.imageId);
      
      return {
        generationTasks: state.generationTasks.map(task => 
          task.id === id ? { ...task, ...updates } : task
        ),
        transitioningImages: newTransitioning
      };
    }
    
    return {
      generationTasks: state.generationTasks.map(task => 
        task.id === id ? { ...task, ...updates } : task
      )
    };
  }),
  
  removeGenerationTask: (id) => set((state) => ({
    generationTasks: state.generationTasks.filter(task => task.id !== id)
  })),
  
  updateDimensions: (width, height, updateSelectionArea = true) => {
    const currentState = get();
    
    // Use prepareGenerationParameters utility to determine resolution and aspect ratio
    const { resolution, aspectRatio } = prepareGenerationParameters(width, height);
    
    // Always use a valid aspect ratio
    const aspectRatioToUse = aspectRatio === "None" ? 
      (currentState.aspectRatio === "None" ? DEFAULT_ASPECT_RATIO : currentState.aspectRatio) : 
      aspectRatio;
    
    // Update dimensions and related state
    const updates: Partial<CanvasStore> = {
      dimensions: { width, height },
      resolution,
      aspectRatio: aspectRatioToUse
    };
    
    // Update selection area if requested
    if (updateSelectionArea && currentState.selectionArea) {
      updates.selectionArea = {
        ...currentState.selectionArea,
        width, 
        height
      };
    }
    
    set(updates);
    persistAspectRatio(aspectRatioToUse);
  },
}));

// Helper function to determine if a task matches an image position
function isTaskAtSamePosition(task: GenerationTask, image: GeneratedImage): boolean {
  const taskArea = task.selectionArea;
  const imgPos = image.position;
  
  // Allow for small positioning differences (10px tolerance)
  return (
    Math.abs(taskArea.x - imgPos.x) < 10 &&
    Math.abs(taskArea.y - imgPos.y) < 10 &&
    Math.abs(taskArea.width - image.width) < 10 &&
    Math.abs(taskArea.height - image.height) < 10
  );
}

export default useCanvasStore;
