import { create } from "zustand";

interface RatingState {
  ratings: Record<string, number>;
  averageRatings: Record<string, number>;
  totalRatings: Record<string, number>;
  setRating: (imageId: string, rating: number) => void;
  updateAverageRating: (
    imageId: string,
    averageRating: number,
    totalRatings: number
  ) => void;
}

const useRatingStore = create<RatingState>((set) => ({
  ratings: {},
  averageRatings: {},
  totalRatings: {},
  setRating: (imageId: string, rating: number) =>
    set((state) => ({
      ratings: { ...state.ratings, [imageId]: rating },
    })),
  updateAverageRating: (
    imageId: string,
    averageRating: number,
    totalRatings: number
  ) =>
    set((state) => ({
      averageRatings: { ...state.averageRatings, [imageId]: averageRating },
      totalRatings: { ...state.totalRatings, [imageId]: totalRatings },
    })),
}));

export default useRatingStore;
