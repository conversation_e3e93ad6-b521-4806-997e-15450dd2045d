import { create } from "zustand";
import {
  InteriorPlaceholder,
  InteriorGeneratedImage,
  PredictionStatus,
  WebhookPayload,
  UpscalePlaceholder,
} from "@/modules/dashboard/main-features/types";

interface InteriorFormState {
  prompt: string;
  room: string | undefined;
  style: string | undefined;
  excludedElements: string | undefined;
  numImages: number;
  imageUrl: string | undefined;
  isPromptActive: boolean;
  isLoading: boolean;
  imagePlaceholders: InteriorPlaceholder[];
  generatedImages: InteriorGeneratedImage[];
  currentPredictionId: string | null;
  conditionScale: number;
  setConditionScale: (conditionScale: number) => void;
  setPrompt: (prompt: string) => void;
  setRoom: (room: string | undefined) => void;
  setStyle: (style: string | undefined) => void;
  setExcludedElements: (excludedElements: string | undefined) => void;
  setNumImages: (numImages: number) => void;
  setImageUrl: (url: string | undefined) => void;
  setIsLoading: (isLoading: boolean) => void;
  setInitialImages: (
    placeholders: InteriorPlaceholder[],
    images: InteriorGeneratedImage[]
  ) => void;
  addImagePlaceholders: (placeholders: InteriorPlaceholder[]) => void;
  updateImageStatus: (id: string, status: PredictionStatus) => void;
  addGeneratedImage: (
    id: string,
    url: string,
    inputImage: string,
    prompt: string,
    style: string | undefined,
    room: string | undefined,
    excludedElements: string | undefined,
    createdAt: Date
  ) => void;
  setCurrentPredictionId: (id: string | null) => void;
  reset: () => void;
  processChannelMessage: (message: { data: WebhookPayload }) => void;
  upscalePlaceholders: UpscalePlaceholder[];
  addUpscalePlaceholder: (placeholder: UpscalePlaceholder) => void;
  updateUpscalePlaceholderStatus: (
    id: string,
    status: PredictionStatus
  ) => void;
  creativity: number;
  setCreativity: (creativity: number) => void;
  removeImagePlaceholder: (id: string) => void;
  maskUrl: string | undefined;
  setMaskUrl: (url: string | undefined) => void;
  errors: {
    [key: string]: string[];
  };
  setError: (field: string, message: string[]) => void;
  clearErrors: () => void;
  setIsPromptActive: (isActive: boolean) => void;
}

const useInteriorFormStore = create<InteriorFormState>((set) => ({
  prompt: "",
  room: undefined,
  style: undefined,
  excludedElements: undefined,
  numImages: 1,
  imageUrl: undefined,
  isPromptActive: false,
  isLoading: false,
  imagePlaceholders: [],
  generatedImages: [],
  currentPredictionId: null,
  conditionScale: 0.7,
  setConditionScale: (conditionScale) => set({ conditionScale }),
  setPrompt: (prompt) => set({ prompt }),
  setRoom: (room) => set({ room }),
  setStyle: (style) => set({ style }),
  setExcludedElements: (excludedElements) => set({ excludedElements }),
  setNumImages: (numImages) => set({ numImages }),
  setImageUrl: (url) => set({ imageUrl: url }),
  setIsLoading: (isLoading) => set({ isLoading }),

  setInitialImages: (placeholders, images) =>
    set({ imagePlaceholders: placeholders, generatedImages: images }),

  addImagePlaceholders: (placeholders) =>
    set((state) => ({
      imagePlaceholders: [...placeholders, ...state.imagePlaceholders],
    })),

  updateImageStatus: (id, status) =>
    set((state) => ({
      imagePlaceholders: state.imagePlaceholders.map((p) =>
        p.id === id ? { ...p, status } : p
      ),
    })),

  addGeneratedImage: (
    id,
    url,
    inputImage,
    prompt,
    style,
    room,
    excludedElements,
    createdAt
  ) =>
    set((state) => {
      const newGeneratedImage: InteriorGeneratedImage = {
        id,
        url,
        inputImage,
        prompt,
        style,
        room,
        excludedElements,
        createdAt,
      };
      console.log("Adding generated image to store:", newGeneratedImage);
      return {
        generatedImages: [newGeneratedImage, ...state.generatedImages],
        imagePlaceholders: state.imagePlaceholders.filter((p) => p.id !== id),
      };
    }),

  setCurrentPredictionId: (id) => set({ currentPredictionId: id }),

  reset: () =>
    set({
      prompt: "",
      room: undefined,
      style: undefined,
      excludedElements: undefined,
      numImages: 2,
      imageUrl: undefined,
      isPromptActive: false,
      isLoading: false,
      imagePlaceholders: [],
      generatedImages: [],
      currentPredictionId: null,
      conditionScale: 0.7,
    }),

  processChannelMessage: (message: { data: WebhookPayload }) => {
    console.log("Processing channel message in interior store:", message.data);
    const {
      id,
      status,
      url,
      inputImage,
      prompt,
      style,
      room,
      excludedElements,
      createdAt,
    } = message.data;

    set((state) => {
      const existingPlaceholderIndex = state.imagePlaceholders.findIndex(
        (p) => p.id === id
      );

      if (existingPlaceholderIndex !== -1) {
        if (status === PredictionStatus.SUCCEEDED && url) {
          // Move the placeholder to generated images
          const newGeneratedImage: InteriorGeneratedImage = {
            id,
            url,
            inputImage: inputImage || "",
            prompt: prompt || "",
            style,
            room,
            excludedElements,
            createdAt: createdAt ? new Date(createdAt) : new Date(),
          };

          return {
            generatedImages: [newGeneratedImage, ...state.generatedImages],
            imagePlaceholders: state.imagePlaceholders.filter(
              (p) => p.id !== id
            ),
            isLoading: false,
          };
        } else {
          // Update the status of the existing placeholder
          const updatedPlaceholders = [...state.imagePlaceholders];
          updatedPlaceholders[existingPlaceholderIndex] = {
            ...updatedPlaceholders[existingPlaceholderIndex],
            status: status as PredictionStatus,
          };

          return {
            imagePlaceholders: updatedPlaceholders,
            isLoading: status !== PredictionStatus.SUCCEEDED,
          };
        }
      }

      return state;
    });
  },

  upscalePlaceholders: [],
  addUpscalePlaceholder: (placeholder) =>
    set((state) => ({
      upscalePlaceholders: [placeholder, ...state.upscalePlaceholders],
    })),
  updateUpscalePlaceholderStatus: (id, status) =>
    set((state) => ({
      upscalePlaceholders: state.upscalePlaceholders.map((p) =>
        p.id === id ? { ...p, status } : p
      ),
    })),
  creativity: 70,
  setCreativity: (creativity) => set({ creativity }),
  removeImagePlaceholder: (id) =>
    set((state) => ({
      imagePlaceholders: state.imagePlaceholders.filter((p) => p.id !== id),
    })),
  maskUrl: undefined,
  setMaskUrl: (url) => set({ maskUrl: url }),
  errors: {},
  setError: (field, message) =>
    set((state) => ({
      errors: { ...state.errors, [field]: message },
    })),
  clearErrors: () => set({ errors: {} }),
  setIsPromptActive: (isActive) =>
    set((state) => ({
      isPromptActive: isActive,
      // Clear the appropriate fields based on mode
      prompt: isActive ? state.prompt : "",
      room: isActive ? undefined : state.room,
      style: isActive ? undefined : state.style,
    })),
}));

export default useInteriorFormStore;
