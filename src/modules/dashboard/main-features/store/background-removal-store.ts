import { create } from "zustand";
import {
  BackgroundRemovalPlaceholder,
  BackgroundRemovalGeneratedImage,
  PredictionStatus,
  BasePlaceholderWithoutPrompt,
  BaseGeneratedImageWithoutPrompt,
} from "@/modules/dashboard/main-features/types";
import { produce } from "immer";

interface BackgroundRemovalState {
  imageUrl: string | null;
  isLoading: boolean;
  placeholders: BackgroundRemovalPlaceholder[];
  generatedImages: BackgroundRemovalGeneratedImage[];
  errors: {
    [key: string]: string[];
  };
  setImageUrl: (url: string | null) => void;
  setIsLoading: (isLoading: boolean) => void;
  addPlaceholder: (placeholder: BackgroundRemovalPlaceholder) => void;
  addGeneratedImage: (image: BackgroundRemovalGeneratedImage) => void;
  updateImageStatus: (id: string, status: PredictionStatus) => void;
  setInitialImages: (
    placeholders: BackgroundRemovalPlaceholder[],
    generatedImages: BackgroundRemovalGeneratedImage[]
  ) => void;
  reset: () => void;
  processChannelMessage: (message: any) => void;
  setError: (field: string, message: string[]) => void;
  clearErrors: () => void;
}

const useBackgroundRemovalStore = create<BackgroundRemovalState>(
  (set, get) => ({
    imageUrl: null,
    isLoading: false,
    placeholders: [],
    generatedImages: [],
    errors: {},
    setImageUrl: (url) => set({ imageUrl: url }),
    setIsLoading: (isLoading) => set({ isLoading }),
    addPlaceholder: (placeholder) =>
      set((state) => ({
        placeholders: [...state.placeholders, placeholder],
      })),
    addGeneratedImage: (image) =>
      set((state) => {
        const updatedPlaceholders = state.placeholders.filter(
          (p) => p.id !== image.id
        );
        return {
          generatedImages: [image, ...state.generatedImages],
          placeholders: updatedPlaceholders,
        };
      }),
    updateImageStatus: (id, status) =>
      set((state) => ({
        placeholders: state.placeholders.map((p) =>
          p.id === id ? { ...p, status } : p
        ),
      })),
    setInitialImages: (placeholders, generatedImages) =>
      set({ placeholders, generatedImages }),
    reset: () =>
      set({
        imageUrl: null,
        isLoading: false,
        placeholders: [],
        generatedImages: [],
        errors: {},
      }),
    processChannelMessage: (message) => {
      console.log("Processing channel message:", message);
      set(
        produce((state) => {
          const { id, status, inputImage, outputImage, createdAt } =
            message.data;
          console.log(`Updating image ${id} to status ${status}`);

          if (status === PredictionStatus.SUCCEEDED && outputImage) {
            const newImage = {
              id,
              inputImage,
              outputImage,
              status: PredictionStatus.SUCCEEDED,
              url: outputImage,
              createdAt: new Date(createdAt),
            };
            state.generatedImages.unshift(newImage);
            state.placeholders = state.placeholders.filter(
              (p: BackgroundRemovalPlaceholder) => p.id !== id
            );
          } else {
            const placeholder = state.placeholders.find(
              (p: BackgroundRemovalPlaceholder) => p.id === id
            );
            if (placeholder) {
              placeholder.status = status as PredictionStatus;
            }
          }

          if (
            [
              PredictionStatus.SUCCEEDED,
              PredictionStatus.FAILED,
              PredictionStatus.CANCELED,
            ].includes(status as PredictionStatus)
          ) {
            state.isLoading = false;
          }
        })
      );
      console.log("Updated state:", get());
    },
    setError: (field, message) =>
      set((state) => ({
        errors: { ...state.errors, [field]: message },
      })),
    clearErrors: () => set({ errors: {} }),
  })
);

export default useBackgroundRemovalStore;
