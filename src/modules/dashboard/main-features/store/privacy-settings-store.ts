import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";
import { cookies } from "next/headers";

interface PrivacySettingsState {
  state: {
    isPublic: boolean;
  };
  setIsPublic: (isPublic: boolean) => void;
  isLoading: boolean;
  setIsLoading: (isLoading: boolean) => void;
}

type SetState = (
  partial:
    | PrivacySettingsState
    | Partial<PrivacySettingsState>
    | ((
        state: PrivacySettingsState
      ) => PrivacySettingsState | Partial<PrivacySettingsState>),
  replace?: boolean | undefined
) => void;

export const usePrivacySettings = create<PrivacySettingsState>()(
  persist(
    (set: SetState) => ({
      state: {
        isPublic: true, // Default to public
      },
      isLoading: false,
      setIsPublic: (isPublic: boolean) => {
        set((state) => ({
          ...state,
          state: {
            ...state.state,
            isPublic,
          },
        }));

        // Set cookie for server-side access
        document.cookie = `privacy-settings=${JSON.stringify({
          state: { isPublic },
        })}; path=/; max-age=31536000`; // 1 year expiry
      },
      setIsLoading: (isLoading: boolean) => set({ isLoading }),
    }),
    {
      name: "privacy-settings",
      storage: createJSONStorage(() => ({
        getItem: (name) => {
          if (typeof window !== "undefined") {
            return JSON.parse(localStorage.getItem(name) || "null");
          }
          return null;
        },
        setItem: (name, value) => {
          if (typeof window !== "undefined") {
            localStorage.setItem(name, JSON.stringify(value));
          }
        },
        removeItem: (name) => {
          if (typeof window !== "undefined") {
            localStorage.removeItem(name);
          }
        },
      })),
      partialize: (state) => ({ state: { isPublic: state.state.isPublic } }),
    }
  )
);
