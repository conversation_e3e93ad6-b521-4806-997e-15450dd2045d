import { create } from "zustand";
import {
  PredictionStatus,
  EditImagePlaceholder,
  EditImageGeneratedImage,
} from "@/modules/dashboard/main-features/types";
import { z } from "zod";

interface ValidationError {
  [key: string]: string[];
}

interface EditImageFormState {
  imageUrl: string | undefined;
  maskUrl: string | undefined;
  prompt: string;
  negativePrompt: string;
  isLoading: boolean;
  imagePlaceholders: EditImagePlaceholder[];
  generatedImages: EditImageGeneratedImage[];
  errors: ValidationError;
  originalWidth: number | null;
  originalHeight: number | null;
  dimensions: {
    width: number;
    height: number;
  };
  originalDimensions: {
    width: number;
    height: number;
  };
  mode: "remove" | "replace";
  generatedImageUrl: string | undefined;
  lines: Array<{
    tool: "brush" | "eraser";
    points: number[];
    strokeWidth: number;
  }>;
  originalInputUrl: string | undefined;
  imageHistory: string[];
  currentImageIndex: number;

  // Methods
  setImageUrl: (url: string | undefined) => void;
  setMaskUrl: (url: string | undefined) => void;
  setPrompt: (prompt: string) => void;
  setNegativePrompt: (prompt: string) => void;
  setIsLoading: (isLoading: boolean) => void;

  addImagePlaceholder: (placeholder: {
    id: string;
    inputImage: string;
    createdAt: Date;
  }) => void;
  addGeneratedImage: (image: EditImageGeneratedImage) => void;
  removeImagePlaceholder: (id: string) => void;
  reset: () => void;

  setError: (field: string, message: string[]) => void;
  clearErrors: () => void;
  validate: () => boolean;
  setDimensions: (width: number, height: number) => void;
  setOriginalDimensions: (width: number, height: number) => void;
  setMode: (mode: "remove" | "replace") => void;
  setGeneratedImageUrl: (url: string | undefined) => void;
  createMaskCanvas: () => HTMLCanvasElement;
  setLines: (
    lines: Array<{
      tool: "brush" | "eraser";
      points: number[];
      strokeWidth: number;
    }>
  ) => void;
  setOriginalInputUrl: (url: string | undefined) => void;
  addToImageHistory: (url: string) => void;
  goBackToOriginal: () => void;
  undoImageChange: () => void;
  redoImageChange: () => void;
}

const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return (...args: any[]) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

const useEditImageFormStore = create<EditImageFormState>((set, get) => ({
  imageUrl: undefined,
  maskUrl: undefined,
  prompt: "",
  negativePrompt: "",
  isLoading: false,
  imagePlaceholders: [],
  generatedImages: [],
  errors: {},
  originalWidth: null,
  originalHeight: null,
  dimensions: {
    width: 0,
    height: 0,
  },
  originalDimensions: {
    width: 0,
    height: 0,
  },
  mode: "remove",
  generatedImageUrl: undefined,
  lines: [],
  originalInputUrl: undefined,
  imageHistory: [],
  currentImageIndex: -1,

  setImageUrl: (url) => set({ imageUrl: url }),
  setMaskUrl: (url) => set({ maskUrl: url }),
  setPrompt: (prompt) => set({ prompt }),
  setNegativePrompt: (prompt) => set({ negativePrompt: prompt }),
  setIsLoading: (isLoading) => set({ isLoading }),

  addImagePlaceholder: (placeholder) =>
    set((state) => ({
      imagePlaceholders: [
        ...state.imagePlaceholders,
        {
          ...placeholder,
          status: PredictionStatus.PROCESSING,
          url: placeholder.inputImage,
          prompt: "",
        },
      ],
    })),

  addGeneratedImage: (image) =>
    set((state) => ({
      generatedImages: [
        {
          ...image,
          replicateId: image.replicateId || "manual",
        },
        ...state.generatedImages,
      ],
      imagePlaceholders: state.imagePlaceholders.filter(
        (p) => p.id !== image.id
      ),
      isLoading: false,
    })),

  removeImagePlaceholder: (id) =>
    set((state) => ({
      imagePlaceholders: state.imagePlaceholders.filter((p) => p.id !== id),
    })),

  reset: () =>
    set({
      imageUrl: undefined,
      maskUrl: undefined,
      prompt: "",
      negativePrompt: "",
      isLoading: false,
      errors: {},
      originalWidth: null,
      originalHeight: null,
      generatedImageUrl: undefined,
      lines: [],
    }),

  setError: (field, message) =>
    set((state) => ({
      errors: { ...state.errors, [field]: message },
    })),

  clearErrors: () => set({ errors: {} }),

  validate: () => {
    const state = get();
    const schema = z
      .object({
        imageUrl: z.string().url().optional(),
        mode: z.enum(["remove", "replace"]),
        prompt: z.string().optional(),
        negativePrompt: z.string().optional(),
      })
      .refine(
        (data) => {
          if (data.mode === "replace") {
            return !!data.prompt && data.prompt.length > 0;
          }
          return true;
        },
        {
          message: "Prompt is required when mode is 'replace'",
          path: ["prompt"],
        }
      );

    try {
      schema.parse({
        imageUrl: state.imageUrl,
        mode: state.mode,
        prompt: state.prompt,
        negativePrompt: state.negativePrompt,
      });
      set({ errors: {} });
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors: ValidationError = {};
        error.errors.forEach((err) => {
          const field = err.path[0] as string;
          if (!errors[field]) {
            errors[field] = [];
          }
          errors[field].push(err.message);
        });
        set({ errors });
      }
      return false;
    }
  },

  setDimensions: (width, height) => set({ dimensions: { width, height } }),
  setOriginalDimensions: (width, height) =>
    set({ originalDimensions: { width, height } }),
  setMode: (mode) => set({ mode }),
  setGeneratedImageUrl: (url) => set({ generatedImageUrl: url }),
  createMaskCanvas: () => {
    const state = get();
    const tempCanvas = document.createElement("canvas");

    // Use original image dimensions for highest quality
    tempCanvas.width = state.originalDimensions.width;
    tempCanvas.height = state.originalDimensions.height;

    const tempCtx = tempCanvas.getContext("2d", {
      willReadFrequently: true,
      alpha: true,
    });

    if (tempCtx) {
      // Enable high-quality image rendering
      tempCtx.imageSmoothingEnabled = true;
      tempCtx.imageSmoothingQuality = "high";

      // Start with white background (inverted from black)
      tempCtx.fillStyle = "white";
      tempCtx.fillRect(
        0,
        0,
        state.originalDimensions.width,
        state.originalDimensions.height
      );

      // Calculate scaling factors
      const scaleX = state.originalDimensions.width / state.dimensions.width;
      const scaleY = state.originalDimensions.height / state.dimensions.height;

      // Draw black lines for the mask (inverted from white)
      state.lines.forEach((line) => {
        tempCtx.beginPath();
        tempCtx.strokeStyle = "black";
        tempCtx.lineWidth = line.strokeWidth * scaleX;
        tempCtx.lineCap = "round";
        tempCtx.lineJoin = "round";

        for (let i = 0; i < line.points.length; i += 2) {
          const x = line.points[i] * scaleX;
          const y = line.points[i + 1] * scaleY;

          if (i === 0) {
            tempCtx.moveTo(x, y);
          } else {
            tempCtx.lineTo(x, y);
          }
        }

        tempCtx.stroke();
      });
    }

    return tempCanvas;
  },
  setLines: (lines) => {
    const state = get();
    set({ lines });

    // Debounced function to update mask URL
    const updateMaskUrl = debounce(() => {
      const canvas = state.createMaskCanvas();
      const maskUrl = canvas.toDataURL("image/png", 1.0); // Use maximum quality
      set({ maskUrl });
    }, 300); // 300ms debounce

    // Call the debounced function
    if (lines.length > 0) {
      updateMaskUrl();
    } else {
      set({ maskUrl: undefined });
    }
  },
  setOriginalInputUrl: (url) => set({ originalInputUrl: url }),
  addToImageHistory: (url) =>
    set((state) => {
      const newHistory = [
        ...state.imageHistory.slice(0, state.currentImageIndex + 1),
        url,
      ];
      return {
        imageHistory: newHistory,
        currentImageIndex: newHistory.length - 1,
        imageUrl: url,
      };
    }),
  goBackToOriginal: () =>
    set((state) => ({
      imageUrl: state.originalInputUrl,
      currentImageIndex: -1,
      lines: [],
      generatedImageUrl: undefined,
    })),
  undoImageChange: () =>
    set((state) => {
      if (state.currentImageIndex <= 0) return state;
      const newIndex = state.currentImageIndex - 1;
      return {
        currentImageIndex: newIndex,
        imageUrl: state.imageHistory[newIndex],
        lines: [],
        generatedImageUrl: undefined,
      };
    }),
  redoImageChange: () =>
    set((state) => {
      if (state.currentImageIndex >= state.imageHistory.length - 1)
        return state;
      const newIndex = state.currentImageIndex + 1;
      return {
        currentImageIndex: newIndex,
        imageUrl: state.imageHistory[newIndex],
        lines: [],
        generatedImageUrl: undefined,
      };
    }),
}));

export default useEditImageFormStore;
