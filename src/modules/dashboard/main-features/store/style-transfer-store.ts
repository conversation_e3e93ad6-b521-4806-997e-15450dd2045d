import { create } from "zustand";
import {
  StyleTransferPlaceholder,
  StyleTransferGeneratedImage,
  PredictionStatus,
  WebhookPayload,
} from "@/modules/dashboard/main-features/types";

interface StyleTransferFormState {
  style_image: string | undefined;
  structure_image: string | undefined;
  model: "fast" | "high-quality" | "realistic" | "cinematic" | "animated";
  prompt: string;
  negative_prompt: string;
  width: number;
  height: number;
  number_of_images: number;
  structure_depth_strength: number;
  structure_denoising_strength: number;
  output_format: "webp" | "jpg" | "png";
  output_quality: number;
  seed: number | undefined;
  isLoading: boolean;
  imagePlaceholders: StyleTransferPlaceholder[];
  generatedImages: StyleTransferGeneratedImage[];
  currentPredictionId: string | null;
  errors: {
    [key: string]: string[];
  };

  // Setters
  setStyleImage: (url: string | undefined) => void;
  setStructureImage: (url: string | undefined) => void;
  setModel: (
    model: "fast" | "high-quality" | "realistic" | "cinematic" | "animated"
  ) => void;
  setPrompt: (prompt: string) => void;
  setNegativePrompt: (prompt: string) => void;
  setWidth: (width: number) => void;
  setHeight: (height: number) => void;
  setNumberOfImages: (num: number) => void;
  setStructureDepthStrength: (strength: number) => void;
  setStructureDenosingStrength: (strength: number) => void;
  setOutputFormat: (format: "webp" | "jpg" | "png") => void;
  setOutputQuality: (quality: number) => void;
  setSeed: (seed: number | undefined) => void;
  setIsLoading: (isLoading: boolean) => void;
  setError: (field: string, message: string[]) => void;
  clearErrors: () => void;

  // Image management
  setInitialImages: (
    placeholders: StyleTransferPlaceholder[],
    images: StyleTransferGeneratedImage[]
  ) => void;
  addImagePlaceholders: (placeholders: StyleTransferPlaceholder[]) => void;
  updateImageStatus: (id: string, status: PredictionStatus) => void;
  addGeneratedImage: (
    id: string,
    url: string,
    inputImage: string,
    styleImage: string,
    model: string | undefined,
    structureImage: string | undefined,
    createdAt: Date
  ) => void;
  removeImagePlaceholder: (id: string) => void;
  setCurrentPredictionId: (id: string | null) => void;
  reset: () => void;
  processChannelMessage: (message: { data: WebhookPayload }) => void;
}

const useStyleTransferStore = create<StyleTransferFormState>((set) => ({
  // Initial state
  style_image: undefined,
  structure_image: undefined,
  model: "fast",
  prompt: "",
  negative_prompt: "",
  width: 1024,
  height: 1024,
  number_of_images: 1,
  structure_depth_strength: 1,
  structure_denoising_strength: 0.65,
  output_format: "webp" as const,
  output_quality: 80,
  seed: undefined,
  isLoading: false,
  imagePlaceholders: [],
  generatedImages: [],
  currentPredictionId: null,
  errors: {},

  // Setters
  setStyleImage: (url) => set({ style_image: url }),
  setStructureImage: (url) => set({ structure_image: url }),
  setModel: (model) => set({ model }),
  setPrompt: (prompt) => set({ prompt }),
  setNegativePrompt: (prompt) => set({ negative_prompt: prompt }),
  setWidth: (width) => set({ width: Math.max(128, Math.min(2048, width)) }),
  setHeight: (height) => set({ height: Math.max(128, Math.min(2048, height)) }),
  setNumberOfImages: (num) =>
    set({ number_of_images: Math.max(1, Math.min(10, num)) }),
  setStructureDepthStrength: (strength) =>
    set({ structure_depth_strength: Math.max(0, Math.min(2, strength)) }),
  setStructureDenosingStrength: (strength) =>
    set({ structure_denoising_strength: Math.max(0, Math.min(1, strength)) }),
  setOutputFormat: (format) => set({ output_format: format }),
  setOutputQuality: (quality) =>
    set({ output_quality: Math.max(0, Math.min(100, quality)) }),
  setSeed: (seed) => set({ seed }),
  setIsLoading: (isLoading) => set({ isLoading }),
  setError: (field, message) =>
    set((state) => ({
      errors: { ...state.errors, [field]: message },
    })),
  clearErrors: () => set({ errors: {} }),

  // Image management
  setInitialImages: (placeholders, images) =>
    set({ imagePlaceholders: placeholders, generatedImages: images }),

  addImagePlaceholders: (placeholders) =>
    set((state) => ({
      imagePlaceholders: [...placeholders, ...state.imagePlaceholders],
    })),

  updateImageStatus: (id, status) =>
    set((state) => ({
      imagePlaceholders: state.imagePlaceholders.map((p) =>
        p.id === id ? { ...p, status } : p
      ),
    })),

  addGeneratedImage: (
    id,
    url,
    inputImage,
    styleImage,
    model,
    structureImage,
    createdAt
  ) =>
    set((state) => {
      const newGeneratedImage: StyleTransferGeneratedImage = {
        id,
        url,
        inputImage,
        styleImage,
        model,
        structureImage,
        prompt: state.prompt,
        createdAt,
      };
      return {
        generatedImages: [newGeneratedImage, ...state.generatedImages],
        imagePlaceholders: state.imagePlaceholders.filter((p) => p.id !== id),
      };
    }),

  removeImagePlaceholder: (id) =>
    set((state) => ({
      imagePlaceholders: state.imagePlaceholders.filter((p) => p.id !== id),
    })),

  setCurrentPredictionId: (id) => set({ currentPredictionId: id }),

  reset: () =>
    set({
      style_image: undefined,
      structure_image: undefined,
      model: "fast",
      prompt: "",
      negative_prompt: "",
      width: 1024,
      height: 1024,
      number_of_images: 1,
      structure_depth_strength: 1,
      structure_denoising_strength: 0.65,
      output_format: "webp",
      output_quality: 80,
      seed: undefined,
      isLoading: false,
      imagePlaceholders: [],
      generatedImages: [],
      currentPredictionId: null,
      errors: {},
    }),

  processChannelMessage: (message: { data: WebhookPayload }) => {
    const {
      id,
      status,
      url,
      inputImage,
      styleImage,
      model,
      structureImage,
      createdAt,
    } = message.data;

    set((state) => {
      const existingPlaceholderIndex = state.imagePlaceholders.findIndex(
        (p) => p.id === id
      );

      if (existingPlaceholderIndex !== -1) {
        if (status === PredictionStatus.SUCCEEDED && url) {
          const newGeneratedImage: StyleTransferGeneratedImage = {
            id,
            url,
            inputImage: inputImage || "",
            styleImage: styleImage || "",
            model,
            structureImage,
            prompt: state.prompt,
            createdAt: createdAt ? new Date(createdAt) : new Date(),
          };

          return {
            generatedImages: [newGeneratedImage, ...state.generatedImages],
            imagePlaceholders: state.imagePlaceholders.filter(
              (p) => p.id !== id
            ),
            isLoading: false,
          };
        } else {
          const updatedPlaceholders = [...state.imagePlaceholders];
          updatedPlaceholders[existingPlaceholderIndex] = {
            ...updatedPlaceholders[existingPlaceholderIndex],
            status: status as PredictionStatus,
          };

          return {
            imagePlaceholders: updatedPlaceholders,
            isLoading: status !== PredictionStatus.SUCCEEDED,
          };
        }
      }

      return state;
    });
  },
}));

export default useStyleTransferStore;
