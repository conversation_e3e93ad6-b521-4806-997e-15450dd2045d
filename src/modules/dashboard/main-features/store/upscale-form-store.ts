import { create } from "zustand";
import {
  UpscalePlaceholder,
  UpscaleGeneratedImage,
  PredictionStatus,
  WebhookPayload,
} from "@/modules/dashboard/main-features/types";
import { v4 as uuidv4 } from "uuid";

interface UpscaleFormState {
  imageUrl: string | undefined;
  upscaleAmount: number;
  creativity: number;
  imageStrength: number;
  isLoading: boolean;
  imagePlaceholders: UpscalePlaceholder[];
  generatedImages: UpscaleGeneratedImage[];
  currentPredictionId: string | null;
  errors: {
    [key: string]: string[];
  };

  setImageUrl: (url: string | undefined) => void;
  setUpscaleAmount: (amount: number) => void;
  setCreativity: (value: number) => void;
  setImageStrength: (value: number) => void;
  setIsLoading: (isLoading: boolean) => void;
  addImagePlaceholders: (placeholders: UpscalePlaceholder[]) => void;
  updateImageStatus: (id: string, status: PredictionStatus) => void;
  addGeneratedImage: (image: UpscaleGeneratedImage) => void;
  setCurrentPredictionId: (id: string | null) => void;
  reset: () => void;
  processChannelMessage: (message: { data: any }) => void;
  addUpscalePlaceholder: (originalImageUrl: string) => void;
  setError: (field: string, message: string[]) => void;
  clearErrors: () => void;
}

const useUpscaleFormStore = create<UpscaleFormState>((set, get) => ({
  imageUrl: undefined,
  upscaleAmount: 2,
  creativity: 50,
  imageStrength: 0.35,
  isLoading: false,
  imagePlaceholders: [],
  generatedImages: [],
  currentPredictionId: null,
  errors: {},

  setImageUrl: (url) => set({ imageUrl: url }),
  setUpscaleAmount: (amount) => set({ upscaleAmount: amount }),
  setCreativity: (value) => set({ creativity: value }),
  setImageStrength: (value) => set({ imageStrength: value }),
  setIsLoading: (isLoading) => set({ isLoading }),

  addImagePlaceholders: (placeholders) =>
    set((state) => ({
      imagePlaceholders: [...placeholders, ...state.imagePlaceholders],
    })),

  updateImageStatus: (id, status) =>
    set((state) => ({
      imagePlaceholders: state.imagePlaceholders.map((p) =>
        p.id === id ? { ...p, status } : p
      ),
    })),

  addGeneratedImage: (image) =>
    set((state) => {
      console.log("Adding generated image to store:", image);
      return {
        generatedImages: [image, ...state.generatedImages],
        imagePlaceholders: state.imagePlaceholders.filter(
          (p) => p.id !== image.id
        ),
      };
    }),

  setCurrentPredictionId: (id) => set({ currentPredictionId: id }),

  reset: () =>
    set({
      imageUrl: undefined,
      upscaleAmount: 2,
      creativity: 50,
      imageStrength: 0.35,
      isLoading: false,
      imagePlaceholders: [],
      generatedImages: [],
      currentPredictionId: null,
      errors: {},
    }),

  processChannelMessage: (message: { data: any }) => {
    const { id, status, url, createdAt, inputImage, updatedCredits } =
      message.data;

    set((state) => {
      let updatedPlaceholders = state.imagePlaceholders;
      let updatedGeneratedImages = state.generatedImages;

      if (status === PredictionStatus.SUCCEEDED && url) {
        // Remove the placeholder
        updatedPlaceholders = state.imagePlaceholders.filter(
          (p) => p.inputImage !== inputImage
        );

        // Add the completed image
        const completedImage: UpscaleGeneratedImage = {
          id,
          inputImage: inputImage || "",
          outputImage: url,
          status: PredictionStatus.SUCCEEDED,
          createdAt: new Date(createdAt),
          url,
          updatedCredits,
        };
        updatedGeneratedImages = [completedImage, ...state.generatedImages];
      } else {
        // Update the status of the placeholder
        updatedPlaceholders = state.imagePlaceholders.map((p) =>
          p.inputImage === inputImage ? { ...p, status } : p
        );
      }

      return {
        imagePlaceholders: updatedPlaceholders,
        generatedImages: updatedGeneratedImages,
        isLoading: false,
      };
    });
  },

  addUpscalePlaceholder: (originalImageUrl: string) =>
    set((state) => {
      const placeholder: UpscalePlaceholder = {
        id: uuidv4(),
        status: PredictionStatus.PROCESSING,
        createdAt: new Date(),
        inputImage: originalImageUrl,
      };
      return {
        imagePlaceholders: [placeholder, ...state.imagePlaceholders],
      };
    }),

  setError: (field, message) =>
    set((state) => ({
      errors: { ...state.errors, [field]: message },
    })),

  clearErrors: () => set({ errors: {} }),
}));

export default useUpscaleFormStore;
