import { createStore } from "zustand/vanilla";
import { useStore } from "zustand";
import {
  PredictionStatus,
  VirtualStagingPlaceholder,
  VirtualStagingGeneratedImage,
  WebhookPayload,
} from "@/modules/dashboard/main-features/types";
import {
  virtualStagingFormSchema,
  VirtualStagingFormData,
} from "@/modules/dashboard/main-features/validations/virtual-staging-schema";
import { z } from "zod";

interface ValidationError {
  [key: string]: string[];
}

interface VirtualStagingFormState {
  imageUrl: string | undefined;
  maskUrl: string | undefined;
  maskLines: any[];
  prompt: string;
  room: string | undefined;
  style: string | undefined;
  excludedElements: string | undefined;
  resolution: string;
  styleType: string;
  aspectRatio: string;
  negativePrompt: string;
  magicPromptOption: string;
  isLoading: boolean;
  imagePlaceholders: VirtualStagingPlaceholder[];
  generatedImages: VirtualStagingGeneratedImage[];
  currentPredictionId: string | null;
  currentStep: number;
  isAdvancedMode: boolean;
  isExampleMask: boolean;
  errors: ValidationError;
  isEditingMask: boolean;
  isMaskSaved: boolean;
  isPublic: boolean;

  validate: () => boolean;
  setError: (field: string, message: string[]) => void;
  clearErrors: () => void;

  setImageUrl: (url: string | undefined) => void;
  setMaskUrl: (url: string | undefined) => void;
  setMaskLines: (lines: any[]) => void;
  setPrompt: (prompt: string) => void;
  setRoom: (room: string | undefined) => void;
  setStyle: (style: string | undefined) => void;
  setExcludedElements: (excludedElements: string | undefined) => void;
  setResolution: (resolution: string) => void;
  setStyleType: (styleType: string) => void;
  setAspectRatio: (aspectRatio: string) => void;
  setNegativePrompt: (negativePrompt: string) => void;
  setMagicPromptOption: (magicPromptOption: string) => void;
  setIsLoading: (isLoading: boolean) => void;
  setIsExampleMask: (isExample: boolean) => void;
  addImagePlaceholders: (placeholders: VirtualStagingPlaceholder[]) => void;
  updateImageStatus: (id: string, status: PredictionStatus) => void;
  updateImagePlaceholder: (id: string, data: Partial<VirtualStagingPlaceholder>) => void;
  addGeneratedImage: (image: VirtualStagingGeneratedImage) => void;
  removeImagePlaceholder: (id: string) => void;
  setCurrentPredictionId: (id: string | null) => void;
  reset: () => void;
  processChannelMessage: (message: { data: WebhookPayload }) => void;
  setCurrentStep: (step: number) => void;
  setIsAdvancedMode: (isAdvanced: boolean) => void;
  nextStep: () => void;
  prevStep: () => void;
  isStepComplete: (step: number) => boolean;
  validateMask: () => boolean;
  setIsEditingMask: (isEditing: boolean) => void;
  setIsMaskSaved: (isSaved: boolean) => void;
  setIsPublic: (value: boolean) => void;
}

const store = createStore<VirtualStagingFormState>((set, get) => ({
  imageUrl: undefined,
  maskUrl: undefined,
  maskLines: [],
  prompt: "",
  room: undefined,
  style: undefined,
  excludedElements: undefined,
  resolution: "None",
  styleType: "None",
  aspectRatio: "1:1",
  negativePrompt: "",
  magicPromptOption: "Auto",
  isLoading: false,
  imagePlaceholders: [],
  generatedImages: [],
  currentPredictionId: null,
  currentStep: 1,
  isAdvancedMode: false,
  isExampleMask: false,
  errors: {},
  isEditingMask: false,
  isMaskSaved: false,
  isPublic: true,

  setImageUrl: (url) => set({ imageUrl: url }),
  setMaskUrl: (url) =>
    set((state) => ({
      maskUrl: url,
      isEditingMask: url !== state.maskUrl ? false : state.isEditingMask,
      isMaskSaved: url !== state.maskUrl ? false : state.isMaskSaved,
    })),
  setMaskLines: (lines) => set({ maskLines: lines }),
  setPrompt: (prompt) => set({ prompt }),
  setRoom: (room) => set({ room }),
  setStyle: (style) => set({ style }),
  setExcludedElements: (excludedElements) =>
    set({
      excludedElements: excludedElements || undefined,
    }),
  setResolution: (resolution) => set({ resolution }),
  setStyleType: (styleType) => set({ styleType }),
  setAspectRatio: (aspectRatio) => set({ aspectRatio }),
  setNegativePrompt: (negativePrompt) => set({ negativePrompt }),
  setMagicPromptOption: (magicPromptOption) => set({ magicPromptOption }),
  setIsLoading: (isLoading) => set({ isLoading }),
  setIsExampleMask: (isExample) => set({ isExampleMask: isExample }),

  addImagePlaceholders: (placeholders) =>
    set((state) => ({
      imagePlaceholders: [
        ...placeholders.map((p) => ({
          ...p,
          url: p.inputImage,
        })),
        ...state.imagePlaceholders,
      ],
    })),

  updateImageStatus: (id, status) =>
    set((state) => ({
      imagePlaceholders: state.imagePlaceholders.map((p) =>
        p.id === id ? { ...p, status } : p
      ),
    })),

  updateImagePlaceholder: (id, data) =>
    set((state) => ({
      imagePlaceholders: state.imagePlaceholders.map((p) =>
        p.id === id ? { ...p, ...data } : p
      ),
    })),

  addGeneratedImage: (image) =>
    set((state) => ({
      generatedImages: [image, ...state.generatedImages],
      imagePlaceholders: state.imagePlaceholders.filter(
        (p) => p.id !== image.id
      ),
    })),

  removeImagePlaceholder: (id) =>
    set((state) => ({
      imagePlaceholders: state.imagePlaceholders.filter((p) => p.id !== id),
    })),

  setCurrentPredictionId: (id) => set({ currentPredictionId: id }),

  setCurrentStep: (step) => set({ currentStep: step }),
  setIsAdvancedMode: (isAdvanced) => set({ isAdvancedMode: isAdvanced }),
  nextStep: () =>
    set((state) => ({ currentStep: Math.min(state.currentStep + 1, 4) })),
  prevStep: () =>
    set((state) => ({ currentStep: Math.max(state.currentStep - 1, 1) })),
  isStepComplete: (step) => {
    const state = get();
    switch (step) {
      case 1:
        return !!state.imageUrl;
      case 2:
        return !!state.maskUrl;
      case 3:
        return state.isAdvancedMode
          ? !!state.prompt
          : !!(state.room && state.style);
      case 4:
        return true;
      default:
        return false;
    }
  },

  reset: () =>
    set({
      imageUrl: undefined,
      maskUrl: undefined,
      maskLines: [],
      prompt: "",
      room: undefined,
      style: undefined,
      excludedElements: undefined,
      resolution: "None",
      styleType: "Realistic",
      aspectRatio: "1:1",
      negativePrompt: "",
      magicPromptOption: "Auto",
      isLoading: false,
      imagePlaceholders: [],
      generatedImages: [],
      currentPredictionId: null,
      isExampleMask: false,
      isEditingMask: false,
      isMaskSaved: false,
    }),

  processChannelMessage: (message: { data: WebhookPayload }) => {
    const {
      id,
      status,
      output,
      inputImage,
      prompt,
      createdAt,
      style,
      room,
      excludedElements,
    } = message.data;
    console.log(
      "Processing channel message:",
      JSON.stringify(message.data, null, 2)
    );

    set((state) => {
      const existingPlaceholderIndex = state.imagePlaceholders.findIndex(
        (p) => p.id === id
      );

      if (existingPlaceholderIndex !== -1) {
        if (status === PredictionStatus.SUCCEEDED && output) {
          const outputImage = Array.isArray(output) ? output[0] : output;
          const updatedImage: VirtualStagingGeneratedImage = {
            ...state.imagePlaceholders[existingPlaceholderIndex],
            outputImage,
            status: PredictionStatus.SUCCEEDED,
            prompt:
              prompt ||
              state.imagePlaceholders[existingPlaceholderIndex].prompt,
            style: style || null,
            room: room || null,
            excludedElements: excludedElements || null,
            createdAt: createdAt ? new Date(createdAt) : new Date(),
            url: outputImage,
          };
          console.log(
            "Updating to generated image:",
            JSON.stringify(updatedImage, null, 2)
          );
          return {
            generatedImages: [updatedImage, ...state.generatedImages],
            imagePlaceholders: state.imagePlaceholders.filter(
              (p) => p.id !== id
            ),
            isLoading: false,
          };
        } else {
          const updatedPlaceholders = [...state.imagePlaceholders];
          updatedPlaceholders[existingPlaceholderIndex] = {
            ...updatedPlaceholders[existingPlaceholderIndex],
            status: status as PredictionStatus,
          };
          console.log(
            "Updating placeholder status:",
            updatedPlaceholders[existingPlaceholderIndex]
          );
          return {
            imagePlaceholders: updatedPlaceholders,
            isLoading: status !== PredictionStatus.SUCCEEDED,
          };
        }
      }
      console.log("No matching placeholder found for id:", id);
      return state;
    });
  },

  validate: () => {
    const state = get();
    const formData: VirtualStagingFormData = {
      imageUrl: state.imageUrl || "",
      maskUrl: state.maskUrl || "",
      prompt: state.prompt,
      room: state.room,
      style: state.style,
      excludedElements: state.excludedElements,
      resolution: state.resolution,
      styleType: state.styleType,
      aspectRatio: state.aspectRatio,
      negativePrompt: state.negativePrompt,
      magicPromptOption: state.magicPromptOption,
    };

    try {
      virtualStagingFormSchema.parse(formData);
      if (!state.validateMask()) {
        return false;
      }
      set({ errors: {} });
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors: ValidationError = {};
        error.errors.forEach((err) => {
          const field = err.path[0] as string;
          if (!errors[field]) {
            errors[field] = [];
          }
          errors[field].push(err.message);
        });
        set({ errors });
      }
      return false;
    }
  },

  setError: (field, message) =>
    set((state) => ({
      errors: { ...state.errors, [field]: message },
    })),

  clearErrors: () => set({ errors: {} }),

  validateMask: () => {
    const state = get();
    if (!state.imageUrl) {
      set((state) => ({
        errors: {
          ...state.errors,
          imageUrl: ["Please upload an image first"],
        },
      }));
      return false;
    }

    if (!state.maskUrl) {
      set((state) => ({
        errors: {
          ...state.errors,
          maskUrl: ["Please create or upload a mask"],
        },
      }));
      return false;
    }

    return true;
  },

  setIsEditingMask: (isEditing) => set({ isEditingMask: isEditing }),
  setIsMaskSaved: (isSaved) => set({ isMaskSaved: isSaved }),
  setIsPublic: (value) => set({ isPublic: value }),
}));

const useVirtualStagingFormStore = () => useStore(store);

export default useVirtualStagingFormStore;
