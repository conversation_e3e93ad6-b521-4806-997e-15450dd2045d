import { create } from "zustand";
import {
  ExteriorPlaceholder,
  ExteriorGeneratedImage,
  PredictionStatus,
  WebhookPayload,
} from "@/modules/dashboard/main-features/types";

interface ExteriorFormState {
  prompt: string;
  building: string | undefined;
  style: string | undefined;
  excludedElements: string | undefined;
  numImages: number;
  imageUrl: string | undefined;
  isPromptActive: boolean;
  isLoading: boolean;
  imagePlaceholders: ExteriorPlaceholder[];
  generatedImages: ExteriorGeneratedImage[];
  currentPredictionId: string | null;
  conditionScale: number;
  creativity: number;
  errors: {
    [key: string]: string[];
  };
  setPrompt: (prompt: string) => void;
  setBuilding: (building: string | undefined) => void;
  setStyle: (style: string | undefined) => void;
  setExcludedElements: (excludedElements: string | undefined) => void;
  setNumImages: (numImages: number) => void;
  setImageUrl: (url: string | undefined) => void;
  setIsLoading: (isLoading: boolean) => void;
  setInitialImages: (
    placeholders: ExteriorPlaceholder[],
    images: ExteriorGeneratedImage[]
  ) => void;
  addImagePlaceholders: (placeholders: ExteriorPlaceholder[]) => void;
  updateImageStatus: (id: string, status: PredictionStatus) => void;
  addGeneratedImage: (
    id: string,
    url: string,
    inputImage: string,
    prompt: string,
    style: string | undefined,
    building: string | undefined,
    excludedElements: string | undefined,
    createdAt: Date
  ) => void;
  setCurrentPredictionId: (id: string | null) => void;
  setConditionScale: (conditionScale: number) => void;
  setCreativity: (creativity: number) => void;
  reset: () => void;
  processChannelMessage: (message: any) => void;
  removeImagePlaceholder: (id: string) => void;
  setError: (field: string, message: string[]) => void;
  clearErrors: () => void;
}

const useExteriorFormStore = create<ExteriorFormState>((set) => ({
  prompt: "",
  building: undefined,
  style: undefined,
  excludedElements: undefined,
  numImages: 2,
  imageUrl: undefined,
  isPromptActive: false,
  isLoading: false,
  imagePlaceholders: [],
  generatedImages: [],
  currentPredictionId: null,
  conditionScale: 0.7,
  creativity: 70,
  errors: {},
  setPrompt: (prompt) =>
    set((state) => ({
      prompt,
      isPromptActive: prompt.trim().length > 0,
      building: prompt.trim().length > 0 ? undefined : state.building,
      style: prompt.trim().length > 0 ? undefined : state.style,
    })),
  setBuilding: (building) =>
    set((state) => ({
      building,
      prompt: state.isPromptActive ? "" : state.prompt,
      isPromptActive: false,
    })),
  setStyle: (style) =>
    set((state) => ({
      style,
      prompt: state.isPromptActive ? "" : state.prompt,
      isPromptActive: false,
    })),
  setExcludedElements: (excludedElements) => set({ excludedElements }),
  setNumImages: (numImages) => set({ numImages }),
  setImageUrl: (url) => set({ imageUrl: url }),
  setIsLoading: (isLoading) => set({ isLoading }),

  setInitialImages: (placeholders, images) =>
    set({ imagePlaceholders: placeholders, generatedImages: images }),

  addImagePlaceholders: (placeholders) =>
    set((state) => ({
      imagePlaceholders: [...placeholders, ...state.imagePlaceholders],
    })),

  updateImageStatus: (id, status) =>
    set((state) => ({
      imagePlaceholders: state.imagePlaceholders.map((p) =>
        p.id === id ? { ...p, status } : p
      ),
    })),

  addGeneratedImage: (
    id,
    url,
    inputImage,
    prompt,
    style,
    building,
    excludedElements,
    createdAt
  ) =>
    set((state) => {
      const newGeneratedImage: ExteriorGeneratedImage = {
        id,
        url,
        inputImage,
        prompt,
        style,
        building,
        excludedElements,
        createdAt,
      };
      console.log("Adding generated image to store:", newGeneratedImage);
      return {
        generatedImages: [newGeneratedImage, ...state.generatedImages],
        imagePlaceholders: state.imagePlaceholders.filter((p) => p.id !== id),
      };
    }),

  removeImagePlaceholder: (id) =>
    set((state) => ({
      imagePlaceholders: state.imagePlaceholders.filter((p) => p.id !== id),
    })),

  setCurrentPredictionId: (id) => set({ currentPredictionId: id }),

  setConditionScale: (conditionScale) => set({ conditionScale }),

  setCreativity: (creativity) => set({ creativity }),

  reset: () =>
    set({
      prompt: "",
      building: undefined,
      style: undefined,
      excludedElements: undefined,
      numImages: 2,
      imageUrl: undefined,
      isPromptActive: false,
      isLoading: false,
      imagePlaceholders: [],
      generatedImages: [],
      currentPredictionId: null,
      conditionScale: 0.7,
      creativity: 70,
      errors: {},
    }),

  processChannelMessage: (message) => {
    const {
      id,
      status,
      url,
      inputImage,
      prompt,
      style,
      building,
      excludedElements,
      createdAt,
    } = message.data;

    set((state) => {
      const existingPlaceholderIndex = state.imagePlaceholders.findIndex(
        (p) => p.id === id
      );

      if (existingPlaceholderIndex !== -1) {
        if (status === PredictionStatus.SUCCEEDED && url) {
          const newGeneratedImage: ExteriorGeneratedImage = {
            id,
            url,
            inputImage: inputImage || "",
            prompt: prompt || "",
            style,
            building,
            excludedElements,
            createdAt: createdAt ? new Date(createdAt) : new Date(),
          };

          return {
            generatedImages: [newGeneratedImage, ...state.generatedImages],
            imagePlaceholders: state.imagePlaceholders.filter(
              (p) => p.id !== id
            ),
            isLoading: false,
          };
        } else {
          const updatedPlaceholders = [...state.imagePlaceholders];
          updatedPlaceholders[existingPlaceholderIndex] = {
            ...updatedPlaceholders[existingPlaceholderIndex],
            status: status as PredictionStatus,
          };

          return {
            imagePlaceholders: updatedPlaceholders,
            isLoading: false,
          };
        }
      }

      return state;
    });
  },

  setError: (field, message) =>
    set((state) => ({
      errors: { ...state.errors, [field]: message },
    })),

  clearErrors: () => set({ errors: {} }),
}));

export default useExteriorFormStore;
