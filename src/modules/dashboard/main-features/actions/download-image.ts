"use server";

export async function downloadImage(
  imageUrl: string
): Promise<{ url: string }> {
  try {
    // Convert the URL to use PNG format
    const url = new URL(imageUrl);
    url.pathname = url.pathname.replace(/\/[^/]*$/, "/public");
    url.searchParams.set("format", "png");

    return { url: url.toString() };
  } catch (error) {
    console.error("Error processing image URL:", error);
    throw error;
  }
}
