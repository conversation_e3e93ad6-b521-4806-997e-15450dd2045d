"use server";

import { z } from "zod";
import Replicate from "replicate";
import {
  StyleTransferParams,
  StyleTransferResult,
  StyleTransferPlaceholder,
  PredictionStatus,
  StyleTransferParamsSchema,
} from "@/modules/dashboard/main-features/types";
import prisma from "@/lib/prisma/prisma";
import { revalidatePath } from "next/cache";
import { auth } from "@/lib/auth/auth";

const REPLICATE_API_TOKEN = process.env.REPLICATE_API_TOKEN;
const WEBHOOK_HOST = process.env.WEBHOOK_HOST;

export async function generateStyleTransfer(
  params: StyleTransferParams
): Promise<StyleTransferResult> {
  try {
    const validatedParams = StyleTransferParamsSchema.parse(params);

    // Check user session
    const session = await auth();
    if (!session?.user?.id) {
      throw new Error("Unauthorized");
    }

    // Check user credits
    const user = await prisma.user.findUnique({
      where: { id: validatedParams.userId },
      select: { imageCredits: true },
    });

    if (!user || user.imageCredits < 1) {
      return {
        success: false,
        error:
          "You need 1 credit to generate a style transfer. Please upgrade to get more credits.",
        updatedCredits: user?.imageCredits || 0,
        type: "INSUFFICIENT_CREDITS",
      };
    }

    // Decrease image credits
    const updatedUser = await prisma.user.update({
      where: { id: validatedParams.userId },
      data: { imageCredits: { decrement: 1 } },
    });

    // Get user's subscription for public/private permission
    const subscription = await prisma.subscription.findUnique({
      where: { userId: session.user.id },
    });

    const isPublic = subscription?.allowPrivateImages ?? false;

    const replicate = new Replicate({
      auth: REPLICATE_API_TOKEN,
    });

    const input = {
      style_image: validatedParams.style_image,
      model: validatedParams.model || "fast",
      width: validatedParams.width || 1024,
      height: validatedParams.height || 1024,
      prompt: validatedParams.prompt || "",
      negative_prompt: validatedParams.negative_prompt || "",
      structure_image: validatedParams.structure_image,
      number_of_images: validatedParams.number_of_images || 1,
      structure_depth_strength: validatedParams.structure_depth_strength || 1,
      structure_denoising_strength:
        validatedParams.structure_denoising_strength || 0.65,
      output_format: validatedParams.output_format || "webp",
      output_quality: validatedParams.output_quality || 80,
      seed: validatedParams.seed,
    };

    const prediction = await replicate.predictions.create({
      version:
        "f1023890703bc0a5a3a2c21b5e498833be5f6ef6e70e9daf6b9b3a4fd8309cf0",
      input,
      webhook: WEBHOOK_HOST
        ? `${WEBHOOK_HOST}/api/webhook/replicate/style-transfer`
        : undefined,
      webhook_events_filter: ["completed"],
    });

    // Save to database
    const dbPrediction = await prisma.styleTransfer.create({
      data: {
        replicate_id: prediction.id,
        userId: validatedParams.userId,
        status: PredictionStatus.PROCESSING,
        inputImage: validatedParams.style_image,
        styleImage: validatedParams.style_image,
        model: validatedParams.model,
        structureImage: validatedParams.structure_image,
        createdAt: new Date(),
      },
    });

    const placeholder: StyleTransferPlaceholder = {
      id: dbPrediction.id,
      status: PredictionStatus.PROCESSING,
      inputImage: validatedParams.style_image,
      styleImage: validatedParams.style_image,
      model: validatedParams.model,
      structureImage: validatedParams.structure_image,
      prompt: validatedParams.prompt || "",
      createdAt: new Date(prediction.created_at),
      isPublic,
    };

    console.log("Replicate API response:", prediction);
    console.log("Saved prediction to database:", dbPrediction);

    return {
      success: true,
      predictionId: prediction.id,
      replicateId: prediction.id,
      placeholders: [placeholder],
      updatedCredits: updatedUser.imageCredits,
    };
  } catch (error) {
    console.error("Error generating style transfer:", error);

    // Refund credits in case of error
    if (
      error instanceof Error &&
      error.message !== "Insufficient image credits"
    ) {
      await prisma.user.update({
        where: { id: params.userId },
        data: { imageCredits: { increment: 1 } },
      });
    }

    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: "Invalid input parameters",
        updatedCredits: 0,
      };
    }
    if (error instanceof Error) {
      return {
        success: false,
        error: error.message,
        updatedCredits: 0,
      };
    }
    return {
      success: false,
      error: "An unexpected error occurred",
      updatedCredits: 0,
    };
  }
}
