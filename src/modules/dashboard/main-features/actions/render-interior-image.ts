"use server";

import { z } from "zod";
import Replicate from "replicate";
import {
  InteriorGenerateImageParams,
  InteriorUserInputSchema,
  InteriorGenerateImageResult,
  InteriorPlaceholder,
  PredictionStatus,
} from "@/modules/dashboard/main-features/types";
import {
  saveInteriorDesignToPrisma,
  getInteriorPromptFromDB,
  prepareNegativePrompt,
} from "@/modules/dashboard/main-features/actions/utils";
import prisma from "@/lib/prisma/prisma";
import { auth } from "@/lib/auth/auth";

const REPLICATE_API_TOKEN = process.env.REPLICATE_API_TOKEN;
const WEBHOOK_HOST = process.env.WEBHOOK_HOST;

export async function generateInteriorImage(
  params: InteriorGenerateImageParams
): Promise<InteriorGenerateImageResult> {
  try {
    const validatedParams = InteriorUserInputSchema.parse(params);

    // Check user session
    const session = await auth();
    if (!session?.user?.id) {
      throw new Error("Unauthorized");
    }

    // Check user credits before proceeding
    const user = await prisma.user.findUnique({
      where: { id: validatedParams.userId },
      select: { imageCredits: true },
    });

    if (!user || user.imageCredits < 1) {
      return {
        success: false,
        error:
          "You need 1 credit to generate an interior image. Please upgrade to get more credits.",
        updatedCredits: user?.imageCredits || 0,
        type: "INSUFFICIENT_CREDITS",
      };
    }

    // Decrease image credits by 1
    const updatedUser = await prisma.user.update({
      where: { id: validatedParams.userId },
      data: { imageCredits: { decrement: 1 } },
    });

    // Get user's subscription only for checking public/private permission
    const subscription = await prisma.subscription.findUnique({
      where: { userId: session.user.id },
    });

    // Set isPublic based on subscription - default to false if no subscription
    const isPublic = subscription?.allowPrivateImages ?? false;

    const replicate = new Replicate({
      auth: REPLICATE_API_TOKEN,
    });

    let prompt: string;
    if (validatedParams.prompt) {
      prompt = validatedParams.prompt;
    } else if (validatedParams.style && validatedParams.room) {
      const dbPrompt = await getInteriorPromptFromDB(
        validatedParams.style,
        validatedParams.room
      );
      if (!dbPrompt) {
        throw new Error("Invalid style or room combination");
      }
      prompt = dbPrompt;
    } else {
      throw new Error("Invalid input parameters");
    }

    const negativePrompt = await prepareNegativePrompt(
      validatedParams.excludedElements
    );

    const predictions = [];
    const placeholders: InteriorPlaceholder[] = [];

    const input = {
      image: validatedParams.image,
      mask: validatedParams.maskUrl,
      prompt: prompt,
      scale_factor: 1,
      creativity: validatedParams.creativity / 100,
      resemblance: validatedParams.conditionScale,
      seed: validatedParams.seed || Math.floor(Math.random() * 1000000),
      dynamic: 6,
      handfix: "disabled",
      pattern: false,
      sharpen: 0,
      sd_model: "juggernaut_reborn.safetensors [338b85bc4f]",
      scheduler: "DPM++ 3M SDE Karras",
      tiling_width: 112,
      output_format: "png",
      tiling_height: 144,
      negative_prompt: negativePrompt,
      num_inference_steps: 18,
    };

    const prediction = await replicate.predictions.create({
      version:
        "dfad41707589d68ecdccd1dfa600d55a208f9310748e44bfe35b4a6291453d5e",
      input,
      webhook: WEBHOOK_HOST
        ? `${WEBHOOK_HOST}/api/webhook/replicate/interior-design`
        : undefined,
      webhook_events_filter: ["completed"],
    });
    predictions.push(prediction);

    const dbPrediction = await saveInteriorDesignToPrisma(
      validatedParams.userId,
      prediction.id,
      validatedParams.image,
      prompt,
      validatedParams.style,
      validatedParams.room
    );

    placeholders.push({
      id: dbPrediction.id,
      status: PredictionStatus.PROCESSING,
      inputImage: validatedParams.image,
      prompt: prompt,
      style: validatedParams.style,
      room: validatedParams.room,
      excludedElements: validatedParams.excludedElements || undefined,
      createdAt: new Date(prediction.created_at),
      isPublic,
    });

    console.log("Replicate API responses:", predictions);
    console.log("Saved predictions to database:", placeholders);

    return {
      success: true,
      predictionId: predictions[0].id,
      replicateId: predictions[0].id,
      placeholders: placeholders,
      updatedCredits: updatedUser.imageCredits,
    };
  } catch (error) {
    console.error("Error generating interior image:", error);

    // Refund credits in case of local error
    if (
      error instanceof Error &&
      error.message !== "Insufficient image credits"
    ) {
      await prisma.user.update({
        where: { id: params.userId },
        data: { imageCredits: { increment: 1 } },
      });
    }

    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: "Invalid input parameters",
        updatedCredits: 0,
      };
    }
    if (error instanceof Error) {
      return {
        success: false,
        error: error.message,
        updatedCredits: 0,
      };
    }
    return {
      success: false,
      error: "An unexpected error occurred",
      updatedCredits: 0,
    };
  }
}
