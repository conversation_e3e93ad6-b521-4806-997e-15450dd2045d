"use server";

import { auth } from "@/lib/auth/auth";
import prisma from "@/lib/prisma/prisma";

export async function getUserImageCredits() {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      console.log("No session or user ID");
      return 0;
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { imageCredits: true },
    });

    console.log("User credits from DB:", user?.imageCredits);
    return user?.imageCredits ?? 0;
  } catch (error) {
    console.error("Error getting user credits:", error);
    return 0;
  }
}
