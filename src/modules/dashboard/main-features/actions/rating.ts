"use server";

import prisma from "@/lib/prisma/prisma";
import { currentUser } from "@/modules/auth/actions/user-actions";
import {
  ImageType as PrismaImageType,
  PredictionStatus,
  Prisma,
} from "@prisma/client";

type ImageType = PrismaImageType;

interface ImageResult {
  id: string;
  status: PredictionStatus;
  outputImage: string | null;
}

interface RatingStats {
  averageRating: number;
  totalRatings: number;
}

const getImageTypeIdField = (imageType: ImageType) => {
  switch (imageType) {
    case "virtual_stagings":
      return "virtualStagingId";
    case "interior_designs":
      return "interiorDesignId";
    case "exterior_designs":
      return "exteriorDesignId";
    case "edit_images":
      return "editImageId";
    case "upscale_images":
      return "upscaleImageId";
    case "background_removals":
      return "backgroundRemovalId";
    case "style_transfers":
      return "styleTransferId";
    default:
      throw new Error(`Unsupported image type: ${imageType}`);
  }
};

const findImage = async (
  imageType: ImageType,
  imageId: string
): Promise<ImageResult | null> => {
  const args = {
    where: {
      OR: [{ id: imageId }, { replicate_id: imageId }],
    },
    select: {
      id: true,
      status: true,
      outputImage: true,
    },
  };

  try {
    switch (imageType) {
      case "virtual_stagings":
        return prisma.virtualStaging.findFirst(args);
      case "interior_designs":
        return prisma.interiorDesign.findFirst(args);
      case "exterior_designs":
        return prisma.exteriorDesign.findFirst(args);
      case "edit_images":
        return prisma.editImage.findFirst(args);
      case "upscale_images":
        return prisma.upscaleImage.findFirst(args);
      case "background_removals":
        return prisma.backgroundRemoval.findFirst(args);
      case "style_transfers":
        return prisma.styleTransfer.findFirst(args);
      default:
        throw new Error(`Unsupported image type: ${imageType}`);
    }
  } catch (error) {
    console.error(`Error finding image: ${error}`);
    throw error;
  }
};

const updateRating = async (
  imageType: ImageType,
  imageId: string,
  data: { averageRating: number; totalRatings: number }
) => {
  const args = {
    where: { id: imageId },
    data,
  };

  try {
    switch (imageType) {
      case "virtual_stagings":
        return await prisma.virtualStaging.update(args);
      case "interior_designs":
        return await prisma.interiorDesign.update(args);
      case "exterior_designs":
        return await prisma.exteriorDesign.update(args);
      case "edit_images":
        return await prisma.editImage.update(args);
      case "upscale_images":
        return await prisma.upscaleImage.update(args);
      case "background_removals":
        return await prisma.backgroundRemoval.update(args);
      case "style_transfers":
        return await prisma.styleTransfer.update(args);
      default:
        throw new Error(`Unsupported image type: ${imageType}`);
    }
  } catch (error) {
    console.error(`Error updating rating: ${error}`);
    throw error;
  }
};

const calculateRatingStats = async (
  tx: Prisma.TransactionClient,
  imageType: ImageType,
  imageId: string
): Promise<RatingStats> => {
  const result = await tx.imageRating.aggregate({
    where: {
      imageType,
      [getImageTypeIdField(imageType)]: imageId,
    },
    _avg: {
      rating: true,
    },
    _count: true,
  });

  return {
    averageRating: result._avg.rating ?? 0,
    totalRatings: result._count ?? 0,
  };
};

const validateRating = (rating: number) => {
  if (rating < 1 || rating > 5) {
    throw new Error("Rating must be between 1 and 5");
  }
  return rating;
};

export async function rateImage(data: {
  imageId: string;
  imageType: ImageType;
  rating: number;
}) {
  try {
    const user = await currentUser();
    if (!user) throw new Error("User not authenticated");
    if (data.rating < 1 || data.rating > 5)
      throw new Error("Rating must be between 1 and 5");

    const image = await findImage(data.imageType, data.imageId);
    if (!image) throw new Error(`Image not found with ID ${data.imageId}`);
    if (image.status !== PredictionStatus.SUCCEEDED)
      throw new Error("Image generation not completed");

    const result = await prisma.$transaction(async (tx) => {
      // Find existing rating first
      const existingRating = await tx.imageRating.findFirst({
        where: {
          userId: user.id,
          imageType: data.imageType,
          [getImageTypeIdField(data.imageType)]: image.id,
        },
      });

      let rating;
      if (existingRating) {
        // Update existing rating
        rating = await tx.imageRating.update({
          where: { id: existingRating.id },
          data: {
            rating: data.rating,
            updatedAt: new Date(),
          },
        });
      } else {
        // Create new rating with only the relevant relation
        const createData: Prisma.ImageRatingCreateInput = {
          user: { connect: { id: user.id } },
          rating: data.rating,
          imageType: data.imageType,
          [data.imageType === "virtual_stagings"
            ? "virtualStaging"
            : data.imageType === "interior_designs"
            ? "interiorDesign"
            : data.imageType === "exterior_designs"
            ? "exteriorDesign"
            : data.imageType === "edit_images"
            ? "editImage"
            : data.imageType === "upscale_images"
            ? "upscaleImage"
            : data.imageType === "style_transfers"
            ? "styleTransfer"
            : "backgroundRemoval"]: { connect: { id: image.id } },
        };

        rating = await tx.imageRating.create({
          data: createData,
        });
      }

      const idField = getImageTypeIdField(data.imageType);
      const stats = await calculateRatingStats(tx, data.imageType, image.id);
      const updatedImage = await updateRating(data.imageType, image.id, stats);

      return { rating, updatedImage };
    });

    return { success: true, data: result };
  } catch (error) {
    console.error("Failed to submit rating:", error);
    return { success: false, error: String(error) };
  }
}

export async function getUserRating(imageId: string, imageType: ImageType) {
  try {
    const user = await currentUser();
    if (!user) return null;

    const idField = getImageTypeIdField(imageType);
    const where = {
      userId: user.id,
      imageType,
      [idField]: imageId,
    };

    const rating = await prisma.imageRating.findFirst({
      where,
    });

    return rating?.rating ?? null;
  } catch (error) {
    console.error("Failed to get user rating:", error);
    return null;
  }
}
