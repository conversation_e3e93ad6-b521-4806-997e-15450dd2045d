"use server";

import prisma from "@/lib/prisma/prisma";

export async function refundImageCredits(userId: string, amount: number = 1) {
  try {
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        imageCredits: {
          increment: amount,
        },
      },
    });

    console.log(`Refunded ${amount} image credit(s) to user ${userId}`);
    return updatedUser.imageCredits;
  } catch (error) {
    console.error(`Error refunding image credits for user ${userId}:`, error);
    throw new Error("Failed to refund image credits");
  }
}
