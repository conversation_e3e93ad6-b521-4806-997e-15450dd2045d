"use server";
import { PredictionStatus, Prisma } from "@prisma/client";
import prisma from "@/lib/prisma/prisma";
import { auth } from "@/lib/auth/auth";
import { DesignType } from "@/types/designs";

const ITEMS_PER_PAGE = 9;

export async function getCompletedDesigns(
  userId: string,
  type: DesignType,
  page: number,
  showFavorites: boolean = false
) {
  const skip = (page - 1) * ITEMS_PER_PAGE;
  const favoritesWhere = showFavorites
    ? {
        favoritedBy: {
          some: {
            id: userId,
          },
        },
      }
    : {};

  const baseWhere = {
    userId,
    status: PredictionStatus.SUCCEEDED,
    ...favoritesWhere,
  };

  const session = await auth();
  if (!session?.user?.id) {
    throw new Error("User not authenticated");
  }

  if (type === "interior") {
    return getDesignsForModel(
      prisma.interiorDesign,
      { ...baseWhere, outputImages: { isEmpty: false } },
      page,
      skip,
      session.user.id,
      "interior"
    );
  } else if (type === "exterior") {
    return getDesignsForModel(
      prisma.exteriorDesign,
      { ...baseWhere, outputImages: { isEmpty: false } },
      page,
      skip,
      session.user.id,
      "exterior"
    );
  } else if (type === "remove-background") {
    return getDesignsForModel(
      prisma.backgroundRemoval,
      { ...baseWhere, outputImage: { not: null } },
      page,
      skip,
      session.user.id,
      "remove-background"
    );
  } else if (type === "upscale") {
    return getDesignsForModel(
      prisma.upscaleImage,
      { ...baseWhere, outputImage: { not: null } },
      page,
      skip,
      session.user.id,
      "upscale"
    );
  } else if (type === "virtual-staging") {
    return getDesignsForModel(
      prisma.virtualStaging,
      { ...baseWhere, outputImage: { not: null } },
      page,
      skip,
      session.user.id,
      "virtual-staging"
    );
  } else if (type === "style-transfer") {
    return getDesignsForModel(
      prisma.styleTransfer,
      { ...baseWhere, outputImage: { not: null } },
      page,
      skip,
      session.user.id,
      "style-transfer"
    );
  }

  return null;
}

async function getDesignsForModel<T>(
  model: {
    findMany: (args: any) => Promise<T[]>;
    count: (args: any) => Promise<number>;
  },
  where: any,
  page: number,
  skip: number,
  userId: string,
  modelType: DesignType
) {
  const baseSelectFields = {
    id: true,
    inputImage: true,
    createdAt: true,
    status: true,
    favoritedBy: {
      where: { id: userId },
      select: { id: true },
    },
  };

  const modelSpecificFields = {
    interior: {
      outputImages: true,
      prompt: true,
      style: true,
      room: true,
    },
    exterior: {
      outputImages: true,
      prompt: true,
      style: true,
      building: true,
    },
    "remove-background": {
      outputImage: true,
    },
    upscale: {
      outputImage: true,
      upscaleAmount: true,
      creativity: true,
    },
    "virtual-staging": {
      outputImage: true,
      prompt: true,
      style: true,
      room: true,
    },
    "style-transfer": {
      outputImage: true,
      model: true,
      prompt: true,
      replicate_id: true,
      completedAt: true,
    },
  } as const;

  const selectFields = {
    ...baseSelectFields,
    ...modelSpecificFields[modelType],
  };

  const [designs, totalCount] = await Promise.all([
    model.findMany({
      where,
      orderBy: { createdAt: "desc" },
      take: ITEMS_PER_PAGE,
      skip,
      select: selectFields,
    }),
    model.count({ where }),
  ]);

  const designsWithFavoriteStatus = designs.map((design: any) => ({
    ...design,
    isFavorite: design.favoritedBy.length > 0,
    favoritedBy: undefined,
    outputImage:
      design.outputImage ||
      (design.outputImages && design.outputImages[0]) ||
      null,
  }));

  const totalPages = Math.ceil(totalCount / ITEMS_PER_PAGE);
  return {
    designs: designsWithFavoriteStatus,
    totalPages,
    currentPage: page,
    totalCount,
  };
}
