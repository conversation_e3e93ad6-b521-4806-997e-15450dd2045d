"use server";

import { z } from "zod";
import Replicate from "replicate";
import {
  VirtualStagingParams,
  VirtualStagingParamsSchema,
  VirtualStagingResult,
  VirtualStagingPlaceholder,
  PredictionStatus,
} from "@/modules/dashboard/main-features/types";
import { saveVirtualStagingToPrisma } from "@/modules/dashboard/main-features/actions/utils";
import prisma from "@/lib/prisma/prisma";
import { AzureOpenAI } from "openai";

import { streamText } from "ai";
import { createStreamableValue } from "ai/rsc";

const REPLICATE_API_TOKEN = process.env.REPLICATE_API_TOKEN;
const WEBHOOK_HOST = process.env.WEBHOOK_HOST;
// Azure OpenAI configuration
const AZURE_API_KEY = process.env.AZURE_OPENAI_API_KEY;
const AZURE_ENDPOINT = process.env.AZURE_OPENAI_ENDPOINT;
const AZURE_API_VERSION = process.env.AZURE_OPENAI_API_VERSION || '2024-12-01-preview';
const AZURE_DEPLOYMENT = process.env.AZURE_OPENAI_DEPLOYMENT || 'o1';
const AZURE_MODEL = process.env.AZURE_OPENAI_MODEL || 'o1';

// Initialize Azure OpenAI client
const azureClient = new AzureOpenAI({
  apiKey: AZURE_API_KEY || "",
  endpoint: AZURE_ENDPOINT || "",
  apiVersion: AZURE_API_VERSION,
  deployment: AZURE_DEPLOYMENT,
});

// Function to convert image URL to base64
async function imageUrlToBase64(url: string): Promise<string> {
  try {
    const response = await fetch(url);
    const arrayBuffer = await response.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    const base64 = buffer.toString('base64');
    
    // Determine content type from response or fallback to jpeg
    const contentType = response.headers.get('content-type') || 'image/jpeg';
    return `data:${contentType};base64,${base64}`;
  } catch (error) {
    console.error('Error converting image to base64:', error);
    throw new Error('Failed to convert image to base64');
  }
}

const VIRTUAL_STAGING_PROMPT_INSTRUCTIONS = `
Below are guidelines on how to create a single descriptive prompt for an image generation model like Ideogram. The goal is to describe the furnished interior scene as if it already exists, focusing on existing furniture, décor, and style, without suggesting structural changes or using instructive language.

1. Begin the prompt with a clear identification of the room type and its overarching style. For example, start with: "A photo of an industrial loft-style living room..." or "An image of a Scandinavian-inspired bedroom...".

2. Describe all elements as if they are already in place, avoiding phrases like "add a" or "place a." The prompt should read as a depiction of a fully realized, existing interior space.

3. Focus on furnishings, décor, and finishes. Include details about furniture materials, colors, and textures, as well as art, plants, rugs, and other decorative items. Mention their placement relative to other elements without instructing new arrangements.

4. Incorporate details about the style and aesthetic, ensuring that furniture, décor, and color palettes are cohesive and complement the established room style.

5. Include plants or greenery as part of the existing scene to add freshness and vitality.

6. Do not give instructions for structural changes. If there are architectural features, mention them only if they are part of the given style and environment. Do not introduce new windows, walls, or other architectural elements that are not part of the described image.

7. Present the entire description in a single, flowing paragraph. The prompt should be concise yet richly detailed, describing a space that feels intentionally designed and naturally existing. Avoid second-person language and avoid telling the model what to do; simply describe what is seen.

8. Aim for a prompt length of approximately 1–3 sentences per major element to create a vivid yet cohesive snapshot of the scene.
`;

type InsufficientCreditsError = {
  success: false;
  error: string;
  updatedCredits: number;
  type: "INSUFFICIENT_CREDITS";
};

export async function generateVirtualStagingImage(
  params: VirtualStagingParams
): Promise<VirtualStagingResult | InsufficientCreditsError> {
  try {
    const validatedParams = VirtualStagingParamsSchema.parse(params);

    // Check user credits before proceeding
    const user = await prisma.user.findUnique({
      where: { id: validatedParams.userId },
      select: { imageCredits: true },
    });

    if (!user || user.imageCredits < 5) {
      return {
        success: false,
        error:
          "You need 5 credits to generate an image. Please upgrade to get more credits.",
        updatedCredits: user?.imageCredits || 0,
        type: "INSUFFICIENT_CREDITS",
      };
    }

    // Decrease image credits by 5 only after confirming sufficient credits
    const updatedUser = await prisma.user.update({
      where: { id: validatedParams.userId },
      data: { imageCredits: { decrement: 5 } },
    });

    let finalPrompt: string;
    let negativePrompt =
      validatedParams.excludedElements || "doors and windows";

    // Properly handle excluded elements
    if (validatedParams.excludedElements) {
      // Add the excluded elements description to the negative prompt
      negativePrompt =
        `${validatedParams.excludedElements}, ${negativePrompt}`.trim();
    }

    if (!validatedParams.isAdvancedMode) {
      const stream = createStreamableValue();
      let fullPrompt = "";

      const streamPromise = new Promise<string>((resolve) => {
        (async () => {
          let textPrompt = `Generate a virtual staging prompt for a ${
            validatedParams.room || "room"
          } in ${
            validatedParams.style || "any suitable"
          } style. Focus solely on adding furniture and décor items without mentioning any structural elements or changes to walls, floors, windows, or ceilings.`;
          
          // Add description of the image since o1 model doesn't support vision
          if (validatedParams.image) {
            textPrompt += ` The image shows a ${validatedParams.room || "room"} that needs to be virtually staged.`;
          }
          
          // Use text-only mode since o1 doesn't support image_url
          const response = await azureClient.chat.completions.create({
            model: AZURE_DEPLOYMENT,
            messages: [
              {
                role: "system",
                content: VIRTUAL_STAGING_PROMPT_INSTRUCTIONS,
              },
              {
                role: "user",
                content: textPrompt,
              },
            ],
            stream: true,
          });

          for await (const chunk of response) {
            const text = chunk.choices[0]?.delta?.content || "";
            if (text) {
              fullPrompt += text;
              stream.update(fullPrompt);
            }
          }

          stream.done();
          resolve(fullPrompt);
        })();
      });

      finalPrompt = await streamPromise;
    } else {
      finalPrompt = validatedParams.prompt || "";
    }

    const replicate = new Replicate({
      auth: REPLICATE_API_TOKEN,
    });

    const prediction = await replicate.predictions.create({
      model: "ideogram-ai/ideogram-v2",
      input: {
        prompt: finalPrompt,
        image: validatedParams.image,
        mask: validatedParams.mask,
        seed: Math.floor(Math.random() * 1000000),
        resolution: validatedParams.resolution,
        style_type: "Auto",
        aspect_ratio: validatedParams.aspect_ratio,
        negative_prompt: negativePrompt,
        magic_prompt_option: validatedParams.magic_prompt_option,
      },
      webhook: WEBHOOK_HOST
        ? `${WEBHOOK_HOST}/api/webhook/replicate/virtual-staging`
        : undefined,
      webhook_events_filter: ["completed"],
    });

    const dbPrediction = await saveVirtualStagingToPrisma(
      validatedParams.userId,
      prediction.id,
      validatedParams.image,
      finalPrompt,
      validatedParams.isAdvancedMode ? undefined : validatedParams.style,
      validatedParams.isAdvancedMode ? undefined : validatedParams.room,
      validatedParams.mask
    );

    const placeholder: VirtualStagingPlaceholder = {
      id: prediction.id,
      status: PredictionStatus.PROCESSING,
      inputImage: validatedParams.image,
      outputImage: undefined,
      prompt: finalPrompt,
      style: validatedParams.isAdvancedMode ? undefined : validatedParams.style,
      room: validatedParams.isAdvancedMode ? undefined : validatedParams.room,
      excludedElements: validatedParams.excludedElements || undefined,
      createdAt: new Date(prediction.created_at),
      url: validatedParams.image,
      mask: validatedParams.mask,
    };

    console.log("Replicate API response:", prediction);
    console.log("Saved prediction to database:", placeholder);

    return {
      success: true,
      predictionId: prediction.id,
      replicateId: prediction.id,
      placeholders: [placeholder],
      updatedCredits: updatedUser.imageCredits,
    };
  } catch (error) {
    console.error("Error generating virtual staging image:", error);

    // Refund credits in case of local error
    if (
      error instanceof Error &&
      error.message !== "Insufficient image credits"
    ) {
      await prisma.user.update({
        where: { id: params.userId },
        data: { imageCredits: { increment: 1 } },
      });
    }

    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: "Invalid input parameters",
        updatedCredits: 0,
        type: "INSUFFICIENT_CREDITS",
      };
    }
    if (error instanceof Error) {
      return {
        success: false,
        error: error.message,
        updatedCredits: 0,
        type: "INSUFFICIENT_CREDITS",
      };
    }
    return {
      success: false,
      error: "An unexpected error occurred",
      updatedCredits: 0,
      type: "INSUFFICIENT_CREDITS",
    };
  }
}
