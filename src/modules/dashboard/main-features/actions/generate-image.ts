"use server";

import { z } from "zod";
import Replicate from "replicate";
import prisma from "@/lib/prisma/prisma";
import { PredictionStatus } from "@prisma/client";
import { GenerationResult } from "../types";

const REPLICATE_API_TOKEN = process.env.REPLICATE_API_TOKEN;

interface GenerateImageParams {
  userId: string;
  prompt: string;
  negativePrompt?: string;
}

const GenerateImageParamsSchema = z.object({
  userId: z.string().min(1),
  prompt: z.string().min(1),
  negativePrompt: z.string().optional(),
});

export async function generateImage(
  params: GenerateImageParams
): Promise<GenerationResult> {
  try {
    const validatedParams = GenerateImageParamsSchema.parse(params);

    const user = await prisma.user.findUnique({
      where: { id: validatedParams.userId },
      select: { imageCredits: true },
    });

    if (!user || user.imageCredits < 1) {
      return {
        success: false,
        error:
          "You need 1 credit to generate an image. Please upgrade to get more credits.",
        updatedCredits: user?.imageCredits || 0,
      };
    }

    const updatedUser = await prisma.user.update({
      where: { id: validatedParams.userId },
      data: { imageCredits: { decrement: 1 } },
    });

    const replicate = new Replicate({
      auth: REPLICATE_API_TOKEN,
    });

    const prediction = await replicate.predictions.create({
      version:
        "39ed52f2a78e934b3ba6e2a89f5b1c712de7dfea535525255b1aa35c5565e08b",
      input: {
        prompt: validatedParams.prompt,
        negative_prompt: validatedParams.negativePrompt || "",
      },
    });

    // Save to database with required fields
    await prisma.editImage.create({
      data: {
        replicate_id: prediction.id,
        userId: validatedParams.userId,
        status: PredictionStatus.PROCESSING,
        prompt: validatedParams.prompt,
        inputImage: "",
        outputImage: null,
      },
    });

    const output = await replicate.wait(prediction);

    if (!output || !output.output) {
      throw new Error("No output received from Replicate");
    }

    const outputUrl = Array.isArray(output.output)
      ? output.output[0]
      : output.output;

    // Update database record with output
    await prisma.editImage.update({
      where: { replicate_id: prediction.id },
      data: {
        status: PredictionStatus.SUCCEEDED,
        outputImage: outputUrl,
        completedAt: new Date(),
      },
    });

    return {
      success: true,
      outputUrl,
      updatedCredits: updatedUser.imageCredits,
    };
  } catch (error) {
    console.error("Error generating image:", error);

    // Refund credits in case of error
    if (
      error instanceof Error &&
      error.message !== "Insufficient image credits"
    ) {
      await prisma.user.update({
        where: { id: params.userId },
        data: { imageCredits: { increment: 1 } },
      });
    }

    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: "Invalid input parameters",
        updatedCredits: 0,
      };
    }

    return {
      success: false,
      error:
        error instanceof Error ? error.message : "An unexpected error occurred",
      updatedCredits: 0,
    };
  }
}
