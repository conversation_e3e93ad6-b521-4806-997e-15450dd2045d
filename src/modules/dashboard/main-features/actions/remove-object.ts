"use server";

import { z } from "zod";
import Replicate from "replicate";
import prisma from "@/lib/prisma/prisma";
import { PredictionStatus } from "@prisma/client";
import sharp from "sharp";

const REPLICATE_API_TOKEN = process.env.REPLICATE_API_TOKEN;

interface RemoveObjectParams {
  userId: string;
  image: string;
  mask: string;
}

type RemoveObjectResult = {
  success: boolean;
  outputUrl?: string;
  replicateId?: string;
  error?: string;
  updatedCredits: number;
  type?: "INSUFFICIENT_CREDITS";
};

interface ReplicateOutput {
  output?: string[] | string | null;
  error?: string;
  status: string;
}

const RemoveObjectParamsSchema = z.object({
  userId: z.string().min(1),
  image: z.string().url(),
  mask: z.string().url(),
});

async function invertMask(maskUrl: string): Promise<string> {
  const response = await fetch(maskUrl);
  const arrayBuffer = await response.arrayBuffer();
  const buffer = Buffer.from(arrayBuffer);

  // Invert the image using sharp
  const invertedBuffer = await sharp(buffer).negate().toBuffer();

  // Convert to base64
  return `data:image/png;base64,${invertedBuffer.toString("base64")}`;
}

export async function removeObject(
  params: RemoveObjectParams
): Promise<RemoveObjectResult> {
  try {
    const validatedParams = RemoveObjectParamsSchema.parse(params);

    const user = await prisma.user.findUnique({
      where: { id: validatedParams.userId },
      select: { imageCredits: true },
    });

    if (!user || user.imageCredits < 1) {
      return {
        success: false,
        error:
          "You need 1 credit to remove objects. Please upgrade to get more credits.",
        updatedCredits: user?.imageCredits || 0,
        type: "INSUFFICIENT_CREDITS",
      };
    }

    const updatedUser = await prisma.user.update({
      where: { id: validatedParams.userId },
      data: { imageCredits: { decrement: 1 } },
    });

    // Invert the mask before sending to LaMa
    const invertedMaskUrl = await invertMask(validatedParams.mask);

    const replicate = new Replicate({
      auth: REPLICATE_API_TOKEN,
    });

    const prediction = await replicate.predictions.create({
      version:
        "0e3a841c913f597c1e4c321560aa69e2bc1f15c65f8c366caafc379240efd8ba",
      input: {
        image: validatedParams.image,
        mask: invertedMaskUrl,
        // Add additional parameters to ensure correct dimensions
        resize_mode: "scale",
        return_mask: false,
      },
    });

    // Save to database with required fields
    await prisma.editImage.create({
      data: {
        replicate_id: prediction.id,
        userId: validatedParams.userId,
        status: PredictionStatus.PROCESSING,
        inputImage: validatedParams.image,
        maskImage: validatedParams.mask,
        prompt: "", // Empty prompt for remove object
        outputImage: null, // Add null outputImage initially
      },
    });

    const output = (await replicate.wait(prediction)) as ReplicateOutput;

    if (output.error) {
      throw new Error(output.error);
    }

    const outputUrl = Array.isArray(output.output)
      ? output.output[0]
      : typeof output.output === "string"
      ? output.output
      : null;

    if (!outputUrl) {
      throw new Error("No output URL received from Replicate");
    }

    // Update database record with output
    await prisma.editImage.update({
      where: { replicate_id: prediction.id },
      data: {
        status: PredictionStatus.SUCCEEDED,
        outputImage: outputUrl,
        completedAt: new Date(),
      },
    });

    return {
      success: true,
      outputUrl,
      replicateId: prediction.id,
      updatedCredits: updatedUser.imageCredits,
    };
  } catch (error) {
    console.error("Error removing object:", error);

    // Refund credits in case of error
    if (
      error instanceof Error &&
      error.message !== "Insufficient image credits"
    ) {
      await prisma.user.update({
        where: { id: params.userId },
        data: { imageCredits: { increment: 1 } },
      });
    }

    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: "Invalid input parameters",
        updatedCredits: 0,
      };
    }

    return {
      success: false,
      error:
        error instanceof Error ? error.message : "An unexpected error occurred",
      updatedCredits: 0,
    };
  }
}
