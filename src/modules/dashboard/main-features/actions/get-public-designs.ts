"use server";
import { PredictionStatus, Prisma } from "@prisma/client";
import prisma from "@/lib/prisma/prisma";

// Define a base OrderByInput type that includes common fields
type BaseOrderByInput = {
  createdAt?: Prisma.SortOrder;
  averageRating?: { sort: Prisma.SortOrder; nulls?: 'first' | 'last' };
  totalRatings?: Prisma.SortOrder;
};

export type PublicDesign = {
  id: string;
  inputImage: string;
  outputImage: string;
  type: string; // Keep as string for frontend consistency
  createdAt: Date;
  averageRating?: number | null; // Add rating fields
  totalRatings?: number | null;
};

// Define the mapping between frontend feature titles and Prisma models/fields
const designTypeMapping = {
  "Interior Design": {
    model: prisma.interiorDesign,
    outputField: "outputImages", // Array field
    typeString: "Interior Design",
  },
  "Exterior Design": {
    model: prisma.exteriorDesign,
    outputField: "outputImages", // Array field
    typeString: "Exterior Design",
  },
  "Upscale Image": {
    model: prisma.upscaleImage,
    outputField: "outputImage", // Single string field
    typeString: "Upscale Image",
  },
  "Virtual Staging": {
    model: prisma.virtualStaging,
    outputField: "displayUrl", // Use the field where the final URL is stored
    typeString: "Virtual Staging",
  },
  // Add other types here if needed (e.g., Style Transfer, Background Removal)
  // "Style Transfer": { model: prisma.styleTransfer, outputField: "outputImage", typeString: "Style Transfer" },
  // "Background Removal": { model: prisma.backgroundRemoval, outputField: "outputImage", typeString: "Background Removal" },
} as const;

// Extract valid type strings for type checking
export type DesignType = keyof typeof designTypeMapping;

// Define the structure for the options parameter
interface GetPublicDesignsOptions {
  type: DesignType; // Make type mandatory for fetching specific category
  limit?: number;
  // Use the BaseOrderByInput type for better type checking
  orderBy?: BaseOrderByInput | BaseOrderByInput[];
}

// Default sorting order
const defaultOrderBy: BaseOrderByInput[] = 
  [
    { averageRating: { sort: 'desc', nulls: 'last' } },
    { totalRatings: 'desc' },
    { createdAt: 'desc' },
  ];


export async function getPublicDesigns(
  options: GetPublicDesignsOptions
): Promise<PublicDesign[]> {
  const { type, limit = 10, orderBy = defaultOrderBy } = options;

  const mapping = designTypeMapping[type];
  if (!mapping) {
    console.error(`Invalid design type provided: ${type}`);
    return [];
  }

  const { model, outputField, typeString } = mapping;

  try {
    // Define common select fields, including new rating fields
    const selectFields: any = { // Use any temporarily, refine if possible
      id: true,
      inputImage: true,
      createdAt: true,
      averageRating: true,
      totalRatings: true,
      [outputField]: true, // Dynamically include the correct output field
    };


    // Fetch designs from the specific model table
    const designs = await (model as any).findMany({ // Use type assertion 'as any' to resolve TS union issue
      where: {
        isPublic: true,
        status: PredictionStatus.SUCCEEDED,
        // inputImage: { not: null }, // Removed: Schema enforces non-nullability
        // Add check for output field presence based on its type
        [outputField]: outputField === 'outputImages'
          ? { isEmpty: false } // Correct check for non-empty array
          : { not: null }, // Check if string is not null
      },
      take: limit,
      orderBy: orderBy, // Use the provided or default orderBy
      select: selectFields,
    });

    // Log raw results from Prisma
    console.log(`Raw ${typeString} designs fetched:`, JSON.stringify(designs, null, 2));

    // Map results to the consistent PublicDesign structure
    const mappedDesigns = designs
      .map((design: any) => {
        // Extract the first image if the output field is an array
        const outputImage =
          outputField === 'outputImages'
            ? design[outputField]?.[0] ?? ""
            : design[outputField] ?? "";

        // Final check for non-empty input/output
        if (design.inputImage && outputImage) {
          return {
            id: design.id,
            inputImage: design.inputImage,
            outputImage: outputImage,
            type: typeString, // Use the consistent string type from mapping
            createdAt: design.createdAt,
            averageRating: design.averageRating,
            totalRatings: design.totalRatings,
          };
        }
        return null;
      })
      .filter((d: PublicDesign | null): d is PublicDesign => d !== null);

    // Log mapped and filtered results
    console.log(`Mapped ${typeString} designs:`, JSON.stringify(mappedDesigns, null, 2));

    return mappedDesigns;

  } catch (error) {
    console.error(`Error fetching ${typeString} designs:`, error);
    return []; // Ensure the catch block returns an empty array on error
  }
}
