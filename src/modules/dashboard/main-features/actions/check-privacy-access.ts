"use server";

import prisma from "@/lib/prisma/prisma";

export async function checkPrivacyAccess(userId: string) {
  try {
    const subscription = await prisma.subscription.findUnique({
      where: { userId },
      select: {
        allowPrivateImages: true,
        isActive: true,
        endDate: true,
      },
    });

    // Check if subscription is valid and active
    const isSubscriptionValid =
      subscription?.isActive &&
      subscription.endDate > new Date() &&
      subscription.allowPrivateImages;

    return {
      canTogglePrivate: !!isSubscriptionValid,
      error: null,
    };
  } catch (error) {
    console.error("Error checking privacy access:", error);
    return {
      canTogglePrivate: false,
      error: "Failed to check privacy access",
    };
  }
}
