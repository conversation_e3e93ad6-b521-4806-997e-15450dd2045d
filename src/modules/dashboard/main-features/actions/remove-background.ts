"use server";

import prisma from "@/lib/prisma/prisma";
import {
  PredictionStatus,
  RemoveBackgroundParams,
  RemoveBackgroundResponse,
} from "@/modules/dashboard/main-features/types";

const REPLICATE_API_URL = "https://api.replicate.com/v1/predictions";
const REPLICATE_API_TOKEN = process.env.REPLICATE_API_TOKEN;
const WEBHOOK_HOST = process.env.WEBHOOK_HOST;

export async function removeBackground(
  params: RemoveBackgroundParams
): Promise<RemoveBackgroundResponse> {
  try {
    const { userId, image } = params;

    // Decrease image credits before making the API call
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { imageCredits: { decrement: 1 } },
    });

    if (updatedUser.imageCredits < 0) {
      throw new Error("Insufficient image credits");
    }

    const input = {
      version:
        "95fcc2a26d3899cd6c2691c900465aaeff466285a65c14638cc5f36f34befaf1",
      webhook: WEBHOOK_HOST
        ? `${WEBHOOK_HOST}/api/webhook/replicate/remove-bg`
        : undefined,
      webhook_events_filter: ["start", "completed"],
      input: {
        image: image,
      },
    };

    console.log("Sending request to Replicate with params:", input);

    const response = await fetch(REPLICATE_API_URL, {
      method: "POST",
      headers: {
        Authorization: `Token ${REPLICATE_API_TOKEN}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(input),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const prediction = await response.json();
    console.log("Replicate API response:", prediction);

    if (!prediction || !prediction.id) {
      throw new Error("Failed to get prediction ID from Replicate");
    }

    const dbBackgroundRemoval = await prisma.backgroundRemoval.create({
      data: {
        replicate_id: prediction.id,
        userId: userId,
        status: PredictionStatus.PROCESSING,
        inputImage: image,
      },
    });

    console.log("Saved background removal to database:", dbBackgroundRemoval);

    return {
      id: dbBackgroundRemoval.id,
      status: PredictionStatus.PROCESSING,
      inputImage: image,
      outputImage: "", // This will be updated when the background removal is complete
      createdAt: dbBackgroundRemoval.createdAt,
      url: "", // Add this line to match the BaseGeneratedImageWithoutPrompt interface
      updatedCredits: updatedUser.imageCredits, // Add this line
    };
  } catch (error) {
    console.error("Error removing background:", error);

    // Refund credits in case of local error
    if (
      error instanceof Error &&
      error.message !== "Insufficient image credits"
    ) {
      await prisma.user.update({
        where: { id: params.userId },
        data: { imageCredits: { increment: 1 } },
      });
    }

    return {
      id: "", // Provide a default or appropriate value
      status: PredictionStatus.FAILED,
      inputImage: params.image,
      outputImage: "",
      createdAt: new Date(),
      url: "", // Add this line to match the BaseGeneratedImageWithoutPrompt interface
      updatedCredits: 0, // Add this line with a default value
    };
  }
}
