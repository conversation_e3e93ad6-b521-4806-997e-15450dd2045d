"use server";

import { z } from "zod";
import Replicate from "replicate";
import {
  UpscaleImageParams,
  UpscalePlaceholder,
  PredictionStatus,
} from "@/modules/dashboard/main-features/types";
import { saveUpscaleImageToPrisma } from "@/modules/dashboard/main-features/actions/utils";
import prisma from "@/lib/prisma/prisma";

const REPLICATE_API_TOKEN = process.env.REPLICATE_API_TOKEN;
const WEBHOOK_HOST = process.env.WEBHOOK_HOST;

const UpscaleImageParamsSchema = z.object({
  userId: z.string().min(1),
  image: z.string().url(),
  upscaleAmount: z.number().min(1).max(4),
  creativity: z.number().min(0).max(100),
  resemblance: z.number().min(0).max(3),
  sourceType: z.string().optional(),
  originalImageId: z.string().optional(),
});

interface UpscaleImageResult {
  success: boolean;
  predictionId?: string;
  placeholder?: UpscalePlaceholder;
  error?: string;
  updatedCredits: number;
  type?: "INSUFFICIENT_CREDITS";
}

export async function upscaleImage(
  params: UpscaleImageParams
): Promise<UpscaleImageResult> {
  try {
    const validatedParams = UpscaleImageParamsSchema.parse(params);

    const user = await prisma.user.findUnique({
      where: { id: validatedParams.userId },
      select: { imageCredits: true },
    });

    if (!user || user.imageCredits < 1) {
      return {
        success: false,
        error:
          "You need 1 credit to upscale an image. Please upgrade to get more credits.",
        updatedCredits: user?.imageCredits || 0,
        type: "INSUFFICIENT_CREDITS",
      };
    }

    const updatedUser = await prisma.user.update({
      where: { id: validatedParams.userId },
      data: { imageCredits: { decrement: 1 } },
    });

    const replicate = new Replicate({
      auth: REPLICATE_API_TOKEN,
    });

    const input = {
      image: validatedParams.image,
      scale_factor: validatedParams.upscaleAmount,
      creativity: validatedParams.creativity / 100, // Convert to 0-1 range
      resemblance: validatedParams.resemblance,
      seed: Math.floor(Math.random() * 1000000),
      prompt: " masterpiece, best quality, highres ",
      dynamic: 6,
      handfix: "disabled",
      pattern: false,
      sharpen: 0,
      sd_model: "juggernaut_reborn.safetensors [338b85bc4f]",
      scheduler: "DPM++ 3M SDE Karras",
      tiling_width: 112,
      output_format: "png",
      tiling_height: 144,
      negative_prompt:
        "(woman portrait, woman painting, person, woman, woman's face, woman's body, woman's face and body, woman's face and body and hair, woman's face and body and hair and clothes, woman's face and body and hair and clothes and shoes, woman's face and body and hair and clothes and shoes and background, worst quality, low quality, normal quality:2) JuggernautNegative-neg",
      num_inference_steps: 18,
      // Custom properties to track source
      source_type: validatedParams.sourceType || "direct_upscale",
      original_image_id: validatedParams.originalImageId || "",
    };

    console.log("Sending request to Replicate API with params:", input);

    const prediction = await replicate.predictions.create({
      version:
        "dfad41707589d68ecdccd1dfa600d55a208f9310748e44bfe35b4a6291453d5e",
      input,
      webhook: WEBHOOK_HOST
        ? `${WEBHOOK_HOST}/api/webhook/replicate/upscale`
        : undefined,
      webhook_events_filter: ["completed"],
    });

    console.log("Replicate API response:", prediction);

    if (!prediction || !prediction.id) {
      throw new Error("Failed to get prediction ID from Replicate");
    }

    const dbPrediction = await saveUpscaleImageToPrisma(
      validatedParams.userId,
      prediction.id,
      validatedParams.image,
      validatedParams.upscaleAmount,
      validatedParams.creativity,
      validatedParams.sourceType,
      validatedParams.originalImageId
    );

    console.log("Saved prediction to database:", dbPrediction);

    const placeholder: UpscalePlaceholder = {
      id: dbPrediction.id,
      status: PredictionStatus.PROCESSING,
      inputImage: validatedParams.image,
      createdAt: new Date(prediction.created_at),
    };

    return {
      success: true,
      predictionId: dbPrediction.id,
      placeholder,
      updatedCredits: updatedUser.imageCredits,
    };
  } catch (error) {
    console.error("Error upscaling image:", error);

    // Refund credits in case of local error
    if (
      error instanceof Error &&
      error.message !== "Insufficient image credits"
    ) {
      await prisma.user.update({
        where: { id: params.userId },
        data: { imageCredits: { increment: 1 } },
      });
    }

    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: "Invalid input parameters",
        updatedCredits: 0,
        type: "INSUFFICIENT_CREDITS",
      };
    }
    if (error instanceof Error) {
      return {
        success: false,
        error: error.message,
        updatedCredits: 0,
        type: "INSUFFICIENT_CREDITS",
      };
    }
    return {
      success: false,
      error: "An unexpected error occurred",
      updatedCredits: 0,
      type: "INSUFFICIENT_CREDITS",
    };
  }
}
