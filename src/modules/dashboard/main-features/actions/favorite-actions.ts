"use server";

import { auth } from "@/lib/auth/auth";
import prisma from "@/lib/prisma/prisma";
import { revalidatePath } from "next/cache";

type DesignType =
  | "interior"
  | "exterior"
  | "remove-background"
  | "upscale"
  | "virtual-staging"
  | "style-transfer";

type ModelType = {
  [K in DesignType]: {
    model: any;
    relation: string;
  };
};

const modelMap: ModelType = {
  interior: {
    model: prisma.interiorDesign,
    relation: "favoriteInteriorDesigns",
  },
  exterior: {
    model: prisma.exteriorDesign,
    relation: "favoriteExteriorDesigns",
  },
  "remove-background": {
    model: prisma.backgroundRemoval,
    relation: "favoriteBackgroundRemovals",
  },
  upscale: { model: prisma.upscaleImage, relation: "favoriteUpscaleImages" },
  "virtual-staging": {
    model: prisma.virtualStaging,
    relation: "favoriteVirtualStagings",
  },
  "style-transfer": {
    model: prisma.styleTransfer,
    relation: "favoriteStyleTransfers",
  },
};

export async function toggleFavorite(
  designId: string,
  designType: DesignType
): Promise<boolean> {
  const session = await auth();
  if (!session?.user?.id) {
    throw new Error("User not authenticated");
  }

  const userId = session.user.id;
  const { model, relation } = modelMap[designType];

  const design = await model.findUnique({
    where: { id: designId },
    include: { favoritedBy: { where: { id: userId } } },
  });

  if (!design) {
    throw new Error("Design not found");
  }

  const isFavorited = design.favoritedBy.length > 0;

  await model.update({
    where: { id: designId },
    data: {
      favoritedBy: {
        [isFavorited ? "disconnect" : "connect"]: {
          id: userId,
        },
      },
    },
  });

  revalidatePath("/dashboard/designs");
  return !isFavorited;
}

export async function getFavoriteStatus(
  designId: string,
  designType: DesignType
): Promise<boolean> {
  const session = await auth();
  if (!session?.user?.id) {
    return false;
  }

  const userId = session.user.id;
  const { model } = modelMap[designType];

  const design = await model.findUnique({
    where: { id: designId },
    include: { favoritedBy: { where: { id: userId } } },
  });

  return design?.favoritedBy.length > 0 || false;
}
