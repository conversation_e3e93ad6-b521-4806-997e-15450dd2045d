import prisma from "@/lib/prisma/prisma";
import {
  InteriorDesign as PrismaInteriorDesign,
  ExteriorDesign as PrismaExteriorDesign,
  BackgroundRemoval as PrismaBackgroundRemoval,
  UpscaleImage as PrismaUpscaleImage,
  VirtualStaging as PrismaVirtualStaging,
  StyleTransfer as PrismaStyleTransfer,
  PredictionStatus,
  ImageRating,
  User,
} from "@prisma/client";
import { Design } from "@/types/designs";

export async function getDesignBySlug(
  userId: string,
  type: string,
  designId: string
): Promise<Design | null> {
  try {
    let design;

    const baseSelect = {
      id: true,
      inputImage: true,
      createdAt: true,
      status: true,
      isPublic: true,
      userId: true,
      replicate_id: true,
      completedAt: true,
      ratings: true,
      favoritedBy: true,
      totalRatings: true,
      averageRating: true,
      user: true,
    };

    const getBaseFields = (design: any) => ({
      ...design,
      averageRating: design.averageRating || undefined,
      totalRatings: design.totalRatings || 0,
      ratings: design.ratings || [],
      favoritedBy: design.favoritedBy || [],
    });

    switch (type) {
      case "interior":
        design = await prisma.interiorDesign.findFirst({
          where: { id: designId, userId },
          select: {
            ...baseSelect,
            outputImages: true,
            prompt: true,
            style: true,
            room: true,
          },
        });
        if (design) {
          return {
            ...getBaseFields(design),
            type: "interior",
            outputImages: design.outputImages || [],
            prompt: design.prompt || undefined,
            style: design.style || undefined,
            room: design.room || undefined,
          };
        }
        break;

      case "exterior":
        design = await prisma.exteriorDesign.findFirst({
          where: { id: designId, userId },
          select: {
            ...baseSelect,
            outputImages: true,
            prompt: true,
            style: true,
            building: true,
          },
        });
        if (design) {
          return {
            ...getBaseFields(design),
            type: "exterior",
            outputImages: design.outputImages || [],
            prompt: design.prompt || undefined,
            style: design.style || undefined,
            building: design.building || undefined,
          };
        }
        break;

      case "virtual-staging":
        design = await prisma.virtualStaging.findFirst({
          where: { id: designId, userId },
          select: {
            ...baseSelect,
            outputImage: true,
            prompt: true,
            style: true,
            room: true,
            excludedElements: true,
          },
        });
        if (design) {
          return {
            ...getBaseFields(design),
            type: "virtual-staging",
            outputImages: design.outputImage ? [design.outputImage] : [],
            outputImage: design.outputImage || undefined,
            prompt: design.prompt || undefined,
            style: design.style || undefined,
            room: design.room || undefined,
            excludedElements: design.excludedElements || undefined,
          };
        }
        break;

      case "upscale":
        design = await prisma.upscaleImage.findFirst({
          where: { id: designId, userId },
          select: {
            ...baseSelect,
            outputImage: true,
            upscaleAmount: true,
            creativity: true,
          },
        });
        if (design) {
          return {
            ...getBaseFields(design),
            type: "upscale",
            outputImages: design.outputImage ? [design.outputImage] : [],
            outputImage: design.outputImage || undefined,
            upscaleAmount: design.upscaleAmount || 0,
            creativity: design.creativity || 0,
          };
        }
        break;

      case "remove-background":
        design = await prisma.backgroundRemoval.findFirst({
          where: { id: designId, userId },
          select: { ...baseSelect, outputImage: true },
        });
        if (design) {
          return {
            ...getBaseFields(design),
            type: "remove-background",
            outputImages: design.outputImage ? [design.outputImage] : [],
            outputImage: design.outputImage || undefined,
          };
        }
        break;

      case "style-transfer":
        design = await prisma.styleTransfer.findFirst({
          where: { id: designId, userId },
          select: {
            ...baseSelect,
            outputImage: true,
            styleImage: true,
            structureImage: true,
            model: true,
            prompt: true,
            negativePrompt: true,
          },
        });
        if (design) {
          return {
            ...getBaseFields(design),
            type: "style-transfer",
            outputImage: design.outputImage || undefined,
            styleImage: design.styleImage || design.inputImage,
            structureImage: design.structureImage || undefined,
            model: design.model || undefined,
            prompt: design.prompt || undefined,
            negativePrompt: design.negativePrompt || undefined,
          };
        }
        break;
    }

    return null;
  } catch (error) {
    console.error("Error fetching design:", error);
    return null;
  }
}
