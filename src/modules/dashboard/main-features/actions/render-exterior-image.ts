"use server";

import { z } from "zod";
import Replicate from "replicate";
import {
  ExteriorGenerateImageParams,
  ExteriorUserInputSchema,
  ExteriorGenerateImageResult,
  ExteriorPlaceholder,
  PredictionStatus,
} from "@/modules/dashboard/main-features/types";
import {
  saveExteriorDesignToPrisma,
  getExteriorPromptFromDB,
  prepareNegativePrompt,
} from "@/modules/dashboard/main-features/actions/utils";
import { revalidatePath } from "next/cache";
import prisma from "@/lib/prisma/prisma";

const REPLICATE_API_TOKEN = process.env.REPLICATE_API_TOKEN;
const WEBHOOK_HOST = process.env.WEBHOOK_HOST;

export async function generateExteriorImage(
  params: ExteriorGenerateImageParams
): Promise<ExteriorGenerateImageResult> {
  try {
    const validatedParams = ExteriorUserInputSchema.parse(params);

    const user = await prisma.user.findUnique({
      where: { id: validatedParams.userId },
      select: { imageCredits: true },
    });

    if (!user || user.imageCredits < 1) {
      return {
        success: false,
        error:
          "You need 1 credit to generate an exterior image. Please upgrade to get more credits.",
        updatedCredits: user?.imageCredits || 0,
        type: "INSUFFICIENT_CREDITS",
      };
    }

    const updatedUser = await prisma.user.update({
      where: { id: validatedParams.userId },
      data: { imageCredits: { decrement: 1 } },
    });

    const replicate = new Replicate({
      auth: REPLICATE_API_TOKEN,
    });

    let prompt: string;
    if (validatedParams.prompt) {
      prompt = validatedParams.prompt;
    } else if (validatedParams.style && validatedParams.building) {
      const dbPrompt = await getExteriorPromptFromDB(
        validatedParams.style,
        validatedParams.building
      );
      if (!dbPrompt) {
        console.log("[Exterior Debug] No DB prompt found for:", {
          style: validatedParams.style,
          building: validatedParams.building,
        });
        throw new Error("Invalid style or building combination");
      }
      prompt = dbPrompt.includes("{building}")
        ? dbPrompt.replace("{building}", validatedParams.building)
        : `${dbPrompt}, ${validatedParams.building} in ${validatedParams.style} style`;
    } else {
      throw new Error("Invalid input parameters");
    }

    console.log("[Exterior Debug] Final prompt:", prompt);

    const negativePrompt = await prepareNegativePrompt(
      validatedParams.excludedElements
    );

    const predictions = [];
    const placeholders: ExteriorPlaceholder[] = [];

    // Generate a random seed for the prediction
    const randomSeed = Math.floor(Math.random() * 1000000);

    const input = {
      image: validatedParams.image,
      prompt: prompt,
      scale_factor: 1,
      creativity: validatedParams.creativity / 100,
      resemblance: validatedParams.conditionScale,
      seed: randomSeed,
      dynamic: 6,
      handfix: "disabled",
      pattern: false,
      sharpen: 0,
      sd_model: "juggernaut_reborn.safetensors [338b85bc4f]",
      scheduler: "DPM++ 3M SDE Karras",
      tiling_width: 112,
      output_format: "png",
      tiling_height: 144,
      negative_prompt: negativePrompt,
      num_inference_steps: 18,
    };

    const prediction = await replicate.predictions.create({
      version:
        "dfad41707589d68ecdccd1dfa600d55a208f9310748e44bfe35b4a6291453d5e",
      input,
      webhook: WEBHOOK_HOST
        ? `${WEBHOOK_HOST}/api/webhook/replicate/exterior-design`
        : undefined,
      webhook_events_filter: ["completed"],
    });
    predictions.push(prediction);

    const dbPrediction = await saveExteriorDesignToPrisma(
      validatedParams.userId,
      prediction.id,
      validatedParams.image,
      prompt,
      validatedParams.style,
      validatedParams.building
    );

    placeholders.push({
      id: dbPrediction.id,
      status: PredictionStatus.PROCESSING,
      inputImage: validatedParams.image,
      prompt: prompt,
      style: validatedParams.style,
      building: validatedParams.building,
      excludedElements: validatedParams.excludedElements || undefined,
      createdAt: new Date(prediction.created_at),
    });

    console.log("Replicate API responses:", predictions);
    console.log("Saved predictions to database:", placeholders);

    return {
      success: true,
      predictionId: predictions[0].id,
      replicateId: predictions[0].id,
      placeholders: placeholders,
      updatedCredits: updatedUser.imageCredits,
    };
  } catch (error) {
    console.error("Error generating exterior image:", error);

    // Refund credits in case of local error
    if (
      error instanceof Error &&
      error.message !== "Insufficient image credits"
    ) {
      await prisma.user.update({
        where: { id: params.userId },
        data: { imageCredits: { increment: 1 } },
      });
    }

    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: "Invalid input parameters",
        updatedCredits: 0,
      };
    }
    if (error instanceof Error) {
      return {
        success: false,
        error: error.message,
        updatedCredits: 0,
      };
    }
    return {
      success: false,
      error: "An unexpected error occurred",
      updatedCredits: 0,
    };
  }
}
