"use server";

import axios from "axios";
import { createCanvas, Image } from "canvas";
import { handleImageUpload } from "./upload-image";

const SEGMIND_API_KEY = process.env.SEGMIND_API_KEY;
const API_URL = "https://api.segmind.com/v1/automatic-mask-generator";

async function imageUrlToBase64(imageUrl: string): Promise<string> {
  try {
    const response = await axios.get(imageUrl, { responseType: "arraybuffer" });
    if (!response.data) {
      throw new Error("Failed to fetch image data");
    }
    return Buffer.from(response.data, "binary").toString("base64");
  } catch (error) {
    console.error("Error in imageUrlToBase64:", error);
    throw error;
  }
}

interface MaskConfig {
  prompt: string;
  threshold?: number;
  invertMask?: boolean;
  growMask?: number;
}

async function generateSingleMask(
  base64Image: string,
  config: MaskConfig
): Promise<Buffer> {
  try {
    if (!SEGMIND_API_KEY) {
      throw new Error("SEGMIND_API_KEY is not set in environment variables");
    }

    const response = await axios.post(
      API_URL,
      {
        prompt: config.prompt,
        image: base64Image,
        threshold: config.threshold ?? 0.3,
        invert_mask: config.invertMask ?? true,
        return_mask: true,
        grow_mask: config.growMask ?? 50,
        seed: Math.floor(Math.random() * 1000000),
        base64: false,
      },
      {
        headers: { "x-api-key": SEGMIND_API_KEY },
        responseType: "arraybuffer",
      }
    );

    if (!response.data) {
      throw new Error("No mask data received from API");
    }

    return Buffer.from(response.data);
  } catch (error) {
    console.error("Error in generateSingleMask:", error);
    throw error;
  }
}

async function mergeMaskBuffers(masks: Buffer[]): Promise<Buffer> {
  try {
    if (masks.length === 0) {
      throw new Error("No masks provided for merging");
    }

    const tempImg = new Image();
    tempImg.src = masks[0];

    const canvas = createCanvas(tempImg.width, tempImg.height);
    const ctx = canvas.getContext("2d");

    ctx.fillStyle = "black";
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    const maskCanvases = masks.map((maskBuffer) => {
      const maskImg = new Image();
      maskImg.src = maskBuffer;

      const maskCanvas = createCanvas(canvas.width, canvas.height);
      const maskCtx = maskCanvas.getContext("2d");
      maskCtx.drawImage(maskImg, 0, 0);

      return maskCtx.getImageData(0, 0, canvas.width, canvas.height);
    });

    const finalImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

    for (let p = 0; p < finalImageData.data.length; p += 4) {
      let isWhite = false;

      for (const maskData of maskCanvases) {
        if (maskData.data[p] > 200) {
          isWhite = true;
          break;
        }
      }

      if (isWhite) {
        finalImageData.data[p] = 0; // R
        finalImageData.data[p + 1] = 0; // G
        finalImageData.data[p + 2] = 0; // B
        finalImageData.data[p + 3] = 255; // A
      } else {
        finalImageData.data[p] = 255; // R
        finalImageData.data[p + 1] = 255; // G
        finalImageData.data[p + 2] = 255; // B
        finalImageData.data[p + 3] = 255; // A
      }
    }

    ctx.putImageData(finalImageData, 0, 0);
    return canvas.toBuffer("image/png");
  } catch (error) {
    console.error("Error merging masks:", error);
    throw error;
  }
}

async function uploadMask(
  buffer: Buffer,
  filename: string,
  folderPath?: string
): Promise<{ url: string }> {
  try {
    const formData = new FormData();
    formData.append(
      "file",
      new Blob([buffer], { type: "image/jpeg" }),
      filename
    );

    if (folderPath) {
      formData.append("metadata", JSON.stringify({ folder: folderPath }));
    }

    const result = await handleImageUpload(formData);

    if (!result || "error" in result) {
      throw new Error("Failed to upload mask");
    }

    return { url: result.displayUrl };
  } catch (error) {
    console.error("Error in uploadMask:", error);
    throw error;
  }
}

export async function generateAutoMask(
  imageUrl: string,
  folderPath?: string
): Promise<{ mergedMask: string }> {
  try {
    const base64Image = await imageUrlToBase64(imageUrl);

    const maskConfigs: MaskConfig[] = [
      {
        prompt: "windows and doors and doorways",
        threshold: 0.2,
        invertMask: false,
        growMask: 5,
      },
      {
        prompt: "ceiling only",
        threshold: 0.3,
        invertMask: false,
        growMask: 25,
      },
    ];

    const maskBuffers = await Promise.all(
      maskConfigs.map((config) => generateSingleMask(base64Image, config))
    );

    const mergedBuffer = await mergeMaskBuffers(maskBuffers);

    const maskUpload = await uploadMask(
      mergedBuffer,
      "combined_mask.jpg",
      folderPath
    );

    return { mergedMask: maskUpload.url };
  } catch (error) {
    console.error("Error in generateAutoMask:", error);
    throw new Error(
      error instanceof Error ? error.message : "Failed to generate auto mask"
    );
  }
}
