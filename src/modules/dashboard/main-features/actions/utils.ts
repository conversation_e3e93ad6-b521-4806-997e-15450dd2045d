"use server";

import { cookies } from "next/headers";
import prisma from "@/lib/prisma/prisma";
import { getUserSubscription } from "@/lib/subscription";
import { PredictionStatus } from "@prisma/client";

// Helper function to get privacy setting
async function getPrivacySetting(userId: string): Promise<boolean> {
  try {
    // First check if user has subscription with private access
    const subscription = await getUserSubscription(userId);

    console.log("[Privacy Debug] User ID:", userId);
    console.log("[Privacy Debug] Subscription:", subscription);

    // If user doesn't have private access subscription, always return public
    if (!subscription?.allowPrivateImages) {
      console.log("[Privacy Debug] No private access in subscription");
      return true;
    }

    try {
      const cookieStore = cookies();
      const privacySettings = cookieStore.get("privacy-settings");

      console.log("[Privacy Debug] Raw cookie value:", privacySettings?.value);

      if (!privacySettings?.value) {
        console.log("[Privacy Debug] No privacy settings found");
        return true;
      }

      const settings = JSON.parse(privacySettings.value);
      console.log("[Privacy Debug] Parsed settings:", settings);

      if (!settings?.state?.hasOwnProperty("isPublic")) {
        console.log("[Privacy Debug] Invalid settings structure");
        return true;
      }

      const isPublic = Boolean(settings.state.isPublic);
      console.log("[Privacy Debug] Final isPublic value:", isPublic);

      return isPublic;
    } catch (error) {
      console.error("[Privacy Debug] Cookie parsing error:", error);
      return true;
    }
  } catch (error) {
    console.error("[Privacy Debug] Main error:", error);
    return true;
  }
}

export async function getInteriorPromptFromDB(
  style: string,
  room: string
): Promise<string | null> {
  const promptRecord = await prisma.prompt.findFirst({
    where: {
      style: style,
      room: room,
      type: "INTERIOR",
    },
  });

  return promptRecord ? promptRecord.prompt : null;
}

export async function getExteriorPromptFromDB(
  style: string,
  building: string
): Promise<string | null> {
  const promptRecord = await prisma.prompt.findFirst({
    where: {
      style: style,
      room: building,
      type: "EXTERIOR",
    },
  });

  return promptRecord ? promptRecord.prompt : null;
}

export async function prepareNegativePrompt(
  excludedElements: string | null | undefined
): Promise<string> {
  let negativePrompt =
    "disfigured, kitsch, ugly, oversaturated, grain, low-res, deformed, blurry, bad anatomy, disfigured, poorly drawn face, mutation, mutated, extra limb, ugly, poorly drawn hands, missing limb, floating limbs, disconnected limbs, malformed hands, blur, out of focus, long neck, long body, mutated hands and fingers(woman portrait, woman painting, person, woman, woman's face, woman's body, woman's face and body, woman's face and body and hair, woman's face and body and hair and clothes, woman's face and body and hair and clothes and shoes, woman's face and body and hair and clothes and shoes and background, worst quality, low quality, normal quality:2) JuggernautNegative-neg,";
  if (excludedElements)
    negativePrompt = `${excludedElements}, ${negativePrompt}`;
  return negativePrompt;
}

export async function saveInteriorDesignToPrisma(
  userId: string,
  replicateId: string,
  inputImage: string,
  prompt?: string,
  style?: string,
  room?: string
) {
  const isPublic = await getPrivacySetting(userId);
  console.log("[Save Debug] Saving interior design with privacy:", isPublic);
  console.log("[Save Debug] User ID:", userId);

  const result = await prisma.interiorDesign.create({
    data: {
      userId,
      replicate_id: replicateId,
      inputImage,
      prompt,
      style,
      room,
      isPublic: isPublic,
    },
  });

  console.log("[Save Debug] Saved design with isPublic:", result.isPublic);
  return result;
}

export async function saveExteriorDesignToPrisma(
  userId: string,
  replicateId: string,
  inputImage: string,
  prompt?: string,
  style?: string,
  building?: string
) {
  const isPublic = await getPrivacySetting(userId);

  return prisma.exteriorDesign.create({
    data: {
      userId,
      replicate_id: replicateId,
      inputImage,
      prompt,
      style,
      building,
      isPublic,
    },
  });
}

export async function saveBackgroundRemovalToPrisma(
  userId: string,
  replicateId: string,
  inputImage: string
) {
  const isPublic = await getPrivacySetting(userId);

  return prisma.backgroundRemoval.create({
    data: {
      userId,
      replicate_id: replicateId,
      inputImage,
      isPublic,
    },
  });
}

export async function saveUpscaleImageToPrisma(
  userId: string,
  replicateId: string,
  inputImage: string,
  upscaleAmount: number,
  creativity: number,
  sourceType?: string,
  originalImageId?: string
) {
  const isPublic = await getPrivacySetting(userId);

  return prisma.upscaleImage.create({
    data: {
      userId,
      replicate_id: replicateId,
      inputImage,
      upscaleAmount,
      creativity,
      isPublic,
    },
  });
}

export async function saveVirtualStagingToPrisma(
  userId: string,
  replicateId: string,
  inputImage: string,
  prompt: string,
  style?: string,
  room?: string,
  mask?: string
) {
  const isPublic = await getPrivacySetting(userId);

  return await prisma.virtualStaging.create({
    data: {
      userId,
      replicate_id: replicateId,
      inputImage,
      prompt,
      style: style || null,
      room: room || null,
      mask: mask || null,
      status: PredictionStatus.PROCESSING,
      isPublic,
    },
  });
}
