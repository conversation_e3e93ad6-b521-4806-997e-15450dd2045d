import { createStore } from "zustand/vanilla";
import { useStore } from "zustand";
import { getUserImageCredits } from "@/modules/dashboard/main-features/actions/image-credits";

type ImageCreditsStore = {
  credits: number | null;
  isLoading: boolean;
  error: Error | null;
  fetchCredits: () => Promise<void>;
  setCredits: (credits: number) => void;
  decrementCredits: (amount: number) => void;
};

const store = createStore<ImageCreditsStore>((set, get) => ({
  credits: null,
  isLoading: false,
  error: null,
  fetchCredits: async () => {
    const currentCredits = get().credits;
    set({ isLoading: true });
    try {
      const credits = await getUserImageCredits();
      // Only update if credits have changed or were null
      if (credits !== currentCredits) {
        set({ credits, isLoading: false, error: null });
      } else {
        set({ isLoading: false, error: null });
      }
    } catch (error) {
      set({
        error: error instanceof Error ? error : new Error("Failed to fetch image credits"),
        isLoading: false,
      });
    }
  },
  // Only allow setting credits through server actions
  setCredits: (credits) => {
    const currentCredits = get().credits;
    if (credits !== currentCredits) {
      set({ credits });
    }
  },
  // Optimistically update credits, but they will be verified on next fetch
  decrementCredits: (amount) => {
    set((state) => ({
      credits: state.credits !== null ? Math.max(0, state.credits - amount) : null,
    }));
    // Trigger a fetch to verify the new credit amount
    get().fetchCredits();
  },
}));

export const useImageCreditsStore = () => useStore(store);
