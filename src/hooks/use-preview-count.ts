import { useState, useEffect } from "react";
import { getUsersForBulkEmail } from "@/modules/admin-dashboard/actions";
import type { EmailFiltersState } from "@/lib/email/types";

export function usePreviewCount(filters: EmailFiltersState) {
  const [count, setCount] = useState<number>(0);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const updatePreviewCount = async () => {
      try {
        setIsLoading(true);
        const result = await getUsersForBulkEmail(filters);
        setCount(result.total);
      } catch (error) {
        console.error("Error fetching preview count:", error);
      } finally {
        setIsLoading(false);
      }
    };

    const debounceTimer = setTimeout(() => {
      updatePreviewCount();
    }, 500);

    return () => clearTimeout(debounceTimer);
  }, [filters]);

  return { count, isLoading };
}
