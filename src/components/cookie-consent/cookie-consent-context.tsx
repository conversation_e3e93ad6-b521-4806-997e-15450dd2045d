"use client";

import {
  createContext,
  useContext,
  useEffect,
  useState,
  type ReactNode,
} from "react";
import {
  getConsentCookie,
  setConsentCookie,
  type CookieConsentOptions,
} from "@/lib/utils/cookies";

interface CookieConsentContextType {
  consent: CookieConsentOptions;
  updateConsent: (newConsent: CookieConsentOptions) => void;
}

const defaultConsent: CookieConsentOptions = {
  hasConsented: false,
  necessary: true,
  analytics: false,
  marketing: false,
};

const CookieConsentContext = createContext<CookieConsentContextType>({
  consent: defaultConsent,
  updateConsent: () => {},
});

export function CookieConsentProvider({ children }: { children: ReactNode }) {
  const [consent, setConsent] = useState<CookieConsentOptions>(defaultConsent);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const savedConsent = getConsentCookie();
    if (savedConsent) {
      setConsent(savedConsent);
    }
    setIsInitialized(true);
  }, []);

  const updateConsent = (newConsent: CookieConsentOptions) => {
    setConsentCookie(newConsent);
    setConsent(newConsent);
  };

  // Don't render children until we've initialized consent
  if (!isInitialized) {
    return null;
  }

  return (
    <CookieConsentContext.Provider value={{ consent, updateConsent }}>
      {children}
    </CookieConsentContext.Provider>
  );
}

export function useCookieConsent() {
  const context = useContext(CookieConsentContext);
  if (context === undefined) {
    throw new Error(
      "useCookieConsent must be used within a CookieConsentProvider"
    );
  }
  return context;
}
