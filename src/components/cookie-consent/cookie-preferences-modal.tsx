"use client";

import { useState } from "react";
import {
  Di<PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/modules/ui/dialog";
import { Switch } from "@/modules/ui/switch";
import {
  COOKIE_CATEGORIES,
  type CookieConsentOptions,
} from "@/lib/utils/cookies";
import { Button } from "@/modules/ui/button";

interface CookiePreferencesModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  initialConsent: CookieConsentOptions;
  onSave: (consent: CookieConsentOptions) => void;
}

export const CookiePreferencesModal = ({
  open,
  onOpenChange,
  initialConsent,
  onSave,
}: CookiePreferencesModalProps) => {
  const [consent, setConsent] = useState<CookieConsentOptions>({
    hasConsented: false,
    necessary: true,
    analytics: initialConsent?.analytics ?? false,
    marketing: initialConsent?.marketing ?? false,
  });

  const handleSave = () => {
    onSave({
      ...consent,
      hasConsented: true,
    });
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        size="md"
        className="sm:max-w-[500px] border-primary-foreground/20"
      >
        <DialogHeader className="space-y-2">
          <DialogTitle className="text-xl font-semibold">
            Cookie Preferences
          </DialogTitle>
          <p className="text-sm text-muted-foreground">
            Manage your cookie preferences. You can enable or disable different
            types of cookies below.
          </p>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {COOKIE_CATEGORIES.map((category) => (
            <div
              key={category.id}
              className="flex items-start justify-between space-x-4 rounded-lg bg-muted/40 p-3"
            >
              <div className="space-y-1">
                <div className="text-sm font-medium leading-none">
                  {category.title}
                </div>
                <div className="text-xs text-muted-foreground">
                  {category.description}
                </div>
                {category.required && (
                  <div className="inline-flex h-5 items-center rounded-md bg-primary/10 px-2 text-xs font-medium text-primary">
                    Required
                  </div>
                )}
              </div>
              <Switch
                className="mt-1"
                checked={consent[category.id as keyof CookieConsentOptions]}
                disabled={category.required}
                onCheckedChange={(checked: boolean) =>
                  setConsent((prev) => ({
                    ...prev,
                    [category.id]: checked,
                  }))
                }
              />
            </div>
          ))}
        </div>

        <div className="flex justify-end gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </Button>
          <Button size="sm" onClick={handleSave}>
            Save Preferences
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
