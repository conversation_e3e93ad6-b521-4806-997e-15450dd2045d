"use client";

import { useState } from "react";
import { But<PERSON> } from "@/modules/ui/button";
import { useCookieConsent } from "./cookie-consent-context";
import { CookiePreferencesModal } from "./cookie-preferences-modal";

export const CookieConsent = () => {
  const { consent, updateConsent } = useCookieConsent();
  const [showPreferences, setShowPreferences] = useState(false);

  if (!consent || consent.hasConsented) {
    return null;
  }

  return (
    <>
      <div className="fixed inset-x-0 bottom-0 z-50 border-t border-border/40 bg-background/80 backdrop-blur-sm shadow-lg">
        <div className=" flex flex-col md:flex-row py-3 px-4 gap-4 items-start md:items-center justify-between">
          <div className="flex items-start md:items-center gap-3 max-w-[600px]">
            <span className="text-lg mt-0.5 md:mt-0">🍪</span>
            <div className="space-y-0.5">
              <h3 className="font-medium leading-tight">
                We value your privacy
              </h3>
              <p className="text-sm text-muted-foreground leading-snug">
                We use cookies to enhance your browsing experience and analyze
                our traffic.
              </p>
            </div>
          </div>

          <div className="flex flex-row gap-2 w-full md:w-auto justify-end">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowPreferences(true)}
              className="whitespace-nowrap flex-1 md:flex-none"
            >
              Cookie Settings
            </Button>
            <Button
              size="sm"
              onClick={() =>
                updateConsent({
                  hasConsented: true,
                  necessary: true,
                  analytics: true,
                  marketing: true,
                })
              }
              className="whitespace-nowrap flex-1 md:flex-none"
            >
              Accept All
            </Button>
          </div>
        </div>
      </div>

      <CookiePreferencesModal
        open={showPreferences}
        onOpenChange={setShowPreferences}
        initialConsent={consent}
        onSave={updateConsent}
      />
    </>
  );
};
