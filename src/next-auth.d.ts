import "next-auth";
import type { Subscription } from "@prisma/client";

declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      imageCredits?: number | null;
      isSubscribed?: boolean;
      subscription?: Subscription | null;
      subscriptionStatus?: string;
      monthlyCredits?: number;
      nextBillingDate?: string | null;
      lastBillingDate?: string | null;
      trialEndsAt?: string | null;
      role?: string;
      createdAt?: Date | null;
    };
  }
}
