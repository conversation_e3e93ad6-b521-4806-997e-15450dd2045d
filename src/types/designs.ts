import { ImageRating, PredictionStatus } from "@prisma/client";

export type DesignType =
  | "interior"
  | "exterior"
  | "remove-background"
  | "upscale"
  | "virtual-staging"
  | "style-transfer";

interface BaseDesign {
  id: string;
  status: PredictionStatus;
  createdAt: Date;
  type: DesignType;
  ratings?: ImageRating[];
  totalRatings?: number;
  averageRating?: number;
  favoritedBy?: { id: string }[];
  isFavorite?: boolean;
}

export interface InteriorDesign extends BaseDesign {
  type: "interior";
  inputImage: string;
  outputImages: string[];
  prompt?: string;
  style?: string;
  room?: string;
}

export interface ExteriorDesign extends BaseDesign {
  type: "exterior";
  inputImage: string;
  outputImages: string[];
  prompt?: string;
  style?: string;
  building?: string;
}

export interface BackgroundRemovalDesign extends BaseDesign {
  type: "remove-background";
  inputImage: string;
  outputImage: string;
}

export interface UpscaleDesign extends BaseDesign {
  type: "upscale";
  inputImage: string;
  outputImage: string;
  upscaleAmount: number;
  creativity: number;
}

export interface VirtualStagingDesign extends BaseDesign {
  type: "virtual-staging";
  inputImage: string;
  outputImage: string;
  prompt: string;
  style?: string;
  room?: string;
}

export interface StyleTransferDesign extends BaseDesign {
  type: "style-transfer";
  inputImage: string;
  styleImage: string;
  structureImage?: string;
  outputImage?: string;
  model?: string;
  prompt?: string;
  negativePrompt?: string;
  completedAt?: Date;
  replicate_id?: string;
}

export type Design =
  | InteriorDesign
  | ExteriorDesign
  | BackgroundRemovalDesign
  | UpscaleDesign
  | VirtualStagingDesign
  | StyleTransferDesign;
