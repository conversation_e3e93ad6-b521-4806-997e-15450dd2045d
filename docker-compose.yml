version: "3.8"

services:
  renovaitor:
    build: .
    image: renovaitor
    container_name: renovaitor-${BUILD_ID:-latest}
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
      - DATABASE_URL=${DATABASE_URL}
    ports:
      - "3002:3002"
    networks:
      - renovaitor-net
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s

networks:
  renovaitor-net:
    name: renovaitor-net
