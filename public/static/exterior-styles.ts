export interface ExteriorStyleDetails {
  id: string;
  name: string;
  category: ExteriorStyleCategory;
  subcategory: ExteriorStyleSubcategory;
  colorPalette: string;
  architecturalFeatures: string[];
  materials: string[];
  roofStyle: string;
  windowTypes: string[];
  landscaping?: string[];
  iconName?: string;
  tags?: string[];
  era?: string;
}

export enum ExteriorStyleCategory {
  Traditional = "Traditional Architecture",
  Modern = "Modern & Contemporary",
  Cultural = "Cultural & Regional",
  Sustainable = "Sustainable & Eco-Friendly",
  Specialty = "Specialty & Themed",
}

export enum ExteriorStyleSubcategory {
  // Traditional
  Colonial = "Colonial",
  Victorian = "Victorian",
  Classical = "Classical",

  // Modern
  Contemporary = "Contemporary",
  Minimalist = "Minimalist",
  Industrial = "Industrial",

  // Cultural
  Mediterranean = "Mediterranean",
  Asian = "Asian",
  European = "European",

  // Sustainable
  EcoFriendly = "Eco-Friendly",
  Biophilic = "Biophilic",
  PassiveHouse = "Passive House",

  // Specialty
  Coastal = "Coastal",
  Mountain = "Mountain",
  Urban = "Urban",
}

export const exteriorStyleData: Record<string, ExteriorStyleDetails> = {
  modernMinimalist: {
    id: "modernMinimalist",
    name: "Modern Minimalist",
    category: ExteriorStyleCategory.Modern,
    subcategory: ExteriorStyleSubcategory.Minimalist,
    colorPalette: "Monochromatic whites, grays, and blacks",
    architecturalFeatures: [
      "Clean lines",
      "Geometric shapes",
      "Open spaces",
      "Minimal ornamentation",
      "Floor-to-ceiling windows",
    ],
    materials: ["Concrete", "Glass", "Steel", "Stone", "Composite materials"],
    roofStyle: "Flat or low-pitched roof",
    windowTypes: [
      "Large picture windows",
      "Aluminum-framed windows",
      "Floor-to-ceiling glass",
    ],
    landscaping: [
      "Geometric patterns",
      "Simple grass layouts",
      "Minimal plantings",
      "Rock gardens",
    ],
    iconName: "Cube",
    tags: ["Minimal", "Contemporary", "Clean", "Geometric"],
  },

  colonialRevival: {
    id: "colonialRevival",
    name: "Colonial Revival",
    category: ExteriorStyleCategory.Traditional,
    subcategory: ExteriorStyleSubcategory.Colonial,
    colorPalette: "White with dark accents (shutters, doors)",
    architecturalFeatures: [
      "Symmetrical facade",
      "Central front door",
      "Columns or pillars",
      "Multi-pane windows",
      "Decorative crown over entrance",
    ],
    materials: ["Brick", "Wood siding", "Stone foundation", "Wooden shutters"],
    roofStyle: "Side-gabled roof",
    windowTypes: [
      "Double-hung windows",
      "Multi-pane design",
      "Symmetrical placement",
    ],
    landscaping: [
      "Formal gardens",
      "Boxwood hedges",
      "Symmetrical plantings",
      "Brick pathways",
    ],
    iconName: "Column",
    tags: ["Traditional", "Classic", "Symmetrical", "Historical"],
    era: "18th-19th century",
  },

  mediterraneanVilla: {
    id: "mediterraneanVilla",
    name: "Mediterranean Villa",
    category: ExteriorStyleCategory.Cultural,
    subcategory: ExteriorStyleSubcategory.Mediterranean,
    colorPalette: "Warm earth tones, terracotta, cream",
    architecturalFeatures: [
      "Arched windows and doorways",
      "Stucco walls",
      "Terracotta roof tiles",
      "Wrought iron details",
      "Courtyards",
    ],
    materials: [
      "Stucco",
      "Terracotta",
      "Natural stone",
      "Wrought iron",
      "Ceramic tiles",
    ],
    roofStyle: "Low-pitched tile roof",
    windowTypes: ["Arched windows", "French doors", "Wrought iron grilles"],
    landscaping: [
      "Mediterranean plants",
      "Olive trees",
      "Terracotta pots",
      "Water features",
    ],
    iconName: "Sun",
    tags: ["Mediterranean", "Romantic", "Warm", "Elegant"],
  },

  contemporaryEco: {
    id: "contemporaryEco",
    name: "Contemporary Eco-House",
    category: ExteriorStyleCategory.Sustainable,
    subcategory: ExteriorStyleSubcategory.EcoFriendly,
    colorPalette: "Natural tones with modern accents",
    architecturalFeatures: [
      "Solar panels",
      "Green roof",
      "Rain water collection",
      "Natural ventilation",
      "Sustainable materials",
    ],
    materials: [
      "Recycled materials",
      "Sustainable wood",
      "Green roof systems",
      "Solar panels",
      "Living walls",
    ],
    roofStyle: "Green roof or solar panel roof",
    windowTypes: [
      "Energy-efficient windows",
      "Strategic placement for ventilation",
      "Double or triple glazing",
    ],
    landscaping: [
      "Native plants",
      "Rain gardens",
      "Edible gardens",
      "Permeable surfaces",
    ],
    iconName: "Leaf",
    tags: ["Sustainable", "Eco-friendly", "Modern", "Green"],
  },

  japaneseModern: {
    id: "japaneseModern",
    name: "Japanese Modern",
    category: ExteriorStyleCategory.Cultural,
    subcategory: ExteriorStyleSubcategory.Asian,
    colorPalette: "Natural woods, blacks, and whites",
    architecturalFeatures: [
      "Clean horizontal lines",
      "Indoor-outdoor integration",
      "Zen gardens",
      "Sliding doors",
      "Minimal ornamentation",
    ],
    materials: ["Cedar wood", "Stone", "Glass", "Steel", "Bamboo"],
    roofStyle: "Flat or slightly pitched roof",
    windowTypes: [
      "Floor-to-ceiling glass",
      "Sliding panels",
      "Geometric patterns",
    ],
    landscaping: ["Rock gardens", "Bamboo", "Moss gardens", "Water features"],
    iconName: "Zen",
    tags: ["Japanese", "Zen", "Minimal", "Natural"],
  },

  mountainLodge: {
    id: "mountainLodge",
    name: "Mountain Lodge",
    category: ExteriorStyleCategory.Specialty,
    subcategory: ExteriorStyleSubcategory.Mountain,
    colorPalette: "Rich browns and natural stone colors",
    architecturalFeatures: [
      "Dramatic rooflines",
      "Large timber beams",
      "Stone foundation",
      "Expansive windows",
      "Covered outdoor spaces",
    ],
    materials: [
      "Heavy timber",
      "Natural stone",
      "Cedar siding",
      "Metal roofing",
      "Weathered wood",
    ],
    roofStyle: "Steep pitched with large overhangs",
    windowTypes: [
      "Floor-to-ceiling windows",
      "Clerestory windows",
      "Mountain-view orientation",
    ],
    landscaping: [
      "Native mountain plants",
      "Boulder features",
      "Natural pathways",
      "Alpine gardens",
    ],
    iconName: "Mountain",
    tags: ["Rustic", "Mountain", "Natural", "Lodge"],
  },

  spanishColonial: {
    id: "spanishColonial",
    name: "Spanish Colonial",
    category: ExteriorStyleCategory.Cultural,
    subcategory: ExteriorStyleSubcategory.Mediterranean,
    colorPalette: "White stucco with terracotta and dark wood",
    architecturalFeatures: [
      "Thick stucco walls",
      "Interior courtyard",
      "Covered corridors",
      "Bell towers",
      "Carved wooden doors",
    ],
    materials: [
      "Stucco",
      "Terracotta tiles",
      "Heavy timber",
      "Wrought iron",
      "Adobe brick",
    ],
    roofStyle: "Low-pitched terracotta tile",
    windowTypes: ["Deep-set windows", "Wooden casements", "Decorative grilles"],
    landscaping: [
      "Desert plants",
      "Courtyard fountains",
      "Tiled pathways",
      "Mission gardens",
    ],
    iconName: "Mission",
    tags: ["Spanish", "Colonial", "Historic", "Mediterranean"],
    era: "16th-19th century",
  },

  artDeco: {
    id: "artDeco",
    name: "Art Deco",
    category: ExteriorStyleCategory.Traditional,
    subcategory: ExteriorStyleSubcategory.Classical,
    colorPalette: "Bold colors with metallic accents",
    architecturalFeatures: [
      "Geometric patterns",
      "Stepped facades",
      "Decorative spires",
      "Curved corners",
      "Chevron patterns",
    ],
    materials: [
      "Smooth stone",
      "Chrome accents",
      "Decorative glass",
      "Terrazzo",
      "Metallic details",
    ],
    roofStyle: "Flat with decorative parapets",
    windowTypes: [
      "Steel-framed windows",
      "Geometric patterns",
      "Glass block sections",
    ],
    landscaping: [
      "Geometric gardens",
      "Art deco sculptures",
      "Symmetrical design",
      "Period lighting",
    ],
    iconName: "Deco",
    tags: ["Art Deco", "Geometric", "Luxurious", "Historic"],
    era: "1920s-1930s",
  },

  prairieStyle: {
    id: "prairieStyle",
    name: "Prairie Style",
    category: ExteriorStyleCategory.Traditional,
    subcategory: ExteriorStyleSubcategory.Classical,
    colorPalette: "Earth tones with natural material colors",
    architecturalFeatures: [
      "Horizontal lines",
      "Low-pitched roofs",
      "Deep overhangs",
      "Integrated planters",
      "Ribbon windows",
    ],
    materials: [
      "Roman brick",
      "Wood trim",
      "Stucco",
      "Natural stone",
      "Art glass",
    ],
    roofStyle: "Low-pitched hipped roof",
    windowTypes: [
      "Horizontal window bands",
      "Art glass details",
      "Casement windows",
    ],
    landscaping: [
      "Native prairie plants",
      "Geometric gardens",
      "Integrated planters",
      "Natural materials",
    ],
    iconName: "Prairie",
    tags: ["Prairie", "Wright", "Organic", "American"],
    era: "Early 20th century",
  },

  tudorRevival: {
    id: "tudorRevival",
    name: "Tudor Revival",
    category: ExteriorStyleCategory.Traditional,
    subcategory: ExteriorStyleSubcategory.European,
    colorPalette: "Dark",
    architecturalFeatures: [
      "Tudor arches",
      "Gothic windows",
      "Decorative brackets",
      "Tiled roofs",
      "Timber framing",
    ],
    materials: [
      "Timber",
      "Tiled roofs",
      "Gothic windows",
      "Decorative brackets",
      "Timber framing",
    ],
    roofStyle: "Tiled roofs",
    windowTypes: ["Tudor windows", "Gothic windows", "Decorative grilles"],
    landscaping: [
      "English gardens",
      "Tudor roses",
      "Timber framing",
      "Tiled pathways",
    ],
    iconName: "Tudor",
    tags: ["Tudor", "Gothic", "Historic", "European"],
    era: "15th-16th century",
  },

  victorianGothic: {
    id: "victorianGothic",
    name: "Victorian Gothic",
    category: ExteriorStyleCategory.Traditional,
    subcategory: ExteriorStyleSubcategory.Victorian,
    colorPalette: "Deep, rich colors with contrasting trim",
    architecturalFeatures: [
      "Steep pitched roofs",
      "Pointed arches",
      "Ornate trim work",
      "Decorative gables",
      "Tower elements",
    ],
    materials: [
      "Stone masonry",
      "Decorative brick",
      "Cast iron",
      "Slate roofing",
      "Stained glass",
    ],
    roofStyle: "Steep pitched with multiple gables",
    windowTypes: [
      "Gothic arched windows",
      "Bay windows",
      "Rose windows",
      "Stained glass accents",
    ],
    landscaping: [
      "Victorian gardens",
      "Ornate fountains",
      "Formal pathways",
      "Period-appropriate plants",
    ],
    iconName: "Castle",
    tags: ["Gothic", "Victorian", "Ornate", "Historical"],
    era: "19th century",
  },

  brutalist: {
    id: "brutalist",
    name: "Brutalist Modern",
    category: ExteriorStyleCategory.Modern,
    subcategory: ExteriorStyleSubcategory.Contemporary,
    colorPalette: "Raw concrete grays with textural variations",
    architecturalFeatures: [
      "Exposed concrete surfaces",
      "Bold geometric shapes",
      "Repetitive angular patterns",
      "Monolithic forms",
      "Sculptural elements",
    ],
    materials: [
      "Raw concrete",
      "Exposed aggregate",
      "Steel",
      "Glass blocks",
      "Rough stone",
    ],
    roofStyle: "Flat or angular geometric forms",
    windowTypes: [
      "Geometric window patterns",
      "Deep-set windows",
      "Glass block sections",
    ],
    landscaping: [
      "Minimalist concrete planters",
      "Geometric hardscaping",
      "Angular water features",
      "Structural plantings",
    ],
    iconName: "Geometry",
    tags: ["Brutalist", "Modern", "Bold", "Concrete"],
  },

  scandinavianModern: {
    id: "scandinavianModern",
    name: "Scandinavian Modern",
    category: ExteriorStyleCategory.Cultural,
    subcategory: ExteriorStyleSubcategory.European,
    colorPalette: "Light woods with black accents and white base",
    architecturalFeatures: [
      "Simple clean lines",
      "Large windows",
      "Connection to nature",
      "Functional design",
      "Minimal decoration",
    ],
    materials: [
      "Light timber",
      "Natural stone",
      "Black metal",
      "Glass",
      "Sustainable materials",
    ],
    roofStyle: "Simple pitched or flat roof",
    windowTypes: [
      "Floor-to-ceiling windows",
      "Clerestory windows",
      "Black-framed windows",
    ],
    landscaping: [
      "Native plantings",
      "Natural stone paths",
      "Simple water features",
      "Birch trees",
    ],
    iconName: "Nordic",
    tags: ["Scandinavian", "Modern", "Minimal", "Natural"],
  },

  passiveHouse: {
    id: "passiveHouse",
    name: "Passive House",
    category: ExteriorStyleCategory.Sustainable,
    subcategory: ExteriorStyleSubcategory.PassiveHouse,
    colorPalette: "Natural materials with modern finishes",
    architecturalFeatures: [
      "Super-insulated walls",
      "Strategic window placement",
      "Airtight construction",
      "Solar orientation",
      "Heat recovery ventilation",
    ],
    materials: [
      "Triple-pane windows",
      "High-performance insulation",
      "Sustainable cladding",
      "Airtight membranes",
      "Recycled materials",
    ],
    roofStyle: "Optimized for solar panels",
    windowTypes: [
      "Triple-glazed windows",
      "South-facing orientation",
      "Thermal bridge-free frames",
    ],
    landscaping: [
      "Deciduous trees for shading",
      "Wind-blocking vegetation",
      "Drought-resistant plants",
      "Natural cooling features",
    ],
    iconName: "Energy",
    tags: ["Sustainable", "Efficient", "Modern", "Green"],
  },

  coastalHampton: {
    id: "coastalHampton",
    name: "Coastal Hampton",
    category: ExteriorStyleCategory.Specialty,
    subcategory: ExteriorStyleSubcategory.Coastal,
    colorPalette: "Crisp whites with navy and gray accents",
    architecturalFeatures: [
      "Shingle siding",
      "Multiple gables",
      "Covered porches",
      "Dormers",
      "Symmetrical design",
    ],
    materials: [
      "Cedar shingles",
      "White trim",
      "Natural stone",
      "Composite decking",
      "Metal roofing",
    ],
    roofStyle: "Multiple gabled roof with dormers",
    windowTypes: ["Multi-pane windows", "Transom windows", "French doors"],
    landscaping: [
      "Coastal grasses",
      "Hydrangeas",
      "Shell pathways",
      "Beach-friendly plants",
    ],
    iconName: "Wave",
    tags: ["Coastal", "Traditional", "Elegant", "Beach"],
  },

  midCenturyModern: {
    id: "midCenturyModern",
    name: "Mid-Century Modern",
    category: ExteriorStyleCategory.Modern,
    subcategory: ExteriorStyleSubcategory.Contemporary,
    colorPalette: "Warm woods with bold accent colors",
    architecturalFeatures: [
      "Post-and-beam construction",
      "Large windows",
      "Indoor-outdoor flow",
      "Flat planes",
      "Angular details",
    ],
    materials: ["Wood", "Glass", "Steel", "Natural stone", "Concrete blocks"],
    roofStyle: "Low-pitched or flat with overhangs",
    windowTypes: [
      "Floor-to-ceiling glass",
      "Clerestory windows",
      "Sliding glass doors",
    ],
    landscaping: [
      "Desert modernism",
      "Geometric planters",
      "Rock gardens",
      "Period-appropriate plants",
    ],
    iconName: "Atomic",
    tags: ["Mid-Century", "Modern", "Retro", "Classic"],
    era: "1945-1969",
  },

  greekRevival: {
    id: "greekRevival",
    name: "Greek Revival",
    category: ExteriorStyleCategory.Traditional,
    subcategory: ExteriorStyleSubcategory.Classical,
    colorPalette: "White with dark green or black accents",
    architecturalFeatures: [
      "Classical columns",
      "Pediments",
      "Symmetrical shape",
      "Entry portico",
      "Decorative moldings",
    ],
    materials: [
      "Painted wood",
      "Stone",
      "Brick",
      "Marble details",
      "Classical ornaments",
    ],
    roofStyle: "Low-pitched gabled or hipped roof",
    windowTypes: [
      "Multi-pane sash windows",
      "Symmetrical placement",
      "Classical surrounds",
    ],
    landscaping: [
      "Formal gardens",
      "Symmetrical layout",
      "Classical statuary",
      "Box hedges",
    ],
    iconName: "Column",
    tags: ["Classical", "Traditional", "Historic", "Formal"],
    era: "1825-1860",
  },

  swissChalet: {
    id: "swissChalet",
    name: "Swiss Chalet",
    category: ExteriorStyleCategory.Cultural,
    subcategory: ExteriorStyleSubcategory.European,
    colorPalette: "Natural wood with white trim and colorful accents",
    architecturalFeatures: [
      "Wide eaves",
      "Decorative trim",
      "Carved railings",
      "Balconies",
      "Exposed beams",
    ],
    materials: [
      "Wood siding",
      "Stone foundation",
      "Carved wood details",
      "Wood shingles",
      "Decorative brackets",
    ],
    roofStyle: "Steep pitched with large overhangs",
    windowTypes: ["Multi-pane windows", "Flower boxes", "Shuttered windows"],
    landscaping: [
      "Alpine gardens",
      "Mountain flowers",
      "Natural stone paths",
      "Traditional herbs",
    ],
    iconName: "Chalet",
    tags: ["Swiss", "Alpine", "Traditional", "Mountain"],
  },

  desertModern: {
    id: "desertModern",
    name: "Desert Modern",
    category: ExteriorStyleCategory.Modern,
    subcategory: ExteriorStyleSubcategory.Contemporary,
    colorPalette: "Desert earth tones with stark contrasts",
    architecturalFeatures: [
      "Integration with landscape",
      "Deep overhangs",
      "Natural cooling",
      "Protected courtyards",
      "Desert views",
    ],
    materials: [
      "Rammed earth",
      "Local stone",
      "Weathering steel",
      "Glass",
      "Concrete",
    ],
    roofStyle: "Flat with deep overhangs",
    windowTypes: [
      "Sun-protected glass",
      "Strategic placement",
      "Desert-rated glazing",
    ],
    landscaping: [
      "Desert natives",
      "Xeriscaping",
      "Rock gardens",
      "Cacti gardens",
    ],
    iconName: "Desert",
    tags: ["Desert", "Modern", "Sustainable", "Regional"],
  },

  gothicRevival: {
    id: "gothicRevival",
    name: "Gothic Revival",
    category: ExteriorStyleCategory.Traditional,
    subcategory: ExteriorStyleSubcategory.Classical,
    colorPalette: "Dark stone with contrasting details",
    architecturalFeatures: [
      "Pointed arches",
      "Steep gables",
      "Decorative tracery",
      "Pinnacles",
      "Flying buttresses",
    ],
    materials: [
      "Stone masonry",
      "Carved wood",
      "Stained glass",
      "Slate roofing",
      "Lead details",
    ],
    roofStyle: "Steep pitched with multiple spires",
    windowTypes: ["Pointed arch windows", "Rose windows", "Tracery windows"],
    landscaping: [
      "Medieval gardens",
      "Formal parterres",
      "Stone paths",
      "Gothic sculptures",
    ],
    iconName: "Gothic",
    tags: ["Gothic", "Historic", "Traditional", "Ornate"],
    era: "19th century",
  },

  contemporaryGlass: {
    id: "contemporaryGlass",
    name: "Contemporary Glass House",
    category: ExteriorStyleCategory.Modern,
    subcategory: ExteriorStyleSubcategory.Minimalist,
    colorPalette: "Transparent with minimal frame colors",
    architecturalFeatures: [
      "Glass walls",
      "Minimal structure",
      "Open plan",
      "Transparency",
      "Indoor-outdoor blur",
    ],
    materials: [
      "Structural glass",
      "Steel frames",
      "Minimal metals",
      "Concrete",
      "Smart glass",
    ],
    roofStyle: "Flat transparent or minimal solid",
    windowTypes: [
      "Floor-to-ceiling glass",
      "Minimal frames",
      "Smart glass systems",
    ],
    landscaping: [
      "Minimalist design",
      "Reflection pools",
      "Simple geometry",
      "Strategic privacy plants",
    ],
    iconName: "Glass",
    tags: ["Glass", "Modern", "Minimal", "Transparent"],
  },

  tropicalModern: {
    id: "tropicalModern",
    name: "Tropical Modern",
    category: ExteriorStyleCategory.Specialty,
    subcategory: ExteriorStyleSubcategory.Coastal,
    colorPalette: "Natural materials with bright accents",
    architecturalFeatures: [
      "Indoor-outdoor living",
      "Deep overhangs",
      "Natural ventilation",
      "Open pavilions",
      "Water features",
    ],
    materials: [
      "Tropical hardwoods",
      "Natural stone",
      "Glass",
      "Sustainable bamboo",
      "Local materials",
    ],
    roofStyle: "Wide overhanging with natural cooling",
    windowTypes: [
      "Louvered windows",
      "Folding glass walls",
      "Ventilation designs",
    ],
    landscaping: [
      "Tropical gardens",
      "Native species",
      "Water features",
      "Natural pools",
    ],
    iconName: "Palm",
    tags: ["Tropical", "Modern", "Sustainable", "Luxury"],
  },

  urbanIndustrialLoft: {
    id: "urbanIndustrialLoft",
    name: "Urban Industrial Loft",
    category: ExteriorStyleCategory.Modern,
    subcategory: ExteriorStyleSubcategory.Industrial,
    colorPalette: "Raw materials with industrial finishes",
    architecturalFeatures: [
      "Exposed structure",
      "Large windows",
      "Industrial elements",
      "High ceilings",
      "Open floor plans",
    ],
    materials: [
      "Exposed brick",
      "Steel frames",
      "Concrete",
      "Metal panels",
      "Industrial glass",
    ],
    roofStyle: "Flat with industrial elements",
    windowTypes: [
      "Factory windows",
      "Steel-framed glass",
      "Industrial skylights",
    ],
    landscaping: [
      "Urban gardens",
      "Container plants",
      "Rooftop gardens",
      "Industrial planters",
    ],
    iconName: "Factory",
    tags: ["Industrial", "Urban", "Modern", "Loft"],
  },
};

// Helper functions
export function getExteriorStylesByCategory() {
  const groupedStyles: Record<
    ExteriorStyleCategory,
    Record<ExteriorStyleSubcategory, ExteriorStyleDetails[]>
  > = {} as Record<
    ExteriorStyleCategory,
    Record<ExteriorStyleSubcategory, ExteriorStyleDetails[]>
  >;

  Object.values(exteriorStyleData).forEach((style) => {
    if (!groupedStyles[style.category]) {
      groupedStyles[style.category] = {} as Record<
        ExteriorStyleSubcategory,
        ExteriorStyleDetails[]
      >;
    }
    if (!groupedStyles[style.category][style.subcategory]) {
      groupedStyles[style.category][style.subcategory] = [];
    }
    groupedStyles[style.category][style.subcategory].push(style);
  });

  return groupedStyles;
}

export function groupExteriorStyleOptions() {
  const groupedStyles = getExteriorStylesByCategory();
  const formattedOptions: Record<
    string,
    { value: string; label: string; isSubcategoryLabel?: boolean }[]
  > = {};

  Object.entries(groupedStyles).forEach(([category, subcategories]) => {
    formattedOptions[category] = [];

    Object.entries(subcategories).forEach(([subcategory, styles]) => {
      formattedOptions[category].push({
        value: `subcategory-${subcategory}`,
        label: subcategory,
        isSubcategoryLabel: true,
      });

      const styleOptions = styles.map((style) => ({
        value: style.id,
        label: style.name,
      }));

      formattedOptions[category].push(...styleOptions);
    });
  });

  return formattedOptions;
}

export function getExteriorStyleById(
  id: string
): ExteriorStyleDetails | undefined {
  return exteriorStyleData[id];
}
