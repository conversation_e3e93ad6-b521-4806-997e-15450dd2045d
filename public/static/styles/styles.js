const roomData = {
  "Living Room": {
    room: "Living room",
    furnitures: "Couch, sofa, coffee table, bookshelves, armchair, area rug",
  },
  "Home Theater": {
    room: "Home theater",
    furnitures:
      "Sofa, media console, sound system, media storage, wall-mounted TV",
  },
  Sunroom: {
    room: "Sunroom",
    furnitures: "Sofa, coffee table, area rug, plants, throw pillows",
  },
  Porch: {
    room: "Porch",
    furnitures:
      "Outdoor sofa, coffee table, area rug, plants, outdoor lighting",
  },
  Nursery: {
    room: "Nursery",
    furnitures: "Crib, changing table, rocking chair, bookshelves, diaper pail",
  },
  "Game Room": {
    room: "Game room",
    furnitures:
      "Game table, bar stools, pool table, foosball table, darts board",
  },
  "Laundry Room": {
    room: "Laundry room",
    furnitures:
      "Washer and dryer, shelving unit, hamper, ironing board, laundry basket",
  },
  "Home Office": {
    room: "Home office",
    furnitures:
      "Desk, office chair, file cabinet, bookshelves, desk lamp, area rug",
  },
  Guestroom: {
    room: "Guest room",
    furnitures: "Bed, nightstand, dresser, wardrobe, bedside lamp, area rug",
  },
  "Outdoor Kitchen": {
    room: "Outdoor kitchen",
    furnitures: "Grill, counter stools, outdoor refrigerator, sink, cabinets",
  },
  Garage: {
    room: "Garage",
    furnitures: "Workbench, shelves, tool storage, bike rack",
  },
  Bedroom: {
    room: "Bedroom",
    furnitures: "Bed, nightstand, dresser, wardrobe, bedside lamp, area rug",
  },
  Kitchen: {
    room: "Kitchen",
    furnitures:
      "Kitchen table, chairs, bar stools, counter stools, refrigerator, stove, microwave, sink",
  },
  "Dining Room": {
    room: "Dining room",
    furnitures: "Dining table, chairs, buffet, china cabinet, area rug",
  },
  Office: {
    room: "Office",
    furnitures:
      "Desk, office chair, file cabinet, bookshelves, desk lamp, area rug",
  },
  Library: {
    room: "Library",
    furnitures: "Bookshelves, reading chair, rug, desk, desk lamp",
  },
  Bathroom: {
    room: "Bathroom",
    furnitures: "Vanity, shower curtain, towel rack, mirror, bathmat",
  },
  Homegym: {
    room: "Home gym",
    furnitures: "Exercise equipment, mats, wall mirror, water bottle holder",
  },
  "Wine Cellar": {
    room: "Wine cellar",
    furnitures:
      "Wine racks, wine fridge, wine glasses, bar stools, wine opener",
  },
  Playroom: {
    room: "Playroom",
    furnitures: "Play structure, swings, slides, climbing wall, sandbox",
  },
  "Garden Shed": {
    room: "Garden shed",
    furnitures: "Workbench, shelves, tool storage, watering cans, garden tools",
  },
  "Art Studio": {
    room: "Art studio",
    furnitures:
      "Art desk, drafting table, easel, paint palette, canvas storage",
  },
  "Music Room": {
    room: "Music room",
    furnitures: "Piano, music stand, instruments, music books, music storage",
  },
  "Pool house": {
    room: "Pool House",
    furnitures: "Wet bar, pool table, outdoor shower, pool toys, beach towels",
  },
  Sauna: {
    room: "Sauna",
    furnitures:
      "Sauna stove, sauna benches, sauna bucket, sauna thermometer, sauna heater",
  },
  "Guest cottage": {
    room: "Guest Cottage",
    furnitures: "Bed, nightstand, dresser, wardrobe, bedside lamp, area rug",
  },
  "Tiki Bar": {
    room: "Tiki Bar",
    furnitures:
      "Wet bar, bar stools, tiki torches, outdoor lighting, tropical decor",
  },
  "Home Spa": {
    room: "Home Spa",
    furnitures: "Massage table, spa tub, spa chair, spa storage, spa lighting",
  },
  "Craft Room": {
    room: "Craft Room",
    furnitures:
      "Art desk, drafting table, easel, paint palette, canvas storage",
  },
  "Sewing Room": {
    room: "Sewing Room",
    furnitures:
      "Sewing machine, cutting table, ironing board, fabric storage, sewing basket",
  },
  "Music Studio": {
    room: "Music Studio",
    furnitures: "Piano, music stand, instruments, music books, music storage",
  },
  "Yoga Studio": {
    room: "Yoga Studio",
    furnitures:
      "Yoga mats, yoga blocks, yoga straps, meditation cushions, yoga blankets",
  },
  "Reading Nook": {
    room: "Reading Nook",
    furnitures:
      "Reading chair, ottoman, bookshelves, floor lamp, throw pillows",
  },
  "Meditation Room": {
    room: "Meditation Room",
    furnitures:
      "Meditation cushion, incense burner, meditation beads, meditation music, meditation candle",
  },
  Bar: {
    room: "Bar",
    furnitures: "Wet bar, bar stools, bar fridge, bar glasses, bar accessories",
  },
  Garden: {
    room: "Garden",
    furnitures:
      "Garden tools, watering cans, garden hose, garden gloves, garden storage",
  },
};

const stylesData = {
  "Art Deco": {
    style: "Art Deco",
    colorPalette: "Bold, geometric shapes, metallic accents",
    feeling: "Glamorous, opulent, luxurious",
    materials: "Glass, metal, marble, mirrors, lacquer",
    furnitureTypes: "Geometric, angular, ornate",
  },
  "Art Nouveau": {
    style: "Art Nouveau",
    colorPalette: "Curves, organic forms, pastel colors",
    feeling: "Elegant, romantic, decorative",
    materials: "Glass, metal, ceramic, stone, fabric",
    furnitureTypes: "Curved, organic, decorative",
  },
  Bohemian: {
    style: "Bohemian",
    colorPalette: "Bright, bold, earthy (orange, red, yellow, green)",
    feeling: "Free-spirited, artistic, eclectic",
    materials: "Wood, metal, stone, ceramic, fabric",
    furnitureTypes: "Vintage, handcrafted, mismatched",
  },
  Coastal: {
    style: "Coastal",
    colorPalette: "Pale blue, sandy beige, white",
    feeling: "Relaxed, airy, beachy",
    materials: "Wood, wicker, jute, seashells, coral",
    furnitureTypes: "Rustic, distressed, woven",
  },
  Contemporary: {
    style: "Contemporary",
    colorPalette: "Neutral tones, pops of bright color",
    feeling: "Sleek, modern, minimalist",
    materials: "Glass, metal, concrete, acrylic, natural woods",
    furnitureTypes: "Geometric, streamlined, minimal",
  },
  Cyberpunk: {
    style: "Cyberpunk",
    colorPalette: "Neon colors, metallic accents, pops of bright color",
    feeling: "Futuristic, edgy, high-tech",
    materials: "Metal, acrylic, holographic displays, neon lights",
    furnitureTypes: "Sleek, modern, high-tech",
  },
  Eclectic: {
    style: "Eclectic",
    colorPalette:
      "Varied, mix of patterns and textures (floral, geometric, abstract)",
    feeling: "Playful, artistic, spontaneous",
    materials: "Wood, metal, stone, ceramic, fabric",
    furnitureTypes: "Vintage, repurposed, mismatched",
  },
  Farmhouse: {
    style: "Farmhouse",
    colorPalette:
      "Neutral tones (white, beige, gray), pops of pastel (pink, blue, green)",
    feeling: "Homey, cozy, rustic",
    materials: "Wood, metal, stone, ceramic, fabric",
    furnitureTypes: "Distressed, reclaimed, handmade",
  },
  "French Country": {
    style: "French Country",
    colorPalette: "Muted, earthy tones, pops of blue and yellow",
    feeling: "Rustic, romantic, charming",
    materials: "Wood, stone, metal, ceramic, fabric",
    furnitureTypes: "Rustic, carved, ornate",
  },
  Gothic: {
    style: "Gothic",
    colorPalette: "velvet, bordeux, black, pops of metallic",
    feeling: "Dramatic, mysterious, elegant",
    materials: "Stone, wood, metal, velvet, leather",
    furnitureTypes: "Ornate, carved, grand",
  },
  HollywoodGlamour: {
    style: "Hollywood Glamour",
    colorPalette: "Bold, metallic accents, pops of bright color",
    feeling: "Glamorous, sophisticated, extravagant",
    materials: "Glass, metal, marble, mirrors, lacquer",
    furnitureTypes: "Ornate, glamorous, bold",
  },
  IndustrialChic: {
    style: "Industrial Chic",
    colorPalette:
      "Neutral tones (black, white, gray), pops of metallic (silver, copper, brass)",
    feeling: "Edgy, urban, sophisticated, raw",
    materials: "Concrete, metal, brick, wood, glass",
    furnitureTypes: "Industrial, reclaimed, chic, utillitarian",
  },
  Japanese: {
    style: "Japanese",
    colorPalette: "Neutral tones, pops of red, yellow, and green",
    feeling: "Peaceful, minimal, Zen",
    materials: "Wood, stone, metal, ceramic, fabric",
    furnitureTypes: "Minimal, streamlined, natural",
  },
  Mediterranean: {
    style: "Mediterranean",
    colorPalette: "Warm, earthy tones, pops of blue and yellow",
    feeling: "Relaxed, elegant, rustic",
    materials: "Terracotta, stone, tile, wood, metal",
    furnitureTypes: "Rustic, carved, ornate",
  },
  Midcenturymodern: {
    style: "Mid-century modern",
    colorPalette:
      "Bold, primary colors (red, yellow, blue), pops of pastel (pink, green, orange)",
    feeling: "Retro, futuristic, sleek",
    materials: "Wood, metal, plastic, fiberglass, vinyl",
    furnitureTypes: "Geometric, angular, minimal",
  },
  Minimalist: {
    style: "Minimalist",
    colorPalette: "Neutral tones, minimal ornamentation",
    feeling: "Simple, functional, uncluttered",
    materials: "Glass, metal, concrete, acrylic, natural woods",
    furnitureTypes: "Sleek, minimal, functional",
  },
  Modern: {
    style: "Modern",
    colorPalette:
      "Monochromatic (all shades of one color), neutral tones (black, white, gray), pops of bright color (red, yellow, blue)",
    feeling: "Sleek, minimalist, clean",
    materials:
      "Glass, steel, concrete, acrylic, natural woods (bamboo, maple, walnut)",
    furnitureTypes: "Geometric, streamlined, minimal",
  },
  Moroccan: {
    style: "Moroccan",
    colorPalette: "Rich, warm tones, intricate patterns",
    feeling: "Exotic, colorful, bold",
    materials: "Wood, metal, stone, ceramic, fabric",
    furnitureTypes: "Intricately carved, ornate, colorful",
  },
  Nautical: {
    style: "Nautical",
    colorPalette: "Navy blue, white, pops of red",
    feeling: "Relaxed, beachy, nautical",
    materials: "Wood, metal, stone, ceramic, fabric",
    furnitureTypes: "Nautical",
  },
  PopArt: {
    style: "Pop Art",
    colorPalette: "Bright, bold colors, graphic patterns",
    feeling: "Playful, colorful, energetic",
    materials: "Plastic, metal, wood, fabric, acrylic",
    furnitureTypes: "Geometric, colorful, bold",
  },
  Retro: {
    style: "Retro",
    colorPalette: "Bright, bold colors, geometric patterns",
    feeling: "Fun, playful, nostalgiac",
    materials: "Plastic, metal, wood, vinyl, fabric",
    furnitureTypes: "Geometric, angular, bold",
  },
  Rustic: {
    style: "Rustic",
    colorPalette:
      "Neutral tones (brown, beige, white), pops of earthy color (green, red, orange)",
    feeling: "Cozy, warm, natural",
    materials: "Wood, stone, metal, natural fibers",
    furnitureTypes: "Distressed, handmade, reclaimed",
  },
  Scandinavian: {
    style: "Scandinavian",
    colorPalette:
      "Neutral tones (white, beige, gray), pops of pastel (pink, blue, green)",
    feeling: "Clean, simple, minimalist",
    materials: "Wood, metal, stone, ceramic, fabric",
    furnitureTypes: "Sleek, minimal, functional",
  },
  ShabbyChic: {
    style: "Shabby Chic",
    colorPalette: "Muted, pastel colors, distressed finishes",
    feeling: "Romantic, vintage, cozy",
    materials: "Wood, metal, stone, ceramic, fabric",
    furnitureTypes: "Distressed, vintage, romantic",
  },
  Southwestern: {
    style: "Southwestern",
    colorPalette: "Earthy tones (orange, red, yellow), pops of turquoise",
    feeling: "Warm, rustic, colorful",
    materials: "Wood, stone, metal, ceramic, fabric",
    furnitureTypes: "Rustic, handcrafted, colorful",
  },
  Tropical: {
    style: "Tropical",
    colorPalette: "Bright, bold, tropical (yellow, green, pink, orange)",
    feeling: "Bright, bold, tropical",
    materials: "Wood, metal, stone, ceramic, fabric",
    furnitureTypes: "Bright, bold, tropical",
  },
  Victorian: {
    style: "Victorian",
    colorPalette: "Muted, pastel colors, pops of deep red",
    feeling: "Elegant, ornate, sophisticated",
    materials: "Wood, metal, stone, ceramic, fabric",
    furnitureTypes: "Ornate, carved, grand",
  },
  Vintage: {
    style: "Vintage",
    colorPalette: "Varied, mix of patterns and textures from past decades",
    feeling: "Nostalgic, antique, retro",
    materials: "Wood, metal, stone, ceramic, fabric",
    furnitureTypes: "Vintage, antique, retro",
  },
  Western: {
    style: "Western",
    colorPalette:
      "Earthy tones (brown, beige, green), pops of red and turquoise",
    feeling: "Rustic, natural, laid-back",
    materials: "Wood, stone, metal, ceramic, fabric",
    furnitureTypes: "Rustic, distressed, handmade",
  },
};
const buildingData = {
  House: {
    room: "House",
    category: "Residential",
  },
  "Apartment Building": {
    name: "Apartment Building",
    category: "Residential",
  },
  Condominium: {
    name: "Condominium",
    category: "Residential",
  },
  Townhouse: {
    name: "Townhouse",
    category: "Residential",
  },
  Duplex: {
    name: "Duplex",
    category: "Residential",
  },
  Penthouse: {
    name: "Penthouse",
    category: "Residential",
  },
  Mansion: {
    name: "Mansion",
    category: "Residential",
  },
  Bungalow: {
    name: "Bungalow",
    category: "Residential",
  },
  Cottage: {
    name: "Cottage",
    category: "Residential",
  },
  Villa: {
    name: "Villa",
    category: "Residential",
  },
  Ranch: {
    name: "Ranch",
    category: "Agricultural",
  },
  "Mobile Home": {
    name: "Mobile Home",
    category: "Residential",
  },
  "Tiny House": {
    name: "Tiny House",
    category: "Residential",
  },
  "Tudor House": {
    name: "Tudor House",
    category: "Residential",
  },
  "Colonial House": {
    name: "Colonial House",
    category: "Residential",
  },
  "Victorian House": {
    name: "Victorian House",
    category: "Residential",
  },
  "Cape Cod House": {
    name: "Cape Cod House",
    category: "Residential",
  },
  Farmhouse: {
    name: "Farmhouse",
    category: "Agricultural",
  },
  "High-rise Building": {
    name: "High-rise Building",
    category: "Commercial",
  },
  Skyscraper: {
    name: "Skyscraper",
    category: "Commercial",
  },
  "Office Building": {
    name: "Office Building",
    category: "Commercial",
  },
  "Commercial Building": {
    name: "Commercial Building",
    category: "Commercial",
  },
  Warehouse: {
    name: "Warehouse",
    category: "Industrial",
  },
  "Retail Store": {
    name: "Retail Store",
    category: "Commercial",
  },
  "Shopping Mall": {
    name: "Shopping Mall",
    category: "Commercial",
  },
  Hotel: {
    name: "Hotel",
    category: "Hospitality",
  },
  Hospital: {
    name: "Hospital",
    category: "Healthcare",
  },
  School: {
    name: "School",
    category: "Educational",
  },
  Library: {
    name: "Library",
    category: "Educational",
  },
  Museum: {
    name: "Museum",
    category: "Cultural",
  },
  Church: {
    name: "Church",
    category: "Religious",
  },
  Mosque: {
    name: "Mosque",
    category: "Religious",
  },
  Synagogue: {
    name: "Synagogue",
    category: "Religious",
  },
  Temple: {
    name: "Temple",
    category: "Religious",
  },
  "Community Center": {
    name: "Community Center",
    category: "Community",
  },
  Gymnasium: {
    name: "Gymnasium",
    category: "Sports",
  },
  "Sports Arena": {
    name: "Sports Arena",
    category: "Sports",
  },
  Factory: {
    name: "Factory",
    category: "Industrial",
  },
  Airport: {
    name: "Airport",
    category: "Infrastructure",
  },
  "Train Station": {
    name: "Train Station",
    category: "Infrastructure",
  },
  "Bus Terminal": {
    name: "Bus Terminal",
    category: "Infrastructure",
  },
  Restaurant: {
    name: "Restaurant",
    category: "Hospitality",
  },
  Cinema: {
    name: "Cinema",
    category: "Entertainment",
  },
};

module.exports = { stylesData, roomData, buildingData };
