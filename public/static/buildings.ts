export interface BuildingDetails {
  id: string;
  name: string;
  category: BuildingCategory;
  subcategory: BuildingSubcategory;
  description: string;
  keyFeatures: string[]; // Main visual/architectural features
  materials: string[]; // Common building materials
  style: string[]; // Architectural styles that commonly apply
  iconName?: string;
  tags?: string[];
}

export enum BuildingCategory {
  Residential = "Residential Buildings",
  Commercial = "Commercial Buildings",
  Cultural = "Cultural & Community",
  Specialty = "Specialty Buildings",
}

export enum BuildingSubcategory {
  // Residential
  SingleFamily = "Single Family Homes",
  MultiFamily = "Multi-Family Buildings",

  // Commercial
  Office = "Office Buildings",
  Retail = "Retail & Shopping",

  // Cultural
  Religious = "Religious Buildings",
  Community = "Community Buildings",

  // Specialty
  Recreational = "Recreational",
  Educational = "Educational",
  Cultural = "Cultural Buildings",
  Industrial = "Industrial Buildings",
  Transportation = "Transportation Buildings",
  Healthcare = "Healthcare Facilities",
  Commercial = "Commercial Buildings",
}

export const buildingData: Record<string, BuildingDetails> = {
  house: {
    id: "house",
    name: "Single Family House",
    category: BuildingCategory.Residential,
    subcategory: BuildingSubcategory.SingleFamily,
    description: "A standalone residential building designed for one family",
    keyFeatures: [
      "Private entrance",
      "Front yard",
      "Backyard",
      "Driveway",
      "Pitched roof",
    ],
    materials: [
      "Brick",
      "Wood siding",
      "Stone accents",
      "Asphalt shingles",
      "Glass windows",
    ],
    style: ["Modern", "Traditional", "Colonial", "Contemporary", "Craftsman"],
    iconName: "House",
    tags: ["Residential", "Private", "Family"],
  },

  apartmentBuilding: {
    id: "apartmentBuilding",
    name: "Apartment Building",
    category: BuildingCategory.Residential,
    subcategory: BuildingSubcategory.MultiFamily,
    description: "A multi-story building containing multiple residential units",
    keyFeatures: [
      "Multiple floors",
      "Balconies",
      "Common entrance",
      "Parking area",
      "Shared facilities",
    ],
    materials: ["Concrete", "Glass", "Steel", "Brick facade", "Metal details"],
    style: ["Modern", "Urban", "Contemporary", "High-rise", "Mid-rise"],
    iconName: "Building",
    tags: ["Multi-family", "Urban", "Residential"],
  },

  officeBuilding: {
    id: "officeBuilding",
    name: "Office Building",
    category: BuildingCategory.Commercial,
    subcategory: BuildingSubcategory.Office,
    description:
      "A commercial building designed for business and professional use",
    keyFeatures: [
      "Glass facade",
      "Multiple floors",
      "Professional entrance",
      "Large windows",
      "Business lobby",
    ],
    materials: [
      "Glass curtain walls",
      "Steel frame",
      "Concrete",
      "Metal panels",
      "Stone accents",
    ],
    style: ["Modern", "Corporate", "Contemporary", "High-tech", "Professional"],
    iconName: "Office",
    tags: ["Commercial", "Business", "Professional"],
  },

  church: {
    id: "church",
    name: "Church",
    category: BuildingCategory.Cultural,
    subcategory: BuildingSubcategory.Religious,
    description: "A religious building for Christian worship",
    keyFeatures: [
      "Steeple/spire",
      "Large doors",
      "Stained glass windows",
      "Bell tower",
      "Religious symbols",
    ],
    materials: [
      "Stone",
      "Brick",
      "Stained glass",
      "Wood beams",
      "Metal roofing",
    ],
    style: ["Gothic", "Traditional", "Modern", "Classical", "Contemporary"],
    iconName: "Church",
    tags: ["Religious", "Worship", "Community"],
  },

  courthouse: {
    id: "courthouse",
    name: "Courthouse",
    category: BuildingCategory.Cultural,
    subcategory: BuildingSubcategory.Community,
    description: "A civic building for legal proceedings and administration",
    keyFeatures: [
      "Grand entrance",
      "Columned facade",
      "Symmetrical design",
      "Dome or cupola",
      "Ceremonial steps",
    ],
    materials: [
      "Limestone",
      "Granite",
      "Marble",
      "Bronze details",
      "Hardwood interiors",
    ],
    style: ["Neoclassical", "Beaux-Arts", "Renaissance Revival"],
    iconName: "Scale",
    tags: ["Civic", "Government", "Justice", "Public"],
  },

  library: {
    id: "library",
    name: "Public Library",
    category: BuildingCategory.Cultural,
    subcategory: BuildingSubcategory.Educational,
    description: "A public building housing collections of books and media",
    keyFeatures: [
      "Reading rooms",
      "Study spaces",
      "Grand entrance",
      "Large windows",
      "Open floor plan",
    ],
    materials: [
      "Brick",
      "Stone",
      "Glass curtain walls",
      "Wood paneling",
      "Acoustic materials",
    ],
    style: ["Classical", "Modern", "Contemporary", "Victorian"],
    iconName: "Book",
    tags: ["Educational", "Public", "Cultural", "Community"],
  },

  museum: {
    id: "museum",
    name: "Museum",
    category: BuildingCategory.Cultural,
    subcategory: BuildingSubcategory.Cultural,
    description: "A building designed to house and display cultural artifacts",
    keyFeatures: [
      "Exhibition spaces",
      "High ceilings",
      "Controlled lighting",
      "Security features",
      "Climate control",
    ],
    materials: [
      "Concrete",
      "Steel",
      "Glass",
      "Stone",
      "Specialized display materials",
    ],
    style: ["Contemporary", "Modernist", "Classical", "Deconstructivist"],
    iconName: "Art",
    tags: ["Cultural", "Arts", "Exhibition", "Public"],
  },

  operaHouse: {
    id: "operaHouse",
    name: "Opera House",
    category: BuildingCategory.Cultural,
    subcategory: BuildingSubcategory.Cultural,
    description: "A grand performance venue for opera and classical music",
    keyFeatures: [
      "Grand foyer",
      "Acoustic design",
      "Orchestra pit",
      "Ornate decoration",
      "Dramatic facade",
    ],
    materials: [
      "Marble",
      "Gold leaf",
      "Ornate plasterwork",
      "Hardwood",
      "Acoustic panels",
    ],
    style: ["Baroque", "Neoclassical", "Contemporary"],
    iconName: "Music",
    tags: ["Performance", "Cultural", "Arts", "Acoustic"],
  },

  skyscraper: {
    id: "skyscraper",
    name: "Modern Skyscraper",
    category: BuildingCategory.Commercial,
    subcategory: BuildingSubcategory.Office,
    description:
      "A high-rise building featuring cutting-edge architectural design",
    keyFeatures: [
      "Curtain wall facade",
      "High-speed elevators",
      "Sky lobby",
      "Green technology",
      "Structural core",
    ],
    materials: [
      "Smart glass",
      "Steel frame",
      "Aluminum panels",
      "Composite materials",
      "High-strength concrete",
    ],
    style: ["Contemporary", "High-tech", "Sustainable"],
    iconName: "Building",
    tags: ["Commercial", "Urban", "Modern", "High-rise"],
  },

  stadium: {
    id: "stadium",
    name: "Sports Stadium",
    category: BuildingCategory.Specialty,
    subcategory: BuildingSubcategory.Recreational,
    description: "A large-scale venue for sporting events and entertainment",
    keyFeatures: [
      "Retractable roof",
      "Tiered seating",
      "VIP boxes",
      "Large displays",
      "Multiple entrances",
    ],
    materials: [
      "Structural steel",
      "Reinforced concrete",
      "ETFE panels",
      "Composite decking",
      "Safety glass",
    ],
    style: ["Modern", "High-tech", "Contemporary"],
    iconName: "Stadium",
    tags: ["Sports", "Entertainment", "Public", "Large-scale"],
  },

  university: {
    id: "university",
    name: "University Campus Building",
    category: BuildingCategory.Specialty,
    subcategory: BuildingSubcategory.Educational,
    description: "An academic building designed for higher education",
    keyFeatures: [
      "Lecture halls",
      "Research facilities",
      "Collaborative spaces",
      "Technology integration",
      "Sustainable design",
    ],
    materials: [
      "Brick facade",
      "Curtain walls",
      "Stone accents",
      "Smart glass",
      "Sustainable materials",
    ],
    style: ["Collegiate Gothic", "Modern", "Contemporary"],
    iconName: "Education",
    tags: ["Educational", "Academic", "Research", "Institutional"],
  },

  hospital: {
    id: "hospital",
    name: "Modern Hospital",
    category: BuildingCategory.Specialty,
    subcategory: BuildingSubcategory.Community,
    description: "A healthcare facility with advanced medical capabilities",
    keyFeatures: [
      "Emergency department",
      "Patient rooms",
      "Operating theaters",
      "Diagnostic centers",
      "Healing gardens",
    ],
    materials: [
      "Antimicrobial surfaces",
      "Medical-grade materials",
      "Smart glass",
      "Acoustic panels",
      "Sustainable materials",
    ],
    style: ["Contemporary", "Functional", "Sustainable"],
    iconName: "Medical",
    tags: ["Healthcare", "Medical", "Community", "Essential"],
  },

  shoppingMall: {
    id: "shoppingMall",
    name: "Shopping Mall",
    category: BuildingCategory.Commercial,
    subcategory: BuildingSubcategory.Retail,
    description: "A large retail complex with multiple stores and amenities",
    keyFeatures: [
      "Atrium space",
      "Food court",
      "Retail units",
      "Parking structure",
      "Entertainment zones",
    ],
    materials: [
      "Structural glass",
      "Steel frame",
      "Polished stone",
      "Metal panels",
      "Decorative elements",
    ],
    style: ["Contemporary", "Modern", "Mixed-use"],
    iconName: "Shop",
    tags: ["Retail", "Commercial", "Entertainment", "Public"],
  },

  dataCenter: {
    id: "dataCenter",
    name: "Data Center",
    category: BuildingCategory.Specialty,
    subcategory: BuildingSubcategory.Commercial,
    description: "A specialized facility for housing computer systems",
    keyFeatures: [
      "Security systems",
      "Cooling infrastructure",
      "Power backup",
      "Server rooms",
      "Network infrastructure",
    ],
    materials: [
      "Reinforced concrete",
      "Security panels",
      "Raised flooring",
      "Technical materials",
      "Fire-resistant materials",
    ],
    style: ["Technical", "Industrial", "Secure"],
    iconName: "Server",
    tags: ["Technology", "Infrastructure", "Security", "Industrial"],
  },

  mosque: {
    id: "mosque",
    name: "Mosque",
    category: BuildingCategory.Cultural,
    subcategory: BuildingSubcategory.Religious,
    description: "An Islamic place of worship with traditional architecture",
    keyFeatures: [
      "Minaret",
      "Prayer hall",
      "Dome",
      "Ablution areas",
      "Geometric patterns",
    ],
    materials: [
      "Marble",
      "Ceramic tiles",
      "Calligraphy",
      "Stone work",
      "Decorative elements",
    ],
    style: ["Islamic", "Traditional", "Contemporary Islamic"],
    iconName: "Mosque",
    tags: ["Religious", "Islamic", "Cultural", "Spiritual"],
  },

  synagogue: {
    id: "synagogue",
    name: "Synagogue",
    category: BuildingCategory.Cultural,
    subcategory: BuildingSubcategory.Religious,
    description: "A Jewish place of worship and community center",
    keyFeatures: [
      "Sanctuary",
      "Bimah",
      "Ark",
      "Community spaces",
      "Memorial features",
    ],
    materials: [
      "Stone",
      "Stained glass",
      "Wood details",
      "Decorative metals",
      "Traditional elements",
    ],
    style: ["Traditional", "Modern", "Contemporary"],
    iconName: "Star",
    tags: ["Religious", "Jewish", "Cultural", "Community"],
  },

  trainStation: {
    id: "trainStation",
    name: "Train Station",
    category: BuildingCategory.Specialty,
    subcategory: BuildingSubcategory.Transportation,
    description: "A major transportation hub for rail services",
    keyFeatures: [
      "Grand concourse",
      "Platform access",
      "Ticket halls",
      "Retail spaces",
      "Information systems",
    ],
    materials: [
      "Steel structure",
      "Glass roof",
      "Stone facade",
      "Metal framework",
      "Durable flooring",
    ],
    style: ["Victorian", "Modern", "Industrial", "Contemporary"],
    iconName: "Train",
    tags: ["Transportation", "Public", "Infrastructure"],
  },

  airport: {
    id: "airport",
    name: "Airport Terminal",
    category: BuildingCategory.Specialty,
    subcategory: BuildingSubcategory.Transportation,
    description: "Modern aviation terminal with passenger facilities",
    keyFeatures: [
      "Check-in halls",
      "Security zones",
      "Departure lounges",
      "Baggage handling",
      "Retail concourse",
    ],
    materials: [
      "Structural steel",
      "Curtain walls",
      "Composite panels",
      "Security glass",
      "Acoustic materials",
    ],
    style: ["Contemporary", "High-tech", "Modern"],
    iconName: "Plane",
    tags: ["Aviation", "Transportation", "International"],
  },

  researchLab: {
    id: "researchLab",
    name: "Research Laboratory",
    category: BuildingCategory.Specialty,
    subcategory: BuildingSubcategory.Educational,
    description: "Advanced research facility with specialized equipment",
    keyFeatures: [
      "Clean rooms",
      "Laboratory spaces",
      "Equipment rooms",
      "Safety systems",
      "Environmental controls",
    ],
    materials: [
      "Clean-room materials",
      "Chemical-resistant surfaces",
      "Specialized glass",
      "Anti-static flooring",
      "Filtered air systems",
    ],
    style: ["Technical", "Modern", "Functional"],
    iconName: "Lab",
    tags: ["Research", "Scientific", "Technical"],
  },

  industrialWarehouse: {
    id: "industrialWarehouse",
    name: "Industrial Warehouse",
    category: BuildingCategory.Commercial,
    subcategory: BuildingSubcategory.Industrial,
    description: "Large-scale storage and distribution facility",
    keyFeatures: [
      "High bay storage",
      "Loading docks",
      "Clear span structure",
      "Climate control",
      "Security systems",
    ],
    materials: [
      "Steel frame",
      "Metal cladding",
      "Concrete floor",
      "Insulated panels",
      "Industrial doors",
    ],
    style: ["Industrial", "Functional", "Modern"],
    iconName: "Warehouse",
    tags: ["Industrial", "Storage", "Distribution"],
  },

  performingArtsCenter: {
    id: "performingArtsCenter",
    name: "Performing Arts Center",
    category: BuildingCategory.Cultural,
    subcategory: BuildingSubcategory.Cultural,
    description: "Multi-venue facility for various performing arts",
    keyFeatures: [
      "Multiple theaters",
      "Rehearsal spaces",
      "Public lobbies",
      "Backstage facilities",
      "Acoustic design",
    ],
    materials: [
      "Acoustic panels",
      "Hardwood",
      "Sound-absorbing materials",
      "Decorative finishes",
      "Stage equipment",
    ],
    style: ["Contemporary", "Modern", "Classical"],
    iconName: "Theater",
    tags: ["Arts", "Performance", "Cultural", "Entertainment"],
  },

  firehouse: {
    id: "firehouse",
    name: "Fire Station",
    category: BuildingCategory.Specialty,
    subcategory: BuildingSubcategory.Community,
    description: "Emergency response facility for firefighting services",
    keyFeatures: [
      "Apparatus bays",
      "Living quarters",
      "Training facilities",
      "Emergency systems",
      "Quick-response design",
    ],
    materials: [
      "Reinforced concrete",
      "Durable metals",
      "Fire-resistant materials",
      "Heavy-duty flooring",
      "Industrial grade finishes",
    ],
    style: ["Functional", "Municipal", "Contemporary"],
    iconName: "Fire",
    tags: ["Emergency", "Municipal", "Community", "Safety"],
  },

  sportingComplex: {
    id: "sportingComplex",
    name: "Sports Complex",
    category: BuildingCategory.Specialty,
    subcategory: BuildingSubcategory.Recreational,
    description: "Multi-purpose sports and recreation facility",
    keyFeatures: [
      "Indoor courts",
      "Swimming pools",
      "Fitness areas",
      "Spectator seating",
      "Training facilities",
    ],
    materials: [
      "Sports flooring",
      "Acoustic treatments",
      "Durable finishes",
      "Safety glass",
      "Water-resistant materials",
    ],
    style: ["Modern", "Functional", "Contemporary"],
    iconName: "Sports",
    tags: ["Sports", "Recreation", "Community", "Fitness"],
  },

  rehabilitationCenter: {
    id: "rehabilitationCenter",
    name: "Rehabilitation Center",
    category: BuildingCategory.Specialty,
    subcategory: BuildingSubcategory.Healthcare,
    description: "Specialized facility for physical rehabilitation and therapy",
    keyFeatures: [
      "Therapy spaces",
      "Exercise areas",
      "Treatment rooms",
      "Accessible design",
      "Recovery facilities",
    ],
    materials: [
      "Non-slip surfaces",
      "Antimicrobial materials",
      "Supportive equipment",
      "Accessible fixtures",
      "Therapeutic elements",
    ],
    style: ["Healthcare", "Modern", "Accessible"],
    iconName: "Health",
    tags: ["Healthcare", "Rehabilitation", "Medical", "Therapy"],
  },

  conventionCenter: {
    id: "conventionCenter",
    name: "Convention Center",
    category: BuildingCategory.Cultural,
    subcategory: BuildingSubcategory.Cultural,
    description: "A large-scale venue for hosting conventions and conferences",
    keyFeatures: [
      "Exhibition halls",
      "Meeting rooms",
      "Networking areas",
      "Food courts",
      "VIP lounges",
    ],
    materials: [
      "Structural steel",
      "Curtain walls",
      "Composite panels",
      "Security glass",
      "Acoustic materials",
    ],
    style: ["Contemporary", "High-tech", "Modern"],
    iconName: "Convention",
    tags: ["Cultural", "Arts", "Entertainment", "Public"],
  },

  mediterraneanVilla: {
    id: "mediterraneanVilla",
    name: "Mediterranean Villa",
    category: BuildingCategory.Residential,
    subcategory: BuildingSubcategory.SingleFamily,
    description: "Luxurious villa inspired by Mediterranean architecture",
    keyFeatures: [
      "Terracotta roof tiles",
      "Stucco exterior",
      "Arched windows",
      "Courtyard",
      "Outdoor living spaces",
      "Swimming pool",
    ],
    materials: [
      "Stucco",
      "Terracotta",
      "Natural stone",
      "Wrought iron",
      "Ceramic tiles",
    ],
    style: ["Mediterranean", "Spanish Colonial", "Tuscan"],
    iconName: "Villa",
    tags: ["Luxury", "Residential", "Mediterranean", "Private"],
  },

  modernVilla: {
    id: "modernVilla",
    name: "Contemporary Villa",
    category: BuildingCategory.Residential,
    subcategory: BuildingSubcategory.SingleFamily,
    description: "Ultra-modern luxury villa with cutting-edge design",
    keyFeatures: [
      "Open floor plan",
      "Floor-to-ceiling windows",
      "Infinity pool",
      "Smart home systems",
      "Sustainable features",
      "Rooftop terrace",
    ],
    materials: [
      "Glass",
      "Steel",
      "Concrete",
      "Natural stone",
      "Sustainable materials",
    ],
    style: ["Modern", "Contemporary", "Minimalist"],
    iconName: "ModernHome",
    tags: ["Luxury", "Modern", "Smart Home", "Sustainable"],
  },

  townhouse: {
    id: "townhouse",
    name: "Urban Townhouse",
    category: BuildingCategory.Residential,
    subcategory: BuildingSubcategory.SingleFamily,
    description: "Multi-level urban residence with vertical living spaces",
    keyFeatures: [
      "Multiple floors",
      "Roof deck",
      "Private entrance",
      "Compact footprint",
      "Urban garden",
      "Garage",
    ],
    materials: ["Brick", "Steel", "Glass", "Stone accents", "Modern cladding"],
    style: ["Urban Contemporary", "Modern", "Traditional"],
    iconName: "Townhouse",
    tags: ["Urban", "Residential", "Vertical Living"],
  },

  colonialMansion: {
    id: "colonialMansion",
    name: "Colonial Mansion",
    category: BuildingCategory.Residential,
    subcategory: BuildingSubcategory.SingleFamily,
    description: "Grand colonial-style residence with classical features",
    keyFeatures: [
      "Symmetrical facade",
      "Column entrance",
      "Multiple chimneys",
      "Formal gardens",
      "Grand staircase",
      "Large windows",
    ],
    materials: [
      "Brick",
      "Wood",
      "Slate roofing",
      "Marble accents",
      "Copper details",
    ],
    style: ["Colonial", "Georgian", "Traditional"],
    iconName: "Mansion",
    tags: ["Luxury", "Historical", "Classical", "Estate"],
  },

  beachHouse: {
    id: "beachHouse",
    name: "Coastal Beach House",
    category: BuildingCategory.Residential,
    subcategory: BuildingSubcategory.SingleFamily,
    description: "Waterfront residence designed for coastal living",
    keyFeatures: [
      "Elevated structure",
      "Wraparound deck",
      "Ocean views",
      "Weather-resistant design",
      "Indoor-outdoor living",
      "Beach access",
    ],
    materials: [
      "Weather-resistant siding",
      "Composite decking",
      "Impact windows",
      "Cedar shakes",
      "Marine-grade hardware",
    ],
    style: ["Coastal", "Contemporary", "Beach Style"],
    iconName: "Beach",
    tags: ["Waterfront", "Coastal", "Vacation", "Resort"],
  },

  luxuryPenthouse: {
    id: "luxuryPenthouse",
    name: "Luxury Penthouse",
    category: BuildingCategory.Residential,
    subcategory: BuildingSubcategory.MultiFamily,
    description: "High-end apartment occupying the top floors of a building",
    keyFeatures: [
      "Panoramic views",
      "Private elevator",
      "High ceilings",
      "Terrace gardens",
      "Smart home integration",
      "Premium finishes",
    ],
    materials: [
      "Floor-to-ceiling glass",
      "Natural stone",
      "Hardwood",
      "Metal accents",
      "Designer fixtures",
    ],
    style: ["Ultra Modern", "Contemporary", "Luxury"],
    iconName: "Penthouse",
    tags: ["Luxury", "Urban", "High-rise", "Premium"],
  },

  ecoHomestead: {
    id: "ecoHomestead",
    name: "Eco-Friendly Homestead",
    category: BuildingCategory.Residential,
    subcategory: BuildingSubcategory.SingleFamily,
    description: "Self-sustainable residence with minimal environmental impact",
    keyFeatures: [
      "Solar panels",
      "Rainwater harvesting",
      "Green roof",
      "Natural ventilation",
      "Organic garden",
      "Energy storage",
    ],
    materials: [
      "Recycled materials",
      "Sustainable wood",
      "Earth materials",
      "Solar technology",
      "Green building materials",
    ],
    style: ["Sustainable", "Modern", "Eco-friendly"],
    iconName: "Eco",
    tags: ["Sustainable", "Off-grid", "Eco-friendly", "Self-sufficient"],
  },

  alpineChalet: {
    id: "alpineChalet",
    name: "Alpine Chalet",
    category: BuildingCategory.Residential,
    subcategory: BuildingSubcategory.SingleFamily,
    description: "Mountain residence designed for alpine environments",
    keyFeatures: [
      "Steep pitched roof",
      "Large overhangs",
      "Wooden balconies",
      "Stone foundation",
      "Cozy interiors",
      "Snow management",
    ],
    materials: [
      "Heavy timber",
      "Natural stone",
      "Wood shingles",
      "Thermal insulation",
      "Weather-resistant materials",
    ],
    style: ["Alpine", "Traditional", "Mountain"],
    iconName: "Chalet",
    tags: ["Mountain", "Ski Resort", "Traditional", "Cozy"],
  },

  smartLoft: {
    id: "smartLoft",
    name: "Smart Urban Loft",
    category: BuildingCategory.Residential,
    subcategory: BuildingSubcategory.MultiFamily,
    description: "Technology-integrated urban living space",
    keyFeatures: [
      "Open concept",
      "Smart automation",
      "Industrial elements",
      "Flexible space",
      "Energy efficiency",
      "Modern amenities",
    ],
    materials: [
      "Exposed brick",
      "Steel beams",
      "Concrete floors",
      "Smart glass",
      "Modern finishes",
    ],
    style: ["Industrial", "Modern", "Tech-forward"],
    iconName: "Loft",
    tags: ["Urban", "Smart", "Modern", "Industrial"],
  },

  luxuryVilla: {
    id: "luxuryVilla",
    name: "Luxury Villa",
    category: BuildingCategory.Residential,
    subcategory: BuildingSubcategory.SingleFamily,
    description: "High-end residential villa with premium amenities",
    keyFeatures: [
      "Grand entrance",
      "High ceilings",
      "Private pool",
      "Home theater",
      "Wine cellar",
      "Smart home systems",
    ],
    materials: [
      "Premium stone",
      "Marble",
      "Hardwood",
      "Smart glass",
      "Custom metalwork",
    ],
    style: ["Contemporary", "Mediterranean", "Modern"],
    iconName: "Villa",
    tags: ["Luxury", "Premium", "High-end", "Residential"],
  },

  boutiqueCondo: {
    id: "boutiqueCondo",
    name: "Boutique Condominium",
    category: BuildingCategory.Residential,
    subcategory: BuildingSubcategory.MultiFamily,
    description: "Exclusive small-scale luxury condominium building",
    keyFeatures: [
      "Concierge service",
      "Rooftop terrace",
      "Private balconies",
      "Security system",
      "Designer lobby",
    ],
    materials: [
      "Glass curtain walls",
      "Natural stone",
      "Architectural concrete",
      "Metal panels",
      "Premium finishes",
    ],
    style: ["Modern", "Urban", "Luxury"],
    iconName: "Building",
    tags: ["Luxury", "Multi-family", "Urban", "Exclusive"],
  },

  countryEstate: {
    id: "countryEstate",
    name: "Country Estate",
    category: BuildingCategory.Residential,
    subcategory: BuildingSubcategory.SingleFamily,
    description: "Large residential property in a rural setting",
    keyFeatures: [
      "Extensive grounds",
      "Guest house",
      "Stables",
      "Tennis court",
      "Private gardens",
      "Water features",
    ],
    materials: [
      "Natural stone",
      "Slate roofing",
      "Timber frames",
      "Brick",
      "Wrought iron",
    ],
    style: ["Traditional", "Classical", "Country"],
    iconName: "Estate",
    tags: ["Luxury", "Rural", "Estate", "Traditional"],
  },

  golfVilla: {
    id: "golfVilla",
    name: "Golf Course Villa",
    category: BuildingCategory.Residential,
    subcategory: BuildingSubcategory.SingleFamily,
    description: "Premium residence overlooking golf course",
    keyFeatures: [
      "Golf course views",
      "Indoor-outdoor living",
      "Entertainment spaces",
      "Private putting green",
      "Golf cart garage",
    ],
    materials: [
      "Stone facade",
      "Clay tiles",
      "Glass walls",
      "Sustainable wood",
      "Premium metals",
    ],
    style: ["Resort", "Mediterranean", "Contemporary"],
    iconName: "Golf",
    tags: ["Golf", "Resort", "Luxury", "Leisure"],
  },

  lakehouse: {
    id: "lakehouse",
    name: "Lake House",
    category: BuildingCategory.Residential,
    subcategory: BuildingSubcategory.SingleFamily,
    description: "Waterfront residence designed for lake living",
    keyFeatures: [
      "Lake views",
      "Boat dock",
      "Wraparound deck",
      "Floor-to-ceiling windows",
      "Outdoor kitchen",
    ],
    materials: [
      "Cedar siding",
      "Natural stone",
      "Weather-resistant materials",
      "Glass",
      "Composite decking",
    ],
    style: ["Contemporary", "Rustic", "Modern"],
    iconName: "Lake",
    tags: ["Waterfront", "Lake", "Leisure", "Nature"],
  },

  vineyardEstate: {
    id: "vineyardEstate",
    name: "Vineyard Estate",
    category: BuildingCategory.Residential,
    subcategory: BuildingSubcategory.SingleFamily,
    description: "Luxury residence within a working vineyard",
    keyFeatures: [
      "Wine cellar",
      "Tasting room",
      "Vineyard views",
      "Entertainment spaces",
      "Guest quarters",
    ],
    materials: [
      "Stone masonry",
      "Terra cotta",
      "Exposed beams",
      "Rustic wood",
      "Iron details",
    ],
    style: ["Tuscan", "Mediterranean", "Traditional"],
    iconName: "Vineyard",
    tags: ["Vineyard", "Estate", "Luxury", "Agricultural"],
  },
};

// Helper functions
export function getBuildingsByCategory() {
  const groupedBuildings: Record<
    BuildingCategory,
    Record<BuildingSubcategory, BuildingDetails[]>
  > = {} as Record<
    BuildingCategory,
    Record<BuildingSubcategory, BuildingDetails[]>
  >;

  Object.values(buildingData).forEach((building) => {
    if (!groupedBuildings[building.category]) {
      groupedBuildings[building.category] = {} as Record<
        BuildingSubcategory,
        BuildingDetails[]
      >;
    }
    if (!groupedBuildings[building.category][building.subcategory]) {
      groupedBuildings[building.category][building.subcategory] = [];
    }
    groupedBuildings[building.category][building.subcategory].push(building);
  });

  return groupedBuildings;
}

export function groupBuildingOptions() {
  const groupedBuildings = getBuildingsByCategory();
  const formattedOptions: Record<
    string,
    { value: string; label: string; isSubcategoryLabel?: boolean }[]
  > = {};

  Object.entries(groupedBuildings).forEach(([category, subcategories]) => {
    formattedOptions[category] = [];

    Object.entries(subcategories).forEach(([subcategory, buildings]) => {
      formattedOptions[category].push({
        value: `subcategory-${subcategory}`,
        label: subcategory,
        isSubcategoryLabel: true,
      });

      const buildingOptions = buildings.map((building) => ({
        value: building.id,
        label: building.name,
      }));

      formattedOptions[category].push(...buildingOptions);
    });
  });

  return formattedOptions;
}

export function getBuildingById(id: string): BuildingDetails | undefined {
  return buildingData[id];
}
