export interface BuildingDetails {
  id: string;
  name: string;
  category: BuildingCategory;
  subcategory: BuildingSubcategory;
  description: string;
  keyFeatures: string[]; // Main visual/architectural features
  materials: string[]; // Common building materials
  style: string[]; // Architectural styles that commonly apply
  iconName?: string;
  tags?: string[];
}

export enum BuildingCategory {
  Residential = "Residential Buildings",
  Commercial = "Commercial Buildings",
  Cultural = "Cultural & Community",
  Specialty = "Specialty Buildings",
}

export enum BuildingSubcategory {
  // Residential
  SingleFamily = "Single Family Homes",
  MultiFamily = "Multi-Family Buildings",

  // Commercial
  Office = "Office Buildings",
  Retail = "Retail & Shopping",

  // Cultural
  Religious = "Religious Buildings",
  Community = "Community Buildings",

  // Specialty
  Recreational = "Recreational",
  Educational = "Educational",
}

export const buildingData: Record<string, BuildingDetails> = {
  house: {
    id: "house",
    name: "Single Family House",
    category: BuildingCategory.Residential,
    subcategory: BuildingSubcategory.SingleFamily,
    description: "A standalone residential building designed for one family",
    keyFeatures: [
      "Private entrance",
      "Front yard",
      "Backyard",
      "Driveway",
      "Pitched roof",
    ],
    materials: [
      "Brick",
      "Wood siding",
      "Stone accents",
      "Asphalt shingles",
      "Glass windows",
    ],
    style: ["Modern", "Traditional", "Colonial", "Contemporary", "Craftsman"],
    iconName: "House",
    tags: ["Residential", "Private", "Family"],
  },

  apartmentBuilding: {
    id: "apartmentBuilding",
    name: "Apartment Building",
    category: BuildingCategory.Residential,
    subcategory: BuildingSubcategory.MultiFamily,
    description: "A multi-story building containing multiple residential units",
    keyFeatures: [
      "Multiple floors",
      "Balconies",
      "Common entrance",
      "Parking area",
      "Shared facilities",
    ],
    materials: ["Concrete", "Glass", "Steel", "Brick facade", "Metal details"],
    style: ["Modern", "Urban", "Contemporary", "High-rise", "Mid-rise"],
    iconName: "Building",
    tags: ["Multi-family", "Urban", "Residential"],
  },

  officeBuilding: {
    id: "officeBuilding",
    name: "Office Building",
    category: BuildingCategory.Commercial,
    subcategory: BuildingSubcategory.Office,
    description:
      "A commercial building designed for business and professional use",
    keyFeatures: [
      "Glass facade",
      "Multiple floors",
      "Professional entrance",
      "Large windows",
      "Business lobby",
    ],
    materials: [
      "Glass curtain walls",
      "Steel frame",
      "Concrete",
      "Metal panels",
      "Stone accents",
    ],
    style: ["Modern", "Corporate", "Contemporary", "High-tech", "Professional"],
    iconName: "Office",
    tags: ["Commercial", "Business", "Professional"],
  },

  church: {
    id: "church",
    name: "Church",
    category: BuildingCategory.Cultural,
    subcategory: BuildingSubcategory.Religious,
    description: "A religious building for Christian worship",
    keyFeatures: [
      "Steeple/spire",
      "Large doors",
      "Stained glass windows",
      "Bell tower",
      "Religious symbols",
    ],
    materials: [
      "Stone",
      "Brick",
      "Stained glass",
      "Wood beams",
      "Metal roofing",
    ],
    style: ["Gothic", "Traditional", "Modern", "Classical", "Contemporary"],
    iconName: "Church",
    tags: ["Religious", "Worship", "Community"],
  },
};

// Helper functions
export function getBuildingsByCategory() {
  const groupedBuildings: Record<
    BuildingCategory,
    Record<BuildingSubcategory, BuildingDetails[]>
  > = {} as Record<
    BuildingCategory,
    Record<BuildingSubcategory, BuildingDetails[]>
  >;

  Object.values(buildingData).forEach((building) => {
    if (!groupedBuildings[building.category]) {
      groupedBuildings[building.category] = {} as Record<
        BuildingSubcategory,
        BuildingDetails[]
      >;
    }
    if (!groupedBuildings[building.category][building.subcategory]) {
      groupedBuildings[building.category][building.subcategory] = [];
    }
    groupedBuildings[building.category][building.subcategory].push(building);
  });

  return groupedBuildings;
}

export function groupBuildingOptions() {
  const groupedBuildings = getBuildingsByCategory();
  const formattedOptions: Record<
    string,
    { value: string; label: string; isSubcategoryLabel?: boolean }[]
  > = {};

  Object.entries(groupedBuildings).forEach(([category, subcategories]) => {
    formattedOptions[category] = [];

    Object.entries(subcategories).forEach(([subcategory, buildings]) => {
      formattedOptions[category].push({
        value: `subcategory-${subcategory}`,
        label: subcategory,
        isSubcategoryLabel: true,
      });

      const buildingOptions = buildings.map((building) => ({
        value: building.id,
        label: building.name,
      }));

      formattedOptions[category].push(...buildingOptions);
    });
  });

  return formattedOptions;
}

export function getBuildingById(id: string): BuildingDetails | undefined {
  return buildingData[id];
}
