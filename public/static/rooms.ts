export interface RoomDetails {
  id: string;
  name: string;
  category: RoomCategory;
  subcategory: RoomSubcategory;
  description: string;
  furnishings: string[];
  features?: string[];
  iconName?: string;
  tags?: string[];
  dimensions?: {
    minSize: number;
    idealSize: number;
  };
  commonPairings?: string[];
  accessibility?: {
    requirements: string[];
    recommendations: string[];
  };
  lighting?: {
    natural: string[];
    artificial: string[];
  };
  storage?: string[];
  techIntegration?: string[];
}

export enum RoomCategory {
  Indoor = "Indoor Spaces",
  Outdoor = "Outdoor Spaces",
  Entertainment = "Entertainment Areas",
  Utility = "Utility Rooms",
  Wellness = "Wellness & Relaxation",
  Workspace = "Work & Creative Spaces",
  Storage = "Storage & Specialized Rooms",
  Transitional = "Transitional Spaces",
  Natural = "Natural Elements",
  Specialized = "Specialized Rooms",
}

export enum RoomSubcategory {
  // Indoor
  LivingArea = "Living Areas",
  Bedroom = "Bedrooms",
  Bathroom = "Bathrooms",
  Kitchen = "Kitchen Spaces",
  DiningArea = "Dining Areas",

  // Transitional
  Circulation = "Circulation Spaces",
  EntrySpaces = "Entry Spaces",

  // Outdoor
  OutdoorLiving = "Outdoor Living",
  OutdoorUtility = "Outdoor Utility",

  // Entertainment
  MediaEntertainment = "Media & Gaming",
  SocialEntertainment = "Social Spaces",

  // Utility
  HomeManagement = "Home Management",
  VehicleStorage = "Vehicle & Equipment Storage",

  // Wellness
  Fitness = "Fitness",
  Relaxation = "Relaxation Spaces",
  PersonalCare = "Personal Care",

  // Workspace
  WorkSpaces = "Professional Spaces",
  CreativeSpaces = "Creative Studios",

  // Storage
  SpecializedStorage = "Specialized Storage",

  // New subcategories
  SmartSpaces = "Smart Home Integration",
  MultiPurpose = "Multi-Purpose Spaces",
}
export const roomData: Record<string, RoomDetails> = {
  // Indoor Living Areas
  livingRoom: {
    id: "livingRoom",
    name: "Living Room",
    description:
      "A central gathering space for family and guests, designed for relaxation and social interaction.",
    category: RoomCategory.Indoor,
    subcategory: RoomSubcategory.LivingArea,
    furnishings: [
      "Sofa",
      "Armchairs",
      "Coffee Table",
      "TV Stand",
      "Bookshelf",
      "Area Rug",
      "Floor Lamp",
    ],
    features: ["Entertainment Space", "Gathering Area"],
    iconName: "Sofa",
    tags: ["Comfort", "Family", "Relaxation"],
  },
  familyRoom: {
    id: "familyRoom",
    name: "Family Room",
    description:
      "A casual, comfortable space for daily family activities and entertainment.",
    category: RoomCategory.Indoor,
    subcategory: RoomSubcategory.LivingArea,
    furnishings: [
      "Sectional Sofa",
      "Entertainment Center",
      "Game Table",
      "Bean Bags",
      "Storage Ottomans",
    ],
    features: ["Casual Gathering", "Multi-Purpose Space"],
    iconName: "Users",
    tags: ["Family Time", "Casual", "Versatile"],
  },

  // Bedrooms
  masterBedroom: {
    id: "masterBedroom",
    name: "Master Bedroom",
    description:
      "The primary bedroom suite offering privacy and luxury for homeowners.",
    category: RoomCategory.Indoor,
    subcategory: RoomSubcategory.Bedroom,
    furnishings: [
      "King Size Bed",
      "Dresser",
      "Nightstands",
      "Armchair",
      "Wardrobe",
      "Full-Length Mirror",
    ],
    features: ["Walk-in Closet", "Ensuite Bathroom"],
    iconName: "Bed",
    tags: ["Personal Space", "Relaxation", "Comfort"],
  },
  guestBedroom: {
    id: "guestBedroom",
    name: "Guest Bedroom",
    description:
      "A welcoming space designed to accommodate visitors with comfort and convenience.",
    category: RoomCategory.Indoor,
    subcategory: RoomSubcategory.Bedroom,
    furnishings: [
      "Queen Size Bed",
      "Dresser",
      "Nightstand",
      "Luggage Rack",
      "Desk",
      "Chair",
    ],
    features: ["Welcoming", "Comfortable"],
    iconName: "Hotel",
    tags: ["Hospitality", "Accommodation"],
  },
  childBedroom: {
    id: "childBedroom",
    name: "Child's Bedroom",
    description:
      "A versatile room that combines sleep, study, and play areas for children.",
    category: RoomCategory.Indoor,
    subcategory: RoomSubcategory.Bedroom,
    furnishings: [
      "Single/Bunk Bed",
      "Study Desk",
      "Bookshelf",
      "Toy Storage",
      "Dresser",
      "Play Area",
    ],
    features: ["Learning Space", "Play Area"],
    iconName: "Baby",
    tags: ["Creativity", "Learning", "Fun"],
  },

  // Bathrooms
  masterBathroom: {
    id: "masterBathroom",
    name: "Master Bathroom",
    description:
      "A private spa-like retreat with luxury amenities and premium fixtures.",
    category: RoomCategory.Indoor,
    subcategory: RoomSubcategory.Bathroom,
    furnishings: [
      "Double Vanity",
      "Bathtub",
      "Shower",
      "Toilet",
      "Bidet",
      "Towel Warmers",
    ],
    features: ["Luxury Features", "Spa-like Atmosphere"],
    iconName: "Bath",
    tags: ["Relaxation", "Personal Care"],
  },
  mainBathroom: {
    id: "mainBathroom",
    name: "Main Bathroom",
    description:
      "A functional shared bathroom serving the household's daily needs.",
    category: RoomCategory.Indoor,
    subcategory: RoomSubcategory.Bathroom,
    furnishings: [
      "Vanity",
      "Bathtub/Shower Combo",
      "Toilet",
      "Storage Cabinet",
    ],
    features: ["Family Use", "Practical Design"],
    iconName: "Shower",
    tags: ["Functionality", "Shared Space"],
  },

  // Kitchens
  mainKitchen: {
    id: "mainKitchen",
    name: "Main Kitchen",
    description:
      "The heart of the home, featuring cooking, preparation, and gathering spaces.",
    category: RoomCategory.Indoor,
    subcategory: RoomSubcategory.Kitchen,
    furnishings: [
      "Kitchen Island",
      "Refrigerator",
      "Stove",
      "Oven",
      "Dishwasher",
      "Pantry",
      "Dining Area",
    ],
    features: ["Cooking", "Meal Preparation", "Gathering Space"],
    iconName: "Utensils",
    tags: ["Cooking", "Family Meals", "Entertaining"],
  },
  butlersPantry: {
    id: "butlersPantry",
    name: "Butler's Pantry",
    description:
      "A preparation and storage space that bridges the kitchen and dining areas.",
    category: RoomCategory.Indoor,
    subcategory: RoomSubcategory.Kitchen,
    furnishings: [
      "Additional Countertop",
      "Storage Cabinets",
      "Wine Fridge",
      "Prep Sink",
    ],
    features: ["Additional Storage", "Entertaining Prep"],
    iconName: "Refrigerator",
    tags: ["Entertaining", "Preparation", "Storage"],
  },

  // Entertainment Areas
  homeTheater: {
    id: "homeTheater",
    name: "Home Theater",
    description:
      "A dedicated entertainment space for immersive movie-watching experiences.",
    category: RoomCategory.Entertainment,
    subcategory: RoomSubcategory.MediaEntertainment,
    furnishings: [
      "Projector/Large Screen",
      "Surround Sound System",
      "Comfortable Seating",
      "Popcorn Machine",
      "Media Storage",
    ],
    features: ["Immersive Viewing", "Acoustic Design"],
    iconName: "Tv",
    tags: ["Movie Night", "Entertainment", "Technology"],
  },
  gameRoom: {
    id: "gameRoom",
    name: "Game Room",
    description:
      "A multi-purpose recreation space for various gaming and entertainment activities.",
    category: RoomCategory.Entertainment,
    subcategory: RoomSubcategory.MediaEntertainment,
    furnishings: [
      "Pool Table",
      "Foosball Table",
      "Arcade Machine",
      "Bar Area",
      "Gaming Consoles",
      "Comfortable Seating",
    ],
    features: ["Social Gathering", "Recreation"],
    iconName: "Gamepad",
    tags: ["Fun", "Gaming", "Social"],
  },

  // Workspace
  homeOffice: {
    id: "homeOffice",
    name: "Home Office",
    description:
      "A professional workspace designed for productivity and focus.",
    category: RoomCategory.Workspace,
    subcategory: RoomSubcategory.WorkSpaces,
    furnishings: [
      "Ergonomic Desk",
      "Office Chair",
      "Computer Setup",
      "Bookshelf",
      "Filing Cabinet",
      "Desk Lamp",
    ],
    features: ["Productivity", "Focused Work Environment"],
    iconName: "Laptop",
    tags: ["Work", "Productivity", "Professional"],
  },
  studyRoom: {
    id: "studyRoom",
    name: "Study Room",
    description:
      "A quiet space dedicated to learning, reading, and academic pursuits.",
    category: RoomCategory.Workspace,
    subcategory: RoomSubcategory.WorkSpaces,
    furnishings: [
      "Large Study Table",
      "Bookshelves",
      "Reading Chair",
      "Filing System",
      "Desk Lamp",
      "Whiteboard",
    ],
    features: ["Learning", "Concentration"],
    iconName: "Book",
    tags: ["Education", "Learning", "Research"],
  },

  // Creative Spaces
  artStudio: {
    id: "artStudio",
    name: "Art Studio",
    description:
      "A creative workspace with optimal lighting and storage for artistic pursuits.",
    category: RoomCategory.Workspace,
    subcategory: RoomSubcategory.CreativeSpaces,
    furnishings: [
      "Easel",
      "Art Desk",
      "Supply Storage",
      "Natural Lighting",
      "Work Surfaces",
      "Inspiration Wall",
    ],
    features: ["Creative Expression", "Artistic Workspace"],
    iconName: "Paintbrush",
    tags: ["Creativity", "Art", "Expression"],
  },
  musicStudio: {
    id: "musicStudio",
    name: "Music Studio",
    description:
      "An acoustically-treated space for music creation and recording.",
    category: RoomCategory.Workspace,
    subcategory: RoomSubcategory.CreativeSpaces,
    furnishings: [
      "Musical Instruments",
      "Recording Equipment",
      "Soundproofing",
      "Mixing Board",
      "Comfortable Seating",
    ],
    features: ["Sound Recording", "Musical Creation"],
    iconName: "Music",
    tags: ["Music", "Creativity", "Performance"],
  },

  // Wellness & Relaxation
  homeGym: {
    id: "homeGym",
    name: "Home Gym",
    description:
      "A dedicated fitness space equipped with exercise equipment for maintaining health and wellness.",
    category: RoomCategory.Wellness,
    subcategory: RoomSubcategory.Fitness,
    furnishings: [
      "Treadmill",
      "Weight Rack",
      "Exercise Bikes",
      "Yoga Mat",
      "Mirror",
      "Sound System",
    ],
    features: ["Fitness", "Personal Training"],
    iconName: "Dumbbell",
    tags: ["Exercise", "Health", "Fitness"],
  },
  yogaStudio: {
    id: "yogaStudio",
    name: "Yoga Studio",
    description:
      "A serene space designed for meditation, yoga practice, and mindful movement.",
    category: RoomCategory.Wellness,
    subcategory: RoomSubcategory.Fitness,
    furnishings: [
      "Yoga Mats",
      "Meditation Cushions",
      "Yoga Blocks",
      "Sound System",
      "Calming Decor",
    ],
    features: ["Mindfulness", "Flexibility Training"],
    iconName: "Yoga",
    tags: ["Meditation", "Wellness", "Mindfulness"],
  },
  homeSpa: {
    id: "homeSpa",
    name: "Home Spa",
    description:
      "A luxurious retreat space for relaxation, rejuvenation, and personal wellness treatments.",
    category: RoomCategory.Wellness,
    subcategory: RoomSubcategory.Relaxation,
    furnishings: [
      "Massage Table",
      "Sauna",
      "Hot Tub",
      "Relaxation Chairs",
      "Aromatherapy Diffuser",
    ],
    features: ["Relaxation", "Personal Wellness"],
    iconName: "Leaf",
    tags: ["Relaxation", "Self-Care", "Wellness"],
  },

  // Outdoor Spaces
  outdoorKitchen: {
    id: "outdoorKitchen",
    name: "Outdoor Kitchen",
    description:
      "An al fresco cooking and dining space perfect for outdoor entertaining and seasonal gatherings.",
    category: RoomCategory.Outdoor,
    subcategory: RoomSubcategory.OutdoorLiving,
    furnishings: [
      "Grill",
      "Outdoor Refrigerator",
      "Countertop",
      "Bar Stools",
      "Dining Area",
      "Shade Structure",
    ],
    features: ["Outdoor Cooking", "Entertaining"],
    iconName: "Grill",
    tags: ["Cooking", "Entertaining", "Outdoor Dining"],
  },
  patio: {
    id: "patio",
    name: "Patio",
    description:
      "An outdoor living area designed for relaxation, dining, and enjoying the natural environment.",
    category: RoomCategory.Outdoor,
    subcategory: RoomSubcategory.OutdoorLiving,
    furnishings: [
      "Outdoor Seating",
      "Dining Table",
      "Umbrellas",
      "Planters",
      "Fire Pit",
    ],
    features: ["Outdoor Relaxation", "Social Gathering"],
    iconName: "Sun",
    tags: ["Relaxation", "Outdoor Living", "Entertaining"],
  },

  // Utility Rooms
  laundryRoom: {
    id: "laundryRoom",
    name: "Laundry Room",
    description:
      "A functional space dedicated to laundry care, clothing maintenance, and household organization.",
    category: RoomCategory.Utility,
    subcategory: RoomSubcategory.HomeManagement,
    furnishings: [
      "Washer",
      "Dryer",
      "Folding Table",
      "Storage Cabinets",
      "Hanging Rack",
      "Iron and Ironing Board",
    ],
    features: ["Clothes Care", "Efficient Laundering"],
    iconName: "Wash",
    tags: ["Cleaning", "Household Management"],
  },
  garage: {
    id: "garage",
    name: "Garage",
    description:
      "A versatile space for vehicle storage, workshop activities, and general storage needs.",
    category: RoomCategory.Utility,
    subcategory: RoomSubcategory.VehicleStorage,
    furnishings: [
      "Car Parking Space",
      "Tool Bench",
      "Storage Shelves",
      "Bike Rack",
      "Work Area",
    ],
    features: ["Vehicle Storage", "Workshop"],
    iconName: "Car",
    tags: ["Storage", "Maintenance", "Utility"],
  },

  // Specialized Storage
  wineCellar: {
    id: "wineCellar",
    name: "Wine Cellar",
    description:
      "A temperature-controlled environment designed for storing and showcasing wine collections.",
    category: RoomCategory.Storage,
    subcategory: RoomSubcategory.SpecializedStorage,
    furnishings: [
      "Wine Racks",
      "Temperature-Controlled Storage",
      "Tasting Area",
      "Wine Fridge",
      "Display Shelves",
    ],
    features: ["Wine Collection", "Climate Control"],
    iconName: "Wine",
    tags: ["Collection", "Storage", "Luxury"],
  },
  pantry: {
    id: "pantry",
    name: "Pantry",
    description:
      "A dedicated storage space for food items, kitchen supplies, and household essentials.",
    category: RoomCategory.Storage,
    subcategory: RoomSubcategory.SpecializedStorage,
    furnishings: [
      "Shelving Units",
      "Storage Containers",
      "Spice Rack",
      "Bulk Storage",
      "Organization Systems",
    ],
    features: ["Food Storage", "Kitchen Organization"],
    iconName: "Refrigerator",
    tags: ["Storage", "Organization", "Kitchen"],
  },

  smartHomeHub: {
    id: "smartHomeHub",
    name: "Smart Home Hub",
    category: RoomCategory.Utility,
    subcategory: RoomSubcategory.SmartSpaces,
    description:
      "A centralized control room for home automation and smart technology management.",
    furnishings: [
      "Control Panels",
      "Network Equipment",
      "Monitoring Displays",
      "Cable Management",
      "Tech Storage",
    ],
    features: ["Home Automation", "System Integration", "Network Control"],
    iconName: "Circuit",
    tags: ["Smart Home", "Technology", "Control", "Automation"],
  },

  podcastStudio: {
    id: "podcastStudio",
    name: "Podcast Studio",
    category: RoomCategory.Workspace,
    subcategory: RoomSubcategory.CreativeSpaces,
    description:
      "A professional-grade audio recording space for podcasting and voice work.",
    furnishings: [
      "Microphones",
      "Audio Interface",
      "Acoustic Panels",
      "Studio Monitors",
      "Recording Desk",
      "Podcast Boom Arms",
    ],
    features: ["Sound Treatment", "Professional Audio", "Broadcasting"],
    iconName: "Microphone",
    tags: ["Audio", "Recording", "Creative", "Professional"],
  },

  zoomRoom: {
    id: "zoomRoom",
    name: "Video Conference Room",
    category: RoomCategory.Workspace,
    subcategory: RoomSubcategory.WorkSpaces,
    description:
      "A dedicated space optimized for video conferencing and virtual meetings.",
    furnishings: [
      "Video Conference Display",
      "Professional Camera",
      "Conference Microphone",
      "Lighting Kit",
      "Ergonomic Seating",
      "Cable Management",
    ],
    features: [
      "Virtual Meetings",
      "Professional Background",
      "Tech Integration",
    ],
    iconName: "Video",
    tags: ["Virtual", "Meetings", "Professional", "Remote Work"],
  },

  indoorGreenhouse: {
    id: "indoorGreenhouse",
    name: "Indoor Greenhouse",
    category: RoomCategory.Natural,
    subcategory: RoomSubcategory.MultiPurpose,
    description:
      "A climate-controlled space for year-round plant cultivation and relaxation.",
    furnishings: [
      "Plant Shelving",
      "Grow Lights",
      "Humidity Control",
      "Potting Station",
      "Seating Area",
      "Irrigation System",
    ],
    features: ["Plant Growth", "Climate Control", "Natural Elements"],
    iconName: "Plant",
    tags: ["Plants", "Nature", "Gardening", "Wellness"],
  },

  petSuite: {
    id: "petSuite",
    name: "Pet Suite",
    category: RoomCategory.Specialized,
    subcategory: RoomSubcategory.MultiPurpose,
    description: "A dedicated space for pet care, grooming, and comfort.",
    furnishings: [
      "Grooming Station",
      "Pet Shower",
      "Storage Solutions",
      "Feeding Area",
      "Pet Beds",
      "Play Equipment",
    ],
    features: ["Pet Care", "Easy Clean", "Pet Comfort"],
    iconName: "Paw",
    tags: ["Pets", "Care", "Comfort", "Organization"],
  },

  meditationRoom: {
    id: "meditationRoom",
    name: "Meditation Room",
    category: RoomCategory.Wellness,
    subcategory: RoomSubcategory.Relaxation,
    description:
      "A serene space designed for mindfulness and spiritual practices.",
    furnishings: [
      "Meditation Cushions",
      "Sound System",
      "Essential Oil Diffuser",
      "Natural Light Control",
      "Storage for Props",
      "Wall Art",
    ],
    features: ["Peaceful Environment", "Sound Control", "Natural Elements"],
    iconName: "Peace",
    tags: ["Meditation", "Mindfulness", "Spiritual", "Calm"],
  },

  craftRoom: {
    id: "craftRoom",
    name: "Craft Room",
    category: RoomCategory.Workspace,
    subcategory: RoomSubcategory.CreativeSpaces,
    description:
      "A versatile workspace for various crafting activities and DIY projects.",
    furnishings: [
      "Craft Table",
      "Storage Units",
      "Task Lighting",
      "Tool Organization",
      "Project Display",
      "Cutting Station",
    ],
    features: ["Creative Space", "Organization", "Project Management"],
    iconName: "Scissors",
    tags: ["Crafts", "DIY", "Creative", "Organization"],
  },

  climateWineCellar: {
    id: "climateWineCellar",
    name: "Climate-Controlled Wine Cellar",
    category: RoomCategory.Storage,
    subcategory: RoomSubcategory.SpecializedStorage,
    description:
      "A sophisticated space for wine storage with precise temperature and humidity control.",
    furnishings: [
      "Wine Racks",
      "Climate Control System",
      "Tasting Area",
      "LED Lighting",
      "Inventory System",
      "Decanting Station",
    ],
    features: [
      "Temperature Control",
      "Humidity Management",
      "Wine Preservation",
    ],
    iconName: "Wine",
    tags: ["Wine", "Storage", "Collection", "Luxury"],
  },

  mudRoom: {
    id: "mudRoom",
    name: "Mud Room",
    category: RoomCategory.Transitional,
    subcategory: RoomSubcategory.EntrySpaces,
    description:
      "A transitional space for outdoor-to-indoor organization and storage.",
    furnishings: [
      "Storage Lockers",
      "Bench Seating",
      "Shoe Storage",
      "Coat Hooks",
      "Weather Mat",
      "Cleaning Station",
    ],
    features: ["Organization", "Easy Clean", "Storage Solutions"],
    iconName: "Boot",
    tags: ["Entry", "Storage", "Organization", "Transition"],
  },

  outdoorYoga: {
    id: "outdoorYoga",
    name: "Outdoor Yoga Deck",
    category: RoomCategory.Outdoor,
    subcategory: RoomSubcategory.OutdoorLiving,
    description: "An open-air space designed for yoga and outdoor exercise.",
    furnishings: [
      "Non-slip Decking",
      "Storage Box",
      "Shade Sail",
      "Plant Surrounds",
      "Lighting",
      "Sound System",
    ],
    features: ["Natural Setting", "Exercise Space", "Outdoor Wellness"],
    iconName: "Sun",
    tags: ["Yoga", "Outdoor", "Exercise", "Wellness"],
  },

  entertainmentLounge: {
    id: "entertainmentLounge",
    name: "Entertainment Lounge",
    category: RoomCategory.Entertainment,
    subcategory: RoomSubcategory.SocialEntertainment,
    description:
      "A sophisticated multi-purpose entertainment space combining gaming, movies, and social activities.",
    furnishings: [
      "Modular Seating",
      "Gaming Station",
      "Projector Screen",
      "Smart Lighting",
      "Bar Setup",
      "Surround Sound",
      "VR Gaming Area",
    ],
    features: ["Multi-screen Setup", "Acoustic Treatment", "Flexible Layout"],
    iconName: "Party",
    tags: ["Entertainment", "Social", "Gaming", "Movies"],
    lighting: {
      natural: ["Controlled Daylight"],
      artificial: ["RGB Smart Lighting", "Zone Controls", "Ambient Strips"],
    },
  },

  multigenerationalSuite: {
    id: "multigenerationalSuite",
    name: "Multigenerational Suite",
    category: RoomCategory.Specialized,
    subcategory: RoomSubcategory.MultiPurpose,
    description:
      "An independent living space designed for extended family members with accessibility and privacy in mind.",
    furnishings: [
      "Kitchenette",
      "Accessible Bathroom",
      "Murphy Bed",
      "Sitting Area",
      "Medical Equipment Storage",
      "Emergency Call System",
    ],
    features: ["Universal Design", "Private Entry", "Accessibility Features"],
    iconName: "Family",
    tags: ["Family", "Accessibility", "Independence", "Comfort"],
    accessibility: {
      requirements: ["Wide Doorways", "Zero-threshold Entry", "Grab Bars"],
      recommendations: ["Motion Sensors", "Voice Controls", "Support Rails"],
    },
  },

  hydroponicGarden: {
    id: "hydroponicGarden",
    name: "Hydroponic Garden",
    category: RoomCategory.Natural,
    subcategory: RoomSubcategory.MultiPurpose,
    description:
      "A modern indoor farming space for year-round sustainable food production.",
    furnishings: [
      "Vertical Growing Systems",
      "LED Grow Lights",
      "Nutrient Control Station",
      "Monitoring Systems",
      "Harvesting Area",
      "Climate Controls",
    ],
    features: ["Food Production", "Sustainable Living", "Smart Monitoring"],
    iconName: "Leaf",
    tags: ["Sustainable", "Garden", "Technology", "Food"],
    techIntegration: [
      "Climate Monitoring",
      "Automated Irrigation",
      "Growth Tracking",
    ],
  },

  kidsPlayroom: {
    id: "kidsPlayroom",
    name: "Kids Adventure Playroom",
    category: RoomCategory.Entertainment,
    subcategory: RoomSubcategory.MultiPurpose,
    description:
      "An imaginative space designed for children's play, learning, and development.",
    furnishings: [
      "Climbing Wall",
      "Reading Nook",
      "Art Station",
      "Storage Solutions",
      "Interactive Play Elements",
      "Educational Displays",
      "Soft Play Area",
    ],
    features: ["Safety Features", "Educational Zones", "Active Play Areas"],
    iconName: "Toy",
    tags: ["Kids", "Play", "Education", "Active"],
    storage: [
      "Toy Organization",
      "Art Supplies",
      "Educational Materials",
      "Sports Equipment",
    ],
  },

  wellnessCenter: {
    id: "wellnessCenter",
    name: "Home Wellness Center",
    category: RoomCategory.Wellness,
    subcategory: RoomSubcategory.Fitness,
    description:
      "A comprehensive space combining fitness, recovery, and wellness treatments.",
    furnishings: [
      "Recovery Zone",
      "Infrared Sauna",
      "Cold Plunge Pool",
      "Massage Area",
      "Meditation Corner",
      "Fitness Equipment",
      "Hydration Station",
    ],
    features: ["Recovery Tools", "Treatment Areas", "Wellness Technology"],
    iconName: "Heart",
    tags: ["Wellness", "Recovery", "Fitness", "Health"],
    techIntegration: [
      "Biometric Monitoring",
      "Recovery Tracking",
      "Climate Control",
    ],
  },

  outdoorLivingRoom: {
    id: "outdoorLivingRoom",
    name: "Outdoor Living Room",
    category: RoomCategory.Outdoor,
    subcategory: RoomSubcategory.OutdoorLiving,
    description:
      "A fully-equipped outdoor space that mirrors indoor comfort with weather protection.",
    furnishings: [
      "Weather-resistant Seating",
      "Outdoor TV",
      "Fire Feature",
      "Outdoor Curtains",
      "All-weather Rugs",
      "Heating Elements",
      "Smart Lighting",
    ],
    features: ["Weather Protection", "Entertainment Setup", "Climate Control"],
    iconName: "Cloud",
    tags: ["Outdoor", "Entertainment", "Comfort", "Social"],
    lighting: {
      natural: ["Daylight", "Sunset Views"],
      artificial: ["Landscape Lighting", "Task Lighting", "Ambient Lighting"],
    },
  },

  formalDining: {
    id: "formalDining",
    name: "Formal Dining Room",
    category: RoomCategory.Indoor,
    subcategory: RoomSubcategory.DiningArea,
    description:
      "An elegant space designed for formal dining and entertaining, featuring sophisticated finishes and ample seating.",
    furnishings: [
      "Dining Table",
      "Dining Chairs",
      "Buffet/Sideboard",
      "China Cabinet",
      "Chandelier",
      "Bar Cart",
      "Table Linens",
    ],
    features: ["Formal Entertaining", "Special Occasions", "Family Gatherings"],
    iconName: "DiningTable",
    tags: ["Dining", "Entertaining", "Formal", "Elegant"],
    lighting: {
      natural: ["Large Windows", "Controlled Natural Light"],
      artificial: ["Chandelier", "Wall Sconces", "Dimmable Lighting"],
    },
  },

  breakfastNook: {
    id: "breakfastNook",
    name: "Breakfast Nook",
    category: RoomCategory.Indoor,
    subcategory: RoomSubcategory.DiningArea,
    description:
      "A cozy, casual dining space perfect for everyday meals and morning routines.",
    furnishings: [
      "Banquette Seating",
      "Cafe Table",
      "Accent Chairs",
      "Storage Bench",
      "Pendant Light",
      "Window Treatments",
    ],
    features: ["Casual Dining", "Natural Light", "Comfortable Seating"],
    iconName: "Coffee",
    tags: ["Casual", "Breakfast", "Cozy", "Family"],
    lighting: {
      natural: ["Bay Window", "Garden View"],
      artificial: ["Pendant Lighting", "Under-cabinet Lights"],
    },
  },

  libraryStudy: {
    id: "libraryStudy",
    name: "Library Study",
    category: RoomCategory.Workspace,
    subcategory: RoomSubcategory.WorkSpaces,
    description:
      "A sophisticated space combining book storage with quiet study and reading areas.",
    furnishings: [
      "Built-in Bookshelves",
      "Reading Chairs",
      "Writing Desk",
      "Library Ladder",
      "Reading Lamps",
      "Globe Stand",
      "Fireplace",
    ],
    features: ["Book Collection", "Reading Nooks", "Study Space"],
    iconName: "Books",
    tags: ["Reading", "Study", "Collection", "Knowledge"],
    lighting: {
      natural: ["North-facing Windows", "Window Seats"],
      artificial: ["Task Lighting", "Ambient Lighting", "Picture Lights"],
    },
  },

  butlerKitchen: {
    id: "butlerKitchen",
    name: "Butler's Kitchen",
    category: RoomCategory.Indoor,
    subcategory: RoomSubcategory.Kitchen,
    description:
      "A secondary kitchen space for meal prep, staging, and storage, bridging main kitchen and dining areas.",
    furnishings: [
      "Prep Sink",
      "Secondary Dishwasher",
      "Coffee Station",
      "Wine Storage",
      "China Storage",
      "Serving Counter",
      "Warming Drawers",
    ],
    features: ["Meal Staging", "Entertainment Prep", "Storage Solutions"],
    iconName: "Service",
    tags: ["Kitchen", "Service", "Storage", "Preparation"],
    storage: [
      "China Storage",
      "Glassware Organization",
      "Linens Storage",
      "Party Supplies",
    ],
  },

  sunRoom: {
    id: "sunRoom",
    name: "Sun Room",
    category: RoomCategory.Indoor,
    subcategory: RoomSubcategory.LivingArea,
    description:
      "A light-filled transitional space connecting indoor and outdoor living areas.",
    furnishings: [
      "Indoor-Outdoor Furniture",
      "Plant Displays",
      "Reading Chair",
      "Side Tables",
      "Ceiling Fans",
      "Window Treatments",
    ],
    features: [
      "Natural Light",
      "Indoor-Outdoor Flow",
      "Plant-friendly Environment",
    ],
    iconName: "Sun",
    tags: ["Light", "Nature", "Relaxation", "Plants"],
    lighting: {
      natural: ["Floor-to-ceiling Windows", "Skylights"],
      artificial: ["Adjustable Lighting", "Plant Grow Lights"],
    },
  },

  musicRoom: {
    id: "musicRoom",
    name: "Music Room",
    category: RoomCategory.Entertainment,
    subcategory: RoomSubcategory.SocialEntertainment,
    description:
      "A dedicated space for musical instruments, practice, and performance.",
    furnishings: [
      "Piano",
      "Instrument Storage",
      "Music Stands",
      "Performance Area",
      "Seating Area",
      "Audio System",
      "Acoustic Panels",
    ],
    features: ["Acoustic Treatment", "Performance Space", "Practice Area"],
    iconName: "Music",
    tags: ["Music", "Performance", "Practice", "Entertainment"],
    techIntegration: [
      "Sound System",
      "Recording Equipment",
      "Acoustic Management",
    ],
  },

  teenLounge: {
    id: "teenLounge",
    name: "Teen Lounge",
    category: RoomCategory.Entertainment,
    subcategory: RoomSubcategory.SocialEntertainment,
    description:
      "A versatile space designed for teenagers to study, socialize, and relax.",
    furnishings: [
      "Comfortable Seating",
      "Study Area",
      "Gaming Setup",
      "Media Center",
      "Charging Station",
      "Bean Bags",
      "Mini Fridge",
    ],
    features: ["Social Space", "Study Area", "Entertainment Zone"],
    iconName: "Group",
    tags: ["Teen", "Social", "Study", "Entertainment"],
    techIntegration: ["Gaming Systems", "Charging Stations", "Smart TV"],
  },

  catioEnclosure: {
    id: "catioEnclosure",
    name: "Catio Enclosure",
    category: RoomCategory.Outdoor,
    subcategory: RoomSubcategory.OutdoorLiving,
    description:
      "A safe outdoor enclosure designed for cats to experience nature while staying protected.",
    furnishings: [
      "Climbing Structures",
      "Perches",
      "Scratching Posts",
      "Sheltered Areas",
      "Water Station",
      "Cat Grass Garden",
      "Resting Platforms",
    ],
    features: ["Pet Safety", "Outdoor Access", "Environmental Enrichment"],
    iconName: "Cat",
    tags: ["Pets", "Outdoor", "Safety", "Nature"],
    lighting: {
      natural: ["Sunlight Access", "Shaded Areas"],
      artificial: ["Safety Lighting", "Night Illumination"],
    },
  },
};

// Add a new function to get rooms by category and subcategory
export function getRoomsByCategory() {
  const groupedRooms: Record<
    RoomCategory,
    Record<RoomSubcategory, RoomDetails[]>
  > = {} as Record<RoomCategory, Record<RoomSubcategory, RoomDetails[]>>;

  Object.values(roomData).forEach((room) => {
    if (!groupedRooms[room.category]) {
      groupedRooms[room.category] = {} as Record<
        RoomSubcategory,
        RoomDetails[]
      >;
    }
    if (!groupedRooms[room.category][room.subcategory]) {
      groupedRooms[room.category][room.subcategory] = [];
    }
    groupedRooms[room.category][room.subcategory].push(room);
  });

  return groupedRooms;
}

// Update the groupRoomOptions function to use subcategories
export function groupRoomOptions() {
  const groupedRooms = getRoomsByCategory();
  const formattedOptions: Record<string, { value: string; label: string }[]> =
    {};

  Object.entries(groupedRooms).forEach(([category, subcategories]) => {
    formattedOptions[category] = [];

    Object.entries(subcategories).forEach(([subcategory, rooms]) => {
      // Add subcategory as a disabled option
      formattedOptions[category].push({
        value: `subcategory-${subcategory}`,
        label: subcategory,
        isSubcategoryLabel: true,
      } as any);

      // Add rooms under this subcategory
      const roomOptions = rooms.map((room) => ({
        value: room.id,
        label: room.name,
      }));

      formattedOptions[category].push(...roomOptions);
    });
  });

  return formattedOptions;
}

export function getRoomById(id: string): RoomDetails | undefined {
  return roomData[id];
}
