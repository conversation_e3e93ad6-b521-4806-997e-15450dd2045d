const styleAdjectives = {
  ArtDeco: ["glamorous", "opulent", "bold", "luxurious", "elegant"],
  ArtNouveau: ["ornate", "intricate", "whimsical", "feminine", "organic"],
  Bohemian: ["mismatched", "eclectic", "carefree", "vibrant", "bohemian"],
  Coastal: ["beachy", "nautical", "light", "airy", "coastal"],
  Contemporary: ["sleek", "modern", "minimalistic", "geometric", "neutral"],
  Cyberpunk: ["edgy", "industrial", "futuristic", "high-tech", "dark"],
  Eclectic: ["eclectic", "vintage", "unique", "varied", "quirky"],
  Farmhouse: ["rustic", "cozy", "homey", "traditional", "woodsy"],
  FrenchCountry: ["warm", "inviting", "earthy", "antique", "vintage"],
  Gothic: ["dark", "dramatic", "mysterious", "ornate", "gothic"],
  HollywoodGlamour: ["luxurious", "opulent", "glamorous", "metallic", "velvet"],
  IndustrialChic: ["edgy", "raw", "exposed", "industrial", "chic"],
  Japanese: ["minimalistic", "serene", "zen", "natural", "japanese"],
  Mediterranean: ["warm", "inviting", "earthy", "antique", "vintage"],
  MidCenturyModern: ["clean", "natural", "bold", "retro", "mid-century"],
  Minimalist: ["minimalistic", "sleek", "clean", "modern", "simple"],
  Modern: ["sleek", "modern", "minimalistic", "geometric", "neutral"],
  Moroccan: ["exotic", "colorful", "patterned", "vibrant", "bohemian"],
  Nautical: ["nautical", "beachy", "coastal", "light", "airy"],
  PopArt: ["colorful", "bold", "graphic", "pop", "art"],
  Retro: ["retro", "vintage", "funky", "colorful", "kitschy"],
  Rustic: ["rustic", "cozy", "homey", "traditional", "woodsy"],
  Scandinavian: ["minimalistic", "sleek", "modern", "light", "Scandinavian"],
  ShabbyChic: ["vintage", "chic", "feminine", "romantic", "shabby"],
  Steampunk: ["industrial", "gothic", "vintage", "steampunk", "steam-powered"],
  Traditional: ["elegant", "classic", "timeless", "formal", "traditional"],
  Transitional: [
    "transitional",
    "minimalist",
    "modern",
    "minimalistic",
    "clean-lined",
  ],
  Tropical: ["exotic", "tropical", "colorful", "vibrant", "lush"],
  Victorian: ["elegant", "ornate", "Victorian", "formal", "regal"],
  Vintage: ["vintage", "retro", "antique", "rustic", "timeworn"],
  Western: ["rustic", "western", "cowboy", "outdoorsy", "wild west"],
};

const furnitureTypes = {
  "Home theater": [
    "Sofa",
    "media console",
    "sound system",
    "media storage",
    "wall-mounted TV",
  ],
  Sunroom: ["Sofa", "coffee table", "area rug", "plants", "throw pillows"],
  Porch: [
    "Outdoor sofa",
    "coffee table",
    "area rug",
    "plants",
    "outdoor lighting",
  ],
  Nursery: [
    "Crib",
    "changing table",
    "rocking chair",
    "bookshelves",
    "diaper pail",
  ],
  "Game room": [
    "Game table",
    "bar stools",
    "pool table",
    "foosball table",
    "darts board",
  ],
  "Laundry room": [
    "Washer and dryer",
    "shelving unit",
    "hamper",
    "ironing board",
    "laundry basket",
  ],
  "Home office": [
    "Desk",
    "office chair",
    "file cabinet",
    "bookshelves",
    "desk lamp",
    "area rug",
  ],
  "Guest room": [
    "Bed",
    "nightstand",
    "dresser",
    "wardrobe",
    "bedside lamp",
    "area rug",
  ],
  "Outdoor kitchen": [
    "Grill",
    "counter stools",
    "outdoor refrigerator",
    "sink",
    "cabinets",
  ],
  Garage: ["Workbench", "shelves", "tool storage", "bike rack"],
  "Living room": [
    "Couch",
    "sofa",
    "coffee table",
    "bookshelves",
    "armchair",
    "area rug",
  ],
  Bedroom: [
    "Bed",
    "nightstand",
    "dresser",
    "wardrobe",
    "bedside lamp",
    "area rug",
  ],
  Kitchen: [
    "Kitchen table",
    "chairs",
    "bar stools",
    "counter stools",
    "refrigerator",
    "stove",
    "microwave",
    "sink",
  ],
  "Dining room": [
    "Dining table",
    "chairs",
    "buffet",
    "china cabinet",
    "area rug",
  ],
  Office: [
    "Desk",
    "office chair",
    "file cabinet",
    "bookshelves",
    "desk lamp",
    "area rug",
  ],
  Library: ["Bookshelves", "reading chair", "rug", "desk", "desk lamp"],
  Bathroom: ["Vanity", "shower curtain", "towel rack", "mirror", "bathmat"],
  "Home gym": [
    "Exercise equipment",
    "mats",
    "wall mirror",
    "water bottle holder",
  ],
  "Wine cellar": [
    "Wine racks",
    "wine fridge",
    "wine glasses",
    "bar stools",
    "wine opener",
  ],
  Playroom: ["Play structure", "swings", "slides", "climbing wall", "sandbox"],
  "Garden shed": [
    "Workbench",
    "shelves",
    "tool storage",
    "watering cans",
    "garden tools",
  ],
  "Art studio": [
    "Art desk",
    "drafting table",
    "easel",
    "paint palette",
    "canvas storage",
  ],
  "Music room": [
    "Piano",
    "music stand",
    "instruments",
    "music books",
    "music storage",
  ],
  "Pool house": [
    "Wet bar",
    "pool table",
    "outdoor shower",
    "pool toys",
    "beach towels",
  ],
  Sauna: [
    "Sauna stove",
    "sauna benches",
    "sauna bucket",
    "sauna thermometer",
    "sauna heater",
  ],
  "Guest cottage": [
    "Bed",
    "nightstand",
    "dresser",
    "wardrobe",
    "bedside lamp",
    "area rug",
  ],
  "Tiki bar": [
    "Wet bar",
    "bar stools",
    "tiki torches",
    "outdoor lighting",
    "tropical decor",
  ],
  "Home spa": [
    "Massage table",
    "spa tub",
    "spa chair",
    "spa storage",
    "spa lighting",
  ],
  "Craft room": [
    "Art desk",
    "drafting table",
    "easel",
    "paint palette",
    "canvas storage",
  ],
  "Sewing room": [
    "Sewing machine",
    "cutting table",
    "ironing board",
    "fabric storage",
    "sewing basket",
  ],
  "Music studio": [
    "Piano",
    "music stand",
    "instruments",
    "music books",
    "music storage",
  ],
  "Yoga studio": [
    "Yoga mats",
    "yoga blocks",
    "yoga straps",
    "meditation cushions",
    "yoga blankets",
  ],
  "Home theater": [
    "Theater seating",
    "sound system",
    "media storage",
    "wall-mounted TV",
  ],
  "Reading nook": [
    "Reading chair",
    "ottoman",
    "bookshelves",
    "floor lamp",
    "throw pillows",
  ],
  "Meditation room": [
    "Meditation cushion",
    "incense burner",
    "meditation beads",
    "meditation music",
    "meditation candle",
  ],
  Bar: [
    "Wet bar",
    "bar stools",
    "bar fridge",
    "bar glasses",
    "bar accessories",
  ],
  Garden: [
    "Garden tools",
    "watering cans",
    "garden hose",
    "garden gloves",
    "garden storage",
  ],
  "Home brewery": [
    "Fermenting equipment",
    "brewing kettle",
    "kegs",
    "bottles",
    "bottle capper",
  ],
};

const decorativeLighting = {
  ArtDeco: [
    "Geometric chandelier",
    "Fringed lamp shades",
    "Elegant wall sconces",
    "Gilded table lamps",
    "Murano glass pendants",
  ],
  ArtNouveau: [
    "Organic-shaped chandelier",
    "Stained glass lamp shades",
    "Elegant wall sconces",
    "Curving table lamps",
    "Floral-inspired pendants",
  ],
  Bohemian: [
    "Beaded chandelier",
    "Colorful fringe lamp shades",
    "Macrame wall sconces",
    "Painted table lamps",
    "Moroccan-inspired pendants",
  ],
  Coastal: [
    "Rope-wrapped chandelier",
    "Seashell lamp shades",
    "Nautical wall sconces",
    "Beach-inspired table lamps",
    "Starfish pendants",
  ],
  Contemporary: [
    "Minimalist chandelier",
    "Clear glass lamp shades",
    "LED wall sconces",
    "Minimalist table lamps",
    "Geometric pendants",
  ],
  Cyberpunk: [
    "Neon chandelier",
    "Futuristic lamp shades",
    "Industrial wall sconces",
    "LED table lamps",
    "Robotic pendants",
  ],
  Eclectic: [
    "Mismatched chandelier",
    "Vintage lamp shades",
    "Unique wall sconces",
    "Vintage table lamps",
    "Repurposed pendants",
  ],
  Farmhouse: [
    "Rustic chandelier",
    "Linen lamp shades",
    "Barn wall sconces",
    "Distressed table lamps",
    "Mason jar pendants",
  ],
  FrenchCountry: [
    "Ornate chandelier",
    "Silk lamp shades",
    "Candle wall sconces",
    "Antique table lamps",
    "Fleur-de-lis pendants",
  ],
  Gothic: [
    "Dark chandelier",
    "Black lamp shades",
    "Candle wall sconces",
    "Victorian table lamps",
    "Skull pendants",
  ],
  HollywoodGlamour: [
    "Crystal chandelier",
    "Fringed lamp shades",
    "Elegant wall sconces",
    "Gilded table lamps",
    "Murano glass pendants",
  ],
  IndustrialChic: [
    "Exposed bulb chandelier",
    "Edison lamp shades",
    "Industrial wall sconces",
    "Vintage table lamps",
    "Metal pendants",
  ],
  Japanese: [
    "Paper lanterns",
    "Zen garden lights",
    "Bamboo or wood pendants",
    "Shoji screen lamps",
    "Minimalist wall sconces",
  ],
  Mediterranean: [
    "Terracotta lamps",
    "Iron chandeliers",
    "Mosaic table lamps",
    "Woven pendants",
    "Ornate wall sconces",
  ],
  Midcenturymodern: [
    "Sputnik chandeliers",
    "Floor lamps with sculptural bases",
    "Ceiling pendants with geometric shapes",
    "Table lamps with colorful shades",
    "Wall sconces with sleek lines",
  ],
  Minimalist: [
    "Clean-lined pendants",
    "LED strip lights",
    "Floor lamps with simple designs",
    "Table lamps with minimalistic shades",
    "Recessed lighting",
  ],
  Modern: [
    "LED strip lights",
    "Sleek pendants",
    "Floor lamps with sculptural bases",
    "Table lamps with sculptural bases",
    "Recessed lighting",
  ],
  Moroccan: [
    "Henna lamps",
    "Moroccan lanterns",
    "Mosaic pendants",
    "Brass wall sconces",
    "Tasseled table lamps",
  ],
  Nautical: [
    "Anchor lights",
    "Rope pendants",
    "Port hole wall sconces",
    "Vintage ship lanterns",
    "Cage pendants",
  ],
  PopArt: [
    "Brightly colored pendants",
    "Floor lamps with bold designs",
    "Table lamps with pop art inspired shades",
    "Wall sconces with pop art inspired shades",
    "Recessed lighting",
  ],
  Retro: [
    "Atomic chandeliers",
    "Floor lamps with sculptural bases",
    "Table lamps with colorful shades",
    "Sputnik pendants",
    "Wall sconces with sleek lines",
  ],
  Rustic: [
    "Mason jar pendants",
    "Wagon wheel chandeliers",
    "Iron wall sconces",
    "Table lamps with rawhide shades",
    "Floor lamps with tree trunk bases",
  ],
  Scandinavian: [
    "Minimalistic pendants",
    "Floor lamps with sleek designs",
    "Table lamps with simple shades",
    "LED strip lights",
    "Recessed lighting",
  ],
  ShabbyChic: [
    "Vintage chandeliers",
    "Distressed table lamps",
    "Ornate wall sconces",
    "Frilly lamp shades",
    "Chic candle holders",
  ],
  Traditional: [
    "Brass chandeliers",
    "Crystal wall sconces",
    "Tiffany-style table lamps",
    "Gold-plated table lamps",
    "Antique candelabras",
  ],
  Transitional: [
    "Sleek chandeliers",
    "Wall sconces with simple designs",
    "Table lamps with white shades",
    "LED strip lights",
    "Recessed lighting",
  ],
  Tropical: [
    "Woven pendants",
    "Floor lamps with bamboo shades",
    "Table lamps with palm leaf shades",
    "LED strip lights",
    "Recessed lighting",
  ],
  Victorian: [
    "Crystal chandeliers",
    "Wall sconces with intricate designs",
    "Table lamps with fringed shades",
    "Gold-plated table lamps",
    "Antique candelabras",
  ],
  Vintage: [
    "Exposed bulb chandeliers",
    "Wall sconces with vintage designs",
    "Table lamps with vintage shades",
    "Floor lamps with vintage designs",
    "Antique candelabras",
  ],
  Western: [
    "Rustic chandeliers",
    "Wall sconces with western designs",
    "Table lamps with western shades",
    "Floor lamps with western designs",
    "Antique candelabras",
  ],
};
const data = {
  ArtDeco: {
    style: "Art Deco",
    colorPalette: "Bold, geometric shapes, metallic accents",
    feeling: "Glamorous, opulent, luxurious",
    materials: "Glass, metal, marble, mirrors, lacquer",
    furnitureTypes: "Geometric, angular, ornate",
  },
  ArtNouveau: {
    style: "Art Nouveau",
    colorPalette: "Curves, organic forms, pastel colors",
    feeling: "Elegant, romantic, decorative",
    materials: "Glass, metal, ceramic, stone, fabric",
    furnitureTypes: "Curved, organic, decorative",
  },
  Bohemian: {
    style: "Bohemian",
    colorPalette: "Bright, bold, earthy (orange, red, yellow, green)",
    feeling: "Free-spirited, artistic, eclectic",
    materials: "Wood, metal, stone, ceramic, fabric",
    furnitureTypes: "Vintage, handcrafted, mismatched",
  },
  Coastal: {
    style: "Coastal",
    colorPalette: "Pale blue, sandy beige, white",
    feeling: "Relaxed, airy, beachy",
    materials: "Wood, wicker, jute, seashells, coral",
    furnitureTypes: "Rustic, distressed, woven",
  },
  Contemporary: {
    style: "Contemporary",
    colorPalette: "Neutral tones, pops of bright color",
    feeling: "Sleek, modern, minimalist",
    materials: "Glass, metal, concrete, acrylic, natural woods",
    furnitureTypes: "Geometric, streamlined, minimal",
  },
  Cyberpunk: {
    style: "Cyberpunk",
    colorPalette: "Neon colors, metallic accents, pops of bright color",
    feeling: "Futuristic, edgy, high-tech",
    materials: "Metal, acrylic, holographic displays, neon lights",
    furnitureTypes: "Sleek, modern, high-tech",
  },
  Eclectic: {
    style: "Eclectic",
    colorPalette:
      "Varied, mix of patterns and textures (floral, geometric, abstract)",
    feeling: "Playful, artistic, spontaneous",
    materials: "Wood, metal, stone, ceramic, fabric",
    furnitureTypes: "Vintage, repurposed, mismatched",
  },
  Farmhouse: {
    style: "Farmhouse",
    colorPalette:
      "Neutral tones (white, beige, gray), pops of pastel (pink, blue, green)",
    feeling: "Homey, cozy, rustic",
    materials: "Wood, metal, stone, ceramic, fabric",
    furnitureTypes: "Distressed, reclaimed, handmade",
  },
  FrenchCountry: {
    style: "French Country",
    colorPalette: "Muted, earthy tones, pops of blue and yellow",
    feeling: "Rustic, romantic, charming",
    materials: "Wood, stone, metal, ceramic, fabric",
    furnitureTypes: "Rustic, carved, ornate",
  },
  Gothic: {
    style: "Gothic",
    colorPalette: "Deep, rich tones, pops of metallic",
    feeling: "Dramatic, mysterious, elegant",
    materials: "Stone, wood, metal, velvet, leather",
    furnitureTypes: "Ornate, carved, grand",
  },
  HollywoodGlamour: {
    style: "Hollywood Glamour",
    colorPalette: "Bold, metallic accents, pops of bright color",
    feeling: "Glamorous, sophisticated, extravagant",
    materials: "Glass, metal, marble, mirrors, lacquer",
    furnitureTypes: "Ornate, glamorous, bold",
  },
  IndustrialChic: {
    style: "Industrial Chic",
    colorPalette:
      "Neutral tones (black, white, gray), pops of metallic (silver, copper, brass)",
    feeling: "Edgy, urban, sophisticated, raw",
    materials: "Concrete, metal, brick, wood, glass",
    furnitureTypes: "Industrial, reclaimed, chic, utillitarian",
  },
  Japanese: {
    style: "Japanese",
    colorPalette: "Neutral tones, pops of red, yellow, and green",
    feeling: "Peaceful, minimal, Zen",
    materials: "Wood, stone, metal, ceramic, fabric",
    furnitureTypes: "Minimal, streamlined, natural",
  },
  Mediterranean: {
    style: "Mediterranean",
    colorPalette: "Warm, earthy tones, pops of blue and yellow",
    feeling: "Relaxed, elegant, rustic",
    materials: "Terracotta, stone, tile, wood, metal",
    furnitureTypes: "Rustic, carved, ornate",
  },
  Midcenturymodern: {
    style: "Mid-century modern",
    colorPalette:
      "Bold, primary colors (red, yellow, blue), pops of pastel (pink, green, orange)",
    feeling: "Retro, futuristic, sleek",
    materials: "Wood, metal, plastic, fiberglass, vinyl",
    furnitureTypes: "Geometric, angular, minimal",
  },
  Minimalist: {
    style: "Minimalist",
    colorPalette: "Neutral tones, minimal ornamentation",
    feeling: "Simple, functional, uncluttered",
    materials: "Glass, metal, concrete, acrylic, natural woods",
    furnitureTypes: "Sleek, minimal, functional",
  },
  Modern: {
    style: "Modern",
    colorPalette:
      "Monochromatic (all shades of one color), neutral tones (black, white, gray), pops of bright color (red, yellow, blue)",
    feeling: "Sleek, minimalist, clean",
    materials:
      "Glass, steel, concrete, acrylic, natural woods (bamboo, maple, walnut)",
    furnitureTypes: "Geometric, streamlined, minimal",
  },
  Moroccan: {
    style: "Moroccan",
    colorPalette: "Rich, warm tones, intricate patterns",
    feeling: "Exotic, colorful, bold",
    materials: "Wood, metal, stone, ceramic, fabric",
    furnitureTypes: "Intricately carved, ornate, colorful",
  },
  Nautical: {
    style: "Nautical",
    colorPalette: "Navy blue, white, pops of red",
    feeling: "Relaxed, beachy, nautical",
    materials: "Wood, metal, stone, ceramic, fabric",
    furnitureTypes: "Nautical",
  },
  "Pop Art": {
    style: "Pop Art",
    colorPalette: "Bright, bold colors, graphic patterns",
    feeling: "Playful, colorful, energetic",
    materials: "Plastic, metal, wood, fabric, acrylic",
    furnitureTypes: "Geometric, colorful, bold",
  },
  Retro: {
    style: "Retro",
    colorPalette: "Bright, bold colors, geometric patterns",
    feeling: "Fun, playful, nostalgiac",
    materials: "Plastic, metal, wood, vinyl, fabric",
    furnitureTypes: "Geometric, angular, bold",
  },
  Rustic: {
    style: "Rustic",
    colorPalette:
      "Neutral tones (brown, beige, white), pops of earthy color (green, red, orange)",
    feeling: "Cozy, warm, natural",
    materials: "Wood, stone, metal, natural fibers",
    furnitureTypes: "Distressed, handmade, reclaimed",
  },
  Scandinavian: {
    style: "Scandinavian",
    colorPalette:
      "Neutral tones (white, beige, gray), pops of pastel (pink, blue, green)",
    feeling: "Clean, simple, minimalist",
    materials: "Wood, metal, stone, ceramic, fabric",
    furnitureTypes: "Sleek, minimal, functional",
  },
  "Shabby Chic": {
    style: "Shabby Chic",
    colorPalette: "Muted, pastel colors, distressed finishes",
    feeling: "Romantic, vintage, cozy",
    materials: "Wood, metal, stone, ceramic, fabric",
    furnitureTypes: "Distressed, vintage, romantic",
  },
  Southwestern: {
    style: "Southwestern",
    colorPalette: "Earthy tones (orange, red, yellow), pops of turquoise",
    feeling: "Warm, rustic, colorful",
    materials: "Wood, stone, metal, ceramic, fabric",
    furnitureTypes: "Rustic, handcrafted, colorful",
  },
  Tropical: {
    style: "Tropical",
    colorPalette: "Bright, bold, tropical (yellow, green, pink, orange)",
    feeling: "Bright, bold, tropical",
    materials: "Wood, metal, stone, ceramic, fabric",
    furnitureTypes: "Bright, bold, tropical",
  },
  Victorian: {
    style: "Victorian",
    colorPalette: "Muted, pastel colors, pops of deep red",
    feeling: "Elegant, ornate, sophisticated",
    materials: "Wood, metal, stone, ceramic, fabric",
    furnitureTypes: "Ornate, carved, grand",
  },
  Vintage: {
    style: "Vintage",
    colorPalette: "Varied, mix of patterns and textures from past decades",
    feeling: "Nostalgic, antique, retro",
    materials: "Wood, metal, stone, ceramic, fabric",
    furnitureTypes: "Vintage, antique, retro",
  },
  Western: {
    style: "Western",
    colorPalette:
      "Earthy tones (brown, beige, green), pops of red and turquoise",
    feeling: "Rustic, natural, laid-back",
    materials: "Wood, stone, metal, ceramic, fabric",
    furnitureTypes: "Rustic, distressed, handmade",
  },
};

const decorativeAccents = {
  ArtDeco: [
    "Geometric sculpture",
    "Metallic vase",
    "Marble figurine",
    "Art deco mirror",
    "Vintage clock",
  ],
  ArtNouveau: [
    "Flowing lines sculpture",
    "Organic shaped vase",
    "Nature inspired figurine",
    "Art nouveau mirror",
    "Vintage clock",
  ],
  Bohemian: [
    "Vintage tapestries",
    "Mismatched throw pillows",
    "Handmade dream catchers",
    "Macrame wall hanging",
    "Vintage lanterns",
  ],
  Coastal: [
    "Starfish decor",
    "Seashell decor",
    "Beach-inspired paintings",
    "Coastal-inspired throw pillows",
    "Lighthouse decor",
  ],
  Contemporary: [
    "Abstract sculpture",
    "Minimalistic vase",
    "Geometric figurine",
    "Contemporary mirror",
    "Large wall art",
  ],
  Cyberpunk: [
    "Neon signs",
    "Robotic decor",
    "Futuristic sculptures",
    "LED wall art",
    "Virtual reality glasses",
  ],
  Eclectic: [
    "Mismatched decor",
    "Vintage posters",
    "Handmade crafts",
    "Mix-and-match throw pillows",
    "Vintage trinkets",
  ],
  Farmhouse: [
    "Vintage milk cans",
    "Galvanized metal decor",
    "Windmill wall decor",
    "Vintage signs",
    "Farm animal figurines",
  ],
  FrenchCountry: [
    "Vintage french posters",
    "Elegant vases",
    "Ornate candelabras",
    "Vintage french clocks",
    "Fleur-de-lis decor",
  ],
  Gothic: [
    "Skull decor",
    "Candles in ornate holders",
    "Gothic-inspired paintings",
    "Vintage candelabras",
    "Gothic-inspired tapestries",
  ],
  HollywoodGlamour: [
    "Vintage Hollywood posters",
    "Glittering decor",
    "Hollywood-inspired paintings",
    "Vintage Hollywood cameras",
    "Hollywood-inspired figurines",
  ],
  IndustrialChic: [
    "Exposed pipes decor",
    "Vintage factory equipment",
    "Industrial wall art",
    "Vintage metal signs",
    "Metal gears",
  ],
  Japanese: [
    "Zen garden decor",
    "Bamboo decor",
    "Japanese-inspired paintings",
    "Japanese-inspired throw pillows",
    "Japanese-inspired lanterns",
  ],
  Mediterranean: [
    "Terracotta decor",
    "Mediterranean-inspired paintings",
    "Mediterranean-inspired throw pillows",
    "Mediterranean-inspired lanterns",
    "Mediterranean-inspired vases",
  ],
  MidCenturyModern: [
    "Mid-century modern sculpture",
    "Mid-century modern vase",
    "Mid-century modern figurine",
    "Mid-century modern mirror",
    "Mid-century modern wall art",
  ],
  Minimalist: [
    "Minimalistic sculpture",
    "Minimalistic vase",
    "Minimalistic figurine",
    "Minimalistic mirror",
    "Minimalistic wall art",
  ],
  Modern: [
    "Abstract sculpture",
    "Minimalistic vase",
    "Geometric figurine",
    "Contemporary mirror",
    "Large wall art",
  ],
  Moroccan: [
    "Moroccan-inspired tapestries",
    "Moroccan-inspired throw pillows",
    "Moroccan-inspired lanterns",
    "Moroccan-inspired vases",
    "Moroccan-inspired wall art",
  ],
  Nautical: [
    "Anchor decor",
    "Nautical-inspired paintings",
    "Nautical-inspired throw pillows",
    "Nautical-inspired lanterns",
    "Nautical-inspired vases",
  ],
  PopArt: [
    "Pop art sculptures",
    "Pop art vases",
    "Pop art figurines",
    "Pop art mirrors",
    "Pop art wall art",
  ],
  Retro: [
    "Retro sculptures",
    "Retro vases",
    "Retro figurines",
    "Retro mirrors",
    "Retro wall art",
  ],
  Rustic: [
    "Rustic sculptures",
    "Rustic vases",
    "Rustic figurines",
    "Rustic mirrors",
    "Rustic wall art",
  ],
  Scandinavian: [
    "Minimalistic sculptures",
    "Sleek vases",
    "Simple figurines",
    "Minimalistic mirrors",
    "Scandinavian-inspired wall art",
  ],
  ShabbyChic: [
    "Vintage decor",
    "Shabby chic-inspired paintings",
    "Shabby chic-inspired throw pillows",
    "Shabby chic-inspired vases",
    "Shabby chic-inspired wall art",
  ],
  Steampunk: [
    "Steampunk-inspired sculptures",
    "Steampunk-inspired vases",
    "Steampunk-inspired figurines",
    "Steampunk-inspired mirrors",
    "Steampunk-inspired wall art",
  ],
  Traditional: [
    "Traditional sculptures",
    "Traditional vases",
    "Traditional figurines",
    "Traditional mirrors",
    "Traditional wall art",
  ],
  Transitional: [
    "Transitional sculptures",
    "Transitional vases",
    "Transitional figurines",
    "Transitional mirrors",
    "Transitional wall art",
  ],
  Tropical: [
    "Tropical sculptures",
    "Tropical vases",
    "Tropical figurines",
    "Tropical mirrors",
    "Tropical wall art",
  ],
  Victorian: [
    "Victorian-inspired sculptures",
    "Victorian-inspired vases",
    "Victorian-inspired figurines",
    "Victorian-inspired mirrors",
    "Victorian-inspired wall art",
  ],
  Vintage: [
    "Vintage sculptures",
    "Vintage vases",
    "Vintage figurines",
    "Vintage mirrors",
    "Vintage wall art",
  ],
  Western: [
    "Western-inspired sculptures",
    "Western-inspired vases",
    "Western-inspired figurines",
    "Western-inspired mirrors",
    "Western-inspired wall art",
  ],
};

const styleColors = {
  ArtDeco: ["black", "gold", "silver", "beige", "white"],
  ArtNouveau: ["green", "blue", "purple", "pink", "yellow"],
  Bohemian: ["orange", "red", "brown", "cream", "gray"],
  Coastal: ["blue", "white", "gray", "beige", "green"],
  Contemporary: ["white", "black", "gray", "beige", "silver"],
  Cyberpunk: ["black", "silver", "neon", "white", "gray"],
  Eclectic: ["multi-colored", "vibrant", "mismatched", "bold", "unique"],
  Farmhouse: ["white", "beige", "light gray", "light blue", "light green"],
  FrenchCountry: ["white", "blue", "yellow", "pink", "green"],
  Gothic: ["black", "purple", "dark red", "dark gray", "dark blue"],
  HollywoodGlamour: ["gold", "silver", "black", "white", "red"],
  IndustrialChic: ["black", "silver", "gray", "white", "brown"],
  Japanese: ["black", "white", "gray", "beige", "light wood"],
  Mediterranean: ["white", "blue", "orange", "yellow", "green"],
  MidCenturyModern: ["orange", "teal", "brown", "beige", "white"],
  Minimalist: ["white", "black", "gray", "beige", "silver"],
  Modern: ["white", "black", "gray", "beige", "silver"],
  Moroccan: ["orange", "red", "green", "yellow", "blue"],
  Nautical: ["blue", "white", "gray", "red", "brown"],
  PopArt: ["bright", "bold", "vibrant", "multi-colored", "neon"],
  Retro: ["pastel", "vintage", "bold", "muted", "funky"],
  Rustic: ["brown", "beige", "green", "orange", "dark red"],
  Scandinavian: ["white", "gray", "black", "light wood", "light blue"],
  ShabbyChic: ["pink", "white", "gray", "beige", "light blue"],
  Steampunk: ["brown", "black", "silver", "copper", "bronze"],
  Traditional: ["red", "green", "blue", "yellow", "orange"],
  Transitional: ["white", "black", "gray", "beige", "silver"],
  Tropical: ["green", "blue", "yellow", "orange", "red"],
  Victorian: [
    "dark red",
    "dark blue",
    "dark green",
    "dark purple",
    "dark gray",
  ],
  Vintage: ["pastel", "vintage", "muted", "bold", "funky"],
  Western: ["brown", "beige", "orange", "green", "blue"],
};

const rooms = [
  "Home theater",
  "Sunroom",
  "Porch",
  "Nursery",
  "Game room",
  "Laundry room",
  "Home office",
  "Guest room",
  "Outdoor kitchen",
  "Garage",
  "Living room",
  "Bedroom",
  "Kitchen",
  "Dining room",
  "Office",
  "Library",
  "Bathroom",
  "Home gym",
  "Wine cellar",
  "Playroom",
  "Garden shed",
  "Art studio",
  "Music room",
  "Pool house",
  "Sauna",
  "Guest cottage",
  "Tiki bar",
  "Home spa",
  "Craft room",
  "Sewing room",
  "Music studio",
  "Yoga studio",
  "Home theater",
  "Reading nook",
  "Meditation room",
  "Bar",
  "Garden",
  "Home brewery",
];

let text = "";

for (let i = 0; i < rooms.length; i++) {
  const room = rooms[i];
  const style = data[room].style;
  const colorPalette = data[room].colorPalette;
  const feeling = data[room].feeling;
  const materials = data[room].materials;
  const furnitureTypes = data[room].furnitureTypes;

  text += `Home decor Photo from Pinterest of ${style} ${room}, ${colorPalette}, ${feeling}, ${materials}, ${furnitureTypes}.`;
}

console.log(text);
