export interface StyleDetails {
  id: string;
  name: string;
  category: StyleCategory;
  subcategory: StyleSubcategory;
  colorPalette: string;
  feeling: string;
  materials: string[];
  furnitureTypes: string[];
  features?: string[];
  iconName?: string;
  tags?: string[];
  accessLevel: "free" | "pro" | "premium";
  previewImages: string[];
  thumbnailImage?: string;
  description?: string;
  popularityScore?: number;
  isNew?: boolean;
  isFeatured?: boolean;
}

export enum StyleCategory {
  Historical = "Historical Styles",
  Modern = "Modern & Contemporary",
  Cultural = "Cultural & Regional",
  Thematic = "Thematic & Specialty",
  Natural = "Natural & Organic",
  Seasonal = "Seasonal & Holiday",
}

export enum StyleSubcategory {
  // Historical
  Classical = "Classical",
  Revival = "Revival",
  Vintage = "Vintage & Retro",

  // Modern
  Contemporary = "Contemporary",
  Minimalist = "Minimalist",
  Industrial = "Industrial",

  // Cultural
  Asian = "Asian",
  European = "European",
  Mediterranean = "Mediterranean",

  // Thematic
  Luxury = "Luxury & Glamour",
  Eclectic = "Eclectic & Artistic",
  Urban = "Urban & Modern",

  // Natural
  Coastal = "Coastal & Beach",
  Rustic = "Rustic & Country",
  Tropical = "Tropical & Exotic",

  // Seasonal
  WinterHoliday = "Winter Holiday",
  FallHoliday = "Fall Holiday",
  SpringHoliday = "Spring Holiday",
  SummerHoliday = "Summer Holiday",
}

export const styleData: Record<string, StyleDetails> = {
  artDeco: {
    id: "artDeco",
    name: "Art Deco",
    category: StyleCategory.Historical,
    subcategory: StyleSubcategory.Revival,
    colorPalette: "Bold geometric patterns with metallic accents",
    feeling: "Glamorous, luxurious, sophisticated",
    materials: [
      "Chrome",
      "Glass",
      "Mirror",
      "Marble",
      "Exotic Woods",
      "Lacquer",
    ],
    furnitureTypes: [
      "Geometric Forms",
      "Streamlined Pieces",
      "Built-in Furniture",
      "Mirrored Surfaces",
    ],
    features: ["Bold Geometry", "Luxury Finishes", "Symmetrical Layouts"],
    iconName: "Diamond",
    tags: ["Luxury", "Geometric", "1920s", "Glamour"],
    accessLevel: "free",
    previewImages: ["/images/virtual-staging-style-images/art-deco.png"],
    thumbnailImage: "/images/virtual-staging-style-images/art-deco.png",
    description:
      "Glamorous Art Deco style with bold geometric patterns and luxurious finishes",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  victorian: {
    id: "victorian",
    name: "Victorian",
    category: StyleCategory.Historical,
    subcategory: StyleSubcategory.Classical,
    colorPalette: "Rich jewel tones with gold accents",
    feeling: "Ornate, sophisticated, traditional",
    materials: ["Dark Woods", "Velvet", "Crystal", "Brass", "Silk"],
    furnitureTypes: [
      "Tufted Furniture",
      "Carved Wood",
      "Ornate Frames",
      "Antique Pieces",
    ],
    features: ["Detailed Millwork", "High Ceilings", "Ornamental Elements"],
    iconName: "Crown",
    tags: ["Classic", "Ornate", "Traditional", "Elegant"],
    accessLevel: "free",
    previewImages: ["/images/virtual-staging-style-images/victorian.png"],
    thumbnailImage: "/images/virtual-staging-style-images/victorian.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  modernMinimalist: {
    id: "modernMinimalist",
    name: "Modern Minimalist",
    category: StyleCategory.Modern,
    subcategory: StyleSubcategory.Minimalist,
    colorPalette: "Neutral tones with monochromatic schemes",
    feeling: "Clean, serene, uncluttered",
    materials: ["Glass", "Steel", "Concrete", "Light Woods", "Natural Stone"],
    furnitureTypes: [
      "Clean Lines",
      "Simple Forms",
      "Multi-functional Pieces",
      "Built-in Storage",
    ],
    features: ["Open Spaces", "Hidden Storage", "Natural Light"],
    iconName: "Square",
    tags: ["Minimal", "Clean", "Contemporary", "Functional"],
    accessLevel: "free",
    previewImages: [
      "/images/virtual-staging-style-images/modern-minimalist.png",
    ],
    thumbnailImage:
      "/images/virtual-staging-style-images/modern-minimalist.png",
    description: "Clean, minimal design with focus on functionality and space",
    popularityScore: 0,
    isNew: true,
    isFeatured: false,
  },

  industrialLoft: {
    id: "industrialLoft",
    name: "Industrial Loft",
    category: StyleCategory.Modern,
    subcategory: StyleSubcategory.Industrial,
    colorPalette: "Raw materials with warm accents",
    feeling: "Urban, authentic, spacious",
    materials: [
      "Exposed Brick",
      "Steel",
      "Concrete",
      "Reclaimed Wood",
      "Metal Pipes",
    ],
    furnitureTypes: [
      "Factory-inspired",
      "Raw Materials",
      "Vintage Industrial",
      "Open Shelving",
    ],
    features: ["High Ceilings", "Exposed Structure", "Large Windows"],
    iconName: "Factory",
    tags: ["Urban", "Raw", "Warehouse", "Modern"],
    accessLevel: "free",
    previewImages: ["/images/virtual-staging-style-images/industrial-loft.png"],
    thumbnailImage: "/images/virtual-staging-style-images/industrial-loft.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  scandinavian: {
    id: "scandinavian",
    name: "Scandinavian",
    category: StyleCategory.Cultural,
    subcategory: StyleSubcategory.European,
    colorPalette: "White base with soft, muted colors",
    feeling: "Cozy, bright, functional",
    materials: ["Light Woods", "Natural Textiles", "Wool", "Glass", "Ceramics"],
    furnitureTypes: [
      "Simple Forms",
      "Functional Design",
      "Natural Materials",
      "Light Woods",
    ],
    features: ["Natural Light", "Minimal Decor", "Functional Spaces"],
    iconName: "Sun",
    tags: ["Hygge", "Nordic", "Minimal", "Functional"],
    accessLevel: "free",
    previewImages: ["/images/virtual-staging-style-images/scandinavian.png"],
    thumbnailImage: "/images/virtual-staging-style-images/scandinavian.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  japaneseZen: {
    id: "japaneseZen",
    name: "Japanese Zen",
    category: StyleCategory.Cultural,
    subcategory: StyleSubcategory.Asian,
    colorPalette: "Natural earth tones with black accents",
    feeling: "Peaceful, balanced, minimal",
    materials: ["Bamboo", "Rice Paper", "Tatami", "Natural Wood", "Stone"],
    furnitureTypes: [
      "Low-profile Furniture",
      "Floor Cushions",
      "Sliding Panels",
      "Platform Beds",
    ],
    features: ["Natural Elements", "Simple Lines", "Indoor-Outdoor Flow"],
    iconName: "Zen",
    tags: ["Minimal", "Peaceful", "Natural", "Balance"],
    accessLevel: "free",
    previewImages: ["/images/virtual-staging-style-images/japanese-zen.png"],
    thumbnailImage: "/images/virtual-staging-style-images/japanese-zen.png",
    description:
      "Traditional Japanese aesthetics with modern minimalist touches",
    popularityScore: 0,
    isNew: false,
    isFeatured: true,
  },

  hollywoodGlam: {
    id: "hollywoodGlam",
    name: "Hollywood Glam",
    category: StyleCategory.Thematic,
    subcategory: StyleSubcategory.Luxury,
    colorPalette: "Black and white with metallic accents",
    feeling: "Luxurious, dramatic, sophisticated",
    materials: ["Velvet", "Mirror", "Crystal", "Gold", "Marble"],
    furnitureTypes: [
      "Tufted Furniture",
      "Mirrored Pieces",
      "Statement Lighting",
      "Plush Seating",
    ],
    features: ["Statement Pieces", "Dramatic Lighting", "Luxe Textures"],
    iconName: "Star",
    tags: ["Glamour", "Luxury", "Drama", "Elegant"],
    accessLevel: "pro",
    previewImages: ["/images/virtual-staging-style-images/hollywood-glam.png"],
    thumbnailImage: "/images/virtual-staging-style-images/hollywood-glam.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  bohemianEclectic: {
    id: "bohemianEclectic",
    name: "Bohemian Eclectic",
    category: StyleCategory.Thematic,
    subcategory: StyleSubcategory.Eclectic,
    colorPalette: "Rich jewel tones with layered patterns",
    feeling: "Free-spirited, artistic, collected",
    materials: [
      "Natural Fibers",
      "Vintage Textiles",
      "Rattan",
      "Mixed Woods",
      "Leather",
    ],
    furnitureTypes: [
      "Vintage Pieces",
      "Floor Cushions",
      "Mixed Patterns",
      "Global Accents",
    ],
    features: ["Layer Textures", "Plant Life", "Global Influences"],
    iconName: "Feather",
    tags: ["Boho", "Eclectic", "Artistic", "Global"],
    accessLevel: "free",
    previewImages: [
      "/images/virtual-staging-style-images/bohemian-eclectic.png",
    ],
    thumbnailImage:
      "/images/virtual-staging-style-images/bohemian-eclectic.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  coastalBeach: {
    id: "coastalBeach",
    name: "Coastal Beach",
    category: StyleCategory.Natural,
    subcategory: StyleSubcategory.Coastal,
    colorPalette: "Ocean blues with sandy neutrals",
    feeling: "Relaxed, breezy, fresh",
    materials: ["Weathered Wood", "Linen", "Jute", "Rattan", "Sea Glass"],
    furnitureTypes: [
      "Slipcovered Seating",
      "Woven Furniture",
      "Distressed Wood",
      "Natural Fiber Rugs",
    ],
    features: ["Natural Light", "Ocean Views", "Breezy Textiles"],
    iconName: "Wave",
    tags: ["Beach", "Coastal", "Relaxed", "Natural"],
    accessLevel: "free",
    previewImages: ["/images/virtual-staging-style-images/coastal-beach.png"],
    thumbnailImage: "/images/virtual-staging-style-images/coastal-beach.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  modernFarmhouse: {
    id: "modernFarmhouse",
    name: "Modern Farmhouse",
    category: StyleCategory.Natural,
    subcategory: StyleSubcategory.Rustic,
    colorPalette: "Warm whites with black accents",
    feeling: "Cozy, welcoming, refined rustic",
    materials: ["Reclaimed Wood", "Shiplap", "Iron", "Cotton", "Stone"],
    furnitureTypes: [
      "Comfortable Upholstery",
      "Farmhouse Tables",
      "Industrial Accents",
      "Vintage Pieces",
    ],
    features: ["Mixed Materials", "Open Concept", "Natural Elements"],
    iconName: "Home",
    tags: ["Rustic", "Modern", "Cozy", "Natural"],
    accessLevel: "free",
    previewImages: [
      "/images/virtual-staging-style-images/modern-farmhouse.png",
    ],
    thumbnailImage: "/images/virtual-staging-style-images/modern-farmhouse.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  tropicalLuxe: {
    id: "tropicalLuxe",
    name: "Tropical Luxe",
    category: StyleCategory.Natural,
    subcategory: StyleSubcategory.Tropical,
    colorPalette: "Lush greens with exotic accents",
    feeling: "Resort-like, luxurious, exotic",
    materials: [
      "Dark Woods",
      "Natural Stone",
      "Bamboo",
      "Rattan",
      "Tropical Prints",
    ],
    furnitureTypes: [
      "Indoor-Outdoor Pieces",
      "Plantation Style",
      "Natural Materials",
      "Statement Pieces",
    ],
    features: ["Indoor Plants", "Natural Elements", "Indoor-Outdoor Flow"],
    iconName: "Palm",
    tags: ["Tropical", "Luxury", "Resort", "Exotic"],
    accessLevel: "pro",
    previewImages: ["/images/virtual-staging-style-images/tropical-luxe.png"],
    thumbnailImage: "/images/virtual-staging-style-images/tropical-luxe.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  neoclassical: {
    id: "neoclassical",
    name: "Neoclassical",
    category: StyleCategory.Historical,
    subcategory: StyleSubcategory.Classical,
    colorPalette: "Muted pastels with gold and ivory",
    feeling: "Refined, symmetrical, stately",
    materials: ["Marble", "Limestone", "Gilded Wood", "Fine Fabrics", "Bronze"],
    furnitureTypes: [
      "Column Details",
      "Empire Style",
      "Symmetrical Pieces",
      "Classical Motifs",
    ],
    features: ["Architectural Details", "Grand Scale", "Classical Elements"],
    iconName: "Columns",
    tags: ["Classical", "Elegant", "Traditional", "Formal"],
    accessLevel: "free",
    previewImages: ["/images/virtual-staging-style-images/neoclassical.png"],
    thumbnailImage: "/images/virtual-staging-style-images/neoclassical.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  ultraModern: {
    id: "ultraModern",
    name: "Ultra Modern",
    category: StyleCategory.Modern,
    subcategory: StyleSubcategory.Contemporary,
    colorPalette: "High contrast with bold accent colors",
    feeling: "Cutting-edge, sophisticated, bold",
    materials: [
      "Smart Glass",
      "Carbon Fiber",
      "Polished Metal",
      "Composite Materials",
      "LED Integration",
    ],
    furnitureTypes: [
      "Modular Systems",
      "Floating Elements",
      "Integrated Technology",
      "Sculptural Pieces",
    ],
    features: ["Smart Home Integration", "Innovative Materials", "Bold Forms"],
    iconName: "Circuit",
    tags: ["Futuristic", "High-Tech", "Innovative", "Bold"],
    accessLevel: "premium",
    previewImages: ["/images/virtual-staging-style-images/ultra-modern.png"],
    thumbnailImage: "/images/virtual-staging-style-images/ultra-modern.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  moroccanRiad: {
    id: "moroccanRiad",
    name: "Moroccan Riad",
    category: StyleCategory.Cultural,
    subcategory: StyleSubcategory.Mediterranean,
    colorPalette: "Rich jewel tones with intricate patterns",
    feeling: "Exotic, layered, intimate",
    materials: [
      "Zellige Tiles",
      "Carved Wood",
      "Wrought Iron",
      "Colorful Textiles",
      "Brass",
    ],
    furnitureTypes: [
      "Floor Cushions",
      "Carved Furniture",
      "Metalwork Details",
      "Traditional Poufs",
    ],
    features: ["Courtyard Design", "Pattern Mixing", "Indoor-Outdoor Flow"],
    iconName: "Pattern",
    tags: ["Exotic", "Pattern-Rich", "Traditional", "Handcrafted"],
    accessLevel: "free",
    previewImages: ["/images/virtual-staging-style-images/moroccan-riad.png"],
    thumbnailImage: "/images/virtual-staging-style-images/moroccan-riad.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  artStudio: {
    id: "artStudio",
    name: "Art Studio Loft",
    category: StyleCategory.Thematic,
    subcategory: StyleSubcategory.Urban,
    colorPalette: "Neutral base with artistic color splashes",
    feeling: "Creative, inspiring, functional",
    materials: [
      "Raw Canvas",
      "Stained Concrete",
      "Metal",
      "Natural Wood",
      "Glass",
    ],
    furnitureTypes: [
      "Work Stations",
      "Display Systems",
      "Flexible Storage",
      "Mobile Furniture",
    ],
    features: ["Studio Lighting", "Work Zones", "Display Areas"],
    iconName: "Palette",
    tags: ["Creative", "Workspace", "Artistic", "Functional"],
    accessLevel: "free",
    previewImages: ["/images/virtual-staging-style-images/art-studio.png"],
    thumbnailImage: "/images/virtual-staging-style-images/art-studio.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  midCenturyPop: {
    id: "midCenturyPop",
    name: "Mid-Century Pop",
    category: StyleCategory.Modern,
    subcategory: StyleSubcategory.Contemporary,
    colorPalette: "Retro colors with contemporary twist",
    feeling: "Playful, nostalgic, modern",
    materials: [
      "Walnut Wood",
      "Molded Plastic",
      "Chrome",
      "Geometric Fabrics",
      "Glass",
    ],
    furnitureTypes: [
      "Iconic Reproductions",
      "Geometric Forms",
      "Statement Pieces",
      "Retro-Inspired",
    ],
    features: ["Period Details", "Modern Updates", "Playful Elements"],
    iconName: "Retro",
    tags: ["Retro", "Modern", "Playful", "Iconic"],
    accessLevel: "free",
    previewImages: ["/images/virtual-staging-style-images/mid-century-pop.png"],
    thumbnailImage: "/images/virtual-staging-style-images/mid-century-pop.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  asianFusion: {
    id: "asianFusion",
    name: "Asian Fusion",
    category: StyleCategory.Cultural,
    subcategory: StyleSubcategory.Asian,
    colorPalette: "Rich earth tones with contemporary accents",
    feeling: "Harmonious, sophisticated, balanced",
    materials: [
      "Dark Woods",
      "Natural Stone",
      "Rice Paper",
      "Metallic Accents",
      "Silk",
    ],
    furnitureTypes: [
      "Mixed Cultural Elements",
      "Contemporary Asian",
      "Statement Pieces",
      "Traditional Forms",
    ],
    features: ["Cultural Fusion", "Modern Elements", "Traditional Details"],
    iconName: "Balance",
    tags: ["Fusion", "Contemporary", "Cultural", "Balanced"],
    accessLevel: "pro",
    previewImages: ["/images/virtual-staging-style-images/asian-fusion.png"],
    thumbnailImage: "/images/virtual-staging-style-images/asian-fusion.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  mediterraneanVilla: {
    id: "mediterraneanVilla",
    name: "Mediterranean Villa",
    category: StyleCategory.Cultural,
    subcategory: StyleSubcategory.Mediterranean,
    colorPalette: "Sun-washed neutrals with sea blues",
    feeling: "Relaxed elegance, timeless, warm",
    materials: [
      "Terracotta",
      "Limestone",
      "Wrought Iron",
      "Ceramic Tiles",
      "Stucco",
    ],
    furnitureTypes: [
      "Hand-carved Wood",
      "Wrought Iron Pieces",
      "Comfortable Upholstery",
      "Traditional Forms",
    ],
    features: ["Arched Elements", "Indoor-Outdoor Living", "Rustic Elegance"],
    iconName: "Sun",
    tags: ["Mediterranean", "Elegant", "Rustic", "Timeless"],
    accessLevel: "free",
    previewImages: [
      "/images/virtual-staging-style-images/mediterranean-villa.png",
    ],
    thumbnailImage:
      "/images/virtual-staging-style-images/mediterranean-villa.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  contemporaryLuxe: {
    id: "contemporaryLuxe",
    name: "Contemporary Luxe",
    category: StyleCategory.Modern,
    subcategory: StyleSubcategory.Contemporary,
    colorPalette: "Sophisticated neutrals with metallic accents",
    feeling: "Luxurious, refined, contemporary",
    materials: [
      "High-gloss Finishes",
      "Exotic Woods",
      "Polished Metals",
      "Premium Textiles",
      "Stone",
    ],
    furnitureTypes: [
      "Designer Pieces",
      "Custom Elements",
      "Statement Lighting",
      "Artistic Forms",
    ],
    features: ["Luxury Details", "Custom Elements", "High-end Finishes"],
    iconName: "Luxury",
    tags: ["Luxury", "Contemporary", "Sophisticated", "High-end"],
    accessLevel: "premium",
    previewImages: [
      "/images/virtual-staging-style-images/contemporary-luxe.png",
    ],
    thumbnailImage:
      "/images/virtual-staging-style-images/contemporary-luxe.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  japaneseMinimalist: {
    id: "japaneseMinimalist",
    name: "Japanese Minimalist",
    category: StyleCategory.Cultural,
    subcategory: StyleSubcategory.Asian,
    colorPalette: "Neutral tones with natural accents",
    feeling: "Serene, minimal, balanced",
    materials: ["Light Wood", "Paper", "Bamboo", "Natural Textiles", "Stone"],
    furnitureTypes: [
      "Low-profile Furniture",
      "Minimalist Pieces",
      "Tatami Elements",
      "Built-in Storage",
    ],
    features: ["Clean Lines", "Natural Materials", "Zen Elements"],
    iconName: "Minimalist",
    tags: ["Minimal", "Japanese", "Zen", "Clean"],
    accessLevel: "premium",
    previewImages: [
      "/images/virtual-staging-style-images/japanese-minimalist.png",
    ],
    thumbnailImage:
      "/images/virtual-staging-style-images/japanese-minimalist.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  grandMillennial: {
    id: "grandMillennial",
    name: "Grand Millennial",
    category: StyleCategory.Thematic,
    subcategory: StyleSubcategory.Eclectic,
    colorPalette: "Traditional patterns with modern twists",
    feeling: "Nostalgic, sophisticated, personalized",
    materials: ["Chintz", "Wicker", "Antique Wood", "Brass", "Porcelain"],
    furnitureTypes: [
      "Vintage Pieces",
      "Floral Patterns",
      "Traditional Forms",
      "Mixed Antiques",
    ],
    features: ["Pattern Mixing", "Traditional Elements", "Modern Updates"],
    iconName: "Vintage",
    tags: ["Traditional", "Modern", "Eclectic", "Nostalgic"],
    accessLevel: "pro",
    previewImages: ["/images/virtual-staging-style-images/grand-millenial.png"],
    thumbnailImage: "/images/virtual-staging-style-images/grand-millenial.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  organicModern: {
    id: "organicModern",
    name: "Organic Modern",
    category: StyleCategory.Modern,
    subcategory: StyleSubcategory.Contemporary,
    colorPalette: "Earth tones with natural textures",
    feeling: "Warm, natural, sophisticated",
    materials: ["Natural Stone", "Raw Wood", "Linen", "Clay", "Jute"],
    furnitureTypes: [
      "Curved Forms",
      "Natural Materials",
      "Sculptural Pieces",
      "Handcrafted Items",
    ],
    features: ["Organic Shapes", "Natural Elements", "Textural Interest"],
    iconName: "Leaf",
    tags: ["Natural", "Modern", "Organic", "Sustainable"],
    accessLevel: "pro",
    previewImages: ["/images/virtual-staging-style-images/organic-modern.png"],
    thumbnailImage: "/images/virtual-staging-style-images/organic-modern.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  californianCasual: {
    id: "californianCasual",
    name: "Californian Casual",
    category: StyleCategory.Natural,
    subcategory: StyleSubcategory.Coastal,
    colorPalette: "Warm neutrals with coastal accents",
    feeling: "Relaxed, airy, effortless",
    materials: ["Light Woods", "Natural Fiber", "Linen", "Canvas", "Leather"],
    furnitureTypes: [
      "Casual Upholstery",
      "Modern Forms",
      "Natural Materials",
      "Layered Textures",
    ],
    features: ["Indoor-Outdoor Flow", "Natural Light", "Casual Elegance"],
    iconName: "Sun",
    tags: ["Casual", "Modern", "Natural", "Coastal"],
    accessLevel: "free",
    previewImages: [
      "/images/virtual-staging-style-images/californian-casual.png",
    ],
    thumbnailImage:
      "/images/virtual-staging-style-images/californian-casual.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  industrialGlam: {
    id: "industrialGlam",
    name: "Industrial Glam",
    category: StyleCategory.Modern,
    subcategory: StyleSubcategory.Industrial,
    colorPalette: "Industrial grays with metallic accents",
    feeling: "Sophisticated, edgy, luxurious",
    materials: ["Polished Concrete", "Brass", "Velvet", "Steel", "Glass"],
    furnitureTypes: [
      "Mixed Metal Pieces",
      "Plush Seating",
      "Industrial Elements",
      "Statement Lighting",
    ],
    features: ["Mixed Metals", "Luxe Details", "Industrial Elements"],
    iconName: "Sparkle",
    tags: ["Industrial", "Glamour", "Modern", "Luxe"],
    accessLevel: "pro",
    previewImages: ["/images/virtual-staging-style-images/industrial-glam.png"],
    thumbnailImage: "/images/virtual-staging-style-images/industrial-glam.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  biophilic: {
    id: "biophilic",
    name: "Biophilic Design",
    category: StyleCategory.Natural,
    subcategory: StyleSubcategory.Tropical,
    colorPalette: "Natural greens with organic neutrals",
    feeling: "Connected to nature, peaceful, alive",
    materials: ["Living Plants", "Natural Wood", "Stone", "Water", "Moss"],
    furnitureTypes: [
      "Organic Forms",
      "Plant-integrated",
      "Natural Materials",
      "Living Walls",
    ],
    features: ["Living Elements", "Natural Light", "Organic Materials"],
    iconName: "Plant",
    tags: ["Natural", "Sustainable", "Green", "Organic"],
    accessLevel: "pro",
    previewImages: ["/images/virtual-staging-style-images/biophilic.png"],
    thumbnailImage: "/images/virtual-staging-style-images/biophilic.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  modernClassical: {
    id: "modernClassical",
    name: "Modern Classical",
    category: StyleCategory.Historical,
    subcategory: StyleSubcategory.Classical,
    colorPalette: "Refined neutrals with classic accents",
    feeling: "Timeless, elegant, refined",
    materials: ["Marble", "Brass", "Walnut", "Linen", "Leather"],
    furnitureTypes: [
      "Updated Classics",
      "Clean Lines",
      "Traditional Forms",
      "Modern Takes",
    ],
    features: ["Classical Details", "Modern Updates", "Refined Elements"],
    iconName: "Classic",
    tags: ["Classical", "Modern", "Refined", "Timeless"],
    accessLevel: "pro",
    previewImages: [
      "/images/virtual-staging-style-images/modern-classical.png",
    ],
    thumbnailImage: "/images/virtual-staging-style-images/modern-classical.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  warmMinimalist: {
    id: "warmMinimalist",
    name: "Warm Minimalist",
    category: StyleCategory.Modern,
    subcategory: StyleSubcategory.Minimalist,
    colorPalette: "Warm neutrals with textural elements",
    feeling: "Cozy, minimal, inviting",
    materials: ["Warm Woods", "Bouclé", "Natural Stone", "Linen", "Clay"],
    furnitureTypes: [
      "Simple Forms",
      "Textural Pieces",
      "Minimal Designs",
      "Comfortable Minimalism",
    ],
    features: ["Clean Lines", "Warm Materials", "Textural Interest"],
    iconName: "Minimal",
    tags: ["Minimal", "Warm", "Modern", "Cozy"],
    accessLevel: "free",
    previewImages: ["/images/virtual-staging-style-images/warm-minimalist.png"],
    thumbnailImage: "/images/virtual-staging-style-images/warm-minimalist.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  artisanal: {
    id: "artisanal",
    name: "Artisanal Modern",
    category: StyleCategory.Thematic,
    subcategory: StyleSubcategory.Eclectic,
    colorPalette: "Natural tones with handcrafted elements",
    feeling: "Authentic, crafted, unique",
    materials: ["Handwoven Textiles", "Ceramics", "Wood", "Metal", "Glass"],
    furnitureTypes: [
      "Handcrafted Pieces",
      "Artisan-made",
      "One-of-a-kind Items",
      "Custom Elements",
    ],
    features: ["Handmade Elements", "Unique Details", "Crafted Materials"],
    iconName: "Craft",
    tags: ["Artisanal", "Handcrafted", "Modern", "Unique"],
    accessLevel: "pro",
    previewImages: ["/images/virtual-staging-style-images/artisanal.png"],
    thumbnailImage: "/images/virtual-staging-style-images/artisanal.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  japandiStyle: {
    id: "japandiStyle",
    name: "Japandi",
    category: StyleCategory.Cultural,
    subcategory: StyleSubcategory.Asian,
    colorPalette: "Muted earth tones with charcoal accents",
    feeling: "Harmonious, peaceful, functional",
    materials: [
      "Light and Dark Woods",
      "Natural Linens",
      "Paper",
      "Bamboo",
      "Handcrafted Ceramics",
    ],
    furnitureTypes: [
      "Low-profile Seating",
      "Minimalist Storage",
      "Functional Pieces",
      "Natural Wood Furniture",
    ],
    features: ["Clean Lines", "Natural Materials", "Functional Beauty"],
    iconName: "Harmony",
    tags: ["Japanese", "Scandinavian", "Minimal", "Natural"],
    accessLevel: "pro",
    previewImages: ["/images/virtual-staging-style-images/japandi.png"],
    thumbnailImage: "/images/virtual-staging-style-images/japandi.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  cottageCore: {
    id: "cottageCore",
    name: "Cottagecore",
    category: StyleCategory.Thematic,
    subcategory: StyleSubcategory.Eclectic,
    colorPalette: "Soft pastels with floral patterns",
    feeling: "Romantic, nostalgic, whimsical",
    materials: [
      "Vintage Textiles",
      "Weathered Woods",
      "Floral Fabrics",
      "Wicker",
      "Ceramic",
    ],
    furnitureTypes: [
      "Vintage Pieces",
      "Floral Upholstery",
      "Antique Finds",
      "Handcrafted Items",
    ],
    features: ["Vintage Elements", "Garden Influences", "Handmade Details"],
    iconName: "Flower",
    tags: ["Romantic", "Vintage", "Rural", "Cozy"],
    accessLevel: "free",
    previewImages: ["/images/virtual-staging-style-images/cottagecore.png"],
    thumbnailImage: "/images/virtual-staging-style-images/cottagecore.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  brutalismInspired: {
    id: "brutalismInspired",
    name: "Neo Brutalism",
    category: StyleCategory.Modern,
    subcategory: StyleSubcategory.Contemporary,
    colorPalette: "Raw concrete grays with bold color blocks",
    feeling: "Bold, architectural, dramatic",
    materials: [
      "Exposed Concrete",
      "Raw Steel",
      "Textured Glass",
      "Rough Stone",
      "Oxidized Metals",
    ],
    furnitureTypes: [
      "Sculptural Forms",
      "Monolithic Pieces",
      "Angular Furniture",
      "Statement Lighting",
    ],
    features: ["Bold Forms", "Raw Materials", "Architectural Elements"],
    iconName: "Geometric",
    tags: ["Modern", "Bold", "Architectural", "Minimal"],
    accessLevel: "premium",
    previewImages: [
      "/images/virtual-staging-style-images/brutalism-inspired.png",
    ],
    thumbnailImage:
      "/images/virtual-staging-style-images/brutalism-inspired.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  newMediterranean: {
    id: "newMediterranean",
    name: "New Mediterranean",
    category: StyleCategory.Cultural,
    subcategory: StyleSubcategory.Mediterranean,
    colorPalette: "Modern terracotta with sage and ocean blues",
    feeling: "Contemporary warmth, relaxed luxury",
    materials: ["Microcement", "Bouclé", "Travertine", "Plaster", "Aged Brass"],
    furnitureTypes: [
      "Curved Forms",
      "Modern Interpretations",
      "Textural Pieces",
      "Artisanal Elements",
    ],
    features: ["Modern Arches", "Textural Surfaces", "Contemporary Takes"],
    iconName: "Arch",
    tags: ["Modern", "Mediterranean", "Warm", "Contemporary"],
    accessLevel: "premium",
    previewImages: [
      "/images/virtual-staging-style-images/new-mediterranean.png",
    ],
    thumbnailImage:
      "/images/virtual-staging-style-images/new-mediterranean.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  digitalNomad: {
    id: "digitalNomad",
    name: "Digital Nomad",
    category: StyleCategory.Modern,
    subcategory: StyleSubcategory.Urban,
    colorPalette: "Tech-friendly neutrals with energetic accents",
    feeling: "Flexible, connected, efficient",
    materials: [
      "Smart Materials",
      "Sustainable Composites",
      "Tech-integrated Fabrics",
      "Modular Elements",
      "Acoustic Materials",
    ],
    furnitureTypes: [
      "Transformable Furniture",
      "Tech-integrated Pieces",
      "Ergonomic Design",
      "Mobile Solutions",
    ],
    features: ["Smart Integration", "Flexible Spaces", "Work-Life Balance"],
    iconName: "Connected",
    tags: ["Modern", "Tech", "Flexible", "Smart"],
    accessLevel: "premium",
    previewImages: ["/images/virtual-staging-style-images/digital-nomad.png"],
    thumbnailImage: "/images/virtual-staging-style-images/digital-nomad.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  maximalismLuxe: {
    id: "maximalismLuxe",
    name: "Maximalism Luxe",
    category: StyleCategory.Thematic,
    subcategory: StyleSubcategory.Luxury,
    colorPalette: "Rich jewel tones with pattern mixing",
    feeling: "Opulent, expressive, bold",
    materials: [
      "Luxury Velvets",
      "Exotic Marbles",
      "Pattern-rich Textiles",
      "Metallic Finishes",
      "Rare Woods",
    ],
    furnitureTypes: [
      "Statement Pieces",
      "Bold Patterns",
      "Mixed Periods",
      "Artistic Elements",
    ],
    features: ["Pattern Play", "Color Mixing", "Curated Collections"],
    iconName: "Luxe",
    tags: ["Maximalist", "Luxury", "Bold", "Artistic"],
    accessLevel: "premium",
    previewImages: ["/images/virtual-staging-style-images/maximalism-luxe.png"],
    thumbnailImage: "/images/virtual-staging-style-images/maximalism-luxe.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  biodesign: {
    id: "biodesign",
    name: "Biodesign",
    category: StyleCategory.Natural,
    subcategory: StyleSubcategory.Tropical,
    colorPalette: "Living greens with organic neutrals",
    feeling: "Sustainable, innovative, natural",
    materials: [
      "Living Materials",
      "Mycelium-based Products",
      "Recycled Elements",
      "Sustainable Tech",
      "Bioplastics",
    ],
    furnitureTypes: [
      "Biomimicry Forms",
      "Sustainable Pieces",
      "Growing Furniture",
      "Natural Tech",
    ],
    features: ["Living Systems", "Sustainable Tech", "Natural Integration"],
    iconName: "Leaf",
    tags: ["Sustainable", "Innovative", "Natural", "Tech"],
    accessLevel: "premium",
    previewImages: ["/images/virtual-staging-style-images/biodesign.png"],
    thumbnailImage: "/images/virtual-staging-style-images/biodesign.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  christmasClassic: {
    id: "christmasClassic",
    name: "Classic Christmas",
    category: StyleCategory.Seasonal,
    subcategory: StyleSubcategory.WinterHoliday,
    colorPalette: "Traditional red and green with gold accents",
    feeling: "Festive, warm, traditional",
    materials: [
      "Pine",
      "Velvet",
      "Crystal",
      "Gold Metallics",
      "Natural Greenery",
    ],
    furnitureTypes: [
      "Traditional Seating",
      "Holiday Displays",
      "Festive Arrangements",
      "Cozy Textiles",
    ],
    features: ["Holiday Decor", "Festive Lighting", "Traditional Elements"],
    iconName: "Tree",
    tags: ["Christmas", "Holiday", "Traditional", "Festive"],
    accessLevel: "free",
    previewImages: [
      "/images/virtual-staging-style-images/christmas-classic.png",
    ],
    thumbnailImage:
      "/images/virtual-staging-style-images/christmas-classic.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  modernChristmas: {
    id: "modernChristmas",
    name: "Modern Christmas",
    category: StyleCategory.Seasonal,
    subcategory: StyleSubcategory.WinterHoliday,
    colorPalette: "White and silver with minimal red accents",
    feeling: "Contemporary, sleek, festive",
    materials: ["Glass", "Chrome", "White Textiles", "LED Lighting", "Acrylic"],
    furnitureTypes: [
      "Contemporary Pieces",
      "Minimalist Displays",
      "Modern Holiday Elements",
      "Clean-lined Furniture",
    ],
    features: ["Modern Decor", "LED Lighting", "Minimalist Holiday Elements"],
    iconName: "Snowflake",
    tags: ["Christmas", "Modern", "Minimal", "Contemporary"],
    accessLevel: "free",
    previewImages: [
      "/images/virtual-staging-style-images/modern-christmas.png",
    ],
    thumbnailImage: "/images/virtual-staging-style-images/modern-christmas.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  halloweenGothic: {
    id: "halloweenGothic",
    name: "Halloween Gothic",
    category: StyleCategory.Seasonal,
    subcategory: StyleSubcategory.FallHoliday,
    colorPalette: "Black and deep purple with orange accents",
    feeling: "Mysterious, dramatic, spooky",
    materials: [
      "Velvet",
      "Aged Metal",
      "Dark Woods",
      "Vintage Glass",
      "Gothic Textiles",
    ],
    furnitureTypes: [
      "Victorian-inspired Pieces",
      "Gothic Elements",
      "Dramatic Lighting",
      "Dark Furniture",
    ],
    features: ["Dramatic Lighting", "Gothic Details", "Spooky Elements"],
    iconName: "Moon",
    tags: ["Halloween", "Gothic", "Dramatic", "Spooky"],
    accessLevel: "free",
    previewImages: [
      "/images/virtual-staging-style-images/halloween-gothic.png",
    ],
    thumbnailImage: "/images/virtual-staging-style-images/halloween-gothic.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  autumnHarvest: {
    id: "autumnHarvest",
    name: "Autumn Harvest",
    category: StyleCategory.Seasonal,
    subcategory: StyleSubcategory.FallHoliday,
    colorPalette: "Warm autumn colors with natural accents",
    feeling: "Cozy, abundant, welcoming",
    materials: [
      "Natural Fibers",
      "Rustic Woods",
      "Woven Textiles",
      "Dried Botanicals",
      "Ceramic",
    ],
    furnitureTypes: [
      "Farmhouse Tables",
      "Comfortable Seating",
      "Rustic Elements",
      "Natural Displays",
    ],
    features: ["Harvest Decor", "Natural Elements", "Warm Lighting"],
    iconName: "Leaf",
    tags: ["Thanksgiving", "Autumn", "Harvest", "Cozy"],
    accessLevel: "free",
    previewImages: ["/images/virtual-staging-style-images/autumn-harvest.png"],
    thumbnailImage: "/images/virtual-staging-style-images/autumn-harvest.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  springEaster: {
    id: "springEaster",
    name: "Spring Easter",
    category: StyleCategory.Seasonal,
    subcategory: StyleSubcategory.SpringHoliday,
    colorPalette: "Soft pastels with fresh green accents",
    feeling: "Fresh, cheerful, renewed",
    materials: [
      "Light Woods",
      "Wicker",
      "Fresh Flowers",
      "Pastel Textiles",
      "Natural Elements",
    ],
    furnitureTypes: [
      "Garden-inspired Pieces",
      "Light Furniture",
      "Woven Elements",
      "Spring Displays",
    ],
    features: ["Fresh Flowers", "Pastel Accents", "Natural Light"],
    iconName: "Flower",
    tags: ["Easter", "Spring", "Fresh", "Pastel"],
    accessLevel: "free",
    previewImages: ["/images/virtual-staging-style-images/spring-easter.png"],
    thumbnailImage: "/images/virtual-staging-style-images/spring-easter.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  summerPatriotic: {
    id: "summerPatriotic",
    name: "Summer Patriotic",
    category: StyleCategory.Seasonal,
    subcategory: StyleSubcategory.SummerHoliday,
    colorPalette: "Classic red, white, and blue",
    feeling: "Festive, traditional, celebratory",
    materials: [
      "Weathered Woods",
      "Cotton Textiles",
      "Woven Materials",
      "Metal Accents",
      "Outdoor Fabrics",
    ],
    furnitureTypes: [
      "Outdoor Furniture",
      "Casual Seating",
      "Picnic Elements",
      "Party Displays",
    ],
    features: ["Patriotic Decor", "Indoor-Outdoor Flow", "Festive Elements"],
    iconName: "Star",
    tags: ["Patriotic", "Summer", "American", "Celebration"],
    accessLevel: "free",
    previewImages: [
      "/images/virtual-staging-style-images/summer-patriotic.png",
    ],
    thumbnailImage: "/images/virtual-staging-style-images/summer-patriotic.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  winterWonderland: {
    id: "winterWonderland",
    name: "Winter Wonderland",
    category: StyleCategory.Seasonal,
    subcategory: StyleSubcategory.WinterHoliday,
    colorPalette: "Icy blues and silvers with white",
    feeling: "Magical, serene, enchanting",
    materials: [
      "Crystal",
      "Silver Metallics",
      "White Textiles",
      "Frosted Glass",
      "Mirrors",
    ],
    furnitureTypes: [
      "Plush Seating",
      "Mirrored Surfaces",
      "Crystal Elements",
      "Winter Displays",
    ],
    features: ["Winter Decor", "Sparkle Elements", "Cool Lighting"],
    iconName: "Ice",
    tags: ["Winter", "Holiday", "Elegant", "Magical"],
    accessLevel: "free",
    previewImages: [
      "/images/virtual-staging-style-images/winter-wonderland.png",
    ],
    thumbnailImage:
      "/images/virtual-staging-style-images/winter-wonderland.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },

  newYearGlam: {
    id: "newYearGlam",
    name: "New Year Glam",
    category: StyleCategory.Seasonal,
    subcategory: StyleSubcategory.WinterHoliday,
    colorPalette: "Black and gold with silver accents",
    feeling: "Glamorous, celebratory, sophisticated",
    materials: [
      "Metallic Fabrics",
      "Mirror",
      "Crystal",
      "Velvet",
      "Champagne Gold",
    ],
    furnitureTypes: [
      "Luxe Seating",
      "Bar Elements",
      "Party Displays",
      "Statement Pieces",
    ],
    features: ["Party Elements", "Glamorous Lighting", "Celebratory Decor"],
    iconName: "Sparkle",
    tags: ["New Year", "Glamour", "Party", "Celebration"],
    accessLevel: "free",
    previewImages: ["/images/virtual-staging-style-images/new-year-glam.png"],
    thumbnailImage: "/images/virtual-staging-style-images/new-year-glam.png",
    description: "",
    popularityScore: 0,
    isNew: false,
    isFeatured: false,
  },
};
// Helper functions similar to rooms.ts
export function getStylesByCategory() {
  const groupedStyles: Record<
    StyleCategory,
    Record<StyleSubcategory, StyleDetails[]>
  > = {} as Record<StyleCategory, Record<StyleSubcategory, StyleDetails[]>>;

  Object.values(styleData).forEach((style) => {
    if (!groupedStyles[style.category]) {
      groupedStyles[style.category] = {} as Record<
        StyleSubcategory,
        StyleDetails[]
      >;
    }
    if (!groupedStyles[style.category][style.subcategory]) {
      groupedStyles[style.category][style.subcategory] = [];
    }
    groupedStyles[style.category][style.subcategory].push(style);
  });

  return groupedStyles;
}

export function groupStyleOptions() {
  const groupedStyles = getStylesByCategory();
  const formattedOptions: Record<
    string,
    { value: string; label: string; isSubcategoryLabel?: boolean }[]
  > = {};

  Object.entries(groupedStyles).forEach(([category, subcategories]) => {
    formattedOptions[category] = [];

    Object.entries(subcategories).forEach(([subcategory, styles]) => {
      // Add subcategory as a disabled option
      formattedOptions[category].push({
        value: `subcategory-${subcategory}`,
        label: subcategory,
        isSubcategoryLabel: true,
      });

      // Add styles under this subcategory
      const styleOptions = styles.map((style) => ({
        value: style.id,
        label: style.name,
      }));

      formattedOptions[category].push(...styleOptions);
    });
  });

  return formattedOptions;
}

export function getStyleById(id: string): StyleDetails | undefined {
  return styleData[id];
}
