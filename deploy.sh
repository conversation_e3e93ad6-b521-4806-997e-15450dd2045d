#!/bin/bash
set -e

APP_NAME="renovaitor"
REPO_DIR="/home/<USER>/renovaitor.com"
RELEASES_DIR="$REPO_DIR/releases"
LOG_DIR="/home/<USER>/logs"
DEPLOY_LOG="$LOG_DIR/deploy.log"
TIMESTAMP=$(date +%Y%m%d%H%M%S)
NEW_RELEASE_DIR="$RELEASES_DIR/$TIMESTAMP"
KEEP_RELEASES=5

# Azure Storage variables
AZURE_STORAGE_ACCOUNT="renovaitorstorage"
AZURE_CONTAINER="renovaitor-static"

log() {
  echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" | tee -a "$DEPLOY_LOG"
}

handle_error() {
  local exit_code=$?
  log "ERROR: $1 (Exit code: $exit_code)"
  # Optionally revert to previous working commit or rollback the symlink
  # git reset --hard HEAD@{1}
  exit $exit_code
}

# Source environment variables from .env file
if [ -f "$REPO_DIR/.env" ]; then
  source "$REPO_DIR/.env"
fi

# Verify Azure Storage Key exists
if [ -z "$AZURE_STORAGE_KEY" ]; then
  handle_error "Azure Storage Key not found. Please set AZURE_STORAGE_KEY in your .env file"
fi

cleanup_old_releases() {
  cd "$RELEASES_DIR"
  local releases_to_delete
  releases_to_delete=$(ls -1t | tail -n +$((KEEP_RELEASES + 1)))
  
  if [ ! -z "$releases_to_delete" ]; then
    echo "$releases_to_delete" | xargs rm -rf
    log "Cleaned up old releases"
  fi

  # Clean up old assets from Azure Blob Storage
  local old_assets
  old_assets=$(az storage blob list \
    --account-name "$AZURE_STORAGE_ACCOUNT" \
    --container-name "$AZURE_CONTAINER" \
    --account-key "$AZURE_STORAGE_KEY" \
    --query "[?starts_with(name, '20')].name" -o tsv | sort -r | tail -n +$((KEEP_RELEASES + 1)))
  
  if [ ! -z "$old_assets" ]; then
    echo "$old_assets" | xargs -I {} az storage blob delete \
      --account-name "$AZURE_STORAGE_ACCOUNT" \
      --container-name "$AZURE_CONTAINER" \
      --account-key "$AZURE_STORAGE_KEY" \
      --name "{}"
    log "Cleaned up old assets from Azure Storage"
  fi
}

health_check() {
  local retry=0
  local max_retries=30
  local endpoint="http://localhost:3000/api/health"
  local wait_time=5  # Increased wait time between retries

  log "Starting health check..."
  
  while [ $retry -lt $max_retries ]; do
    local response
    response=$(curl -s -w "\n%{http_code}" "$endpoint")
    local status_code=$(echo "$response" | tail -n1)
    local body=$(echo "$response" | head -n-1)
    
    if [ "$status_code" = "200" ]; then
      log "Health check passed on attempt $((retry + 1))"
      return 0
    fi
    
    retry=$((retry + 1))
    log "Health check attempt $retry failed (Status: $status_code, Body: $body). Waiting ${wait_time}s..."
    sleep $wait_time
  done

  log "Health check failed after $max_retries attempts"
  return 1
}

create_directories() {
  mkdir -p "$LOG_DIR"
  touch "$DEPLOY_LOG"
  chmod 755 "$LOG_DIR"
  chmod 644 "$DEPLOY_LOG"
  mkdir -p "$RELEASES_DIR"
}

trap 'handle_error "An unexpected error occurred."' ERR

main() {
  log "Starting deployment process..."
  create_directories

  cd "$REPO_DIR"
  log "Fetching latest changes from Git..."
  git fetch --all || handle_error "Failed to fetch"
  git reset --hard origin/main || handle_error "Failed to reset to origin/main"

  log "Creating new release directory at $NEW_RELEASE_DIR"
  mkdir -p "$NEW_RELEASE_DIR"

  rsync -a --exclude=".git" --exclude="node_modules" --exclude="releases" . "$NEW_RELEASE_DIR"

  cd "$NEW_RELEASE_DIR"

  # Check for .env files and copy them if they don't exist in the new release
  if [ ! -f ".env" ]; then
    if [ -f "$REPO_DIR/.env" ]; then
      log "Copying .env from repository root..."
      cp "$REPO_DIR/.env" .env
    elif [ -f "$REPO_DIR/.env.local" ]; then
      log "Copying .env.local from repository root..."
      cp "$REPO_DIR/.env.local" .env
    else
      handle_error "No .env or .env.local file found in repository root"
    fi
  fi

  # Install dependencies
  log "Installing dependencies..."
  yarn install || handle_error "Failed to install dependencies"

  # Remove any conflicting ESLint configs from parent directories
  log "Cleaning up ESLint configuration..."
  rm -f "$REPO_DIR/.eslintrc.json" "$REPO_DIR/.eslintrc.js" || true
  
  # Ensure only the project-level ESLint config exists
  if [ ! -f ".eslintrc.json" ]; then
    echo '{
      "extends": "next/core-web-vitals"
    }' > .eslintrc.json
  fi

  # Set BUILD_ID for consistent asset versioning
  export BUILD_ID=$TIMESTAMP
  export ASSET_PREFIX="https://${AZURE_STORAGE_ACCOUNT}.blob.core.windows.net/${AZURE_CONTAINER}/${TIMESTAMP}"

  log "Building application with ASSET_PREFIX=$ASSET_PREFIX"
  ASSET_PREFIX="$ASSET_PREFIX" BUILD_ID="$BUILD_ID" NODE_ENV=production DISABLE_ESLINT_PLUGIN=true yarn build || handle_error "Failed to build application"

  # Upload static files to Azure with versioned path
  log "Uploading static files to Azure..."
  log "Static files directory: .next/static"
  ls -la .next/static || log "Warning: Static directory not found"
  
  az storage blob upload-batch \
    --account-name "$AZURE_STORAGE_ACCOUNT" \
    --account-key "$AZURE_STORAGE_KEY" \
    -d "$AZURE_CONTAINER/$TIMESTAMP" \
    -s ".next/static" \
    --content-cache-control "public, max-age=********, immutable" || handle_error "Failed to upload static files"
    
  # Verify upload
  log "Verifying uploaded files..."
  az storage blob list \
    --account-name "$AZURE_STORAGE_ACCOUNT" \
    --account-key "$AZURE_STORAGE_KEY" \
    --container-name "$AZURE_CONTAINER" \
    --prefix "$TIMESTAMP" \
    --query "[].name" -o tsv || log "Warning: Could not verify uploads"

  # Graceful reload with zero downtime
  if pm2 show $APP_NAME > /dev/null 2>&1; then
    log "Performing zero-downtime reload..."
    ln -sfn "$NEW_RELEASE_DIR" "$REPO_DIR/current.new"
    mv -Tf "$REPO_DIR/current.new" "$REPO_DIR/current"
    
    log "Reloading PM2 processes..."
    pm2 reload ecosystem.config.js --update-env || handle_error "Failed to reload PM2"
    
    log "Waiting for application to stabilize..."
    sleep 10  # Give the app some time to start up
    
    if ! health_check; then
      log "Health check failed after reload, attempting rollback..."
      # Restore previous symlink if it exists
      if [ -f "$REPO_DIR/current.old" ]; then
        log "Rolling back to previous release..."
        mv -f "$REPO_DIR/current.old" "$REPO_DIR/current"
        pm2 reload ecosystem.config.js --update-env || log "Warning: Rollback reload failed"
        
        if health_check; then
          log "Rollback successful"
        else
          handle_error "Both deployment and rollback failed. Manual intervention required."
        fi
      else
        handle_error "Health check failed and no previous release found for rollback"
      fi
      exit 1
    fi
  else
    log "First-time deployment, starting PM2..."
    ln -sfn "$NEW_RELEASE_DIR" "$REPO_DIR/current"
    pm2 start ecosystem.config.js || handle_error "Failed to start PM2"
    
    log "Waiting for first-time deployment to stabilize..."
    sleep 15  # Give more time for first deployment
    
    if ! health_check; then
      handle_error "First-time deployment health check failed"
    fi
  fi

  # Cleanup old releases
  cleanup_old_releases

  log "Deployment completed successfully!"
}

main
exit 0
