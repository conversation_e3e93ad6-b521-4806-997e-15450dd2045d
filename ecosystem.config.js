module.exports = {
  apps: [
    {
      name: "renovaitor",
      script: "yarn",
      args: ["start"],
      cwd: "/home/<USER>/renovaitor.com/",
      instances: "max",
      exec_mode: "cluster",
      env: {
        NODE_ENV: "production",
        NEXT_TELEMETRY_DISABLED: 1,
        PORT: 3000,
      },
      error_file: "/home/<USER>/logs/renovaitor-err.log",
      out_file: "/home/<USER>/logs/renovaitor-out.log",
      merge_logs: true,
      wait_ready: true,
      listen_timeout: 10000,
      kill_timeout: 5000,
    },
  ],
};
