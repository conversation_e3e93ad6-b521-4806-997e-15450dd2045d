# Renovaitor

Renovaitor is an AI-powered interior and exterior design platform that helps users visualize and transform their spaces using cutting-edge artificial intelligence technology.

## Features

- AI-generated interior and exterior designs
- Virtual staging feature - Fill in your space with furnitures and decor. AI make design suggestions to your space and creates images with these suggestions. without changinbg structure of your space.
- Multiple design styles to choose from
- User-friendly dashboard
- Secure authentication system with Google sign-in
- Real-time updates on design generation progress
- Image credit system for design generations
- magic image editor tool - remove objects and make changes add objects
- Image upscaling functionality with adding more clarity and detail to images
- Style transfer feature

## Technologies Used

- Next.js
- React
- TypeScript
- Prisma
- NextAuth.js
- Zustand
- Tailwind CSS
- Radix UI
- Ably for real-time updates
- Lemon Squeezy for payments
- Replicate for AI image processing

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- PostgreSQL database
- Ably account for real-time updates
- Lemon Squeezy account for payments
- Replicate API key for AI image processing
- Google OAuth credentials for sign-in

### Installation

1. Clone the repository:

   ```
   git clone https://github.com/your-username/renovaitor.git
   cd renovaitor
   ```

2. Install dependencies:

   ```
   npm install
   ```

3. Set up environment variables:
   Create a `.env.local` file in the root directory and add the following variables:

   ```
   DATABASE_URL=your_postgresql_connection_string
   NEXTAUTH_SECRET=your_nextauth_secret
   GOOGLE_CLIENT_ID=your_google_client_id
   GOOGLE_CLIENT_SECRET=your_google_client_secret
   ABLY_API_KEY=your_ably_api_key
   REPLICATE_API_TOKEN=your_replicate_api_token
   LEMON_SQUEEZY_API_KEY=your_lemon_squeezy_api_key
   WEBHOOK_HOST=your_webhook_host
   ```

4. Run database migrations:

   ```
   npx prisma migrate dev
   ```

5. Start the development server:

   ```
   npm run dev
   ```

6. Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

## Usage

1. Sign up or log in to your account (Google sign-in available).
2. Navigate to the dashboard and choose a design tool (interior, exterior, background removal, or upscaling).
3. Upload a photo of your space or the image you want to process.
4. For interior/exterior design:
   - Select a design style or use the advanced mode to provide a custom prompt.
   - Generate AI-powered designs and view the results.
5. For background removal:
   - Upload an image and let the AI remove the background.
6. For image upscaling:
   - Upload an image and choose the upscaling options.
7. View, download, or further process your generated images.

## Acknowledgements

- [Next.js](https://nextjs.org/)
- [React](https://reactjs.org/)
- [TypeScript](https://www.typescriptlang.org/)
- [Prisma](https://www.prisma.io/)
- [NextAuth.js](https://next-auth.js.org/)
- [Zustand](https://github.com/pmndrs/zustand)
- [Tailwind CSS](https://tailwindcss.com/)
- [Radix UI](https://www.radix-ui.com/)
- [Ably](https://ably.com/)
- [Lemon Squeezy](https://www.lemonsqueezy.com/)
- [Replicate](https://replicate.com/)
