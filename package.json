{"name": "renovaitor.com", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate"}, "dependencies": {"@ai-sdk/azure": "^1.3.6", "@ai-sdk/openai": "^0.0.66", "@auth/prisma-adapter": "^2.4.1", "@hookform/resolvers": "^3.6.0", "@lemonsqueezy/lemonsqueezy.js": "^3.3.1", "@next/third-parties": "14.2.4", "@prisma/client": "^5.16.0", "@prisma/extension-pulse": "^1.1.1", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-portal": "^1.1.4", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@react-email/components": "^0.0.19", "@react-email/render": "^1.0.5", "@tanstack/react-table": "^8.17.3", "@tsparticles/engine": "^3.4.0", "@tsparticles/react": "^3.0.0", "@tsparticles/slim": "^3.4.0", "@types/ably": "^1.0.0", "@types/bcryptjs": "^2.4.6", "ably": "^2.3.1", "ai": "^3.4.9", "axios": "^1.7.7", "bcryptjs": "^3.0.2", "canvas": "^2.11.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.1", "filepond": "^4.31.1", "filepond-plugin-file-validate-size": "^2.2.8", "filepond-plugin-image-exif-orientation": "^1.0.11", "filepond-plugin-image-preview": "^4.6.12", "framer-motion": "^11.9.0", "immer": "^10.1.1", "keen-slider": "^6.8.6", "konva": "^9.3.16", "lodash": "^4.17.21", "lucide-react": "^0.394.0", "next": "14.2.4", "next-auth": "^5.0.0-beta.19", "next-themes": "^0.3.0", "openai": "^4.67.3", "react": "^18", "react-compare-slider": "^3.1.0", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-draggable": "^4.4.6", "react-dropzone": "^14.3.5", "react-email": "^2.1.4", "react-filepond": "^7.1.2", "react-hook-form": "^7.51.5", "react-icons": "^5.4.0", "react-intersection-observer": "^9.13.1", "react-konva": "^18.2.10", "react-markdown": "^9.0.1", "react-share": "^5.1.1", "replicate": "^0.31.0", "resend": "^3.3.0", "sharp": "^0.33.5", "slugify": "^1.6.6", "sonner": "^2.0.2", "swiper": "^11.1.14", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "use-image": "^1.1.1", "uuid": "^10.0.0", "vaul": "^0.9.9", "zod": "^3.23.8", "zustand": "^4.5.4"}, "devDependencies": {"@tailwindcss/typography": "^0.5.15", "@types/bcrypt": "^5.0.2", "@types/lodash": "^4.17.13", "@types/node": "^22.10.1", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-input-mask": "^3.0.5", "@types/uuid": "^10.0.0", "eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8", "prisma": "^5.16.1", "tailwindcss": "^3.4.1", "typescript": "^5"}, "pnpm": {"ignoredBuiltDependencies": ["@prisma/client", "@prisma/engines", "@swc/core", "@tsparticles/engine", "bcrypt", "canvas", "esbuild", "prisma", "sharp"]}}