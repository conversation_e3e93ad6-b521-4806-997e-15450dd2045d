#!/bin/bash
set -e

# Configuration
APP_NAME="renovaitor"
BUILD_ID=$(date +%Y%m%d%H%M%S)
DOCKER_COMPOSE="docker compose"

# Load environment variables
if [ -f .env ]; then
  export $(cat .env | grep -v '^#' | xargs)
fi

echo "Starting deployment process..."

# Build new image
echo "Building new Docker image..."
BUILD_ID=$BUILD_ID $DOCKER_COMPOSE build

# Start new container
echo "Starting new container..."
BUILD_ID=$BUILD_ID $DOCKER_COMPOSE up -d

# Wait for health check
echo "Waiting for health check..."
sleep 10

# Check if new container is healthy
if docker inspect renovaitor-$BUILD_ID | grep -q '"Status": "healthy"'; then
  echo "New container is healthy"
  
  # Remove old containers except the current one
  echo "Removing old containers..."
  docker ps -a | grep renovaitor | grep -v renovaitor-$BUILD_ID | awk '{print $1}' | xargs -r docker rm -f
  
  echo "Deployment successful!"
else
  echo "New container is not healthy, rolling back..."
  docker rm -f renovaitor-$BUILD_ID
  BUILD_ID=latest $DOCKER_COMPOSE up -d
  exit 1
fi