# Canvas Module Documentation

This document covers the functionality, components, and utilities used to create an interactive canvas experience along with an AI-backed image editing service. The system is composed of both server‐side actions (e.g. image editing using the Replicate API) and client‐side components (using React, Konva, and Zustand) with several helper types and custom hooks.

---

## Table of Contents

- [Overview](#overview)
- [Server-Side Image Editing (`edit-image.ts`)](#server-side-image-editing-edit-imagetts)
  - [Key Functions](#key-functions)
  - [Workflow & Error Handling](#workflow--error-handling)
  - [Data Types & Validation](#data-types--validation)
- [Client-Side Canvas Components](#client-side-canvas-components)
  - [ImageCanvas Component](#imagecanvas-component)
  - [Toolbar Components](#toolbar-components)
    - [ToolbarButton](#toolbarbutton)
    - [MainToolbar](#maintoolbar)
- [Data Type Definitions](#data-type-definitions)
  - [Image Dimensions](#image-dimensions)
  - [Canvas & Edit Image Types](#canvas--edit-image-types)
- [State Management with Zustand](#state-management-with-zustand)
- [Custom Hooks for Canvas Operations](#custom-hooks-for-canvas-operations)
  - [use-brush-composite](#use-brush-composite)
  - [use-canvas-pointer](#use-canvas-pointer)
  - [use-canvas-zoom](#use-canvas-zoom)
- [Integration & Workflow](#integration--workflow)
- [Conclusion](#conclusion)

---

## Overview

The canvas module is designed to provide a rich, interactive environment for image editing and manipulation. Users can upload images, apply AI-assisted edits (e.g. remove objects, replace content, or generate new images), perform drawing operations, and manage state transitions (e.g., zoom, pan, and object transformations). The system integrates:

- **Server Actions:** Powered by the Replicate API to perform AI image edits via various modes.
- **Client Components:** Built using React and [react-konva](https://konvajs.org/) for rendering images, shapes, and handling user interaction.
- **Global State Management:** Handled by Zustand providing an organized canvas state.
- **Custom Hooks:** For common operations like computing pointer positions, zooming, and composing brush strokes.

---

## Server-Side Image Editing (`edit-image.ts`)

The file [`src/modules/dashboard/main-features/actions/edit-image.ts`](src/modules/dashboard/main-features/actions/edit-image.ts) handles the image editing operations on the server. This action integrates with the Replicate API and supports three modes:

- **Remove:** Remove unwanted objects from an image using a mask.
- **Replace:** Replace a part of an image with new content prompted by the user.
- **Generate:** Create a new image based on a textual prompt, optionally using an input image.

### Key Functions

- **`editImage(params: EditImageParams): Promise<EditImageResult>`**  
  This is the main exported function that:
  - **Validates Input:** Uses the Zod schema (`EditImageParamsSchema`) to ensure that parameters meet the required structure.
  - **User Credit Check:** Calls `checkUserCredits` to verify the user has sufficient credits. If not, it returns an error.
  - **Deduct Credits:** Deducts one image credit via `deductUserCredits`.
  - **Initialize Replicate:** Establishes a connection to the Replicate API using the API token.
  - **Process Based on Mode:**
    - **Remove Mode:** Invokes `processRemoveMode` to call the LAMA model.
    - **Replace Mode:** Invokes `processReplaceMode` to use the Ideogram model with additional prompt details.
    - **Generate Mode:** Invokes `processGenerateMode` to generate an image possibly without an input mask.
  - **Result Handling:**  
    - If successful, it saves the result to the database using `saveEditImageResult` and responds with the generated image URL.
    - On failure, logs the error, refunds credits if applicable, and returns an error message.

- **Database Helpers:**
  - **`checkUserCredits`**: Queries the database for the user and checks if they have at least one image credit.
  - **`deductUserCredits`**: Decrements the user's image credits upon a successful image edit process.
  - **`saveEditImageResult`**: Persists the result and metadata of the image edit into the database.

- **Processing Functions:**
  - **`processRemoveMode`**: Calls the Replicate model (LAMA) to remove objects identified by the mask.
  - **`processReplaceMode`**: Uses the ideogram model to replace content in the image based on a prompt.
  - **`processGenerateMode`**: Uses the ideogram model to generate a new image from a textual description.

### Workflow & Error Handling

1. **Input Validation:**  
   Using Zod, the input is strictly validated to ensure images and masks are provided as valid URLs or data URLs (depending on mode).

2. **Credit Verification:**  
   Ensures that the user has sufficient credits before proceeding with the edit.

3. **Processing & Response:**  
   Depending on the mode, the corresponding processing function is invoked. If no output URL is received from the AI model, an error is thrown.

4. **Database Logging:**  
   Final results are logged in the database with timestamp and status.

5. **Error Management:**  
   On catching an error, credits may be refunded (if not related to insufficient credits) and the error details are returned to the frontend.

### Data Types & Validation

The file [`src/modules/dashboard/main-features/types/edit-image.ts`](src/modules/dashboard/main-features/types/edit-image.ts) defines:
- **Input Interfaces:**  
  - `RemoveEditImageParams` for removal,  
  - `ReplaceEditImageParams` for replacement,  
  - `GenerateEditImageParams` for generation.
- **Zod Schemas:**  
  These ensure that required fields such as `userId`, `image`, `mask`, and `prompt` are correctly formatted.
- **Result Types:**  
  Defines `EditImageResult` aligning with the returned structure from the Replicate call.

---

## Client-Side Canvas Components

### ImageCanvas Component

- **Location:** [`src/modules/dashboard/main-features/components/canvas/image-canvas.tsx`](src/modules/dashboard/main-features/components/canvas/image-canvas.tsx)
- **Purpose:**  
  Provides the core canvas element where users interact with:
  - Loaded images,
  - Generated images,
  - Drawing layers (lines for masks, brush strokes),
  - Selection areas.
- **Key Features:**
  - **Konva Integration:** Uses `Stage`, `Layer`, and `KonvaImage` from `react-konva` to render graphical content.
  - **Event Handling:**  
    - Mouse events (down, move, up) for drawing and selection.
    - Wheel events for zooming.
  - **Dynamic State Management:**  
    The component communicates with a Zustand store to manage application state such as current mode, drawn lines, and generated images.
  - **Image Upload & Download:**  
    Supports uploading images via a hidden file input and downloading the canvas output as PNG.

### Toolbar Components

These components provide user controls for interacting with the canvas.

#### ToolbarButton

- **Location:** [`src/modules/dashboard/main-features/components/canvas/toolbar/toolbar-button.tsx`](src/modules/dashboard/main-features/components/canvas/toolbar/toolbar-button.tsx)
- **Functionality:**  
  A reusable button component that:
  - Wraps an icon and label (displayed via a Tooltip).
  - Supports style variations to indicate active or disabled states.
  - Is used throughout the toolbar for actions such as pan, select, undo, redo, and more.
  
#### MainToolbar

- **Location:** [`src/modules/dashboard/main-features/components/canvas/toolbar/main-toolbar.tsx`](src/modules/dashboard/main-features/components/canvas/toolbar/main-toolbar.tsx)
- **Components:**  
  - **NavigationTools:** For panning and selecting canvas objects.
  - **MagicFillButton:** For toggling into MagicFill mode that drives AI image editing.
  - **ImageActions:** Provides buttons for uploading images, generating a new image (triggering `editImage`), saving, and downloading.
  - **HistoryControls:** Buttons for undo/redo functionality.
- **Additional Features:**  
  When the canvas mode is set to MagicFill, additional controls appear to manage mask tools (e.g., brush, eraser, lasso) and options to invert or cancel the magic fill operation.

---

## Data Type Definitions

### Image Dimensions

- **Location:** [`src/modules/dashboard/main-features/types/image-dimensions.ts`](src/modules/dashboard/main-features/types/image-dimensions.ts)
- **Purpose:**  
  Defines the supported aspect ratios and custom resolution state for images.
- **Key Types:**
  - `AspectRatioOption`: An option for a given aspect ratio (e.g., 1:1, 16:9) with corresponding pixel dimensions.
  - `ImageDimensionsState`: Stores settings like chosen ratio, custom resolution state, and orientation.

### Canvas & Edit Image Types

- **Location:** [`src/modules/dashboard/main-features/types/canvas.ts`](src/modules/dashboard/main-features/types/canvas.ts)  
  Defines:
  - **Canvas State:**  
    Types for positions, sizes, drawn lines (`CanvasLine`), selection areas (`CanvasRect`), and modes (`CanvasMode` such as Pan, Move, MagicFill, etc.).
  - **Generated Images:**  
    Structure for containing generated image elements, their positions, dimensions, and transformation information.
  
- **Edit Image Types:**  
  As described above, found in [`src/modules/dashboard/main-features/types/edit-image.ts`](src/modules/dashboard/main-features/types/edit-image.ts).

---

## State Management with Zustand

- **Location:** [`src/modules/dashboard/main-features/store/canvas-store.ts`](src/modules/dashboard/main-features/store/canvas-store.ts)
- **Overview:**  
  The Zustand store encapsulates the entire canvas state. It maintains:
  - **Visual Parameters:**  
    Such as canvas mode, zoom level, drawn lines, and mask tool selections.
  - **User Information:**  
    User ID, prompt text, and uploaded image details.
  - **History Management:**  
    Supports undo/redo operations for drawn lines and modifications.
- **Methods Provided:**
  - **State Setters:**  
    `setMode`, `setMaskTool`, `setBrushSize`, `setPrompt`, `setInputImagePosition`, etc.
  - **Line Operations:**  
    `startNewLine`, `updateActiveLine`, and `clearLines`.
  - **Undo/Redo Support:**  
    By maintaining a history of lines and allowing state rollback.

---

## Custom Hooks for Canvas Operations

### use-brush-composite

- **Location:** [`src/modules/dashboard/main-features/hooks/use-brush-composite.ts`](src/modules/dashboard/main-features/hooks/use-brush-composite.ts)
- **Purpose:**  
  Composes all finalized brush strokes into one composite image.
- **How It Works:**
  - Creates an offscreen canvas.
  - Iterates over finalized brush strokes (excluding any actively drawn stroke).
  - Applies a gradient (from purple to blue) for the strokes.
  - Returns a composite `HTMLImageElement` once rendered.

### use-canvas-pointer

- **Location:** [`src/modules/dashboard/main-features/hooks/use-canvas-pointer.ts`](src/modules/dashboard/main-features/hooks/use-canvas-pointer.ts)
- **Purpose:**  
  Provides utility functions to correctly compute pointer positions relative to the canvas,
  taking into account the stage’s transformation (scale and translation).
- **Functions:**
  - `getRelativePointerPosition`: Adjusts the raw pointer coordinates.
  - `snapToGrid`: Rounds the pointer position to the nearest grid point (useful for precise drawing).

### use-canvas-zoom

- **Location:** [`src/modules/dashboard/main-features/hooks/use-canvas-zoom.ts`](src/modules/dashboard/main-features/hooks/use-canvas-zoom.ts)
- **Purpose:**  
  Implements controlled zoom behavior using the mouse wheel when the Ctrl key is pressed.
- **Functionality:**
  - Calculates the new scale.
  - Adjusts the canvas’ position so that zoom occurs in relation to the pointer’s location.
  - Optionally calls a callback with the new zoom level.

---

## Integration & Workflow

1. **User Interaction:**  
   - The **ImageCanvas** component renders the canvas where users can draw, select, and transform images.
   - Toolbar buttons (e.g., from **ToolbarButton** and **MainToolbar**) allow users to switch modes (pan, move, magic fill) and trigger actions like generating an image.

2. **State Management:**  
   - As users interact with the canvas, the Zustand store is updated with the current mode, drawn lines, and selected images.
   - Custom hooks ensure that pointer and zoom events are correctly computed and applied.

3. **Image Editing Flow:**  
   - When a user triggers image generation (often through the magic fill button), the client calls the server via the `editImage` function.
   - The server validates inputs, checks credits, processes the image with the appropriate AI model, and saves the result.
   - The new image URL is then used on the client to add the generated image to the canvas.

---

## Conclusion

This documentation provides a comprehensive overview of the canvas module and its integration with AI-based image editing. By combining server-side processing (using the Replicate API and robust validation with Zod) with a dynamic client-side canvas (powered by react-konva and Zustand), the system delivers a powerful and interactive image manipulation toolset.

Developers looking to extend or modify this functionality should refer to the respective sections above for details on:
- The image editing process in `edit-image.ts`
- The React components that render canvas interactions
- The custom hooks and state management practices that tie everything together.

