# Canvas Usage Guide

## Overview

This guide provides instructions for implementing and using the Canvas module in your application. It covers basic setup, common usage patterns, and advanced customization options.

## Installation

To use the Canvas module, you need to install the required dependencies:

```bash
npm install konva react-konva use-image zustand
# or
yarn add konva react-konva use-image zustand
# or
pnpm add konva react-konva use-image zustand
```

## Basic Implementation

### 1. Import the Canvas Module

```tsx
import { ImageCanvas } from "@/modules/canvas/image-canvas";
```

### 2. Add the Canvas to Your Component

```tsx
export default function CanvasPage() {
  return (
    <div className="w-full h-screen">
      <ImageCanvas className="w-full h-full" />
    </div>
  );
}
```

### 3. Set Up Required Context Providers

The Canvas module requires certain context providers to be available in your application:

```tsx
import { ToastProvider } from "@/modules/ui/toast";
import { ThemeProvider } from "next-themes";
import { AblyProvider } from "ably/react";

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <ToastProvider>
        <AblyProvider clientId={`user-${userId}`}>
          {children}
        </AblyProvider>
      </ToastProvider>
    </ThemeProvider>
  );
}
```

## Common Usage Patterns

### Drawing and Masking

The Canvas module provides tools for drawing and masking:

1. Select the Magic Fill mode from the toolbar
2. Choose a drawing tool (brush, eraser, rectangle, or lasso)
3. Draw on the canvas to create a mask
4. Click "Proceed" to confirm the mask
5. Enter a prompt and click "Generate" to fill the masked area

```tsx
// Example of programmatically setting the mode and tool
import useCanvasStore from "@/modules/canvas/store/canvas-store";
import { CanvasMode } from "@/modules/canvas/types/canvas";

const SetupMagicFill = () => {
  const store = useCanvasStore();
  
  const startMagicFill = () => {
    store.setMode(CanvasMode.MagicFill);
    store.setMaskTool("brush");
    store.setBrushSize(20);
  };
  
  return (
    <button onClick={startMagicFill}>
      Start Magic Fill
    </button>
  );
};
```

### Image Generation

To generate images on the canvas:

1. Select the Generate mode from the toolbar
2. Draw a selection rectangle on the canvas
3. Enter a prompt in the input field
4. Click "Generate" to create an image in the selected area

```tsx
// Example of programmatically generating an image
import useCanvasStore from "@/modules/canvas/store/canvas-store";
import { CanvasMode } from "@/modules/canvas/types/canvas";
import { useGenerationTasks } from "@/modules/canvas/hooks/use-generation-tasks";

const GenerateImageButton = ({ userId }: { userId: string }) => {
  const store = useCanvasStore();
  const { handleGenerate } = useGenerationTasks(userId, `predictions-${userId}`);
  
  const setupAndGenerate = async () => {
    // Set up the canvas for generation
    store.setMode(CanvasMode.Generate);
    store.setPrompt("A beautiful landscape with mountains and a lake");
    
    // Set the selection area
    store.setSelectionArea({
      x: 100,
      y: 100,
      width: 512,
      height: 512
    });
    
    // Generate the image
    await handleGenerate();
  };
  
  return (
    <button onClick={setupAndGenerate}>
      Generate Landscape
    </button>
  );
};
```

### Image Upload and Manipulation

To upload and manipulate images:

1. Click the Upload button in the toolbar
2. Select an image from your device
3. The image will be added to the canvas
4. Click on the image to select it
5. Use the transformer handles to resize, rotate, or move the image

```tsx
// Example of programmatically uploading an image
import { useFileUpload } from "@/modules/canvas/hooks/use-file-upload";
import { useRef } from "react";

const UploadButton = () => {
  const stageRef = useRef<Konva.Stage>(null);
  const dimensions = { width: window.innerWidth, height: window.innerHeight };
  const { fileInputRef, triggerFileUpload, handleFileInput } = useFileUpload();
  
  return (
    <>
      <button onClick={triggerFileUpload}>
        Upload Image
      </button>
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        accept="image/*"
        onChange={(e) => handleFileInput(e, stageRef, dimensions)}
      />
    </>
  );
};
```

### Exporting Canvas Content

To export content from the canvas:

1. Create a selection area on the canvas
2. Click the Download button in the toolbar
3. The selected area will be exported as a PNG image

```tsx
// Example of programmatically exporting canvas content
import { useCanvasExport } from "@/modules/canvas/hooks/use-canvas-export";
import { useRef } from "react";

const ExportButton = () => {
  const stageRef = useRef<Konva.Stage>(null);
  const { exportSelectionAsImage } = useCanvasExport();
  const selectionArea = { x: 100, y: 100, width: 512, height: 512 };
  
  const handleExport = async () => {
    if (stageRef.current) {
      await exportSelectionAsImage(
        selectionArea,
        stageRef,
        `export-${Date.now()}.png`
      );
    }
  };
  
  return (
    <button onClick={handleExport}>
      Export Selection
    </button>
  );
};
```

## Advanced Customization

### Custom Toolbar

You can create a custom toolbar for the canvas:

```tsx
import useCanvasStore from "@/modules/canvas/store/canvas-store";
import { CanvasMode } from "@/modules/canvas/types/canvas";

const CustomToolbar = () => {
  const store = useCanvasStore();
  const mode = store.mode;
  
  return (
    <div className="custom-toolbar">
      <button
        className={mode === CanvasMode.Move ? "active" : ""}
        onClick={() => store.setMode(CanvasMode.Move)}
      >
        Move
      </button>
      <button
        className={mode === CanvasMode.Generate ? "active" : ""}
        onClick={() => store.setMode(CanvasMode.Generate)}
      >
        Generate
      </button>
      <button
        className={mode === CanvasMode.MagicFill ? "active" : ""}
        onClick={() => store.setMode(CanvasMode.MagicFill)}
      >
        Magic Fill
      </button>
      <button
        className={mode === CanvasMode.Pan ? "active" : ""}
        onClick={() => store.setMode(CanvasMode.Pan)}
      >
        Pan
      </button>
      <button
        className={mode === CanvasMode.SelectArea ? "active" : ""}
        onClick={() => store.setMode(CanvasMode.SelectArea)}
      >
        Select Area
      </button>
    </div>
  );
};
```

### Custom Canvas Items

You can create custom canvas items by extending the base canvas item:

```tsx
import { BaseCanvasItem } from "@/modules/canvas/components/canvas-items/base-canvas-item";
import { CanvasItemProps } from "@/modules/canvas/components/canvas-items/canvas-item-types";
import { Group, Text, Rect } from "react-konva";

interface TextCanvasItemProps extends CanvasItemProps {
  text: string;
  fontSize: number;
  fill: string;
}

export const TextCanvasItem: React.FC<TextCanvasItemProps> = ({
  x,
  y,
  width,
  height,
  rotation,
  text,
  fontSize,
  fill,
  isSelected,
  isDraggable,
  onClick,
  onDragEnd,
  onTransformEnd
}) => {
  return (
    <BaseCanvasItem
      x={x}
      y={y}
      width={width}
      height={height}
      rotation={rotation}
      isSelected={isSelected}
      isDraggable={isDraggable}
      onClick={onClick}
      onDragEnd={onDragEnd}
      onTransformEnd={onTransformEnd}
    >
      <Group>
        <Rect
          width={width}
          height={height}
          fill="transparent"
          stroke={isSelected ? "blue" : "transparent"}
          strokeWidth={1}
        />
        <Text
          text={text}
          fontSize={fontSize}
          fill={fill}
          width={width}
          height={height}
          align="center"
          verticalAlign="middle"
        />
      </Group>
    </BaseCanvasItem>
  );
};
```

### Custom Hooks

You can create custom hooks to extend the canvas functionality:

```typescript
import { useCallback, useState } from "react";
import useCanvasStore from "@/modules/canvas/store/canvas-store";
import { CanvasRect } from "@/modules/canvas/types/canvas";

export const useCanvasGrid = (gridSize: number = 20) => {
  const store = useCanvasStore();
  const [showGrid, setShowGrid] = useState(false);
  
  const toggleGrid = useCallback(() => {
    setShowGrid(prev => !prev);
  }, []);
  
  const snapToGrid = useCallback((rect: CanvasRect): CanvasRect => {
    return {
      x: Math.round(rect.x / gridSize) * gridSize,
      y: Math.round(rect.y / gridSize) * gridSize,
      width: Math.round(rect.width / gridSize) * gridSize,
      height: Math.round(rect.height / gridSize) * gridSize
    };
  }, [gridSize]);
  
  const renderGrid = useCallback(() => {
    if (!showGrid) return null;
    
    const { width, height } = store.dimensions;
    const lines = [];
    
    // Vertical lines
    for (let x = 0; x < width; x += gridSize) {
      lines.push({
        points: [x, 0, x, height],
        stroke: "#ddd",
        strokeWidth: 1
      });
    }
    
    // Horizontal lines
    for (let y = 0; y < height; y += gridSize) {
      lines.push({
        points: [0, y, width, y],
        stroke: "#ddd",
        strokeWidth: 1
      });
    }
    
    return lines;
  }, [showGrid, store.dimensions, gridSize]);
  
  return {
    showGrid,
    toggleGrid,
    snapToGrid,
    renderGrid
  };
};
```

### Custom State Management

You can extend the canvas store with custom state and actions:

```typescript
import { create } from "zustand";
import useCanvasStore from "@/modules/canvas/store/canvas-store";

interface CustomCanvasState {
  customProperty: string;
  setCustomProperty: (value: string) => void;
  
  // Add more custom state and actions as needed
}

export const useCustomCanvasStore = create<CustomCanvasState>((set) => ({
  customProperty: "default",
  setCustomProperty: (value) => set({ customProperty: value }),
}));

// Usage example
const MyComponent = () => {
  const canvasStore = useCanvasStore();
  const customStore = useCustomCanvasStore();
  
  return (
    <div>
      <p>Canvas Mode: {canvasStore.mode}</p>
      <p>Custom Property: {customStore.customProperty}</p>
      <button onClick={() => customStore.setCustomProperty("new value")}>
        Update Custom Property
      </button>
    </div>
  );
};
```

## Integration with External Services

### Custom Image Generation Service

You can integrate the canvas with a custom image generation service:

```typescript
// Server action
export async function generateImageWithCustomService(
  prompt: string,
  userId: string,
  options: any
): Promise<GenerateImageResult> {
  try {
    // Call your custom service
    const response = await fetch("https://your-custom-service.com/generate", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        prompt,
        userId,
        options
      })
    });
    
    const data = await response.json();
    
    if (data.success) {
      return {
        success: true,
        predictionId: data.id,
        updatedCredits: data.credits
      };
    } else {
      return {
        success: false,
        error: data.error,
        updatedCredits: data.credits,
        type: "UNKNOWN_ERROR"
      };
    }
  } catch (error) {
    console.error("Error generating image:", error);
    return {
      success: false,
      error: "Failed to generate image",
      updatedCredits: 0,
      type: "UNKNOWN_ERROR"
    };
  }
}

// Client hook
export const useCustomGenerationService = (userId: string) => {
  const store = useCanvasStore();
  const { toast } = useToast();
  
  const generateWithCustomService = useCallback(async () => {
    // Implementation similar to handleGenerate in useGenerationTasks
    // but using the custom service
  }, [store, userId, toast]);
  
  return {
    generateWithCustomService
  };
};
```

## Performance Optimization

### Optimizing Canvas Rendering

To optimize canvas rendering performance:

1. Use appropriate layer management
2. Implement virtualization for large numbers of items
3. Use memoization for expensive calculations
4. Batch updates to minimize re-renders

```tsx
import { memo, useMemo } from "react";
import { Layer } from "react-konva";
import useCanvasStore from "@/modules/canvas/store/canvas-store";

// Memoized canvas item component
const MemoizedCanvasItem = memo(({ item }) => {
  // Component implementation
});

// Optimized layer component
const OptimizedCanvasLayer = () => {
  const items = useCanvasStore(state => state.generatedImages);
  
  // Only render items that are in the viewport
  const visibleItems = useMemo(() => {
    // Calculate which items are in the viewport
    return items.filter(item => {
      // Implement visibility check
      return true; // Placeholder
    });
  }, [items]);
  
  return (
    <Layer>
      {visibleItems.map((item, index) => (
        <MemoizedCanvasItem key={item.id || index} item={item} />
      ))}
    </Layer>
  );
};
```

## Troubleshooting

### Common Issues and Solutions

1. **Images not loading**
   - Check CORS settings for image sources
   - Ensure images have the `crossOrigin="anonymous"` attribute
   - Verify that the image URLs are accessible

2. **Performance issues**
   - Reduce the number of canvas items
   - Optimize layer management
   - Use appropriate image sizes
   - Implement virtualization for large canvases

3. **Selection and transformation issues**
   - Ensure the transformer is properly attached to the selected item
   - Check z-index of layers and items
   - Verify that event handlers are properly set up

4. **API integration issues**
   - Check authentication and authorization
   - Verify API endpoints and parameters
   - Implement proper error handling
   - Add logging for debugging

## Best Practices

1. **State Management**
   - Use the canvas store for all canvas-related state
   - Use selectors to optimize performance
   - Keep state updates atomic and focused

2. **Component Structure**
   - Follow the component hierarchy outlined in the architecture
   - Use composition for complex components
   - Keep components focused on a single responsibility

3. **Event Handling**
   - Use the provided hooks for event handling
   - Implement proper cleanup for event listeners
   - Handle edge cases and errors

4. **API Integration**
   - Follow the API integration patterns outlined in the documentation
   - Implement proper error handling
   - Provide clear feedback to users

5. **Performance**
   - Optimize rendering with memoization
   - Use appropriate layer management
   - Implement virtualization for large canvases
   - Batch updates to minimize re-renders

## Examples

### Basic Canvas Implementation

```tsx
import { ImageCanvas } from "@/modules/canvas/image-canvas";
import { ToastProvider } from "@/modules/ui/toast";
import { ThemeProvider } from "next-themes";
import { AblyProvider } from "ably/react";

export default function CanvasPage() {
  const userId = "user-123"; // Get from authentication
  
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <ToastProvider>
        <AblyProvider clientId={`user-${userId}`}>
          <div className="w-full h-screen">
            <ImageCanvas className="w-full h-full" />
          </div>
        </AblyProvider>
      </ToastProvider>
    </ThemeProvider>
  );
}
```

### Custom Canvas Implementation

```tsx
import { ImageCanvas } from "@/modules/canvas/image-canvas";
import { CustomToolbar } from "@/components/custom-toolbar";
import { CustomSidebar } from "@/components/custom-sidebar";
import useCanvasStore from "@/modules/canvas/store/canvas-store";
import { useEffect } from "react";

export default function CustomCanvasPage() {
  const store = useCanvasStore();
  
  // Initialize canvas with custom settings
  useEffect(() => {
    store.setBrushSize(30);
    store.setResolution("1024x1024");
    store.setStyleType("Realistic");
    
    // Clean up on unmount
    return () => {
      store.clearLines();
      store.setGeneratedImages([]);
    };
  }, []);
  
  return (
    <div className="flex h-screen">
      <CustomSidebar />
      <div className="flex-1 relative">
        <CustomToolbar />
        <ImageCanvas className="w-full h-full" />
      </div>
    </div>
  );
}
```

## Resources

- [Konva.js Documentation](https://konvajs.org/docs/)
- [React Konva Documentation](https://konvajs.org/docs/react/)
- [Zustand Documentation](https://github.com/pmndrs/zustand)
- [Canvas API Reference](https://developer.mozilla.org/en-US/docs/Web/API/Canvas_API)

## Support

For issues and support with the Canvas module, please:

1. Check the troubleshooting section in this guide
2. Review the API documentation
3. Check for known issues in the repository
4. Submit a detailed bug report if needed
