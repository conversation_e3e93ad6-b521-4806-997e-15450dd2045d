# Canvas Components

## Overview

The Canvas module consists of several key components that work together to provide a comprehensive drawing and image manipulation experience. This document details the main components, their responsibilities, and how they interact.

## Core Components

### ImageCanvas

`ImageCanvas` is the main component that orchestrates the entire canvas experience. It:
- Initializes the canvas state and hooks
- Sets up event handlers for user interactions
- Renders the Konva Stage and all layers
- Manages the component lifecycle

```tsx
export function ImageCanvas({ className }: { className?: string }) {
  // State initialization, hooks, and event handlers
  
  return (
    <div className={cn("relative", className)}>
      <CanvasControls />
      <Stage>
        <Layer>
          {/* Background */}
        </Layer>
        <CanvasLayersComponent />
        <PlaceholderLayer />
        <Layer>
          {/* Magic Fill Composite */}
        </Layer>
        <Layer>
          {/* Active Stroke Preview */}
        </Layer>
        <Layer>
          {/* Selection Rectangle */}
        </Layer>
      </Stage>
      <MainToolbar />
      <MagicFillToolbar />
      <PromptInput />
      <ImageSidebar />
    </div>
  );
}
```

### Canvas Layers

#### CanvasLayers

The `CanvasLayers` component renders the main content of the canvas, including:
- Input (uploaded) images
- Generated images
- Brush composite for masks

```tsx
const CanvasLayers: React.FC<{
  inputImage: HTMLImageElement | null;
  inputImageDimensions?: { width: number; height: number } | null;
  inputImagePosition: CanvasPosition;
  generatedImages: GeneratedImage[];
  transformConfig: TransformConfig;
  brushComposite: HTMLImageElement | null;
  mode: CanvasMode;
  onImageClick: (index: number, e: Konva.KonvaEventObject<any>) => void;
  onImageDragEnd: (index: number, e: Konva.KonvaEventObject<any>) => void;
  onTransformEnd: (index: number, e: Konva.KonvaEventObject<any>) => void;
  onImageUpdate: (index: number, updatedImage: GeneratedImage) => void;
  selectedImageRef: React.RefObject<Konva.Image>;
  transformerRef: React.RefObject<Konva.Transformer>;
}> = (props) => {
  return (
    <Layer>
      {/* Input Image */}
      {/* Generated Images */}
      {/* Brush Composite */}
    </Layer>
  );
};
```

#### PlaceholderLayer

The `PlaceholderLayer` component displays placeholders for images that are being generated:

```tsx
export const PlaceholderLayer: React.FC<{
  tasks: GenerationTask[];
  mode: CanvasMode;
  selectedPlaceholderId: string | null;
  onPlaceholderDragEnd: (id: string, position: CanvasPosition) => void;
  onPlaceholderTransform: (id: string, width: number, height: number) => void;
  onPlaceholderClick: (id: string, e: Konva.KonvaEventObject<any>) => void;
}> = (props) => {
  return (
    <Layer>
      {/* Placeholder items for generating images */}
    </Layer>
  );
};
```

#### ActiveStrokePreview

The `ActiveStrokePreview` component provides real-time feedback for the current brush stroke:

```tsx
export const ActiveStrokePreview: React.FC<{
  currentLine: CanvasLine;
}> = ({ currentLine }) => {
  return (
    <>
      {/* Line or shape preview based on the current tool */}
    </>
  );
};
```

### Canvas Items

#### UnifiedCanvasItem

The `UnifiedCanvasItem` component provides a consistent interface for all canvas items:

```tsx
export const UnifiedCanvasItem: React.FC<{
  type: "image" | "placeholder";
  x: number;
  y: number;
  width: number;
  height: number;
  rotation: number;
  image?: GeneratedImage;
  isSelected: boolean;
  isDraggable: boolean;
  onClick: (e: Konva.KonvaEventObject<any>) => void;
  onDragEnd: (e: Konva.KonvaEventObject<any>) => void;
  onTransformEnd: (e: Konva.KonvaEventObject<any>) => void;
  onImageUpdate?: (updatedImage: GeneratedImage) => void;
}> = (props) => {
  return (
    <Group>
      {/* Image or placeholder based on type */}
      {/* Transformer for selected items */}
    </Group>
  );
};
```

#### ImageCanvasItem

The `ImageCanvasItem` component renders an image on the canvas:

```tsx
export const ImageCanvasItem: React.FC<{
  image: GeneratedImage;
  isSelected: boolean;
  onSelect: (e: Konva.KonvaEventObject<any>) => void;
  onDragEnd: (e: Konva.KonvaEventObject<any>) => void;
  onTransformEnd: (e: Konva.KonvaEventObject<any>) => void;
}> = (props) => {
  return (
    <Image
      image={props.image.image}
      x={props.image.position.x}
      y={props.image.position.y}
      width={props.image.width}
      height={props.image.height}
      rotation={props.image.rotation}
      draggable={true}
      onClick={props.onSelect}
      onDragEnd={props.onDragEnd}
      onTransformEnd={props.onTransformEnd}
    />
  );
};
```

#### PlaceholderCanvasItem

The `PlaceholderCanvasItem` component displays a placeholder for generating images:

```tsx
export const PlaceholderCanvasItem: React.FC<{
  x: number;
  y: number;
  width: number;
  height: number;
  status: 'pending' | 'generating' | 'complete' | 'error';
  isSelected: boolean;
  onClick: (e: Konva.KonvaEventObject<any>) => void;
  onDragEnd: (e: Konva.KonvaEventObject<any>) => void;
  onTransformEnd: (e: Konva.KonvaEventObject<any>) => void;
}> = (props) => {
  return (
    <Group>
      {/* Placeholder rectangle */}
      {/* Status indicator */}
    </Group>
  );
};
```

### Toolbar Components

#### MainToolbar

The `MainToolbar` component provides the main tools for canvas operations:

```tsx
export const MainToolbar: React.FC<CanvasToolbarProps> = ({
  mode,
  onModeChange,
  onGenerate,
  onUpload,
  onUndo,
  onRedo,
  onSave,
  onDownload,
  canUndo,
  canRedo,
}) => {
  return (
    <div className="toolbar">
      {/* Tool buttons */}
      {/* Mode selection */}
      {/* Action buttons */}
    </div>
  );
};
```

#### MagicFillToolbar

The `MagicFillToolbar` component provides tools specific to the magic fill operation:

```tsx
export const MagicFillToolbar: React.FC<MagicFillToolbarProps> = ({
  activeMaskTool,
  onMaskToolChange,
  onInvert,
  onCancelMagicFill,
  onProceedMagicFill,
  onExportSelection,
}) => {
  return (
    <div className="magic-fill-toolbar">
      {/* Mask tool selection */}
      {/* Action buttons */}
    </div>
  );
};
```

#### PromptInput

The `PromptInput` component allows users to enter text prompts for image generation:

```tsx
export const PromptInput: React.FC<{
  onGenerate: () => Promise<void>;
}> = ({ onGenerate }) => {
  const store = useCanvasStore();
  
  return (
    <div className="prompt-input">
      <input
        type="text"
        value={store.prompt}
        onChange={(e) => store.setPrompt(e.target.value)}
        placeholder="Enter a prompt..."
      />
      <button onClick={onGenerate}>Generate</button>
    </div>
  );
};
```

### Control Components

#### CanvasControls

The `CanvasControls` component provides zoom and navigation controls:

```tsx
export const CanvasControls: React.FC<{
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetZoom: () => void;
  onUndo?: () => void;
  onRedo?: () => void;
  zoomLevel: number;
  canUndo?: boolean;
  canRedo?: boolean;
}> = (props) => {
  return (
    <div className="canvas-controls">
      {/* Zoom controls */}
      {/* Undo/Redo buttons */}
    </div>
  );
};
```

### Sidebar Components

#### ImageSidebar

The `ImageSidebar` component displays information and controls for the selected image:

```tsx
export const ImageSidebar: React.FC<{
  selectedImage: GeneratedImage | null;
  onClose: () => void;
  onDuplicate: (image: GeneratedImage) => void;
  onDelete: () => void;
  isOpen: boolean;
  className?: string;
}> = (props) => {
  return (
    <div className={cn("image-sidebar", props.className, props.isOpen ? "open" : "closed")}>
      {/* Image details */}
      {/* Image controls */}
    </div>
  );
};
```

### Overlay Components

#### LoadingOverlay

The `LoadingOverlay` component displays a loading indicator during operations:

```tsx
export const LoadingOverlay: React.FC<{
  isLoading: boolean;
  message?: string;
  opacity?: number;
}> = ({ isLoading, message = "Loading...", opacity = 0.7 }) => {
  if (!isLoading) return null;
  
  return (
    <div className="loading-overlay" style={{ opacity }}>
      <div className="spinner" />
      <div className="message">{message}</div>
    </div>
  );
};
```

## Component Interactions

### Image Selection and Transformation

1. User clicks on an image in the `CanvasLayers` component
2. `handleImageClick` is called, which:
   - Updates the `transformConfig` in the store
   - Opens the `ImageSidebar`
3. The `Transformer` component is attached to the selected image
4. User can drag, resize, or rotate the image
5. On transformation end, `handleTransformEnd` is called to update the image properties

### Drawing and Magic Fill

1. User selects the Magic Fill mode in the `MainToolbar`
2. `MagicFillToolbar` is displayed with mask tool options
3. User draws on the canvas using the selected mask tool
4. `handleMouseDown`, `handleMouseMove`, and `handleMouseUp` manage the drawing state
5. The `ActiveStrokePreview` component shows real-time feedback
6. User clicks "Proceed" in the `MagicFillToolbar`
7. `handleProceedMagicFill` is called, which:
   - Creates a selection area based on the drawn mask
   - Shows the `PromptInput` component
8. User enters a prompt and clicks "Generate"
9. `handleMagicFill` is called to generate an image in the selected area

### Image Generation

1. User selects the Generate mode in the `MainToolbar`
2. User draws a selection rectangle on the canvas
3. `PromptInput` is displayed for entering a text prompt
4. User enters a prompt and clicks "Generate"
5. `handleGenerate` is called, which:
   - Creates a generation task
   - Displays a placeholder in the `PlaceholderLayer`
   - Sends the request to the API
6. When the image is generated, it's added to the canvas in the `CanvasLayers` component

## Component Styling

The canvas components use a combination of:
- Tailwind CSS for utility-based styling
- CSS modules for component-specific styles
- Inline styles for dynamic properties

Example of the canvas container styles:

```css
.canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.canvas-controls {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 50;
}

.toolbar {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 50;
}

.magic-fill-toolbar {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 50;
}

.image-sidebar {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 320px;
  z-index: 40;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.image-sidebar.open {
  transform: translateX(0);
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 100;
}
```
