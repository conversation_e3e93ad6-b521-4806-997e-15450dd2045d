# Canvas API Integration

## Overview

The Canvas module integrates with external APIs to provide advanced functionality such as image generation, image editing, and file uploads. This document details the API integrations, how they are implemented, and how to use them.

## API Integration Architecture

The Canvas module uses a server-action based approach for API integrations, with the following components:

1. **Server Actions**: Functions that run on the server to interact with external APIs
2. **Client Hooks**: React hooks that call server actions and manage state
3. **Real-time Updates**: Integration with Ably for real-time updates on long-running operations
4. **Type Definitions**: TypeScript types for API requests and responses

## Image Generation API

### Server Action: Generate Image

The image generation functionality is implemented in server actions that interact with AI image generation services:

```typescript
// Example server action for image generation
export async function generateImage(
  prompt: string,
  userId: string,
  options: {
    resolution?: string;
    aspectRatio?: string;
    negativePrompt?: string;
    styleType?: string;
    seed?: number;
  }
): Promise<GenerateImageResult> {
  try {
    // Validate user and credits
    const user = await getUserById(userId);
    if (!user) {
      return {
        success: false,
        error: "User not found",
        updatedCredits: 0,
        type: "USER_NOT_FOUND"
      };
    }
    
    // Check if user has enough credits
    if (user.credits < 1) {
      return {
        success: false,
        error: "Insufficient credits",
        updatedCredits: user.credits,
        type: "INSUFFICIENT_CREDITS"
      };
    }
    
    // Prepare parameters for the AI service
    const parameters = {
      prompt,
      negative_prompt: options.negativePrompt || "",
      width: 1024,
      height: 1024,
      // ... other parameters
    };
    
    // If resolution is provided, override width and height
    if (options.resolution && options.resolution !== "None") {
      const [width, height] = options.resolution.split("x").map(Number);
      parameters.width = width;
      parameters.height = height;
    }
    
    // Create prediction in database
    const prediction = await prisma.prediction.create({
      data: {
        prompt,
        status: PredictionStatus.PROCESSING,
        userId,
        // ... other data
      }
    });
    
    // Start the generation process with the AI service
    const response = await aiService.createPrediction(parameters);
    
    // Update prediction with external ID
    await prisma.prediction.update({
      where: { id: prediction.id },
      data: {
        externalId: response.id,
        // ... other updates
      }
    });
    
    // Deduct credits from user
    await prisma.user.update({
      where: { id: userId },
      data: {
        credits: {
          decrement: 1
        }
      }
    });
    
    // Return success response
    return {
      success: true,
      predictionId: prediction.id,
      replicateId: response.id,
      updatedCredits: user.credits - 1
    };
  } catch (error) {
    console.error("Error generating image:", error);
    return {
      success: false,
      error: "Failed to generate image",
      updatedCredits: 0,
      type: "UNKNOWN_ERROR"
    };
  }
}
```

### Client Hook: useGenerationTasks

The client-side hook for managing image generation tasks:

```typescript
export const useGenerationTasks = (
  userId: string,
  channelName: string
) => {
  const store = useCanvasStore();
  const { toast } = useToast();
  const { captureSelection } = useCanvasOperations();
  
  const handleGenerate = useCallback(async () => {
    if (!userId) {
      toast({
        title: "Authentication required",
        description: "Please sign in to generate images",
        variant: "destructive"
      });
      return;
    }
    
    // Check if there's a valid selection area
    if (!store.selectionArea) {
      toast({
        title: "No selection area",
        description: "Please select an area on the canvas first",
        variant: "destructive"
      });
      return;
    }
    
    // Check if prompt is provided
    if (!store.prompt.trim()) {
      toast({
        title: "No prompt provided",
        description: "Please enter a prompt for the image generation",
        variant: "destructive"
      });
      return;
    }
    
    try {
      // Set generating state
      store.setIsGenerating(true);
      
      // Create a unique task ID
      const taskId = `task-${crypto.randomUUID()}`;
      
      // Add the task to the store
      store.addGenerationTask({
        id: taskId,
        selectionArea: store.selectionArea,
        prompt: store.prompt,
        status: 'pending',
        type: 'generate'
      });
      
      // Prepare options for the API
      const options = {
        resolution: store.resolution,
        aspectRatio: store.aspectRatio,
        negativePrompt: store.negativePrompt,
        styleType: store.styleType,
        seed: store.seed
      };
      
      // Call the server action
      const result = await generateImage(store.prompt, userId, options);
      
      if (result.success) {
        // Update the task with the prediction ID
        store.updateGenerationTask(taskId, {
          predictionId: result.predictionId,
          status: 'generating'
        });
        
        toast({
          title: "Generation started",
          description: "Your image is being generated"
        });
      } else {
        // Handle error
        store.updateGenerationTask(taskId, {
          status: 'error'
        });
        
        toast({
          title: "Generation failed",
          description: result.error || "Failed to start generation",
          variant: "destructive"
        });
        
        // Remove the task after a delay
        setTimeout(() => {
          store.removeGenerationTask(taskId);
        }, 3000);
      }
    } catch (error) {
      console.error("Error in handleGenerate:", error);
      toast({
        title: "Generation failed",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      store.setIsGenerating(false);
    }
  }, [store, userId, toast, captureSelection]);
  
  const handleChannelUpdate = useCallback((message: any) => {
    // Handle real-time updates from Ably
    const data = message.data;
    
    if (!data || !data.id) return;
    
    // Find the task with the matching prediction ID
    const task = store.generationTasks.find(t => t.predictionId === data.id);
    if (!task) return;
    
    if (data.status === PredictionStatus.SUCCEEDED) {
      // Handle successful generation
      if (data.outputUrl) {
        // Update the task with the image URL
        store.updateGenerationTask(task.id, {
          status: 'complete',
          imageUrl: data.outputUrl
        });
        
        // Load the image
        const img = new Image();
        img.crossOrigin = "anonymous";
        img.src = data.outputUrl;
        
        img.onload = () => {
          // Add the image to the canvas
          store.addGeneratedImage({
            id: `gen-${crypto.randomUUID()}`,
            image: img,
            src: data.outputUrl,
            element: img,
            position: {
              x: task.selectionArea.x,
              y: task.selectionArea.y
            },
            width: task.selectionArea.width,
            height: task.selectionArea.height,
            rotation: 0,
            isSelected: false,
            isNew: true,
            prompt: data.prompt,
            model: data.model
          });
          
          toast({
            title: "Image generated",
            description: "Your image has been generated successfully"
          });
        };
        
        img.onerror = () => {
          console.error("Failed to load generated image:", data.outputUrl);
          toast({
            title: "Image loading failed",
            description: "The generated image could not be loaded",
            variant: "destructive"
          });
        };
      }
    } else if (data.status === PredictionStatus.FAILED) {
      // Handle failed generation
      store.updateGenerationTask(task.id, {
        status: 'error'
      });
      
      toast({
        title: "Generation failed",
        description: data.error || "The image generation process failed",
        variant: "destructive"
      });
      
      // Remove the task after a delay
      setTimeout(() => {
        store.removeGenerationTask(task.id);
      }, 3000);
    }
  }, [store, toast]);
  
  return {
    handleGenerate,
    handleChannelUpdate
  };
};
```

## Image Editing API

### Server Action: Edit Image

The image editing functionality is implemented in server actions:

```typescript
// Example server action for image editing
export async function editImage(
  imageData: string,
  userId: string,
  options: {
    prompt: string;
    maskData?: string;
    resolution?: string;
    negativePrompt?: string;
  }
): Promise<EditImageResult> {
  try {
    // Validate user and credits
    const user = await getUserById(userId);
    if (!user) {
      return {
        success: false,
        error: "User not found",
        updatedCredits: 0,
        type: "USER_NOT_FOUND"
      };
    }
    
    // Check if user has enough credits
    if (user.credits < 2) {
      return {
        success: false,
        error: "Insufficient credits (requires 2 credits)",
        updatedCredits: user.credits,
        type: "INSUFFICIENT_CREDITS"
      };
    }
    
    // Prepare parameters for the AI service
    const parameters = {
      prompt: options.prompt,
      negative_prompt: options.negativePrompt || "",
      image: imageData,
      mask: options.maskData,
      // ... other parameters
    };
    
    // Create prediction in database
    const prediction = await prisma.prediction.create({
      data: {
        prompt: options.prompt,
        status: PredictionStatus.PROCESSING,
        userId,
        type: "EDIT",
        // ... other data
      }
    });
    
    // Start the editing process with the AI service
    const response = await aiService.createEditPrediction(parameters);
    
    // Update prediction with external ID
    await prisma.prediction.update({
      where: { id: prediction.id },
      data: {
        externalId: response.id,
        // ... other updates
      }
    });
    
    // Deduct credits from user
    await prisma.user.update({
      where: { id: userId },
      data: {
        credits: {
          decrement: 2
        }
      }
    });
    
    // Return success response
    return {
      success: true,
      predictionId: prediction.id,
      replicateId: response.id,
      updatedCredits: user.credits - 2
    };
  } catch (error) {
    console.error("Error editing image:", error);
    return {
      success: false,
      error: "Failed to edit image",
      updatedCredits: 0,
      type: "UNKNOWN_ERROR"
    };
  }
}
```

### Client Hook: useMagicFill

The client-side hook for managing image editing (magic fill):

```typescript
export const useMagicFill = (
  userId: string,
  addGeneratedImage: (image: GeneratedImage) => void
) => {
  const store = useCanvasStore();
  const { toast } = useToast();
  const { captureSelection } = useCanvasOperations();
  
  const handleMagicFill = useCallback(async (stageRef: React.RefObject<Konva.Stage>) => {
    if (!userId) {
      toast({
        title: "Authentication required",
        description: "Please sign in to use magic fill",
        variant: "destructive"
      });
      return;
    }
    
    // Check if there's a valid selection area
    if (!store.selectionArea) {
      toast({
        title: "No selection area",
        description: "Please select an area on the canvas first",
        variant: "destructive"
      });
      return;
    }
    
    // Check if prompt is provided
    if (!store.prompt.trim()) {
      toast({
        title: "No prompt provided",
        description: "Please enter a prompt for the magic fill",
        variant: "destructive"
      });
      return;
    }
    
    try {
      // Set generating state
      store.setIsGenerating(true);
      
      // Create a unique task ID
      const taskId = `task-${crypto.randomUUID()}`;
      
      // Add the task to the store
      store.addGenerationTask({
        id: taskId,
        selectionArea: store.selectionArea,
        prompt: store.prompt,
        status: 'pending',
        type: 'magic-fill'
      });
      
      // Capture the selection area
      const imageData = await captureSelection(stageRef);
      if (!imageData) {
        throw new Error("Failed to capture selection area");
      }
      
      // Create a composite of the brush strokes for the mask
      const maskData = await createMaskFromLines(
        store.lines,
        store.selectionArea,
        stageRef
      );
      
      // Prepare options for the API
      const options = {
        prompt: store.prompt,
        maskData,
        resolution: store.resolution,
        negativePrompt: store.negativePrompt
      };
      
      // Call the server action
      const result = await editImage(imageData, userId, options);
      
      if (result.success) {
        // Update the task with the prediction ID
        store.updateGenerationTask(taskId, {
          predictionId: result.predictionId,
          status: 'generating'
        });
        
        toast({
          title: "Magic fill started",
          description: "Your image is being processed"
        });
      } else {
        // Handle error
        store.updateGenerationTask(taskId, {
          status: 'error'
        });
        
        toast({
          title: "Magic fill failed",
          description: result.error || "Failed to start magic fill",
          variant: "destructive"
        });
        
        // Remove the task after a delay
        setTimeout(() => {
          store.removeGenerationTask(taskId);
        }, 3000);
      }
    } catch (error) {
      console.error("Error in handleMagicFill:", error);
      toast({
        title: "Magic fill failed",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      store.setIsGenerating(false);
    }
  }, [store, userId, toast, captureSelection, addGeneratedImage]);
  
  return {
    handleMagicFill
  };
};
```

## Image Upload API

### Server Action: Upload Image

The image upload functionality is implemented in server actions:

```typescript
// Example server action for image upload
export async function uploadImage(
  imageData: string,
  userId: string,
  filename: string
): Promise<UploadImageResult> {
  try {
    // Validate user
    const user = await getUserById(userId);
    if (!user) {
      return {
        success: false,
        error: "User not found",
        type: "USER_NOT_FOUND"
      };
    }
    
    // Convert data URL to buffer
    const base64Data = imageData.replace(/^data:image\/\w+;base64,/, "");
    const buffer = Buffer.from(base64Data, "base64");
    
    // Generate a unique filename
    const uniqueFilename = `${userId}/${Date.now()}-${filename}`;
    
    // Upload to storage service (e.g., S3, Cloudinary)
    const uploadResult = await storageService.uploadBuffer(
      buffer,
      uniqueFilename,
      {
        contentType: "image/png"
      }
    );
    
    // Create record in database
    const image = await prisma.userImage.create({
      data: {
        userId,
        url: uploadResult.url,
        filename: uniqueFilename,
        // ... other data
      }
    });
    
    // Return success response
    return {
      success: true,
      imageId: image.id,
      url: uploadResult.url
    };
  } catch (error) {
    console.error("Error uploading image:", error);
    return {
      success: false,
      error: "Failed to upload image",
      type: "UNKNOWN_ERROR"
    };
  }
}
```

### Client Hook: useFileUpload

The client-side hook for managing file uploads:

```typescript
export const useFileUpload = () => {
  const store = useCanvasStore();
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const triggerFileUpload = useCallback(() => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, [fileInputRef]);
  
  const handleFileInput = useCallback((
    e: React.ChangeEvent<HTMLInputElement>,
    stageRef: React.RefObject<Konva.Stage>,
    dimensions: CanvasSize
  ) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    
    const file = files[0];
    
    // Check file type
    if (!file.type.startsWith("image/")) {
      toast({
        title: "Invalid file type",
        description: "Please select an image file",
        variant: "destructive"
      });
      return;
    }
    
    // Check file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "Please select an image smaller than 10MB",
        variant: "destructive"
      });
      return;
    }
    
    // Create a FileReader to read the image
    const reader = new FileReader();
    
    reader.onload = (event) => {
      if (!event.target || typeof event.target.result !== "string") return;
      
      // Create an image element to get dimensions
      const img = new Image();
      img.src = event.target.result;
      
      img.onload = () => {
        // Calculate position for the image
        const position = findAvailableCanvasPosition(
          img.width,
          img.height,
          stageRef.current,
          dimensions,
          store.generatedImages.map(img => ({
            x: img.position.x,
            y: img.position.y,
            width: img.width,
            height: img.height
          }))
        );
        
        // Set the input image in the store
        store.setInputImage(img);
        store.setInputImagePosition(position);
        store.setInputImageDimensions({
          width: img.width,
          height: img.height
        });
        
        toast({
          title: "Image uploaded",
          description: "The image has been added to the canvas"
        });
      };
      
      img.onerror = () => {
        toast({
          title: "Failed to load image",
          description: "The selected image could not be loaded",
          variant: "destructive"
        });
      };
    };
    
    reader.onerror = () => {
      toast({
        title: "Failed to read file",
        description: "An error occurred while reading the file",
        variant: "destructive"
      });
    };
    
    // Read the file as a data URL
    reader.readAsDataURL(file);
    
    // Reset the input to allow selecting the same file again
    e.target.value = "";
  }, [store, toast]);
  
  return {
    fileInputRef,
    triggerFileUpload,
    handleFileInput
  };
};
```

## Real-time Updates with Ably

The Canvas module uses Ably for real-time updates on long-running operations:

### Setting Up the Channel

```typescript
// In the ImageCanvas component
const channelName = currentUser.data?.id 
  ? `predictions-${currentUser.data.id}`
  : "";

const { channel } = useChannel(channelName);

// Set up subscription to Ably channel
useEffect(() => {
  if (!channel) return;
  
  channel.subscribe('prediction-update', handleChannelUpdate);
  
  return () => {
    channel.unsubscribe('prediction-update', handleChannelUpdate);
  };
}, [channel, handleChannelUpdate]);
```

### Handling Channel Updates

```typescript
const handleChannelUpdate = useCallback((message: any) => {
  const data = message.data;
  
  if (!data || !data.id) return;
  
  // Find the task with the matching prediction ID
  const task = store.generationTasks.find(t => t.predictionId === data.id);
  if (!task) return;
  
  if (data.status === PredictionStatus.SUCCEEDED) {
    // Handle successful generation
    // ...
  } else if (data.status === PredictionStatus.FAILED) {
    // Handle failed generation
    // ...
  }
}, [store, toast]);
```

## API Types

The Canvas module defines TypeScript types for API requests and responses:

```typescript
// Base types for predictions
export enum PredictionStatus {
  PROCESSING = "PROCESSING",
  SUCCEEDED = "SUCCEEDED",
  FAILED = "FAILED",
  CANCELED = "CANCELED",
}

// Error types
export type ErrorType = 
  | "INSUFFICIENT_CREDITS" 
  | "INVALID_INPUT" 
  | "USER_NOT_FOUND" 
  | "USER_CREDIT_ERROR" 
  | "VALIDATION_ERROR" 
  | "UNKNOWN_ERROR";

// Generate image result
export type GenerateImageResult = {
  success: boolean;
  predictionId?: string;
  replicateId?: string;
  error?: string;
  updatedCredits: number;
  type?: ErrorType;
};

// Edit image result
export type EditImageResult = {
  success: boolean;
  predictionId?: string;
  replicateId?: string;
  error?: string;
  updatedCredits: number;
  type?: ErrorType;
};

// Upload image result
export type UploadImageResult = {
  success: boolean;
  imageId?: string;
  url?: string;
  error?: string;
  type?: ErrorType;
};
```

## Best Practices for API Integration

1. **Error Handling**: Implement comprehensive error handling for all API calls.
2. **Loading States**: Show loading states during API operations.
3. **Feedback**: Provide clear feedback to users about the status of operations.
4. **Retry Logic**: Implement retry logic for transient failures.
5. **Caching**: Cache API responses when appropriate to reduce API calls.
6. **Rate Limiting**: Respect API rate limits to avoid being throttled.
7. **Authentication**: Securely handle authentication for API calls.
8. **Validation**: Validate input data before sending it to the API.
9. **Logging**: Log API calls and responses for debugging.
10. **Testing**: Test API integrations with mock data.

## Extending API Integration

To add a new API integration to the Canvas module:

1. **Create a Server Action**: Implement a server action that interacts with the API.
2. **Define Types**: Define TypeScript types for the request and response.
3. **Create a Client Hook**: Implement a client-side hook that calls the server action.
4. **Add UI Components**: Create UI components for the new functionality.
5. **Update Store**: Update the canvas store to support the new functionality.
6. **Add Documentation**: Document the new API integration.

Example of adding a new API integration:

```typescript
// Server action
export async function applyImageFilter(
  imageData: string,
  userId: string,
  filter: string
): Promise<ApplyFilterResult> {
  try {
    // Implementation
    // ...
    
    return {
      success: true,
      filteredImageUrl: result.url
    };
  } catch (error) {
    console.error("Error applying filter:", error);
    return {
      success: false,
      error: "Failed to apply filter"
    };
  }
}

// Client hook
export const useImageFilters = (userId: string) => {
  const store = useCanvasStore();
  const { toast } = useToast();
  
  const applyFilter = useCallback(async (
    imageIndex: number,
    filter: string
  ) => {
    if (!userId) {
      toast({
        title: "Authentication required",
        description: "Please sign in to apply filters",
        variant: "destructive"
      });
      return;
    }
    
    const image = store.generatedImages[imageIndex];
    if (!image) {
      toast({
        title: "No image selected",
        description: "Please select an image to apply the filter",
        variant: "destructive"
      });
      return;
    }
    
    try {
      // Convert image to data URL
      const canvas = document.createElement("canvas");
      canvas.width = image.width;
      canvas.height = image.height;
      const ctx = canvas.getContext("2d");
      if (!ctx) throw new Error("Failed to get canvas context");
      
      ctx.drawImage(image.image, 0, 0, image.width, image.height);
      const imageData = canvas.toDataURL("image/png");
      
      // Call the server action
      const result = await applyImageFilter(imageData, userId, filter);
      
      if (result.success && result.filteredImageUrl) {
        // Load the filtered image
        const img = new Image();
        img.crossOrigin = "anonymous";
        img.src = result.filteredImageUrl;
        
        img.onload = () => {
          // Update the image in the store
          store.updateImageTransform(imageIndex, {
            image: img,
            src: result.filteredImageUrl
          });
          
          toast({
            title: "Filter applied",
            description: `The ${filter} filter has been applied to the image`
          });
        };
        
        img.onerror = () => {
          toast({
            title: "Failed to load filtered image",
            description: "The filtered image could not be loaded",
            variant: "destructive"
          });
        };
      } else {
        toast({
          title: "Failed to apply filter",
          description: result.error || "An error occurred while applying the filter",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Error in applyFilter:", error);
      toast({
        title: "Failed to apply filter",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    }
  }, [store, userId, toast]);
  
  return {
    applyFilter
  };
};
```
