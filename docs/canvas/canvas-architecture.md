# Canvas Architecture

## Overview

The Canvas module follows a modular architecture that separates concerns and promotes maintainability. It uses a combination of React components, custom hooks, and a centralized state management system to create a cohesive and performant drawing and image manipulation experience.

## Directory Structure

```
src/modules/canvas/
├── actions/                 # Server actions and API integrations
│   ├── edit-image.ts        # Image editing functionality
│   └── upload-image.ts      # Image upload handling
├── components/              # UI components
│   ├── canvas-items/        # Individual canvas item components
│   ├── canvas-layers/       # Layer management components
│   ├── controls/            # UI controls for canvas operations
│   ├── overlays/            # Overlay components (loading, etc.)
│   ├── sidebar/             # Sidebar components for settings
│   ├── toolbar/             # Toolbar components
│   └── ...                  # Other components
├── hooks/                   # Custom React hooks
│   ├── use-brush-composite.tsx     # Brush composition hook
│   ├── use-canvas-export.ts        # Canvas export functionality
│   ├── use-canvas-items.ts         # Canvas item management
│   ├── use-canvas-operations.ts    # Canvas drawing operations
│   ├── use-canvas-pointer.ts       # Pointer event handling
│   ├── use-canvas-zoom.ts          # Zoom functionality
│   ├── use-file-upload.ts          # File upload handling
│   ├── use-generation-tasks.ts     # Image generation task management
│   └── use-magic-fill.ts           # Magic fill functionality
├── store/                   # State management
│   └── canvas-store.ts      # Zustand store for canvas state
├── styles/                  # CSS styles
│   └── canvas-container.css # Canvas-specific styles
├── types/                   # TypeScript type definitions
│   ├── base.ts              # Base type definitions
│   ├── canvas.ts            # Canvas-specific types
│   ├── edit-image.ts        # Image editing types
│   ├── image-dimensions.ts  # Image dimension types
│   └── index.ts             # Type exports
├── utils/                   # Utility functions
│   └── canvas-utils.ts      # Canvas helper functions
└── image-canvas.tsx         # Main canvas component
```

## Core Architecture Components

### 1. Main Canvas Component

The `ImageCanvas` component serves as the entry point and orchestrates the entire canvas experience. It:
- Initializes the canvas state
- Sets up event handlers
- Renders the canvas layers
- Manages the component lifecycle

### 2. State Management

The canvas module uses Zustand for state management, providing:
- A centralized store for all canvas state
- Immutable state updates
- History tracking for undo/redo
- Performance optimizations

### 3. Component Hierarchy

```
ImageCanvas
├── Stage (Konva)
│   ├── Background Layer
│   ├── Canvas Layers
│   │   ├── Input Image
│   │   ├── Generated Images
│   │   └── Brush Composite
│   ├── Placeholder Layer
│   ├── Magic Fill Layer
│   ├── Active Stroke Layer
│   └── Selection Layer
├── Toolbar Components
│   ├── Main Toolbar
│   ├── Magic Fill Toolbar
│   └── Prompt Input
├── Control Components
│   └── Canvas Controls
└── Sidebar Components
    └── Image Sidebar
```

### 4. Custom Hooks

The module uses custom hooks to encapsulate specific functionality:
- `useCanvasOperations`: Handles drawing operations and mouse events
- `useCanvasZoom`: Manages zoom and pan functionality
- `useCanvasItems`: Handles canvas item management (selection, transformation)
- `useCanvasExport`: Provides export functionality
- `useGenerationTasks`: Manages image generation tasks
- `useMagicFill`: Handles magic fill operations
- `useFileUpload`: Manages file upload functionality

### 5. Canvas Layers

The canvas uses a layered approach for rendering:
1. **Background Layer**: Renders the canvas background
2. **Canvas Layers**: Renders input images and generated images
3. **Placeholder Layer**: Shows placeholders for generating images
4. **Magic Fill Layer**: Displays the magic fill composite
5. **Active Stroke Layer**: Shows real-time stroke preview
6. **Selection Layer**: Displays selection rectangles

## Data Flow

1. **User Interaction**: User interacts with the canvas (mouse/touch events)
2. **Event Handling**: Event handlers process the interaction
3. **State Updates**: State is updated through the Zustand store
4. **Component Re-rendering**: Components re-render based on state changes
5. **Canvas Rendering**: Konva updates the canvas display

## Integration Points

The canvas module integrates with:
- **File System**: For uploading and downloading images
- **API Services**: For image generation and processing
- **Authentication**: For user-specific operations
- **Notification System**: For providing feedback to users

## Performance Considerations

The canvas architecture includes several performance optimizations:
- **Layer-based Rendering**: Only updates layers that change
- **Debounced Operations**: Prevents excessive re-renders
- **Memoization**: Caches expensive calculations
- **Batch Updates**: Groups state updates for efficiency
- **Virtualization**: Only renders visible elements

## Extensibility

The modular architecture allows for easy extension:
- New tools can be added by extending the toolbar and implementing handlers
- Additional layer types can be created by adding new layer components
- Custom operations can be implemented through new hooks
- External services can be integrated through the actions directory
