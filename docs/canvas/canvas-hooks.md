# Canvas Hooks

## Overview

The Canvas module uses custom React hooks to encapsulate specific functionality and logic. These hooks provide a clean separation of concerns and make the code more maintainable and reusable. This document details the main hooks, their responsibilities, and how to use them.

## Core Hooks

### useCanvasStore

While not a traditional hook, `useCanvasStore` is the foundation for state management in the canvas module:

```typescript
const useCanvasStore = create<CanvasStore>((set, get) => ({
  // State properties
  mode: CanvasMode.Move,
  maskTool: "brush",
  brushSize: 20,
  lines: [],
  dimensions: { width: 0, height: 0 },
  // ... more state properties
  
  // Methods for updating state
  setMode: (mode) => set({ mode }),
  setBrushSize: (size) => set({ brushSize: size }),
  // ... more methods
}));
```

Usage:
```typescript
const store = useCanvasStore();
// Access state
const mode = store.mode;
// Update state
store.setMode(CanvasMode.MagicFill);
```

### useCanvasOperations

The `useCanvasOperations` hook manages drawing operations and mouse interactions:

```typescript
export const useCanvasOperations = () => {
  const store = useCanvasStore();
  const [isDrawing, setIsDrawing] = useState(false);
  const [localSelectionRect, setLocalSelectionRect] = useState<CanvasRect | null>(null);
  const [activeLine, setActiveLine] = useState<CanvasLine | null>(null);
  
  // Mouse event handlers
  const handleMouseDown = useCallback((e: Konva.KonvaEventObject<MouseEvent>, stageRef: React.RefObject<Konva.Stage>) => {
    // Handle mouse down for drawing and selection
  }, [store]);
  
  const handleMouseMove = useCallback((e: Konva.KonvaEventObject<MouseEvent>, stageRef: React.RefObject<Konva.Stage>) => {
    // Handle mouse move for drawing and selection
  }, [isDrawing, localSelectionRect, store]);
  
  const handleMouseUp = useCallback((stageRef: React.RefObject<Konva.Stage>) => {
    // Handle mouse up for drawing and selection
  }, [isDrawing, localSelectionRect, store]);
  
  // Other methods
  
  return {
    isDrawing,
    localSelectionRect,
    activeLine,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    // ... more methods and state
  };
};
```

Usage:
```typescript
const {
  handleMouseDown,
  handleMouseMove,
  handleMouseUp,
  isDrawing
} = useCanvasOperations();

// In JSX
<Stage
  onMouseDown={(e) => handleMouseDown(e, stageRef)}
  onMouseMove={(e) => handleMouseMove(e, stageRef)}
  onMouseUp={() => handleMouseUp(stageRef)}
/>
```

### useCanvasZoom

The `useCanvasZoom` hook manages zoom and pan functionality:

```typescript
export const useCanvasZoom = ({
  minScale = 0.1,
  maxScale = 5,
  scaleFactor = 1.1,
  defaultScale = 1
}: ZoomOptions = {}) => {
  const store = useCanvasStore();
  
  const handleWheel = useCallback((e: Konva.KonvaEventObject<WheelEvent>, stageRef: React.RefObject<Konva.Stage>) => {
    // Handle zoom with mouse wheel
  }, [store]);
  
  const zoomIn = useCallback((stageRef: React.RefObject<Konva.Stage>, dimensions: CanvasSize) => {
    // Zoom in logic
  }, [store]);
  
  const zoomOut = useCallback((stageRef: React.RefObject<Konva.Stage>, dimensions: CanvasSize) => {
    // Zoom out logic
  }, [store]);
  
  const resetZoom = useCallback((stageRef: React.RefObject<Konva.Stage>, dimensions: CanvasSize) => {
    // Reset zoom logic
  }, [store]);
  
  const initializeZoom = useCallback((stageRef: React.RefObject<Konva.Stage>, dimensions: CanvasSize) => {
    // Initialize zoom logic
  }, [defaultScale]);
  
  return {
    handleWheel,
    zoomIn,
    zoomOut,
    resetZoom,
    initializeZoom,
    defaultScale
  };
};
```

Usage:
```typescript
const { 
  handleWheel, 
  zoomIn, 
  zoomOut, 
  resetZoom 
} = useCanvasZoom({
  minScale: 0.1,
  maxScale: 5,
  scaleFactor: 1.1,
  defaultScale: 0.7
});

// In JSX
<Stage onWheel={(e) => handleWheel(e, stageRef)} />
<Button onClick={() => zoomIn(stageRef, dimensions)}>Zoom In</Button>
```

### useCanvasItems

The `useCanvasItems` hook manages canvas items (images, placeholders):

```typescript
export const useCanvasItems = () => {
  const store = useCanvasStore();
  const [selectedPlaceholderId, setSelectedPlaceholderId] = useState<string | null>(null);
  
  const handleImageClick = useCallback((index: number, e: Konva.KonvaEventObject<any>) => {
    // Handle image selection
  }, [store]);
  
  const handleImageDragEnd = useCallback((index: number, e: Konva.KonvaEventObject<any>) => {
    // Handle image drag end
  }, [store]);
  
  const handleTransformEnd = useCallback((index: number, e: Konva.KonvaEventObject<any>) => {
    // Handle image transform end
  }, [store]);
  
  const handleImageUpdate = useCallback((index: number, updatedImage: GeneratedImage) => {
    // Update image properties
  }, [store]);
  
  const handlePlaceholderClick = useCallback((id: string, e: Konva.KonvaEventObject<any>) => {
    // Handle placeholder selection
  }, [store]);
  
  const deselectAll = useCallback(() => {
    // Deselect all items
  }, [store]);
  
  const addGeneratedImage = useCallback((image: GeneratedImage) => {
    // Add a new generated image
  }, [store]);
  
  const deleteSelectedImage = useCallback(() => {
    // Delete the selected image
  }, [store]);
  
  return {
    selectedPlaceholderId,
    setSelectedPlaceholderId,
    handleImageClick,
    handleImageDragEnd,
    handleTransformEnd,
    handleImageUpdate,
    handlePlaceholderClick,
    deselectAll,
    addGeneratedImage,
    deleteSelectedImage
  };
};
```

Usage:
```typescript
const { 
  handleImageClick, 
  handleImageDragEnd, 
  handleTransformEnd,
  deleteSelectedImage
} = useCanvasItems();

// In JSX
<UnifiedCanvasItem
  onClick={(e) => handleImageClick(index, e)}
  onDragEnd={(e) => handleImageDragEnd(index, e)}
  onTransformEnd={(e) => handleTransformEnd(index, e)}
/>
<Button onClick={deleteSelectedImage}>Delete</Button>
```

### useCanvasExport

The `useCanvasExport` hook provides functionality for exporting canvas content:

```typescript
export const useCanvasExport = () => {
  const { toast } = useToast();
  
  const captureSelectionAreaImage = useCallback(async (
    selection: CanvasRect,
    stageRef: React.RefObject<Konva.Stage>
  ): Promise<string | null> => {
    // Capture the selected area as an image
  }, []);
  
  const exportSelectionAsImage = useCallback(async (
    selection: CanvasRect,
    stageRef: React.RefObject<Konva.Stage>,
    filename: string
  ): Promise<void> => {
    // Export the selected area as a downloadable image
  }, [captureSelectionAreaImage, toast]);
  
  const captureAndUploadSelectionArea = useCallback(async (
    selection: CanvasRect,
    stageRef: React.RefObject<Konva.Stage>,
    userId: string
  ): Promise<string | null> => {
    // Capture and upload the selected area
  }, [captureSelectionAreaImage, toast]);
  
  return {
    captureSelectionAreaImage,
    exportSelectionAsImage,
    captureAndUploadSelectionArea
  };
};
```

Usage:
```typescript
const { exportSelectionAsImage } = useCanvasExport();

// Export the selected area
const handleExport = async () => {
  if (store.selectionArea && stageRef.current) {
    await exportSelectionAsImage(
      store.selectionArea,
      stageRef,
      `export-${Date.now()}.png`
    );
  }
};
```

### useGenerationTasks

The `useGenerationTasks` hook manages image generation tasks:

```typescript
export const useGenerationTasks = (
  userId: string,
  channelName: string
) => {
  const store = useCanvasStore();
  const { toast } = useToast();
  const { captureSelection } = useCanvasOperations();
  
  const handleGenerate = useCallback(async () => {
    // Handle image generation
  }, [store, captureSelection, userId, toast]);
  
  const handleChannelUpdate = useCallback((message: any) => {
    // Handle updates from the real-time channel
  }, [store, toast]);
  
  const handlePlaceholderDragEnd = useCallback((id: string, position: CanvasPosition) => {
    // Handle placeholder drag end
  }, [store]);
  
  const handlePlaceholderTransform = useCallback((id: string, width: number, height: number) => {
    // Handle placeholder transform
  }, [store]);
  
  return {
    handleGenerate,
    handleChannelUpdate,
    handlePlaceholderDragEnd,
    handlePlaceholderTransform
  };
};
```

Usage:
```typescript
const { 
  handleGenerate,
  handleChannelUpdate
} = useGenerationTasks(
  currentUser.id,
  `predictions-${currentUser.id}`
);

// Generate an image
<Button onClick={handleGenerate}>Generate</Button>

// Subscribe to channel updates
useEffect(() => {
  channel.subscribe('prediction-update', handleChannelUpdate);
  return () => {
    channel.unsubscribe('prediction-update', handleChannelUpdate);
  };
}, [channel, handleChannelUpdate]);
```

### useMagicFill

The `useMagicFill` hook manages magic fill operations:

```typescript
export const useMagicFill = (
  userId: string,
  addGeneratedImage: (image: GeneratedImage) => void
) => {
  const store = useCanvasStore();
  const { toast } = useToast();
  const { captureSelection } = useCanvasOperations();
  
  const handleMagicFill = useCallback(async (stageRef: React.RefObject<Konva.Stage>) => {
    // Handle magic fill operation
  }, [store, captureSelection, userId, toast, addGeneratedImage]);
  
  return {
    handleMagicFill
  };
};
```

Usage:
```typescript
const { handleMagicFill } = useMagicFill(
  currentUser.id,
  addGeneratedImage
);

// Perform magic fill
<Button onClick={() => handleMagicFill(stageRef)}>Magic Fill</Button>
```

### useFileUpload

The `useFileUpload` hook manages file upload functionality:

```typescript
export const useFileUpload = () => {
  const store = useCanvasStore();
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const triggerFileUpload = useCallback(() => {
    // Trigger the file input click
  }, [fileInputRef]);
  
  const handleFileInput = useCallback((
    e: React.ChangeEvent<HTMLInputElement>,
    stageRef: React.RefObject<Konva.Stage>,
    dimensions: CanvasSize
  ) => {
    // Handle file selection and upload
  }, [store, toast]);
  
  return {
    fileInputRef,
    triggerFileUpload,
    handleFileInput
  };
};
```

Usage:
```typescript
const { 
  fileInputRef, 
  triggerFileUpload, 
  handleFileInput 
} = useFileUpload();

// In JSX
<Button onClick={triggerFileUpload}>Upload</Button>
<input
  type="file"
  ref={fileInputRef}
  className="hidden"
  accept="image/*"
  onChange={(e) => handleFileInput(e, stageRef, dimensions)}
/>
```

### useBrushComposite

The `useBrushComposite` hook manages brush composite for magic fill:

```typescript
export const useBrushComposite = (
  lines: CanvasLine[],
  dimensions: CanvasSize,
  isDrawing: boolean
) => {
  const [composite, setComposite] = useState<HTMLImageElement | null>(null);
  
  useEffect(() => {
    // Create composite image from brush strokes
  }, [lines, dimensions, isDrawing]);
  
  return composite;
};
```

Usage:
```typescript
const brushComposite = useBrushComposite(
  store.lines,
  dimensions,
  isDrawing
);

// In JSX
{brushComposite && (
  <Image image={brushComposite} x={0} y={0} listening={false} />
)}
```

## Hook Composition

The canvas hooks are designed to be composable, allowing for complex functionality to be built from simpler building blocks. For example:

```typescript
// In ImageCanvas component
const { 
  handleMouseDown,
  handleMouseMove,
  handleMouseUp,
  handleStageClick,
  handleMaskToolChange,
  handleCancelMagicFill,
  handleProceedMagicFill,
  captureSelection
} = useCanvasOperations();

const { 
  handleImageClick,
  handleImageDragEnd,
  handleTransformEnd,
  handleImageUpdate,
  handlePlaceholderClick,
  deleteSelectedImage
} = useCanvasItems();

const { 
  handleWheel, 
  zoomIn, 
  zoomOut, 
  resetZoom,
  initializeZoom
} = useCanvasZoom({
  minScale: 0.1,
  maxScale: 5,
  scaleFactor: 1.1,
  defaultScale: 0.7
});

const { 
  captureSelectionAreaImage, 
  exportSelectionAsImage 
} = useCanvasExport();

const { 
  fileInputRef, 
  triggerFileUpload, 
  handleFileInput 
} = useFileUpload();

const { 
  handleGenerate,
  handleChannelUpdate,
  handlePlaceholderDragEnd,
  handlePlaceholderTransform
} = useGenerationTasks(
  currentUser.id,
  `predictions-${currentUser.id}`
);

const { handleMagicFill } = useMagicFill(
  currentUser.id,
  addGeneratedImage
);
```

## Best Practices for Using Canvas Hooks

1. **Separation of Concerns**: Each hook should focus on a specific aspect of functionality.
2. **Memoization**: Use `useCallback` and `useMemo` to prevent unnecessary re-renders.
3. **Dependency Management**: Carefully manage dependencies in hooks to avoid infinite loops.
4. **Error Handling**: Include proper error handling in hooks that perform async operations.
5. **Type Safety**: Use TypeScript to ensure type safety in hook parameters and return values.
6. **Documentation**: Document the purpose, parameters, and return values of each hook.
7. **Testing**: Write unit tests for hooks to ensure they work as expected.

## Creating Custom Canvas Hooks

When creating new hooks for the canvas module, follow these guidelines:

1. **Naming**: Use the `use` prefix followed by a descriptive name (e.g., `useCanvasExport`).
2. **State Management**: Use the canvas store for shared state and local state for hook-specific state.
3. **Parameters**: Accept parameters that allow the hook to be customized.
4. **Return Values**: Return an object with named properties for better readability.
5. **Side Effects**: Use `useEffect` for side effects and cleanup.
6. **Performance**: Optimize for performance, especially for operations that run frequently.
7. **Reusability**: Design hooks to be reusable across different components.

Example of a custom hook:

```typescript
export const useCanvasHistory = () => {
  const store = useCanvasStore();
  
  const canUndo = useMemo(() => {
    return store.currentLineIndex > 0 || store.currentImageIndex > 0;
  }, [store.currentLineIndex, store.currentImageIndex]);
  
  const canRedo = useMemo(() => {
    return (
      store.currentLineIndex < store.lineHistory.length - 1 ||
      store.currentImageIndex < store.imageHistory.length - 1
    );
  }, [
    store.currentLineIndex,
    store.lineHistory.length,
    store.currentImageIndex,
    store.imageHistory.length
  ]);
  
  const undo = useCallback(() => {
    store.undo();
  }, [store]);
  
  const redo = useCallback(() => {
    store.redo();
  }, [store]);
  
  return {
    canUndo,
    canRedo,
    undo,
    redo
  };
};
```
