# Canvas Utilities

## Overview

The Canvas module includes a set of utility functions that provide common functionality for canvas operations. These utilities handle calculations, transformations, validations, and other helper operations that are used throughout the canvas module.

## Core Utilities

The main utilities are defined in `src/modules/canvas/utils/canvas-utils.ts` and provide functionality for:

- Aspect ratio calculations
- Resolution management
- Position calculations
- Data conversions
- Layout helpers

## Aspect Ratio Utilities

### gcd (Greatest Common Divisor)

Calculates the greatest common divisor of two numbers, used for simplifying aspect ratios:

```typescript
export const gcd = (a: number, b: number): number => (b === 0 ? a : gcd(b, a % b));
```

### computeAspectRatio

Computes the aspect ratio from width and height values:

```typescript
export function computeAspectRatio(width: number, height: number): string {
  const divisor = gcd(width, height);
  return `${width / divisor}:${height / divisor}`;
}
```

### Standard Aspect Ratios

Defines the standard aspect ratios supported by the application:

```typescript
export const STANDARD_ASPECT_RATIOS = [
  "1:1", "16:9", "9:16", "4:3", "3:4", "3:2", "2:3", 
  "16:10", "10:16", "3:1", "1:3"
];
```

### isValidAspectRatio

Validates if a given string is a standard aspect ratio:

```typescript
export function isValidAspectRatio(ratio: string): ratio is "1:1" | "16:9" | "9:16" | "4:3" | "3:4" | "3:2" | "2:3" | "16:10" | "10:16" | "3:1" | "1:3" {
  return STANDARD_ASPECT_RATIOS.includes(ratio);
}
```

### findClosestAspectRatio

Finds the closest standard aspect ratio to given dimensions:

```typescript
export function findClosestAspectRatio(width: number, height: number): string {
  const standardRatios = {
    "1:1": 1,
    "16:9": 16/9,
    "9:16": 9/16,
    "4:3": 4/3,
    "3:4": 3/4,
    "3:2": 3/2,
    "2:3": 2/3,
    "16:10": 16/10,
    "10:16": 10/16,
    "3:1": 3/1,
    "1:3": 1/3
  };
  
  const targetRatio = width / height;
  
  let closestRatio = "1:1";
  let smallestDiff = Number.MAX_VALUE;
  
  Object.entries(standardRatios).forEach(([ratio, value]) => {
    const diff = Math.abs(value - targetRatio);
    if (diff < smallestDiff) {
      smallestDiff = diff;
      closestRatio = ratio;
    }
  });
  
  return closestRatio;
}
```

## Resolution Utilities

### getValidResolutions

Returns the list of valid resolutions supported by the API:

```typescript
export function getValidResolutions(): string[] {
  return [
    "1024x1024", "1408x704", "704x1408", "1312x736", "736x1312", 
    "1280x800", "800x1280", "1120x896", "896x1120", "1248x832", 
    "832x1248", "1152x864", "864x1152", "1536x512", "512x1536",
    // ... more resolutions
  ];
}
```

### isValidResolution

Validates if a given string is a supported resolution:

```typescript
export function isValidResolution(resolution: string): boolean {
  return VALID_RESOLUTIONS.includes(resolution);
}
```

### formatResolution

Formats dimensions as a resolution string:

```typescript
export function formatResolution(width: number, height: number): string {
  return `${width}x${height}`;
}
```

### findClosestValidResolution

Finds the closest valid resolution to given dimensions:

```typescript
export const findClosestValidResolution = (width: number, height: number): string => {
  // Check if the requested resolution is already valid
  const requestedResolution = `${width}x${height}`;
  if (isValidResolution(requestedResolution)) {
    return requestedResolution;
  }

  // If aspect ratio is important, we can provide a weighting
  const targetAspectRatio = width / height;
  
  // Calculate scores for each valid resolution
  const resolutionsWithScores = VALID_RESOLUTIONS.map(resolution => {
    const [w, h] = resolution.split("x").map(Number);
    
    // Calculate distance based on area difference and aspect ratio difference
    const areaDiff = Math.abs((w * h) - (width * height));
    const aspectRatioDiff = Math.abs((w / h) - targetAspectRatio);
    
    // Combine these factors - we weight aspect ratio difference more heavily
    const score = (areaDiff / 1000000) + (aspectRatioDiff * 10);
    
    return { resolution, score };
  });
  
  // Sort by score (lower is better) and get the best match
  resolutionsWithScores.sort((a, b) => a.score - b.score);
  
  if (resolutionsWithScores.length > 0) {
    return resolutionsWithScores[0].resolution;
  }
  
  return "None";
};
```

## Image Generation Utilities

### prepareGenerationParameters

Calculates and prepares image generation parameters based on dimensions:

```typescript
export function prepareGenerationParameters(
  width: number,
  height: number
): {
  resolution: string;
  aspectRatio: "1:1" | "16:9" | "9:16" | "4:3" | "3:4" | "3:2" | "2:3" | "16:10" | "10:16" | "3:1" | "1:3" | "None";
} {
  const resolutionString = formatResolution(width, height);
  const aspectRatio = computeAspectRatio(width, height);
  
  // Resolution takes priority over aspect ratio in API
  if (isValidResolution(resolutionString)) {
    return {
      resolution: resolutionString,
      // When valid resolution is provided, aspect ratio is not used by API
      // but we still store it for consistency in UI
      aspectRatio: isValidAspectRatio(aspectRatio) 
        ? aspectRatio as any 
        : "None" as any // Use "None" instead of forcing 1:1 for custom aspect ratios
    };
  } else {
    // Find the closest standard aspect ratio if exact match not found
    const closest = findClosestAspectRatio(width, height);
    
    // Fall back to aspect ratio when resolution isn't valid
    return {
      resolution: "None",
      aspectRatio: closest as any // Use the closest match instead of forcing 1:1
    };
  }
}
```

## Canvas Position Utilities

### getRelativePointerPosition

Gets the relative pointer position on a Konva stage:

```typescript
export function getRelativePointerPosition(stage: Konva.Stage): { x: number; y: number } | null {
  const pointer = stage.getPointerPosition();
  if (pointer) {
    return {
      x: (pointer.x - stage.x()) / stage.scaleX(),
      y: (pointer.y - stage.y()) / stage.scaleY(),
    };
  }
  return null;
}
```

### snapToGrid

Snaps a position to a grid:

```typescript
export function snapToGrid(pos: { x: number; y: number }, gridSize = 1) {
  return {
    x: Math.round(pos.x / gridSize) * gridSize,
    y: Math.round(pos.y / gridSize) * gridSize,
  };
}
```

### findAvailableCanvasPosition

Finds an available position for placing a new item on the canvas:

```typescript
export function findAvailableCanvasPosition(
  width: number,
  height: number,
  stage: Konva.Stage | null,
  dimensions: CanvasSize,
  occupiedSpaces: CanvasRect[]
): CanvasPosition {
  if (!stage) {
    return { x: 100, y: 100 };
  }
  
  const scale = stage.scaleX();
  
  // Get the actual visible area of the canvas in virtual space
  const visibleAreaTopLeft = {
    x: (0 - stage.x()) / scale,
    y: (0 - stage.y()) / scale
  };
  
  const visibleAreaBottomRight = {
    x: (dimensions.width - stage.x()) / scale,
    y: (dimensions.height - stage.y()) / scale
  };
  
  // Adjust padding for better spacing
  const padding = 20;
  
  // If no existing elements, return a position near the center of the visible area
  if (occupiedSpaces.length === 0) {
    return {
      x: visibleAreaTopLeft.x + (visibleAreaBottomRight.x - visibleAreaTopLeft.x - width) / 2,
      y: visibleAreaTopLeft.y + (visibleAreaBottomRight.y - visibleAreaTopLeft.y - height) / 2
    };
  }
  
  // Helper function to check if rectangles overlap
  const doRectanglesOverlap = (rect1: CanvasRect, rect2: CanvasRect) => {
    return (
      rect1.x < rect2.x + rect2.width &&
      rect1.x + rect1.width > rect2.x &&
      rect1.y < rect2.y + rect2.height &&
      rect1.y + rect1.height > rect2.y
    );
  };
  
  // Define a grid of potential positions to check, constrained to the visible area
  const gridSpacing = 100;
  const gridPositions: CanvasPosition[] = [];
  
  // Create a grid of positions within the visible area
  for (let x = visibleAreaTopLeft.x + padding; 
       x < visibleAreaBottomRight.x - width - padding; 
       x += gridSpacing) {
    for (let y = visibleAreaTopLeft.y + padding; 
         y < visibleAreaBottomRight.y - height - padding; 
         y += gridSpacing) {
      gridPositions.push({ x, y });
    }
  }
  
  // Default position if we can't find a better one (top-left of visible area with padding)
  const defaultPosition = { 
    x: visibleAreaTopLeft.x + padding, 
    y: visibleAreaTopLeft.y + padding 
  };
  
  // If we have no grid positions, return default
  if (gridPositions.length === 0) return defaultPosition;
  
  // Calculate "congestion" at each grid position
  const positionScores = gridPositions.map(pos => {
    const rect = { 
      x: pos.x, 
      y: pos.y, 
      width, 
      height 
    };
    
    // Count overlaps with occupied spaces
    let overlaps = 0;
    for (const space of occupiedSpaces) {
      if (doRectanglesOverlap(rect, space)) {
        overlaps++;
      }
    }
    
    // Calculate minimum distance to visible area edges
    const distToRightEdge = visibleAreaBottomRight.x - (pos.x + width);
    const distToBottomEdge = visibleAreaBottomRight.y - (pos.y + height);
    const distToLeftEdge = pos.x - visibleAreaTopLeft.x;
    const distToTopEdge = pos.y - visibleAreaTopLeft.y;
    const minEdgeDist = Math.min(distToRightEdge, distToBottomEdge, distToLeftEdge, distToTopEdge);
    
    // Calculate score - penalize overlaps heavily, prefer being away from edges but not too far
    const score = (overlaps * -1000) + Math.min(minEdgeDist, 200);
    
    return { pos, score, overlaps };
  });
  
  // Sort positions by score (highest first)
  positionScores.sort((a, b) => b.score - a.score);
  
  // If we found a position with no overlaps, use it
  const bestPosition = positionScores.find(p => p.overlaps === 0);
  if (bestPosition) {
    return bestPosition.pos;
  }
  
  // If all positions overlap, use the first grid position
  return positionScores[0]?.pos || defaultPosition;
}
```

## Data Conversion Utilities

### dataURLtoBlob

Converts a data URL to a Blob:

```typescript
export function dataURLtoBlob(dataurl: string): Blob {
  const arr = dataurl.split(",");
  const mimeMatch = arr[0].match(/:(.*?);/);
  const mime = mimeMatch ? mimeMatch[1] : "";
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], { type: mime });
}
```

## Orientation Utilities

### getOrientation

Determines the orientation based on width and height:

```typescript
export function getOrientation(width: number, height: number): CanvasOrientation {
  return width >= height ? "landscape" : "portrait";
}
```

## Using Canvas Utilities

The canvas utilities are used throughout the canvas module to provide consistent functionality:

### In Components

```typescript
import { findAvailableCanvasPosition, getRelativePointerPosition } from "../utils/canvas-utils";

// In a component
const handleAddImage = () => {
  if (!stageRef.current) return;
  
  const position = findAvailableCanvasPosition(
    newImage.width,
    newImage.height,
    stageRef.current,
    dimensions,
    store.generatedImages.map(img => ({
      x: img.position.x,
      y: img.position.y,
      width: img.width,
      height: img.height
    }))
  );
  
  addGeneratedImage({
    ...newImage,
    position
  });
};

// In an event handler
const handleMouseDown = (e: Konva.KonvaEventObject<MouseEvent>) => {
  const stage = e.target.getStage();
  if (!stage) return;
  
  const pos = getRelativePointerPosition(stage);
  if (!pos) return;
  
  // Use the position for drawing or selection
};
```

### In Hooks

```typescript
import { prepareGenerationParameters, dataURLtoBlob } from "../utils/canvas-utils";

// In a hook
const handleExport = async () => {
  if (!selection || !stageRef.current) return null;
  
  // Capture the selection as a data URL
  const dataUrl = stageRef.current.toDataURL({
    x: selection.x,
    y: selection.y,
    width: selection.width,
    height: selection.height,
    pixelRatio: 2
  });
  
  // Convert to blob for upload
  const blob = dataURLtoBlob(dataUrl);
  
  // Upload the blob
  // ...
};

// In another hook
const handleUpdateDimensions = (width: number, height: number) => {
  // Get the appropriate resolution and aspect ratio
  const { resolution, aspectRatio } = prepareGenerationParameters(width, height);
  
  // Update the store
  store.setResolution(resolution);
  store.setAspectRatio(aspectRatio);
};
```

### In Store Actions

```typescript
import { prepareGenerationParameters, findClosestValidResolution } from "../utils/canvas-utils";

// In the store
setSelectionArea: (area: CanvasRect | null) => set((state) => {
  if (!area) {
    return { selectionArea: null };
  }
  
  // Use prepareGenerationParameters to derive resolution and aspect ratio
  const { resolution, aspectRatio } = prepareGenerationParameters(area.width, area.height);
  
  // Update related state based on selection area
  const updates: Partial<CanvasStore> = { 
    selectionArea: area
  };
  
  // Only update resolution and aspect ratio if they're significant changes
  if (resolution !== state.resolution) {
    updates.resolution = resolution;
  }
  
  if (aspectRatio !== "None" && aspectRatio !== state.aspectRatio) {
    updates.aspectRatio = aspectRatio;
    persistAspectRatio(aspectRatio);
  } else if (state.aspectRatio === "None") {
    updates.aspectRatio = DEFAULT_ASPECT_RATIO;
    persistAspectRatio(DEFAULT_ASPECT_RATIO);
  }
  
  return updates;
}),
```

## Best Practices for Canvas Utilities

1. **Pure Functions**: Keep utility functions pure (no side effects) for easier testing and reasoning.
2. **Single Responsibility**: Each utility function should have a single responsibility.
3. **Type Safety**: Use TypeScript to ensure type safety in parameters and return values.
4. **Documentation**: Document the purpose, parameters, and return values of each utility function.
5. **Performance**: Optimize performance-critical utilities, especially those used in frequent operations.
6. **Caching**: Cache expensive calculations when appropriate (e.g., valid resolutions).
7. **Error Handling**: Include proper error handling in utilities that could fail.
8. **Testability**: Write utilities that are easy to test with unit tests.

## Creating Custom Canvas Utilities

When creating new utilities for the canvas module, follow these guidelines:

1. **Naming**: Use descriptive names that indicate the purpose of the utility.
2. **Parameters**: Accept specific parameters rather than complex objects when possible.
3. **Return Values**: Return specific values rather than complex objects when possible.
4. **Validation**: Validate input parameters to prevent errors.
5. **Default Values**: Provide sensible default values for optional parameters.
6. **Reusability**: Design utilities to be reusable across different parts of the application.
7. **Consistency**: Maintain consistency with existing utilities in naming and behavior.

Example of a custom utility:

```typescript
/**
 * Calculates the scale factor needed to fit content within a container
 * while maintaining aspect ratio.
 * 
 * @param contentWidth The width of the content to fit
 * @param contentHeight The height of the content to fit
 * @param containerWidth The width of the container
 * @param containerHeight The height of the container
 * @param padding Optional padding to apply (default: 0)
 * @returns The scale factor to apply to the content
 */
export function calculateFitScale(
  contentWidth: number,
  contentHeight: number,
  containerWidth: number,
  containerHeight: number,
  padding: number = 0
): number {
  // Adjust container dimensions for padding
  const adjustedContainerWidth = containerWidth - (padding * 2);
  const adjustedContainerHeight = containerHeight - (padding * 2);
  
  // Calculate scale factors for width and height
  const scaleX = adjustedContainerWidth / contentWidth;
  const scaleY = adjustedContainerHeight / contentHeight;
  
  // Return the smaller scale factor to ensure content fits in both dimensions
  return Math.min(scaleX, scaleY);
}
```
