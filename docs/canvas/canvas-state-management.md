# Canvas State Management

## Overview

The Canvas module uses Zustand for state management, providing a centralized store for all canvas-related state. This approach offers several advantages:

- **Simplified State Management**: Zustand provides a simple API for managing complex state
- **Performance Optimization**: Only components that use specific parts of the state are re-rendered
- **Immutable Updates**: State is updated immutably, making it easier to track changes
- **Middleware Support**: Zustand supports middleware for additional functionality like persistence
- **TypeScript Integration**: Strong typing for state and actions

## Canvas Store Structure

The canvas store is defined in `src/modules/canvas/store/canvas-store.ts` and consists of:

1. **State Properties**: The data that represents the current state of the canvas
2. **Actions**: Methods that update the state
3. **Derived State**: Computed values based on the state
4. **Utility Functions**: Helper functions for state management

### State Properties

```typescript
interface CanvasStore extends CanvasState {
  // Base state from CanvasState
  mode: CanvasMode;
  maskTool: MaskToolMode;
  brushSize: number;
  lines: CanvasLine[];
  dimensions: CanvasSize;
  selectionArea: CanvasRect | null;
  magicFillAreaSelection: boolean;
  zoom: number;
  userId: string;
  prompt: string;
  negativePrompt: string;
  generatedImages: GeneratedImage[];
  transformConfig: TransformConfig;
  inputImage: HTMLImageElement | null;
  inputImagePosition: CanvasPosition;
  inputImageDimensions: { width: number; height: number } | null;
  isGenerating: boolean;
  isUploadingImage: boolean;

  // Additional state
  imageHistory: GeneratedImage[][];
  currentImageIndex: number;
  viewportOffset: CanvasPosition;
  lineHistory: CanvasLine[][];
  currentLineIndex: number;
  maskUrl: string | null;
  
  // API related states
  resolution: string;
  styleType: "None" | "Auto" | "General" | "Realistic" | "Design" | "Render 3D" | "Anime";
  aspectRatio: AspectRatioType;
  magicPromptOption: "Auto" | "On" | "Off";
  seed?: number;
  
  // Generation tasks for better coordination
  generationTasks: GenerationTask[];
  transitioningImages: Set<string>;
  
  // Methods
  // ...
}
```

### Actions

```typescript
interface CanvasStore extends CanvasState {
  // ... state properties
  
  // Basic setters
  setMode: (mode: CanvasMode) => void;
  setMaskTool: (tool: MaskToolMode) => void;
  setBrushSize: (size: number) => void;
  setPrompt: (prompt: string) => void;
  setNegativePrompt: (prompt: string) => void;
  setUserId: (id: string) => void;
  setZoom: (zoom: number) => void;
  
  // Line operations
  startNewLine: (line: CanvasLine) => void;
  updateActiveLine: (line: CanvasLine) => void;
  clearLines: () => void;
  
  // Canvas operations
  setDimensions: (dimensions: CanvasSize) => void;
  setSelectionArea: (area: CanvasRect | null) => void;
  setMagicFillAreaSelection: (value: boolean) => void;
  setGeneratedImages: (images: GeneratedImage[]) => void;
  setTransformConfig: (config: TransformConfig) => void;
  setInputImage: (image: HTMLImageElement | null) => void;
  setInputImagePosition: (position: CanvasPosition) => void;
  setInputImageDimensions: (dimensions: { width: number; height: number }) => void;
  setIsGenerating: (value: boolean) => void;
  setIsUploadingImage: (value: boolean) => void;
  
  // API related methods
  setResolution: (resolution: string) => void;
  setStyleType: (styleType: "None" | "Auto" | "General" | "Realistic" | "Design" | "Render 3D" | "Anime") => void;
  setAspectRatio: (ratio: AspectRatioType) => void;
  setMagicPromptOption: (option: "Auto" | "On" | "Off") => void;
  setSeed: (seed?: number) => void;
  
  // Complex operations
  invertMask: () => void;
  undo: () => void;
  redo: () => void;
  setImagePosition: (index: number, position: CanvasPosition) => void;
  updateImageTransform: (index: number, updates: Partial<GeneratedImage>) => void;
  addGeneratedImage: (image: GeneratedImage) => void;
  
  // Task management methods
  addGenerationTask: (task: GenerationTask) => void;
  updateGenerationTask: (id: string, updates: Partial<GenerationTask>) => void;
  removeGenerationTask: (id: string) => void;
  
  // Dimension updates
  updateDimensions: (width: number, height: number, updateSelectionArea?: boolean) => void;
}
```

## Store Implementation

The canvas store is implemented using Zustand's `create` function:

```typescript
const useCanvasStore = create<CanvasStore>((set, get) => ({
  // Initial state
  mode: CanvasMode.Move,
  maskTool: "brush",
  brushSize: 20,
  lines: [],
  dimensions: { width: 0, height: 0 },
  // ... more initial state
  
  // Methods
  setMode: (mode) => {
    const currentState = get();
    const currentAspectRatio = currentState.aspectRatio;
    
    set({ 
      mode,
      aspectRatio: currentAspectRatio === "None" ? DEFAULT_ASPECT_RATIO : currentAspectRatio 
    });
    
    persistAspectRatio(currentAspectRatio === "None" ? DEFAULT_ASPECT_RATIO : currentAspectRatio);
  },
  
  // ... more methods
}));
```

## State Updates

State updates in Zustand are performed by calling the setter methods:

```typescript
// Simple update
store.setMode(CanvasMode.MagicFill);

// Complex update with multiple changes
store.setSelectionArea({
  x: 100,
  y: 100,
  width: 400,
  height: 300
});
```

For more complex updates that depend on the current state, the methods use the `get` function to access the current state:

```typescript
startNewLine: (line: CanvasLine) => set((state) => {
  const newLines = [...state.lines, line];
  const newLineHistory = [
    ...state.lineHistory.slice(0, state.currentLineIndex + 1),
    newLines
  ];
  
  return {
    lines: newLines,
    lineHistory: newLineHistory,
    currentLineIndex: newLineHistory.length - 1
  };
}),
```

## History Management

The canvas store includes history management for undo/redo functionality:

```typescript
undo: () => {
  const state = get();
  
  // Handle lines first (brush strokes)
  if (state.currentLineIndex > 0) {
    set({
      currentLineIndex: state.currentLineIndex - 1,
      lines: state.lineHistory[state.currentLineIndex - 1],
    });
  }
  
  // Handle images
  if (state.currentImageIndex > 0) {
    set({
      currentImageIndex: state.currentImageIndex - 1,
      generatedImages: state.imageHistory[state.currentImageIndex - 1] || [],
    });
  }
},

redo: () => {
  const state = get();
  
  // Handle lines first (brush strokes)
  if (state.currentLineIndex < state.lineHistory.length - 1) {
    set({
      currentLineIndex: state.currentLineIndex + 1,
      lines: state.lineHistory[state.currentLineIndex + 1],
    });
  }
  
  // Handle images
  if (state.currentImageIndex < state.imageHistory.length - 1) {
    set({
      currentImageIndex: state.currentImageIndex + 1,
      generatedImages: state.imageHistory[state.currentImageIndex + 1] || [],
    });
  }
},
```

## Task Management

The canvas store includes task management for tracking image generation tasks:

```typescript
addGenerationTask: (task) => set((state) => ({
  generationTasks: [...state.generationTasks, task]
})),

updateGenerationTask: (id, updates) => set((state) => {
  // Find the task first
  const existingTask = state.generationTasks.find(task => task.id === id);
  if (!existingTask) return state;
  
  // Handle imageId updates
  if (updates.imageId) {
    const newTransitioning = new Set(state.transitioningImages);
    newTransitioning.add(updates.imageId);
    
    return {
      generationTasks: state.generationTasks.map(task => 
        task.id === id ? { ...task, ...updates } : task
      ),
      transitioningImages: newTransitioning
    };
  }
  
  return {
    generationTasks: state.generationTasks.map(task => 
      task.id === id ? { ...task, ...updates } : task
    )
  };
}),

removeGenerationTask: (id) => set((state) => ({
  generationTasks: state.generationTasks.filter(task => task.id !== id)
})),
```

## Persistence

The canvas store includes persistence for certain settings:

```typescript
// Helper function to persist aspect ratio to localStorage
const persistAspectRatio = (ratio: string) => {
  if (ratio === "None") return;
  
  try {
    localStorage.setItem('lastAspectRatio', ratio);
  } catch (e) {
    console.warn('Failed to save aspect ratio to localStorage:', e);
  }
};
```

## Using the Store in Components

Components can access the store using the `useCanvasStore` hook:

```typescript
const ImageCanvas: React.FC = () => {
  const store = useCanvasStore();
  
  // Access state
  const mode = store.mode;
  const brushSize = store.brushSize;
  
  // Update state
  const handleModeChange = (newMode: CanvasMode) => {
    store.setMode(newMode);
  };
  
  // Render based on state
  return (
    <div>
      <MainToolbar
        mode={mode}
        onModeChange={handleModeChange}
      />
      <Stage>
        {/* ... */}
      </Stage>
    </div>
  );
};
```

## Selectors for Performance

For better performance, components can use selectors to only subscribe to specific parts of the state:

```typescript
const BrushSizeControl: React.FC = () => {
  const brushSize = useCanvasStore(state => state.brushSize);
  const setBrushSize = useCanvasStore(state => state.setBrushSize);
  
  return (
    <Slider
      value={brushSize}
      onChange={(value) => setBrushSize(value)}
      min={1}
      max={100}
    />
  );
};
```

## Derived State

Derived state can be computed from the store state:

```typescript
const CanvasControls: React.FC = () => {
  const currentLineIndex = useCanvasStore(state => state.currentLineIndex);
  const lineHistory = useCanvasStore(state => state.lineHistory);
  const currentImageIndex = useCanvasStore(state => state.currentImageIndex);
  const imageHistory = useCanvasStore(state => state.imageHistory);
  
  // Derived state
  const canUndo = currentLineIndex > 0 || currentImageIndex > 0;
  const canRedo = 
    currentLineIndex < lineHistory.length - 1 ||
    currentImageIndex < imageHistory.length - 1;
  
  return (
    <div>
      <Button disabled={!canUndo} onClick={store.undo}>Undo</Button>
      <Button disabled={!canRedo} onClick={store.redo}>Redo</Button>
    </div>
  );
};
```

## State Debugging

For debugging purposes, the store includes logging in development mode:

```typescript
// Add debug logging for history in development mode
useEffect(() => {
  if (process.env.NODE_ENV === 'development') {
    console.log('Image history setup:', {
      imageHistory: store.imageHistory,
      currentImageIndex: store.currentImageIndex,
      lineHistory: store.lineHistory,
      currentLineIndex: store.currentLineIndex
    });
  }
}, [store.imageHistory.length, store.currentImageIndex, store.lineHistory.length, store.currentLineIndex]);
```

## Best Practices for Canvas State Management

1. **Single Source of Truth**: Keep all canvas-related state in the canvas store.
2. **Immutable Updates**: Always update state immutably to ensure proper tracking of changes.
3. **Atomic Updates**: Keep state updates as small and focused as possible.
4. **Selectors for Performance**: Use selectors to only subscribe to the parts of the state that are needed.
5. **Derived State**: Compute derived state from the store state rather than duplicating state.
6. **Type Safety**: Use TypeScript to ensure type safety in state and actions.
7. **History Management**: Implement proper history management for undo/redo functionality.
8. **Error Handling**: Include error handling in state updates that could fail.
9. **Persistence**: Persist important settings to localStorage for a better user experience.
10. **Debugging**: Include debugging information in development mode for easier troubleshooting.
