import type { Config } from "tailwindcss";

const config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: {
        DEFAULT: "1rem",
        sm: "2rem",
        lg: "4rem",
        xl: "5rem",
        "2xl": "6rem",
      },
      screens: {
        xs: "400px",
        sm: "640px",
        md: "768px",
        lg: "1024px",
        xl: "1280px",
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        "primary-light": "hsl(var(--primary-light))",
        "primary-dark": "hsl(var(--primary-dark))",
        "secondary-light": "hsl(var(--secondary-light))",
        "secondary-dark": "hsl(var(--secondary-dark))",
        filepond: {
          light: {
            background: "hsl(var(--background))",
            altBackground: "hsl(220, 14%, 96%)", // Slightly darker than the default light background
            text: "hsl(var(--foreground))",
            buttonBg: "hsl(var(--secondary))",
            buttonText: "hsl(var(--secondary-foreground))",
            border: "hsl(var(--border))",
          },
          dark: {
            background: "hsl(var(--background))",
            altBackground: "hsl(224, 71%, 8%)", // Slightly lighter than the default dark background
            text: "hsl(var(--foreground))",
            buttonBg: "hsl(var(--secondary))",
            buttonText: "hsl(var(--secondary-foreground))",
            border: "hsl(var(--border))",
          },
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0", opacity: "0" },
          to: { height: "var(--radix-accordion-content-height)", opacity: "1" },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
            opacity: "1",
          },
          to: { height: "0", opacity: "0" },
        },
        spotlight: {
          "0%": {
            opacity: "0",
            transform: "translate(-72%, -62%) scale(0.5)",
          },
          "100%": {
            opacity: "1",
            transform: "translate(-50%,-40%) scale(1)",
          },
        },
        "gradient-x": {
          "0%, 100%": {
            backgroundSize: "200% 200%",
            backgroundPosition: "left center",
          },
          "50%": {
            backgroundSize: "200% 200%",
            backgroundPosition: "right center",
          },
        },
        pulse: {
          "0%, 100%": {
            opacity: "0.4",
          },
          "50%": {
            opacity: "0.8",
          },
        },
        "fade-in": {
          "0%": {
            opacity: "0",
            transform: "translate3d(0, 0, 0)",
          },
          "100%": {
            opacity: "1",
            transform: "translate3d(0, 0, 0)",
          },
        },
        "tree-pulse": {
          "0%, 100%": { transform: "scale(1)" },
          "50%": { transform: "scale(1.1)" },
        },
        "santa-bounce": {
          "0%, 100%": { transform: "translateY(0)" },
          "50%": { transform: "translateY(-4px)" },
        },
        "optimized-snow-1": {
          "0%": { transform: "translateY(-10%) translateX(0)" },
          "50%": { transform: "translateY(50%) translateX(5%)" },
          "100%": { transform: "translateY(110%) translateX(0)", opacity: "0" },
        },
        "optimized-snow-2": {
          "0%": { transform: "translateY(-10%) translateX(0)" },
          "50%": { transform: "translateY(50%) translateX(-5%)" },
          "100%": { transform: "translateY(110%) translateX(0)", opacity: "0" },
        },
        "optimized-snow-3": {
          "0%": { transform: "translateY(-10%) translateX(0)" },
          "50%": { transform: "translateY(50%) translateX(3%)" },
          "100%": { transform: "translateY(110%) translateX(0)", opacity: "0" },
        },
        "fade-in-up": {
          "0%": {
            opacity: "0",
            transform: "translate3d(0, 20px, 0)",
          },
          "100%": {
            opacity: "1",
            transform: "translate3d(0, 0, 0)",
          },
        },
        "fade-in-left": {
          "0%": {
            opacity: "0",
            transform: "translateX(-20px)",
          },
          "100%": {
            opacity: "1",
            transform: "translateX(0)",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s cubic-bezier(0.4, 0, 0.2, 1)",
        "accordion-up": "accordion-up 0.2s cubic-bezier(0.4, 0, 0.2, 1)",
        spotlight: "spotlight 2s ease .75s 1 forwards",
        "gradient-x": "gradient-x 15s ease-in-out infinite",
        pulse: "pulse 6s ease-in-out infinite",
        "fade-in": "fade-in 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards",
        "tree-pulse": "tree-pulse 2s ease-in-out infinite",
        "santa-bounce": "santa-bounce 1.5s ease-in-out infinite",
        "optimized-snow-1": "optimized-snow-1 3s ease-in-out infinite",
        "optimized-snow-2": "optimized-snow-2 3.5s ease-in-out infinite",
        "optimized-snow-3": "optimized-snow-3 4s ease-in-out infinite",
        "fade-in-up":
          "fade-in-up 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards var(--delay, 0s)",
        "fade-in-left": "fade-in-left 0.5s ease-out forwards",
        "video-fade-in": "video-fade-in 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards",
      },
      boxShadow: {
        soft: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
        glow: "0 0 15px rgba(var(--primary-rgb), 0.5)",
      },
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "card-gradient":
          "linear-gradient(180deg, hsl(var(--background)) 0%, hsl(var(--background) / 0.95) 100%)",
        "button-gradient":
          "linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.9) 50%, hsl(var(--secondary)) 100%)",
        "subtle-gradient":
          "radial-gradient(circle at top right, hsl(var(--primary) / 0.15), transparent 70%)",
        "feature-gradient":
          "linear-gradient(to bottom right, hsl(var(--background) / 0.98), hsl(var(--background) / 0.95))",
        "header-gradient":
          "linear-gradient(to right, hsl(var(--primary)), hsl(var(--primary) / 0.9), hsl(var(--secondary)))",
        "spotlight-gradient":
          "radial-gradient(circle at center, var(--tw-gradient-stops))",
        "glow-gradient":
          "linear-gradient(to right, transparent, hsl(var(--primary) / 0.1), transparent)",
      },
      typography: {
        DEFAULT: {
          css: {
            maxWidth: "none",
            color: "inherit",
            a: {
              color: "inherit",
              textDecoration: "underline",
              fontWeight: "500",
            },
            strong: {
              color: "inherit",
              fontWeight: "600",
            },
            h1: {
              color: "inherit",
            },
            h2: {
              color: "inherit",
            },
            h3: {
              color: "inherit",
            },
            h4: {
              color: "inherit",
            },
            code: {
              color: "inherit",
            },
            pre: {
              color: "inherit",
              backgroundColor: "var(--tw-prose-pre-bg)",
            },
          },
        },
      },
    },
  },
  plugins: [require("tailwindcss-animate"), require("@tailwindcss/typography")],
  future: {
    hoverOnlyWhenSupported: true,
  },
} satisfies Config;

export default config;
