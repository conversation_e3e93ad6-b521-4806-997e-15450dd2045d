generator client {
  provider   = "prisma-client-js"
  engineType = "binary"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model User {
  id                         String              @id @default(cuid())
  name                       String?
  email                      String?             @unique
  emailVerified              DateTime?
  image                      String?
  role                       Role                @default(USER)
  password                   String?
  createdAt                  DateTime?           @default(now())
  imageCredits               Int                 @default(0)
  lastCreditReset            DateTime?
  editedImages               EditedImage[]
  accounts                   Account[]
  backgroundRemovals         BackgroundRemoval[]
  designSuggestions          DesignSuggestion[]
  editImages                 EditImage[]
  exteriorDesigns            ExteriorDesign[]
  interiorDesigns            InteriorDesign[]
  sessions                   Session[]
  sketchToReals              SketchToReal[]
  styleTransfers             StyleTransfer[]
  subscription               Subscription?
  transactions               Transaction[]
  UpscaleImage               UpscaleImage[]
  virtualStagings            VirtualStaging[]
  favoriteBackgroundRemovals BackgroundRemoval[] @relation("FavoriteBackgroundRemovals")
  favoriteEditImages         EditImage[]         @relation("FavoriteEditImages")
  favoriteExteriorDesigns    ExteriorDesign[]    @relation("FavoriteExteriorDesigns")
  favoriteInteriorDesigns    InteriorDesign[]    @relation("FavoriteInteriorDesigns")
  favoriteStyleTransfers     StyleTransfer[]     @relation("FavoriteStyleTransfers")
  favoriteUpscaleImages      UpscaleImage[]      @relation("FavoriteUpscaleImages")
  favoriteVirtualStagings    VirtualStaging[]    @relation("FavoriteVirtualStagings")
  referralCode               ReferralCode?
  referralsGiven             Referral[]          @relation("ReferrerUser")
  referredBy                 Referral?           @relation("ReferredUser")
  totalReferrals             Int                 @default(0)
  referralCredits            Int                 @default(0)
  industry                   String?
  source                     String?
  Feedback                   Feedback[]
  imageRatings               ImageRating[]

  @@map("users")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model Prompt {
  id        String     @id @default(cuid())
  style     String
  room      String
  prompt    String
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
  type      PromptType

  @@unique([style, room, type])
  @@map("prompts")
}

model InteriorDesign {
  id           String           @id @default(cuid())
  replicate_id String           @unique
  userId       String
  status       PredictionStatus @default(PROCESSING)
  inputImage   String
  outputImages String[]
  displayUrl   String?          @map("display_url")
  downloadUrl  String?          @map("download_url")
  error        String?
  prompt       String?
  style        String?
  room         String?
  createdAt    DateTime         @default(now())
  completedAt  DateTime?
  isPublic     Boolean          @default(true)
  user         User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  favoritedBy  User[]           @relation("FavoriteInteriorDesigns")
  rating       Int?
  ratedAt      DateTime?
  averageRating Float?
  totalRatings  Int     @default(0)
  ratings      ImageRating[]

  @@map("interior_designs")
  @@index([userId])
  @@index([rating])
}

model ExteriorDesign {
  id           String           @id @default(cuid())
  replicate_id String           @unique
  userId       String
  status       PredictionStatus @default(PROCESSING)
  inputImage   String
  outputImages String[]
  displayUrl   String?          @map("display_url")
  downloadUrl  String?          @map("download_url")
  error        String?
  prompt       String?
  style        String?
  building     String?
  createdAt    DateTime         @default(now())
  completedAt  DateTime?
  isPublic     Boolean          @default(true)
  user         User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  favoritedBy  User[]           @relation("FavoriteExteriorDesigns")
  rating       Int?
  ratedAt      DateTime?
  averageRating Float?
  totalRatings  Int     @default(0)
  ratings      ImageRating[]

  @@map("exterior_designs")
  @@index([userId])
  @@index([rating])
}

model BackgroundRemoval {
  id           String           @id @default(cuid())
  replicate_id String           @unique
  userId       String
  status       PredictionStatus @default(PROCESSING)
  inputImage   String
  outputImage  String?
  displayUrl   String?          @map("display_url")
  downloadUrl  String?          @map("download_url")
  error        String?
  createdAt    DateTime         @default(now())
  completedAt  DateTime?
  isPublic     Boolean          @default(true)
  user         User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  favoritedBy  User[]           @relation("FavoriteBackgroundRemovals")
  rating       Int?
  ratedAt      DateTime?
  averageRating Float?
  totalRatings  Int     @default(0)
  ratings      ImageRating[]

  @@map("background_removals")
  @@index([userId])
  @@index([rating])
}

model StyleTransfer {
  id             String           @id @default(cuid())
  replicate_id   String           @unique
  userId         String
  status         PredictionStatus @default(PROCESSING)
  inputImage     String
  styleImage     String
  structureImage String?
  model          String?
  outputImage    String?
  displayUrl     String?          @map("display_url")
  downloadUrl    String?          @map("download_url")
  error          String?
  prompt         String?
  negativePrompt String?
  createdAt      DateTime         @default(now())
  completedAt    DateTime?
  isPublic       Boolean          @default(true)
  user           User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  favoritedBy    User[]           @relation("FavoriteStyleTransfers")
  rating         Int?
  ratedAt        DateTime?
  averageRating  Float?
  totalRatings   Int              @default(0)
  ratings        ImageRating[]

  @@map("style_transfers")
  @@index([userId])
  @@index([rating])
}

model SketchToReal {
  id           String           @id @default(cuid())
  replicate_id String           @unique
  userId       String
  status       PredictionStatus @default(PROCESSING)
  inputImage   String
  outputImage  String?
  createdAt    DateTime         @default(now())
  completedAt  DateTime?
  user         User             @relation(fields: [userId], references: [id])

  @@map("sketch_to_reals")
}

model UpscaleImage {
  id            String           @id @default(cuid())
  replicate_id  String           @unique
  userId        String
  status        PredictionStatus @default(PROCESSING)
  inputImage    String
  outputImage   String?
  displayUrl    String?          @map("display_url")
  downloadUrl   String?          @map("download_url")
  error         String?
  upscaleAmount Float
  creativity    Float
  createdAt     DateTime         @default(now())
  completedAt   DateTime?
  isPublic      Boolean          @default(true)
  user          User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  favoritedBy   User[]           @relation("FavoriteUpscaleImages")
  rating        Int?
  ratedAt       DateTime?
  averageRating Float?
  totalRatings  Int     @default(0)
  ratings       ImageRating[]

  @@map("upscale_images")
  @@index([userId])
  @@index([rating])
}

model Transaction {
  id          String   @id @default(cuid())
  userId      String
  amount      Float
  credits     Int
  orderId     String
  packageName String?
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now()) @updatedAt
  user        User     @relation(fields: [userId], references: [id])

  @@index([userId])
  @@map("transactions")
}

model DesignSuggestion {
  id              String   @id @default(cuid())
  userId          String?
  room            String
  style           String
  dimensions      String
  suggestion      String
  inputImage      String?
  outputImage     String?
  createdAt       DateTime @default(now())
  slug            String   @unique
  anonymousId     String?
  metaDescription String?
  user            User?    @relation(fields: [userId], references: [id])

  @@map("design_suggestions")
}

model VirtualStaging {
  id                String           @id @default(cuid())
  replicate_id      String           @unique
  userId            String
  status            PredictionStatus @default(PROCESSING)
  inputImage        String
  outputImage       String?
  displayUrl        String?          @map("display_url")
  downloadUrl       String?          @map("download_url")
  error             String?
  prompt            String
  style             String?
  room              String?
  excludedElements  String?
  createdAt         DateTime         @default(now())
  completedAt       DateTime?
  isPublic          Boolean          @default(true)
  cloudflareImageId String?
  user              User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  favoritedBy       User[]           @relation("FavoriteVirtualStagings")
  rating            Int?
  ratedAt           DateTime?
  averageRating     Float?
  totalRatings      Int     @default(0)
  ratings           ImageRating[]
  mask              String?

  @@map("virtual_stagings")
  @@index([userId])
  @@index([rating])
}

model EditImage {
  id           String           @id @default(cuid())
  replicate_id String           @unique
  userId       String
  status       PredictionStatus @default(PROCESSING)
  inputImage   String
  outputImage  String?
  displayUrl   String?          @map("display_url")
  downloadUrl  String?          @map("download_url")
  error        String?
  maskImage    String?
  prompt       String
  clientTaskId String?
  createdAt    DateTime         @default(now())
  completedAt  DateTime?
  isPublic     Boolean          @default(true)
  user         User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  favoritedBy  User[]           @relation("FavoriteEditImages")
  rating       Int?
  ratedAt      DateTime?
  averageRating Float?
  totalRatings  Int     @default(0)
  ratings      ImageRating[]

  @@map("edit_images")
  @@index([userId])
  @@index([rating])
}

model EditedImage {
  id          String           @id @default(cuid())
  replicateId String           @unique
  userId      String
  status      PredictionStatus
  inputImage  String
  maskImage   String?
  outputImage String?
  prompt      String
  createdAt   DateTime         @default(now())
  completedAt DateTime?
  user        User             @relation(fields: [userId], references: [id])

  @@index([userId])
}

model Subscription {
  id                     String             @id @default(cuid())
  userId                 String             @unique
  packageName            String
  isActive               Boolean            @default(true)
  startDate              DateTime
  endDate                DateTime
  paymentStatus          SubscriptionStatus @default(ACTIVE)
  allowPrivateImages     Boolean            @default(false)
  createdAt              DateTime           @default(now())
  updatedAt              DateTime           @updatedAt
  lastBillingDate        DateTime?
  nextBillingDate        DateTime?
  canceledAt             DateTime?
  effectiveCancelDate    DateTime?
  trialEndsAt            DateTime?
  pausedAt               DateTime?
  resumesAt              DateTime?
  subscriptionId         String?
  variantId              String?
  customerPortalUrl      String?
  updatePaymentMethodUrl String?
  cancelUrl              String?
  monthlyCredits         Int                @default(0)
  user                   User               @relation(fields: [userId], references: [id])

  @@map("subscriptions")
}

enum PromptType {
  INTERIOR
  EXTERIOR
}

enum PredictionStatus {
  STARTING
  PROCESSING
  PROCESSING_OUTPUT
  SUCCEEDED
  FAILED
  CANCELED
}

enum Role {
  USER
  ADMIN
}

enum SubscriptionStatus {
  ACTIVE
  FAILED
  CANCELED
  EXPIRED
  PAUSED
  PAST_DUE
}

model ReferralCode {
  id        String     @id @default(cuid())
  code      String     @unique
  userId    String     @unique
  user      User       @relation(fields: [userId], references: [id])
  referrals Referral[]
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt

  @@map("referral_codes")
}

model Referral {
  id             String         @id @default(cuid())
  referralCodeId String
  referralCode   ReferralCode   @relation(fields: [referralCodeId], references: [id])
  referrerId     String
  referrer       User           @relation("ReferrerUser", fields: [referrerId], references: [id])
  referredId     String         @unique
  referred       User           @relation("ReferredUser", fields: [referredId], references: [id])
  status         ReferralStatus @default(PENDING)
  creditedAt     DateTime?
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt

  @@map("referrals")
}

enum ReferralStatus {
  PENDING
  CREDITED
  EXPIRED
}

model Feedback {
  id                 String   @id @default(cuid())
  userId             String?
  type               String
  reason             String
  additionalFeedback String?
  createdAt          DateTime @default(now())
  user               User?    @relation(fields: [userId], references: [id])

  @@map("feedbacks")
}

model ImageRating {
  id                    String            @id @default(cuid())
  userId                String
  imageType             ImageType
  rating                Int
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt
  user                  User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  // Separate ID fields for each type
  virtualStagingId      String?
  interiorDesignId      String?
  exteriorDesignId      String?
  editImageId           String?
  upscaleImageId        String?
  backgroundRemovalId   String?
  styleTransferId       String?
  
  // Relations with their respective ID fields
  virtualStaging        VirtualStaging?   @relation(fields: [virtualStagingId], references: [id], onDelete: Cascade)
  interiorDesign        InteriorDesign?   @relation(fields: [interiorDesignId], references: [id], onDelete: Cascade)
  exteriorDesign        ExteriorDesign?   @relation(fields: [exteriorDesignId], references: [id], onDelete: Cascade)
  editImage             EditImage?        @relation(fields: [editImageId], references: [id], onDelete: Cascade)
  upscaleImage          UpscaleImage?     @relation(fields: [upscaleImageId], references: [id], onDelete: Cascade)
  backgroundRemoval     BackgroundRemoval? @relation(fields: [backgroundRemovalId], references: [id], onDelete: Cascade)
  styleTransfer         StyleTransfer?    @relation(fields: [styleTransferId], references: [id], onDelete: Cascade)

  @@unique([userId, imageType, virtualStagingId, interiorDesignId, exteriorDesignId, editImageId, upscaleImageId, backgroundRemovalId, styleTransferId])
  @@map("image_ratings")
  @@index([userId])
  @@index([rating])
  @@index([virtualStagingId])
  @@index([interiorDesignId])
  @@index([exteriorDesignId])
  @@index([editImageId])
  @@index([upscaleImageId])
  @@index([backgroundRemovalId])
  @@index([styleTransferId])
}

enum ImageType {
  virtual_stagings
  interior_designs
  exterior_designs
  edit_images
  upscale_images
  background_removals
  style_transfers
}
