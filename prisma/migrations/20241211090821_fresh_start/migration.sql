-- Create<PERSON>num
CREATE TYPE "PromptType" AS ENUM ('INTERIOR', 'EXTERIOR');

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "PredictionStatus" AS ENUM ('STARTING', 'PROCESSING', 'SUCCEEDED', 'FAILED', 'CANCELED');

-- C<PERSON><PERSON>num
CREATE TYPE "Role" AS ENUM ('USER', 'ADMIN');

-- <PERSON>reate<PERSON>num
CREATE TYPE "SubscriptionStatus" AS ENUM ('ACTIVE', 'FAILED', 'CANCELED', 'EXPIRED', 'PAUSED', 'PAST_DUE');

-- CreateTable
CREATE TABLE "accounts" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "providerAccountId" TEXT NOT NULL,
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" TEXT,
    "scope" TEXT,
    "id_token" TEXT,
    "session_state" TEXT,

    CONSTRAINT "accounts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "sessions" (
    "id" TEXT NOT NULL,
    "sessionToken" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "sessions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "users" (
    "id" TEXT NOT NULL,
    "name" TEXT,
    "email" TEXT,
    "emailVerified" TIMESTAMP(3),
    "image" TEXT,
    "role" "Role" NOT NULL DEFAULT 'USER',
    "password" TEXT,
    "createdAt" TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP,
    "imageCredits" INTEGER NOT NULL DEFAULT 0,
    "lastCreditReset" TIMESTAMP(3),

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "verification_tokens" (
    "identifier" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expires" TIMESTAMP(3) NOT NULL
);

-- CreateTable
CREATE TABLE "prompts" (
    "id" TEXT NOT NULL,
    "style" TEXT NOT NULL,
    "room" TEXT NOT NULL,
    "prompt" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "type" "PromptType" NOT NULL,

    CONSTRAINT "prompts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "interior_designs" (
    "id" TEXT NOT NULL,
    "replicate_id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "status" "PredictionStatus" NOT NULL DEFAULT 'PROCESSING',
    "inputImage" TEXT NOT NULL,
    "outputImages" TEXT[],
    "prompt" TEXT,
    "style" TEXT,
    "room" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),
    "isPublic" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "interior_designs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "exterior_designs" (
    "id" TEXT NOT NULL,
    "replicate_id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "status" "PredictionStatus" NOT NULL DEFAULT 'PROCESSING',
    "inputImage" TEXT NOT NULL,
    "outputImages" TEXT[],
    "prompt" TEXT,
    "style" TEXT,
    "building" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),
    "isPublic" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "exterior_designs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "background_removals" (
    "id" TEXT NOT NULL,
    "replicate_id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "status" "PredictionStatus" NOT NULL DEFAULT 'PROCESSING',
    "inputImage" TEXT NOT NULL,
    "outputImage" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),
    "isPublic" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "background_removals_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "style_transfers" (
    "id" TEXT NOT NULL,
    "replicate_id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "status" "PredictionStatus" NOT NULL DEFAULT 'PROCESSING',
    "inputImage" TEXT NOT NULL,
    "outputImage" TEXT,
    "style" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),

    CONSTRAINT "style_transfers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "sketch_to_reals" (
    "id" TEXT NOT NULL,
    "replicate_id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "status" "PredictionStatus" NOT NULL DEFAULT 'PROCESSING',
    "inputImage" TEXT NOT NULL,
    "outputImage" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),

    CONSTRAINT "sketch_to_reals_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "upscale_images" (
    "id" TEXT NOT NULL,
    "replicate_id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "status" "PredictionStatus" NOT NULL DEFAULT 'PROCESSING',
    "inputImage" TEXT NOT NULL,
    "outputImage" TEXT,
    "upscaleAmount" DOUBLE PRECISION NOT NULL,
    "creativity" DOUBLE PRECISION NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),
    "isPublic" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "upscale_images_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "transactions" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "credits" INTEGER NOT NULL,
    "orderId" TEXT NOT NULL,
    "packageName" TEXT,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "transactions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "design_suggestions" (
    "id" TEXT NOT NULL,
    "userId" TEXT,
    "room" TEXT NOT NULL,
    "style" TEXT NOT NULL,
    "dimensions" TEXT NOT NULL,
    "suggestion" TEXT NOT NULL,
    "inputImage" TEXT,
    "outputImage" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "slug" TEXT NOT NULL,
    "anonymousId" TEXT,
    "metaDescription" TEXT,

    CONSTRAINT "design_suggestions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "virtual_stagings" (
    "id" TEXT NOT NULL,
    "replicate_id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "status" "PredictionStatus" NOT NULL DEFAULT 'PROCESSING',
    "inputImage" TEXT NOT NULL,
    "outputImage" TEXT,
    "prompt" TEXT NOT NULL,
    "style" TEXT,
    "room" TEXT,
    "excludedElements" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),
    "isPublic" BOOLEAN NOT NULL DEFAULT true,
    "cloudflareImageId" TEXT,

    CONSTRAINT "virtual_stagings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "edit_images" (
    "id" TEXT NOT NULL,
    "replicate_id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "status" "PredictionStatus" NOT NULL DEFAULT 'PROCESSING',
    "inputImage" TEXT NOT NULL,
    "outputImage" TEXT,
    "maskImage" TEXT,
    "prompt" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),
    "isPublic" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "edit_images_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EditedImage" (
    "id" TEXT NOT NULL,
    "replicateId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "status" "PredictionStatus" NOT NULL,
    "inputImage" TEXT NOT NULL,
    "maskImage" TEXT,
    "outputImage" TEXT,
    "prompt" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),

    CONSTRAINT "EditedImage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "subscriptions" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "packageName" TEXT NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "paymentStatus" "SubscriptionStatus" NOT NULL DEFAULT 'ACTIVE',
    "allowPrivateImages" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "lastBillingDate" TIMESTAMP(3),
    "nextBillingDate" TIMESTAMP(3),
    "canceledAt" TIMESTAMP(3),
    "effectiveCancelDate" TIMESTAMP(3),
    "trialEndsAt" TIMESTAMP(3),
    "pausedAt" TIMESTAMP(3),
    "resumesAt" TIMESTAMP(3),
    "subscriptionId" TEXT,
    "variantId" TEXT,
    "customerPortalUrl" TEXT,
    "updatePaymentMethodUrl" TEXT,
    "cancelUrl" TEXT,
    "monthlyCredits" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "subscriptions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_FavoriteVirtualStagings" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "_FavoriteInteriorDesigns" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "_FavoriteExteriorDesigns" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "_FavoriteBackgroundRemovals" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "_FavoriteUpscaleImages" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "_FavoriteEditImages" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "accounts_provider_providerAccountId_key" ON "accounts"("provider", "providerAccountId");

-- CreateIndex
CREATE UNIQUE INDEX "sessions_sessionToken_key" ON "sessions"("sessionToken");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "verification_tokens_token_key" ON "verification_tokens"("token");

-- CreateIndex
CREATE UNIQUE INDEX "verification_tokens_identifier_token_key" ON "verification_tokens"("identifier", "token");

-- CreateIndex
CREATE UNIQUE INDEX "prompts_style_room_type_key" ON "prompts"("style", "room", "type");

-- CreateIndex
CREATE UNIQUE INDEX "interior_designs_replicate_id_key" ON "interior_designs"("replicate_id");

-- CreateIndex
CREATE UNIQUE INDEX "exterior_designs_replicate_id_key" ON "exterior_designs"("replicate_id");

-- CreateIndex
CREATE UNIQUE INDEX "background_removals_replicate_id_key" ON "background_removals"("replicate_id");

-- CreateIndex
CREATE UNIQUE INDEX "style_transfers_replicate_id_key" ON "style_transfers"("replicate_id");

-- CreateIndex
CREATE UNIQUE INDEX "sketch_to_reals_replicate_id_key" ON "sketch_to_reals"("replicate_id");

-- CreateIndex
CREATE UNIQUE INDEX "upscale_images_replicate_id_key" ON "upscale_images"("replicate_id");

-- CreateIndex
CREATE INDEX "transactions_userId_idx" ON "transactions"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "design_suggestions_slug_key" ON "design_suggestions"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "virtual_stagings_replicate_id_key" ON "virtual_stagings"("replicate_id");

-- CreateIndex
CREATE UNIQUE INDEX "edit_images_replicate_id_key" ON "edit_images"("replicate_id");

-- CreateIndex
CREATE UNIQUE INDEX "EditedImage_replicateId_key" ON "EditedImage"("replicateId");

-- CreateIndex
CREATE INDEX "EditedImage_userId_idx" ON "EditedImage"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "subscriptions_userId_key" ON "subscriptions"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "_FavoriteVirtualStagings_AB_unique" ON "_FavoriteVirtualStagings"("A", "B");

-- CreateIndex
CREATE INDEX "_FavoriteVirtualStagings_B_index" ON "_FavoriteVirtualStagings"("B");

-- CreateIndex
CREATE UNIQUE INDEX "_FavoriteInteriorDesigns_AB_unique" ON "_FavoriteInteriorDesigns"("A", "B");

-- CreateIndex
CREATE INDEX "_FavoriteInteriorDesigns_B_index" ON "_FavoriteInteriorDesigns"("B");

-- CreateIndex
CREATE UNIQUE INDEX "_FavoriteExteriorDesigns_AB_unique" ON "_FavoriteExteriorDesigns"("A", "B");

-- CreateIndex
CREATE INDEX "_FavoriteExteriorDesigns_B_index" ON "_FavoriteExteriorDesigns"("B");

-- CreateIndex
CREATE UNIQUE INDEX "_FavoriteBackgroundRemovals_AB_unique" ON "_FavoriteBackgroundRemovals"("A", "B");

-- CreateIndex
CREATE INDEX "_FavoriteBackgroundRemovals_B_index" ON "_FavoriteBackgroundRemovals"("B");

-- CreateIndex
CREATE UNIQUE INDEX "_FavoriteUpscaleImages_AB_unique" ON "_FavoriteUpscaleImages"("A", "B");

-- CreateIndex
CREATE INDEX "_FavoriteUpscaleImages_B_index" ON "_FavoriteUpscaleImages"("B");

-- CreateIndex
CREATE UNIQUE INDEX "_FavoriteEditImages_AB_unique" ON "_FavoriteEditImages"("A", "B");

-- CreateIndex
CREATE INDEX "_FavoriteEditImages_B_index" ON "_FavoriteEditImages"("B");

-- AddForeignKey
ALTER TABLE "accounts" ADD CONSTRAINT "accounts_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "sessions" ADD CONSTRAINT "sessions_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "interior_designs" ADD CONSTRAINT "interior_designs_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "exterior_designs" ADD CONSTRAINT "exterior_designs_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "background_removals" ADD CONSTRAINT "background_removals_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "style_transfers" ADD CONSTRAINT "style_transfers_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "sketch_to_reals" ADD CONSTRAINT "sketch_to_reals_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "upscale_images" ADD CONSTRAINT "upscale_images_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "transactions" ADD CONSTRAINT "transactions_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "design_suggestions" ADD CONSTRAINT "design_suggestions_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "virtual_stagings" ADD CONSTRAINT "virtual_stagings_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "edit_images" ADD CONSTRAINT "edit_images_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EditedImage" ADD CONSTRAINT "EditedImage_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "subscriptions" ADD CONSTRAINT "subscriptions_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_FavoriteVirtualStagings" ADD CONSTRAINT "_FavoriteVirtualStagings_A_fkey" FOREIGN KEY ("A") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_FavoriteVirtualStagings" ADD CONSTRAINT "_FavoriteVirtualStagings_B_fkey" FOREIGN KEY ("B") REFERENCES "virtual_stagings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_FavoriteInteriorDesigns" ADD CONSTRAINT "_FavoriteInteriorDesigns_A_fkey" FOREIGN KEY ("A") REFERENCES "interior_designs"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_FavoriteInteriorDesigns" ADD CONSTRAINT "_FavoriteInteriorDesigns_B_fkey" FOREIGN KEY ("B") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_FavoriteExteriorDesigns" ADD CONSTRAINT "_FavoriteExteriorDesigns_A_fkey" FOREIGN KEY ("A") REFERENCES "exterior_designs"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_FavoriteExteriorDesigns" ADD CONSTRAINT "_FavoriteExteriorDesigns_B_fkey" FOREIGN KEY ("B") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_FavoriteBackgroundRemovals" ADD CONSTRAINT "_FavoriteBackgroundRemovals_A_fkey" FOREIGN KEY ("A") REFERENCES "background_removals"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_FavoriteBackgroundRemovals" ADD CONSTRAINT "_FavoriteBackgroundRemovals_B_fkey" FOREIGN KEY ("B") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_FavoriteUpscaleImages" ADD CONSTRAINT "_FavoriteUpscaleImages_A_fkey" FOREIGN KEY ("A") REFERENCES "upscale_images"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_FavoriteUpscaleImages" ADD CONSTRAINT "_FavoriteUpscaleImages_B_fkey" FOREIGN KEY ("B") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_FavoriteEditImages" ADD CONSTRAINT "_FavoriteEditImages_A_fkey" FOREIGN KEY ("A") REFERENCES "edit_images"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_FavoriteEditImages" ADD CONSTRAINT "_FavoriteEditImages_B_fkey" FOREIGN KEY ("B") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
