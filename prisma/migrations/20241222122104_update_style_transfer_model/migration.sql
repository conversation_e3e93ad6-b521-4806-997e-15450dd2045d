/*
  Warnings:

  - You are about to drop the column `style` on the `style_transfers` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[userId,imageType,virtualStagingId,interiorDesignId,exteriorDesignId,editImageId,upscaleImageId,backgroundRemovalId,styleTransferId]` on the table `image_ratings` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `styleImage` to the `style_transfers` table without a default value. This is not possible if the table is not empty.

*/
-- AlterEnum
ALTER TYPE "ImageType" ADD VALUE 'style_transfers';

-- DropForeignKey
ALTER TABLE "style_transfers" DROP CONSTRAINT "style_transfers_userId_fkey";

-- DropIndex
DROP INDEX "image_ratings_userId_imageType_virtualStagingId_interiorDes_key";

-- AlterTable
ALTER TABLE "image_ratings" ADD COLUMN     "styleTransferId" TEXT;

-- AlterTable
ALTER TABLE "style_transfers" DROP COLUMN "style",
ADD COLUMN     "averageRating" DOUBLE PRECISION,
ADD COLUMN     "isPublic" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "model" TEXT,
ADD COLUMN     "negativePrompt" TEXT,
ADD COLUMN     "prompt" TEXT,
ADD COLUMN     "ratedAt" TIMESTAMP(3),
ADD COLUMN     "rating" INTEGER,
ADD COLUMN     "structureImage" TEXT,
ADD COLUMN     "styleImage" TEXT NOT NULL,
ADD COLUMN     "totalRatings" INTEGER NOT NULL DEFAULT 0;

-- CreateTable
CREATE TABLE "_FavoriteStyleTransfers" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "_FavoriteStyleTransfers_AB_unique" ON "_FavoriteStyleTransfers"("A", "B");

-- CreateIndex
CREATE INDEX "_FavoriteStyleTransfers_B_index" ON "_FavoriteStyleTransfers"("B");

-- CreateIndex
CREATE INDEX "image_ratings_styleTransferId_idx" ON "image_ratings"("styleTransferId");

-- CreateIndex
CREATE UNIQUE INDEX "image_ratings_userId_imageType_virtualStagingId_interiorDes_key" ON "image_ratings"("userId", "imageType", "virtualStagingId", "interiorDesignId", "exteriorDesignId", "editImageId", "upscaleImageId", "backgroundRemovalId", "styleTransferId");

-- CreateIndex
CREATE INDEX "style_transfers_userId_idx" ON "style_transfers"("userId");

-- CreateIndex
CREATE INDEX "style_transfers_rating_idx" ON "style_transfers"("rating");

-- AddForeignKey
ALTER TABLE "style_transfers" ADD CONSTRAINT "style_transfers_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "image_ratings" ADD CONSTRAINT "image_ratings_styleTransferId_fkey" FOREIGN KEY ("styleTransferId") REFERENCES "style_transfers"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_FavoriteStyleTransfers" ADD CONSTRAINT "_FavoriteStyleTransfers_A_fkey" FOREIGN KEY ("A") REFERENCES "style_transfers"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_FavoriteStyleTransfers" ADD CONSTRAINT "_FavoriteStyleTransfers_B_fkey" FOREIGN KEY ("B") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
