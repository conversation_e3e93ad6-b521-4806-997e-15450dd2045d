/*
  Warnings:

  - You are about to drop the column `imageId` on the `image_ratings` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[userId,imageType,virtualStagingId,interiorDesignId,exteriorDesignId,editImageId,upscaleImageId,backgroundRemovalId]` on the table `image_ratings` will be added. If there are existing duplicate values, this will fail.

*/
-- DropForeignKey
ALTER TABLE "image_ratings" DROP CONSTRAINT "image_ratings_background_removal_fkey";

-- DropForeignKey
ALTER TABLE "image_ratings" DROP CONSTRAINT "image_ratings_edit_image_fkey";

-- DropForeignKey
ALTER TABLE "image_ratings" DROP CONSTRAINT "image_ratings_exterior_design_fkey";

-- DropForeignKey
ALTER TABLE "image_ratings" DROP CONSTRAINT "image_ratings_interior_design_fkey";

-- DropForeignKey
ALTER TABLE "image_ratings" DROP CONSTRAINT "image_ratings_upscale_image_fkey";

-- DropF<PERSON><PERSON><PERSON>ey
ALTER TABLE "image_ratings" DROP CONSTRAINT "image_ratings_virtual_staging_fkey";

-- DropIndex
DROP INDEX "image_ratings_imageId_idx";

-- DropIndex
DROP INDEX "image_ratings_userId_imageId_imageType_key";

-- AlterTable
ALTER TABLE "image_ratings" DROP COLUMN "imageId",
ADD COLUMN     "backgroundRemovalId" TEXT,
ADD COLUMN     "editImageId" TEXT,
ADD COLUMN     "exteriorDesignId" TEXT,
ADD COLUMN     "interiorDesignId" TEXT,
ADD COLUMN     "upscaleImageId" TEXT,
ADD COLUMN     "virtualStagingId" TEXT;

-- CreateIndex
CREATE INDEX "image_ratings_virtualStagingId_idx" ON "image_ratings"("virtualStagingId");

-- CreateIndex
CREATE INDEX "image_ratings_interiorDesignId_idx" ON "image_ratings"("interiorDesignId");

-- CreateIndex
CREATE INDEX "image_ratings_exteriorDesignId_idx" ON "image_ratings"("exteriorDesignId");

-- CreateIndex
CREATE INDEX "image_ratings_editImageId_idx" ON "image_ratings"("editImageId");

-- CreateIndex
CREATE INDEX "image_ratings_upscaleImageId_idx" ON "image_ratings"("upscaleImageId");

-- CreateIndex
CREATE INDEX "image_ratings_backgroundRemovalId_idx" ON "image_ratings"("backgroundRemovalId");

-- CreateIndex
CREATE UNIQUE INDEX "image_ratings_userId_imageType_virtualStagingId_interiorDes_key" ON "image_ratings"("userId", "imageType", "virtualStagingId", "interiorDesignId", "exteriorDesignId", "editImageId", "upscaleImageId", "backgroundRemovalId");

-- AddForeignKey
ALTER TABLE "image_ratings" ADD CONSTRAINT "image_ratings_virtualStagingId_fkey" FOREIGN KEY ("virtualStagingId") REFERENCES "virtual_stagings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "image_ratings" ADD CONSTRAINT "image_ratings_interiorDesignId_fkey" FOREIGN KEY ("interiorDesignId") REFERENCES "interior_designs"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "image_ratings" ADD CONSTRAINT "image_ratings_exteriorDesignId_fkey" FOREIGN KEY ("exteriorDesignId") REFERENCES "exterior_designs"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "image_ratings" ADD CONSTRAINT "image_ratings_editImageId_fkey" FOREIGN KEY ("editImageId") REFERENCES "edit_images"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "image_ratings" ADD CONSTRAINT "image_ratings_upscaleImageId_fkey" FOREIGN KEY ("upscaleImageId") REFERENCES "upscale_images"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "image_ratings" ADD CONSTRAINT "image_ratings_backgroundRemovalId_fkey" FOREIGN KEY ("backgroundRemovalId") REFERENCES "background_removals"("id") ON DELETE CASCADE ON UPDATE CASCADE;
