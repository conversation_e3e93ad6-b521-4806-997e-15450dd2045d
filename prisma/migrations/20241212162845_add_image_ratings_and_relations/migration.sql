/*
  Warnings:

  - Changed the type of `imageType` on the `image_ratings` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- CreateEnum
CREATE TYPE "ImageType" AS ENUM ('virtual_stagings', 'interior_designs', 'exterior_designs', 'edit_images', 'upscale_images', 'background_removals');

-- DropForeignKey
ALTER TABLE "background_removals" DROP CONSTRAINT "background_removals_userId_fkey";

-- DropForeignKey
ALTER TABLE "edit_images" DROP CONSTRAINT "edit_images_userId_fkey";

-- DropForeign<PERSON>ey
ALTER TABLE "exterior_designs" DROP CONSTRAINT "exterior_designs_userId_fkey";

-- DropForeignKey
ALTER TABLE "interior_designs" DROP CONSTRAINT "interior_designs_userId_fkey";

-- DropForeignKey
ALTER TABLE "upscale_images" DROP CONSTRAINT "upscale_images_userId_fkey";

-- Drop<PERSON><PERSON><PERSON><PERSON>ey
ALTER TABLE "virtual_stagings" DROP CONSTRAINT "virtual_stagings_userId_fkey";

-- AlterTable
ALTER TABLE "image_ratings" DROP COLUMN "imageType",
ADD COLUMN     "imageType" "ImageType" NOT NULL;

-- CreateIndex
CREATE INDEX "background_removals_userId_idx" ON "background_removals"("userId");

-- CreateIndex
CREATE INDEX "background_removals_rating_idx" ON "background_removals"("rating");

-- CreateIndex
CREATE INDEX "edit_images_userId_idx" ON "edit_images"("userId");

-- CreateIndex
CREATE INDEX "edit_images_rating_idx" ON "edit_images"("rating");

-- CreateIndex
CREATE INDEX "exterior_designs_userId_idx" ON "exterior_designs"("userId");

-- CreateIndex
CREATE INDEX "exterior_designs_rating_idx" ON "exterior_designs"("rating");

-- CreateIndex
CREATE INDEX "image_ratings_imageId_idx" ON "image_ratings"("imageId");

-- CreateIndex
CREATE INDEX "image_ratings_userId_idx" ON "image_ratings"("userId");

-- CreateIndex
CREATE INDEX "image_ratings_rating_idx" ON "image_ratings"("rating");

-- CreateIndex
CREATE UNIQUE INDEX "image_ratings_userId_imageId_imageType_key" ON "image_ratings"("userId", "imageId", "imageType");

-- CreateIndex
CREATE INDEX "interior_designs_userId_idx" ON "interior_designs"("userId");

-- CreateIndex
CREATE INDEX "interior_designs_rating_idx" ON "interior_designs"("rating");

-- CreateIndex
CREATE INDEX "upscale_images_userId_idx" ON "upscale_images"("userId");

-- CreateIndex
CREATE INDEX "upscale_images_rating_idx" ON "upscale_images"("rating");

-- CreateIndex
CREATE INDEX "virtual_stagings_userId_idx" ON "virtual_stagings"("userId");

-- CreateIndex
CREATE INDEX "virtual_stagings_rating_idx" ON "virtual_stagings"("rating");

-- AddForeignKey
ALTER TABLE "interior_designs" ADD CONSTRAINT "interior_designs_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "exterior_designs" ADD CONSTRAINT "exterior_designs_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "background_removals" ADD CONSTRAINT "background_removals_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "upscale_images" ADD CONSTRAINT "upscale_images_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "virtual_stagings" ADD CONSTRAINT "virtual_stagings_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "edit_images" ADD CONSTRAINT "edit_images_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "image_ratings" ADD CONSTRAINT "image_ratings_interior_design_fkey" FOREIGN KEY ("imageId") REFERENCES "interior_designs"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "image_ratings" ADD CONSTRAINT "image_ratings_exterior_design_fkey" FOREIGN KEY ("imageId") REFERENCES "exterior_designs"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "image_ratings" ADD CONSTRAINT "image_ratings_virtual_staging_fkey" FOREIGN KEY ("imageId") REFERENCES "virtual_stagings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "image_ratings" ADD CONSTRAINT "image_ratings_edit_image_fkey" FOREIGN KEY ("imageId") REFERENCES "edit_images"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "image_ratings" ADD CONSTRAINT "image_ratings_upscale_image_fkey" FOREIGN KEY ("imageId") REFERENCES "upscale_images"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "image_ratings" ADD CONSTRAINT "image_ratings_background_removal_fkey" FOREIGN KEY ("imageId") REFERENCES "background_removals"("id") ON DELETE CASCADE ON UPDATE CASCADE;
