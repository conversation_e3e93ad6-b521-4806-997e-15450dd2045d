const { PrismaClient } = require("@prisma/client");
const {
  stylesData,
  roomData,
  buildingData,
} = require("../public/static/styles/styles");

const prisma = new PrismaClient();

async function generateAndStorePrompts() {
  const batchSize = 100;
  const prompts = [];

  // Generate interior prompts
  for (const [styleKey, styleValue] of Object.entries(stylesData)) {
    for (const [roomKey, roomValue] of Object.entries(roomData)) {
      const prompt = `Interior design photography of a ${styleValue.style} style ${roomValue.room}, ${roomValue.furnitures}, ${styleValue.materials}, ${styleValue.furnitureTypes}, (masterpiece), (high quality), best quality, real, (realistic), super detailed, (full detail), (4k), 8k,`;

      prompts.push({
        style: styleKey,
        room: roomKey,
        prompt: prompt,
        type: "INTERIOR",
      });

      if (prompts.length >= batchSize) {
        await insertPromptBatch(prompts);
        prompts.length = 0;
      }
    }
  }

  // Generate exterior prompts
  for (const [styleKey, styleValue] of Object.entries(stylesData)) {
    for (const [buildingKey, buildingValue] of Object.entries(buildingData)) {
      const prompt = `Exterior design photography of a ${
        styleValue.style
      } style ${buildingValue.building}, ${styleValue.materials}, ${
        styleValue.architecturalElements || ""
      }, (masterpiece), (high quality), best quality, real, (realistic), super detailed, (full detail), (4k), 8k,`;

      prompts.push({
        style: styleKey,
        room: buildingKey, // We're using the 'room' field for buildings as well
        prompt: prompt,
        type: "EXTERIOR",
      });

      if (prompts.length >= batchSize) {
        await insertPromptBatch(prompts);
        prompts.length = 0;
      }
    }
  }

  // Insert any remaining prompts
  if (prompts.length > 0) {
    await insertPromptBatch(prompts);
  }

  console.log("All prompts have been generated and stored.");
}

async function insertPromptBatch(prompts) {
  try {
    await prisma.prompt.createMany({
      data: prompts,
      skipDuplicates: true,
    });
    console.log(`Inserted batch of ${prompts.length} prompts`);
  } catch (error) {
    console.error("Error inserting prompt batch:", error);
  }
}

generateAndStorePrompts()
  .catch((e) => {
    console.error("Script error:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
