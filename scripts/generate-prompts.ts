import { PrismaClient, PromptType } from "@prisma/client";
import { buildingData, BuildingDetails } from "../public/static/buildings";
import {
  exteriorStyleData,
  ExteriorStyleDetails,
} from "../public/static/exterior-styles";
import { roomData } from "../public/static/rooms";
import { styleData } from "../public/static/styles";

const prisma = new PrismaClient();

async function deleteExistingPrompts() {
  try {
    await prisma.prompt.deleteMany({});
    console.log("Successfully deleted all existing prompts");
  } catch (error) {
    console.error("Error deleting existing prompts:", error);
    throw error;
  }
}

async function generateAndStorePrompts() {
  // First, delete all existing prompts
  await deleteExistingPrompts();

  const batchSize = 100;
  const prompts: Array<{
    style: string;
    room: string;
    prompt: string;
    type: PromptType;
  }> = [];

  // Generate interior prompts
  for (const [styleKey, styleValue] of Object.entries(styleData)) {
    for (const [roomKey, roomValue] of Object.entries(roomData)) {
      const features = roomValue.features || [];
      const materials = styleValue.materials || [];

      const prompt = `Interior design photography of a ${
        styleValue.name
      } style ${roomValue.name}, ${
        roomValue.description
      }, featuring ${features.join(", ")}, with ${materials.join(
        ", "
      )} materials, (masterpiece), (high quality), best quality, real, (realistic), super detailed, (full detail), (4k), 8k,`;

      prompts.push({
        style: styleKey,
        room: roomKey,
        prompt: prompt,
        type: PromptType.INTERIOR,
      });

      if (prompts.length >= batchSize) {
        await insertPromptBatch(prompts);
        prompts.length = 0;
      }
    }
  }

  // Generate exterior prompts
  for (const [styleKey, styleValue] of Object.entries(exteriorStyleData)) {
    for (const [buildingKey, buildingValue] of Object.entries(buildingData)) {
      const keyFeatures = buildingValue.keyFeatures || [];
      const materials = styleValue.materials || [];
      const architecturalFeatures = styleValue.architecturalFeatures || [];

      const prompt = `Exterior design photography of a ${
        styleValue.name
      } style ${buildingValue.name}, featuring ${keyFeatures.join(
        ", "
      )}, with ${materials.join(", ")}, ${architecturalFeatures.join(
        ", "
      )}, (masterpiece), (high quality), best quality, real, (realistic), super detailed, (full detail), (4k), 8k,`;

      prompts.push({
        style: styleKey,
        room: buildingKey, // Using room field for building as per schema
        prompt: prompt,
        type: PromptType.EXTERIOR,
      });

      if (prompts.length >= batchSize) {
        await insertPromptBatch(prompts);
        prompts.length = 0;
      }
    }
  }

  // Insert any remaining prompts
  if (prompts.length > 0) {
    await insertPromptBatch(prompts);
  }

  console.log("All prompts have been generated and stored.");
}

async function insertPromptBatch(
  prompts: Array<{
    style: string;
    room: string;
    prompt: string;
    type: PromptType;
  }>
) {
  try {
    await prisma.prompt.createMany({
      data: prompts,
      skipDuplicates: true,
    });
    console.log(`Inserted batch of ${prompts.length} prompts`);
  } catch (error) {
    console.error("Error inserting prompt batch:", error);
  }
}

generateAndStorePrompts()
  .catch((e) => {
    console.error("Script error:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
