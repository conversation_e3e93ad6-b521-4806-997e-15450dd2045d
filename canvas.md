# Canvas Module Documentation

This document provides a detailed overview of the Canvas module used in our application. It covers the TypeScript types, state management using Zustand, UI components, interaction workflows, and specific components like the canvas, toolbars, and selection rectangle.

---

## 1. Overview

The Canvas module is responsible for rendering and managing an interactive image editing canvas. Users can upload images, draw masks (using brush, eraser, rectangle, or lasso tools), generate new images based on prompts, and apply magic fill effects. The module primarily leverages React, Zustand for state management, Konva for canvas manipulation, and a variety of UI components.

## 2. Type Definitions (src/modules/dashboard/main-features/types/canvas.ts)

The module uses several TypeScript interfaces and types to ensure type safety and clarity. Key types include:

- **CanvasPosition**: Represents the x and y coordinates on the canvas.
  ```typescript
  interface CanvasPosition {
    x: number;
    y: number;
  }
  ```

- **CanvasSize**: Specifies canvas dimensions;
  ```typescript
  interface CanvasSize {
    width: number;
    height: number;
  }
  ```

- **CanvasRect**: Defines a rectangular area, used for selection and cropping.
  ```typescript
  interface CanvasRect {
    x: number;
    y: number;
    width: number;
    height: number;
  }
  ```

- **CanvasLine**: Represents a drawn stroke/mask. Contains the tool type, stroke points, and stroke width.
  ```typescript
  interface CanvasLine {
    tool: "brush" | "eraser" | "rectangle" | "lasso";
    points: number[];
    strokeWidth: number;
  }
  ```

- **GeneratedImage**: An object holding information for images generated or edited on the canvas, including position, dimensions, rotation, and selection state.
  ```typescript
  interface GeneratedImage {
    image: HTMLImageElement;
    position: CanvasPosition;
    width: number;
    height: number;
    rotation: number;
    isSelected: boolean;
  }
  ```

- **TransformConfig**: Holds state related to transforming/moving generated images,
  ```typescript
  interface TransformConfig {
    selectedImageIndex: number | null;
    isDragging: boolean;
    isResizing: boolean;
    isRotating: boolean;
  }
  ```

- **CanvasState**: The base state interface for the canvas, which includes properties like mode, tool settings, dimensions, prompts, and images.

- **CanvasToolbarProps and MagicFillToolbarProps**: Define the props expected by the toolbars.

- **CanvasMode (Enum)**: Represents the current mode of the canvas such as Move, Pan, MagicFill, Generate, and SelectArea.

## 3. State Management (src/modules/dashboard/main-features/store/canvas-store.ts)

The application uses Zustand to manage the canvas state in a centralized store. The store extends the `CanvasState` and adds additional state properties and methods:

### State Properties

- **Base State (from CanvasState):**
  - `mode`: The current canvas mode (e.g., Move, Pan, Generate, MagicFill, or SelectArea).
  - `maskTool`: The currently selected mask tool (e.g., brush, eraser, rectangle, lasso).
  - `brushSize`: The current brush size used for drawing.
  - `lines`: An array of `CanvasLine` objects representing the drawn strokes.
  - `dimensions`: Holds the canvas width and height.
  - `selectionArea`: A `CanvasRect` defining the user's selected cropping area (if any).
  - `magicFillAreaSelection`: Boolean flag indicating if the selection area for magic fill is active.
  - `zoom`: The zoom level of the canvas.
  - `userId`: The ID of the current user.
  - `prompt` & `negativePrompt`: Prompts used for image generation.
  - `generatedImages`: An array of `GeneratedImage` objects produced after image generation/editing.
  - `transformConfig`: The transformation configuration for manipulating images.
  - `inputImage` & `inputImagePosition`: The original uploaded image and its position.
  - `isGenerating` & `isUploadingImage`: Loading flags for asynchronous operations.

- **Additional State:**
  - `imageHistory`: History of image URLs (as strings) for undo/redo features.
  - `currentImageIndex`: Tracks the current position in the image history.
  - `viewportOffset`: The current offset of the canvas viewport.
  - `lineHistory` & `currentLineIndex`: Manage history for drawn lines to support undo/redo for brush strokes.
  - `maskUrl`: URL of the generated mask image (if any).

### State Methods

Key methods exposed by the store include:

- `setMode`, `setMaskTool`, `setBrushSize`, `setPrompt`, `setNegativePrompt`, `setUserId`, `setZoom`: For updating various simple properties.
- `startNewLine` & `updateActiveLine`: For adding or updating a line in the drawings.
- `clearLines`: Clears all brush strokes and resets the mask URL.
- `setDimensions`, `setSelectionArea`, `setMagicFillAreaSelection`: For managing layout and selection areas.
- `setGeneratedImages`, `setTransformConfig`, `setInputImage`, `setInputImagePosition`: For image management operations.
- `setIsGenerating` & `setIsUploadingImage`: To manage loading states during async operations.
- `invertMask`: To toggle the mask tool between brush and eraser.
- `undo` & `redo`: Provides simple undo/redo functionality for line drawings.
- Image transformation related methods such as `setImagePosition`, `updateImageTransform`, and `addGeneratedImage`.

## 4. UI Components

The module provides several React components that interact with the canvas state and provide an intuitive UI for the user.

### A. Main Toolbar (src/modules/dashboard/main-features/components/canvas/toolbar/main-toolbar.tsx)

The `MainToolbar` component is a client-side component that renders multiple tool sections:

- **NavigationTools:** Provides buttons for panning the canvas and selecting/translating images.
- **MagicFillButton:** Shown when no selection area is defined, allows the user to enter MagicFill mode.
- **HistoryControls:** Offers undo and redo functionalities for brush strokes and other state changes.
- **ImageActions:** Includes buttons for uploading images, generating images, saving, and downloading images.

When a user clicks the generate image button (with the upload or generate icon), the following occurs:

- In **Generate Mode** (`CanvasMode.Generate`):
  - The toolbar triggers the `handleGenerate` function which clears any mask drawing (`clearLines`), sets the loading state (`setIsGenerating`), and calls an action (e.g., `editImage` with mode "generate") to obtain a new image based on the prompt. On success, the new image is added to `generatedImages`.

- In **MagicFill Mode** (`CanvasMode.MagicFill`):
  - The toolbar does not directly call image generation. Instead, it relies on the user drawing a mask and then clicking the "Next" button in the `MagicFillToolbar` to proceed with the mask selection. The actual image generation in magic fill is triggered later in the `ImageCanvas` component via `handleMagicFill`.

### B. Magic Fill Toolbar (src/modules/dashboard/main-features/components/canvas/toolbar/magic-fill-toolbar.tsx)

This toolbar is specific to the MagicFill mode. It provides buttons for different mask tools:

- **Rectangle Mask, Lasso Mask, Brush Mask, Eraser:** Allow different approaches for drawing the mask.
- **Brush Settings:** Toggles additional settings related to the brush tool.

It also provides controls for cancelling magic fill or proceeding to the next step using the drawn mask. Note that it does not directly trigger the generation of the image – its role is to define the mask (and selection area) required for a magic fill. The actual generation is handled in the `ImageCanvas` component once a valid mask and/or selection area has been established.

### C. Image Canvas (src/modules/dashboard/main-features/components/canvas/image-canvas.tsx)

The `ImageCanvas` component is the core of the module, integrating Konva for canvas rendering. Key responsibilities include:

- **Rendering Layers:**
  - A background grid.
  - The uploaded input image.
  - Generated images with transformation capabilities.
  - Active brushes/strokes for real-time feedback.
  - A composite mask layer for MagicFill processes (via `MagicFillCompositeLayer`).
  
- **Event Handling:**
  - Mouse events (`onMouseDown`, `onMouseMove`, `onMouseUp`) for drawing strokes or setting selection areas.
  - Wheel events for zooming the canvas.
  - Drag and transform events for moving/resizing images.

- **Workflow for Generation:**
  - The component conditionally sets the generate behavior based on the current mode. The `onGenerate` prop passed to `MainToolbar` is set to:
    - `handleGenerate` in Generate mode (where image generation is initiated using the prompt).
    - `handleMagicFill` in MagicFill mode (which processes the mask and selection area, crops the images, uploads them, and then calls the replace action via `editImage`).

### D. Selection Rectangle (src/modules/dashboard/main-features/components/canvas/SelectionRect.tsx)

This component defines a draggable and resizable selection rectangle using Konva's `Rect` and `Transformer`.

- **Props:**
  - `x, y, width, height`: Define the current position and size of the selection rectangle.
  - `onChange`: Callback function to update the selection attributes when the rectangle is moved or resized.

The `SelectionRect` provides an interface for users to adjust the area of the image to be affected by magic fill operations.

## 5. Workflow for Image Generation

### Generate Mode

1. **User Action:** Clicks the generate image button
2. **Toolbar Behavior:** `MainToolbar` triggers `handleGenerate`
3. **Processing:**
   - Clears any existing brush strokes or masks via `clearLines`
   - Sets loading state (`setIsGenerating`)
   - Calls an asynchronous action (`editImage` with mode "generate") with the current prompt parameters
4. **Update Store:** On success, the generated image is added to `generatedImages` using `addGeneratedImage`.

### MagicFill Mode

1. **User Action:** Selects MagicFill mode (via `MagicFillButton`) and draws a mask using the available tools in `MagicFillToolbar`.
2. **Toolbar Behavior:**
   - The MagicFill toolbar provides options to choose mask tool types or adjust brush settings.
   - Once drawing is complete, the user can choose to cancel or proceed (using the Cancel/Next buttons).
3. **Processing in ImageCanvas:**
   - If no selection area was defined, the `handleProceedMagicFill` callback calculates a default selection area based on the drawn mask or centers it in the image.
   - The prompt input appears, and triggering generate now calls `handleMagicFill`, which:
     - Crops the input image and the drawn mask based on the selection area
     - Uploads the cropped images
     - Calls `editImage` with mode "replace" using the uploaded image URLs
4. **Update Store:** The newly generated image replaces or adds to the images on the canvas.

## 6. Summary

- **Canvas Types and Interfaces:** Define the shape and requirements of positions, sizes, masks, lines, and images.
- **State Management (Zustand):** Holds both base and additional state related to drawing, selection, image generation, and undo/redo functionality.
- **UI Components:** The `MainToolbar` and `MagicFillToolbar` manage user input, while `ImageCanvas` is responsible for rendering the various visual elements and handling user interactions.
- **Image Generation Workflows:** Vary depending on the mode. In Generate mode, image generation is straightforward, based on the prompt. In MagicFill mode, the mask and selection area must be properly defined before triggering image generation.

## Conclusion

This documentation outlines the interplay between various components in the Canvas module. It highlights the importance of the unified state (via Zustand), clear type definitions, and separation of concerns between different UI components. Troubleshooting issues, such as why the MagicFill toolbar might not trigger image generation, should focus on ensuring that the workflow in `ImageCanvas` correctly distinguishes between Generate and MagicFill modes and that the proper callbacks (like `handleMagicFill` and `handleProceedMagicFill`) are being triggered. 