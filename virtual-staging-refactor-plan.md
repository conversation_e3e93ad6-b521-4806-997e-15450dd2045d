<PLANNING>
1.  **consolidate-state-and-hydration**:
    *   **Goal**: Ensure a single source of truth for initial data and streamline store initialization.
    *   **Action**: Modify `page.tsx` (Server Component) to fetch *all* required initial data: user details, credits (`getUserImageCredits`), and privacy access status (`checkPrivacyAccess`).
    *   **Action**: Implement a client-side provider or initialization logic (e.g., a component that runs once) to hydrate the Zustand stores (`useImageCreditsStore`, `usePrivacySettings`) with this server-fetched data. This removes the need for the `useEffect`-based `checkPrivacyAccess` call within `virtual-staging-settings-form.tsx`.
    *   **Action**: Centralize `hasEnoughCredits` logic within `useImageCreditsStore` or a selector hook based on it, avoiding redundant checks in multiple components.

2.  **refactor-form-component-structure**:
    *   **Goal**: Improve separation of concerns and reduce prop drilling.
    *   **Action**: Extract mobile drawer logic (`Drawer`, `DrawerTrigger`, `DrawerContent`, `useMobileDrawer` usage) from `virtual-staging-settings-form.tsx` into a dedicated wrapper component or hook that conditionally renders the drawer around `virtual-staging-form-content.tsx` based on `isMobile`.
    *   **Action**: Extract paywall logic (`PaywallDialog` rendering, state (`showPaywall`, `dialogContent`), and trigger conditions) from `virtual-staging-settings-form.tsx`. Create a dedicated `VirtualStagingPaywall` component or hook that consumes `useImageCreditsStore` and `useSubscription` and handles showing the dialog based on credits or privacy attempts.
    *   **Action**: Refine `virtual-staging-settings-form.tsx` to be a leaner client container, primarily responsible for:
        *   Connecting the form state (via `useVirtualStagingFormStore`).
        *   Handling the form submission logic (see step 3).
        *   Rendering the `virtual-staging-form-content.tsx` (potentially wrapped by the new drawer component).
        *   Rendering the new `VirtualStagingPaywall` component.
    *   **Action**: Minimize props passed to `virtual-staging-form-content.tsx`. Allow it to read directly from `useVirtualStagingFormStore` via selectors where feasible. Pass down only essential event handlers (like `onSubmit`, `onPrivacyToggle` if still needed at that level) or have it dispatch actions/call store methods. Consider breaking `virtual-staging-form-content.tsx` further by step if it remains too large.

3.  **optimize-form-submission**:
    *   **Goal**: Simplify form state management related to submission using modern patterns.
    *   **Action**: Refactor the form submission in `virtual-staging-settings-form.tsx` to use React's `useActionState` (or `useFormState`) hook with the `generateVirtualStagingImage` server action.
    *   **Action**: Modify `generateVirtualStagingImage` server action to return a more structured response indicating success (with prediction ID/placeholders), validation errors, credit errors, or other specific failure reasons.
    *   **Action**: Use the state returned by `useActionState` to handle pending states (for the submit button, potentially disabling parts of the form) and display errors directly related to the submission attempt, reducing manual `setIsLoading` and toast calls for submission-related feedback. Keep Zod validation but potentially trigger it within the action or just before calling it.

4.  **streamline-image-display**:
    *   **Goal**: Ensure the display component is robust and focused.
    *   **Action**: Review the Ably subscription logic in `virtual-staging-image-display.tsx` for cleanup best practices (e.g., unsubscribing effectively).
    *   **Action**: Consolidate state updates within the component. Ensure `isDownloading` and `isUpscaling` states are managed cleanly, perhaps using a single state object keyed by image ID.
    *   **Action**: Ensure the `upscaleImage` action call follows similar patterns (potentially using `useActionState` if complex feedback is needed, or just handling promise states if simple).

5.  **enhance-code-quality-and-typing**:
    *   **Goal**: Improve maintainability and developer experience.
    *   **Action**: Define shared types (e.g., for `Prediction`, form parameters, server action responses) in dedicated `types/*.ts` files and import them consistently.
    *   **Action**: Reduce direct prop drilling further by leveraging Zustand selectors within components needing specific state slices.
    *   **Action**: Add JSDoc comments to exported components, hooks, stores, and complex functions as per your guidelines.
    *   **Action**: Ensure consistent error handling patterns, mapping server action error responses to user-facing feedback (toasts, inline messages).
</PLANNING>